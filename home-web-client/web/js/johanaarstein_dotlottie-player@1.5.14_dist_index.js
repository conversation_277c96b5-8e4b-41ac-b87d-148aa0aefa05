!function(exports){"use strict";/**
	 * @license
	 * Copyright 2019 Google LLC
	 * SPDX-License-Identifier: BSD-3-Clause
	 */let t$1=window,e$4=t$1.ShadowRoot&&(void 0===t$1.ShadyCSS||t$1.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,s$3=Symbol(),n$5=new WeakMap,o$4=class{constructor(D,F,O){if(this._$cssResult$=!0,O!==s$3)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=D,this.t=F}get styleSheet(){let D=this.o,F=this.t;if(e$4&&void 0===D){let O=void 0!==F&&1===F.length;O&&(D=n$5.get(F)),void 0===D&&((this.o=D=new CSSStyleSheet).replaceSync(this.cssText),O&&n$5.set(F,D))}return D}toString(){return this.cssText}},r$2=D=>new o$4("string"==typeof D?D:D+"",void 0,s$3),i$5=(D,...F)=>{let O=1===D.length?D[0]:F.reduce((F,O,U)=>F+(D=>{if(!0===D._$cssResult$)return D.cssText;if("number"==typeof D)return D;throw Error("Value passed to 'css' function must be a 'css' function result: "+D+". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.")})(O)+D[U+1],D[0]);return new o$4(O,D,s$3)},S$1=(D,F)=>{e$4?D.adoptedStyleSheets=F.map(D=>D instanceof CSSStyleSheet?D:D.styleSheet):F.forEach(F=>{let O=document.createElement("style"),U=t$1.litNonce;void 0!==U&&O.setAttribute("nonce",U),O.textContent=F.cssText,D.appendChild(O)})},c$1=e$4?D=>D:D=>D instanceof CSSStyleSheet?(D=>{let F="";for(let O of D.cssRules)F+=O.cssText;return r$2(F)})(D):D,e$3=window,r$1=e$3.trustedTypes,h$1=r$1?r$1.emptyScript:"",o$3=e$3.reactiveElementPolyfillSupport,n$4={toAttribute(D,F){switch(F){case Boolean:D=D?h$1:null;break;case Object:case Array:D=null==D?D:JSON.stringify(D)}return D},fromAttribute(D,F){let O=D;switch(F){case Boolean:O=null!==D;break;case Number:O=null===D?null:Number(D);break;case Object:case Array:try{O=JSON.parse(D)}catch(D){O=null}}return O}},a$1=(D,F)=>F!==D&&(F==F||D==D),l$2={attribute:!0,type:String,converter:n$4,reflect:!1,hasChanged:a$1},d$1="finalized",u$1=class extends HTMLElement{constructor(){super(),this._$Ei=new Map,this.isUpdatePending=!1,this.hasUpdated=!1,this._$El=null,this.u()}static addInitializer(D){var F;this.finalize(),(null!==(F=this.h)&&void 0!==F?F:this.h=[]).push(D)}static get observedAttributes(){this.finalize();let D=[];return this.elementProperties.forEach((F,O)=>{let U=this._$Ep(O,F);void 0!==U&&(this._$Ev.set(U,O),D.push(U))}),D}static createProperty(D,F=l$2){if(F.state&&(F.attribute=!1),this.finalize(),this.elementProperties.set(D,F),!F.noAccessor&&!this.prototype.hasOwnProperty(D)){let O="symbol"==typeof D?Symbol():"__"+D,U=this.getPropertyDescriptor(D,O,F);void 0!==U&&Object.defineProperty(this.prototype,D,U)}}static getPropertyDescriptor(D,F,O){return{get(){return this[F]},set(U){let G=this[D];this[F]=U,this.requestUpdate(D,G,O)},configurable:!0,enumerable:!0}}static getPropertyOptions(D){return this.elementProperties.get(D)||l$2}static finalize(){if(this.hasOwnProperty(d$1))return!1;this[d$1]=!0;let D=Object.getPrototypeOf(this);if(D.finalize(),void 0!==D.h&&(this.h=[...D.h]),this.elementProperties=new Map(D.elementProperties),this._$Ev=new Map,this.hasOwnProperty("properties")){let D=this.properties,F=[...Object.getOwnPropertyNames(D),...Object.getOwnPropertySymbols(D)];for(let O of F)this.createProperty(O,D[O])}return this.elementStyles=this.finalizeStyles(this.styles),!0}static finalizeStyles(D){let F=[];if(Array.isArray(D)){let O=new Set(D.flat(1/0).reverse());for(let D of O)F.unshift(c$1(D))}else void 0!==D&&F.push(c$1(D));return F}static _$Ep(D,F){let O=F.attribute;return!1===O?void 0:"string"==typeof O?O:"string"==typeof D?D.toLowerCase():void 0}u(){var D;this._$E_=new Promise(D=>this.enableUpdating=D),this._$AL=new Map,this._$Eg(),this.requestUpdate(),null===(D=this.constructor.h)||void 0===D||D.forEach(D=>D(this))}addController(D){var F,O;(null!==(F=this._$ES)&&void 0!==F?F:this._$ES=[]).push(D),void 0!==this.renderRoot&&this.isConnected&&(null===(O=D.hostConnected)||void 0===O||O.call(D))}removeController(D){var F;null===(F=this._$ES)||void 0===F||F.splice(this._$ES.indexOf(D)>>>0,1)}_$Eg(){this.constructor.elementProperties.forEach((D,F)=>{this.hasOwnProperty(F)&&(this._$Ei.set(F,this[F]),delete this[F])})}createRenderRoot(){var D;let F=null!==(D=this.shadowRoot)&&void 0!==D?D:this.attachShadow(this.constructor.shadowRootOptions);return S$1(F,this.constructor.elementStyles),F}connectedCallback(){var D;void 0===this.renderRoot&&(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),null===(D=this._$ES)||void 0===D||D.forEach(D=>{var F;return null===(F=D.hostConnected)||void 0===F?void 0:F.call(D)})}enableUpdating(D){}disconnectedCallback(){var D;null===(D=this._$ES)||void 0===D||D.forEach(D=>{var F;return null===(F=D.hostDisconnected)||void 0===F?void 0:F.call(D)})}attributeChangedCallback(D,F,O){this._$AK(D,O)}_$EO(D,F,O=l$2){var U;let G=this.constructor._$Ep(D,O);if(void 0!==G&&!0===O.reflect){let W=(void 0!==(null===(U=O.converter)||void 0===U?void 0:U.toAttribute)?O.converter:n$4).toAttribute(F,O.type);this._$El=D,null==W?this.removeAttribute(G):this.setAttribute(G,W),this._$El=null}}_$AK(D,F){var O;let U=this.constructor,G=U._$Ev.get(D);if(void 0!==G&&this._$El!==G){let D=U.getPropertyOptions(G),W="function"==typeof D.converter?{fromAttribute:D.converter}:void 0!==(null===(O=D.converter)||void 0===O?void 0:O.fromAttribute)?D.converter:n$4;this._$El=G,this[G]=W.fromAttribute(F,D.type),this._$El=null}}requestUpdate(D,F,O){let U=!0;void 0!==D&&(((O=O||this.constructor.getPropertyOptions(D)).hasChanged||a$1)(this[D],F)?(this._$AL.has(D)||this._$AL.set(D,F),!0===O.reflect&&this._$El!==D&&(void 0===this._$EC&&(this._$EC=new Map),this._$EC.set(D,O))):U=!1),!this.isUpdatePending&&U&&(this._$E_=this._$Ej())}async _$Ej(){this.isUpdatePending=!0;try{await this._$E_}catch(D){Promise.reject(D)}let D=this.scheduleUpdate();return null!=D&&await D,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){var D;if(!this.isUpdatePending)return;this.hasUpdated,this._$Ei&&(this._$Ei.forEach((D,F)=>this[F]=D),this._$Ei=void 0);let F=!1,O=this._$AL;try{(F=this.shouldUpdate(O))?(this.willUpdate(O),null===(D=this._$ES)||void 0===D||D.forEach(D=>{var F;return null===(F=D.hostUpdate)||void 0===F?void 0:F.call(D)}),this.update(O)):this._$Ek()}catch(D){throw F=!1,this._$Ek(),D}F&&this._$AE(O)}willUpdate(D){}_$AE(D){var F;null===(F=this._$ES)||void 0===F||F.forEach(D=>{var F;return null===(F=D.hostUpdated)||void 0===F?void 0:F.call(D)}),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(D)),this.updated(D)}_$Ek(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$E_}shouldUpdate(D){return!0}update(D){void 0!==this._$EC&&(this._$EC.forEach((D,F)=>this._$EO(F,this[F],D)),this._$EC=void 0),this._$Ek()}updated(D){}firstUpdated(D){}};u$1[d$1]=!0,u$1.elementProperties=new Map,u$1.elementStyles=[],u$1.shadowRootOptions={mode:"open"},null==o$3||o$3({ReactiveElement:u$1}),(null!==(s$2=e$3.reactiveElementVersions)&&void 0!==s$2?s$2:e$3.reactiveElementVersions=[]).push("1.6.2");let i$4=window,s$1=i$4.trustedTypes,e$2=s$1?s$1.createPolicy("lit-html",{createHTML:D=>D}):void 0,o$2="$lit$",n$3=`lit$${(Math.random()+"").slice(9)}$`,l$1="?"+n$3,h=`<${l$1}>`,r=document,d=()=>r.createComment(""),u=D=>null===D||"object"!=typeof D&&"function"!=typeof D,c=Array.isArray,v=D=>c(D)||"function"==typeof(null==D?void 0:D[Symbol.iterator]),a="[ 	\n\f\r]",f=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,_=/-->/g,m=/>/g,p=RegExp(`>|${a}(?:([^\\s"'>=/]+)(${a}*=${a}*(?:[^ 	
\f\r"'\`<>=]|("|')|))|$)`,"g"),g=/'/g,$=/"/g,y=/^(?:script|style|textarea|title)$/i,w=D=>(F,...O)=>({_$litType$:D,strings:F,values:O}),x$1=w(1),T=Symbol.for("lit-noChange"),A=Symbol.for("lit-nothing"),E=new WeakMap,C=r.createTreeWalker(r,129,null,!1),P=(D,F)=>{let O=D.length-1,U=[],G,W=2===F?"<svg>":"",Y=f;for(let F=0;F<O;F++){let O=D[F],X,J,K=-1,Z=0;for(;Z<O.length&&(Y.lastIndex=Z,null!==(J=Y.exec(O)));)Z=Y.lastIndex,Y===f?"!--"===J[1]?Y=_:void 0!==J[1]?Y=m:void 0!==J[2]?(y.test(J[2])&&(G=RegExp("</"+J[2],"g")),Y=p):void 0!==J[3]&&(Y=p):Y===p?">"===J[0]?(Y=null!=G?G:f,K=-1):void 0===J[1]?K=-2:(K=Y.lastIndex-J[2].length,X=J[1],Y=void 0===J[3]?p:'"'===J[3]?$:g):Y===$||Y===g?Y=p:Y===_||Y===m?Y=f:(Y=p,G=void 0);let Q=Y===p&&D[F+1].startsWith("/>")?" ":"";W+=Y===f?O+h:K>=0?(U.push(X),O.slice(0,K)+o$2+O.slice(K)+n$3+Q):O+n$3+(-2===K?(U.push(void 0),F):Q)}let X=W+(D[O]||"<?>")+(2===F?"</svg>":"");if(!Array.isArray(D)||!D.hasOwnProperty("raw"))throw Error("invalid template strings array");return[void 0!==e$2?e$2.createHTML(X):X,U]};class V{constructor({strings:D,_$litType$:F},O){let U;this.parts=[];let G=0,W=0,Y=D.length-1,X=this.parts,[J,K]=P(D,F);if(this.el=V.createElement(J,O),C.currentNode=this.el.content,2===F){let D=this.el.content,F=D.firstChild;F.remove(),D.append(...F.childNodes)}for(;null!==(U=C.nextNode())&&X.length<Y;){if(1===U.nodeType){if(U.hasAttributes()){let D=[];for(let F of U.getAttributeNames())if(F.endsWith(o$2)||F.startsWith(n$3)){let O=K[W++];if(D.push(F),void 0!==O){let D=U.getAttribute(O.toLowerCase()+o$2).split(n$3),F=/([.?@])?(.*)/.exec(O);X.push({type:1,index:G,name:F[2],strings:D,ctor:"."===F[1]?k:"?"===F[1]?I:"@"===F[1]?L:R})}else X.push({type:6,index:G})}for(let F of D)U.removeAttribute(F)}if(y.test(U.tagName)){let D=U.textContent.split(n$3),F=D.length-1;if(F>0){U.textContent=s$1?s$1.emptyScript:"";for(let O=0;O<F;O++)U.append(D[O],d()),C.nextNode(),X.push({type:2,index:++G});U.append(D[F],d())}}}else if(8===U.nodeType){if(U.data===l$1)X.push({type:2,index:G});else{let D=-1;for(;-1!==(D=U.data.indexOf(n$3,D+1));)X.push({type:7,index:G}),D+=n$3.length-1}}G++}}static createElement(D,F){let O=r.createElement("template");return O.innerHTML=D,O}}function N(D,F,O=D,U){var G,W,Y,X;if(F===T)return F;let J=void 0!==U?null===(G=O._$Co)||void 0===G?void 0:G[U]:O._$Cl,K=u(F)?void 0:F._$litDirective$;return(null==J?void 0:J.constructor)!==K&&(null===(W=null==J?void 0:J._$AO)||void 0===W||W.call(J,!1),void 0===K?J=void 0:(J=new K(D))._$AT(D,O,U),void 0!==U?(null!==(Y=(X=O)._$Co)&&void 0!==Y?Y:X._$Co=[])[U]=J:O._$Cl=J),void 0!==J&&(F=N(D,J._$AS(D,F.values),J,U)),F}class S{constructor(D,F){this._$AV=[],this._$AN=void 0,this._$AD=D,this._$AM=F}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(D){var F;let{el:{content:O},parts:U}=this._$AD,G=(null!==(F=null==D?void 0:D.creationScope)&&void 0!==F?F:r).importNode(O,!0);C.currentNode=G;let W=C.nextNode(),Y=0,X=0,J=U[0];for(;void 0!==J;){if(Y===J.index){let F;2===J.type?F=new M(W,W.nextSibling,this,D):1===J.type?F=new J.ctor(W,J.name,J.strings,this,D):6===J.type&&(F=new z(W,this,D)),this._$AV.push(F),J=U[++X]}Y!==(null==J?void 0:J.index)&&(W=C.nextNode(),Y++)}return C.currentNode=r,G}v(D){let F=0;for(let O of this._$AV)void 0!==O&&(void 0!==O.strings?(O._$AI(D,O,F),F+=O.strings.length-2):O._$AI(D[F])),F++}}class M{constructor(D,F,O,U){var G;this.type=2,this._$AH=A,this._$AN=void 0,this._$AA=D,this._$AB=F,this._$AM=O,this.options=U,this._$Cp=null===(G=null==U?void 0:U.isConnected)||void 0===G||G}get _$AU(){var D,F;return null!==(F=null===(D=this._$AM)||void 0===D?void 0:D._$AU)&&void 0!==F?F:this._$Cp}get parentNode(){let D=this._$AA.parentNode,F=this._$AM;return void 0!==F&&11===(null==D?void 0:D.nodeType)&&(D=F.parentNode),D}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(D,F=this){u(D=N(this,D,F))?D===A||null==D||""===D?(this._$AH!==A&&this._$AR(),this._$AH=A):D!==this._$AH&&D!==T&&this._(D):void 0!==D._$litType$?this.g(D):void 0!==D.nodeType?this.$(D):v(D)?this.T(D):this._(D)}k(D){return this._$AA.parentNode.insertBefore(D,this._$AB)}$(D){this._$AH!==D&&(this._$AR(),this._$AH=this.k(D))}_(D){this._$AH!==A&&u(this._$AH)?this._$AA.nextSibling.data=D:this.$(r.createTextNode(D)),this._$AH=D}g(D){var F;let{values:O,_$litType$:U}=D,G="number"==typeof U?this._$AC(D):(void 0===U.el&&(U.el=V.createElement(U.h,this.options)),U);if((null===(F=this._$AH)||void 0===F?void 0:F._$AD)===G)this._$AH.v(O);else{let D=new S(G,this),F=D.u(this.options);D.v(O),this.$(F),this._$AH=D}}_$AC(D){let F=E.get(D.strings);return void 0===F&&E.set(D.strings,F=new V(D)),F}T(D){c(this._$AH)||(this._$AH=[],this._$AR());let F=this._$AH,O,U=0;for(let G of D)U===F.length?F.push(O=new M(this.k(d()),this.k(d()),this,this.options)):O=F[U],O._$AI(G),U++;U<F.length&&(this._$AR(O&&O._$AB.nextSibling,U),F.length=U)}_$AR(D=this._$AA.nextSibling,F){var O;for(null===(O=this._$AP)||void 0===O||O.call(this,!1,!0,F);D&&D!==this._$AB;){let F=D.nextSibling;D.remove(),D=F}}setConnected(D){var F;void 0===this._$AM&&(this._$Cp=D,null===(F=this._$AP)||void 0===F||F.call(this,D))}}class R{constructor(D,F,O,U,G){this.type=1,this._$AH=A,this._$AN=void 0,this.element=D,this.name=F,this._$AM=U,this.options=G,O.length>2||""!==O[0]||""!==O[1]?(this._$AH=Array(O.length-1).fill(new String),this.strings=O):this._$AH=A}get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}_$AI(D,F=this,O,U){let G=this.strings,W=!1;if(void 0===G)(W=!u(D=N(this,D,F,0))||D!==this._$AH&&D!==T)&&(this._$AH=D);else{let U,Y;let X=D;for(D=G[0],U=0;U<G.length-1;U++)(Y=N(this,X[O+U],F,U))===T&&(Y=this._$AH[U]),W||(W=!u(Y)||Y!==this._$AH[U]),Y===A?D=A:D!==A&&(D+=(null!=Y?Y:"")+G[U+1]),this._$AH[U]=Y}W&&!U&&this.j(D)}j(D){D===A?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=D?D:"")}}class k extends R{constructor(){super(...arguments),this.type=3}j(D){this.element[this.name]=D===A?void 0:D}}let H=s$1?s$1.emptyScript:"";class I extends R{constructor(){super(...arguments),this.type=4}j(D){D&&D!==A?this.element.setAttribute(this.name,H):this.element.removeAttribute(this.name)}}class L extends R{constructor(D,F,O,U,G){super(D,F,O,U,G),this.type=5}_$AI(D,F=this){var O;if((D=null!==(O=N(this,D,F,0))&&void 0!==O?O:A)===T)return;let U=this._$AH,G=D===A&&U!==A||D.capture!==U.capture||D.once!==U.once||D.passive!==U.passive,W=D!==A&&(U===A||G);G&&this.element.removeEventListener(this.name,this,U),W&&this.element.addEventListener(this.name,this,D),this._$AH=D}handleEvent(D){var F,O;"function"==typeof this._$AH?this._$AH.call(null!==(O=null===(F=this.options)||void 0===F?void 0:F.host)&&void 0!==O?O:this.element,D):this._$AH.handleEvent(D)}}class z{constructor(D,F,O){this.element=D,this.type=6,this._$AN=void 0,this._$AM=F,this.options=O}get _$AU(){return this._$AM._$AU}_$AI(D){N(this,D)}}let j=i$4.litHtmlPolyfillSupport;null==j||j(V,M),(null!==(t=i$4.litHtmlVersions)&&void 0!==t?t:i$4.litHtmlVersions=[]).push("2.7.4");let B=(D,F,O)=>{var U,G;let W=null!==(U=null==O?void 0:O.renderBefore)&&void 0!==U?U:F,Y=W._$litPart$;if(void 0===Y){let D=null!==(G=null==O?void 0:O.renderBefore)&&void 0!==G?G:null;W._$litPart$=Y=new M(F.insertBefore(d(),D),D,void 0,null!=O?O:{})}return Y._$AI(D),Y};class s extends u$1{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){var D,F;let O=super.createRenderRoot();return null!==(D=(F=this.renderOptions).renderBefore)&&void 0!==D||(F.renderBefore=O.firstChild),O}update(D){let F=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(D),this._$Do=B(F,this.renderRoot,this.renderOptions)}connectedCallback(){var D;super.connectedCallback(),null===(D=this._$Do)||void 0===D||D.setConnected(!0)}disconnectedCallback(){var D;super.disconnectedCallback(),null===(D=this._$Do)||void 0===D||D.setConnected(!1)}render(){return T}}s.finalized=!0,s._$litElement$=!0,null===(l=globalThis.litElementHydrateSupport)||void 0===l||l.call(globalThis,{LitElement:s});let n$2=globalThis.litElementPolyfillSupport;null==n$2||n$2({LitElement:s}),(null!==(o$1=globalThis.litElementVersions)&&void 0!==o$1?o$1:globalThis.litElementVersions=[]).push("3.3.2");/**
	 * @license
	 * Copyright 2017 Google LLC
	 * SPDX-License-Identifier: BSD-3-Clause
	 */let e$1=D=>F=>{var O,U;return"function"==typeof F?(O=D,U=F,customElements.define(O,U),U):((D,F)=>{let{kind:O,elements:U}=F;return{kind:O,elements:U,finisher(F){customElements.define(D,F)}}})(D,F)},i$3=(D,F)=>"method"!==F.kind||!F.descriptor||"value"in F.descriptor?{kind:"field",key:Symbol(),placement:"own",descriptor:{},originalKey:F.key,initializer(){"function"==typeof F.initializer&&(this[F.key]=F.initializer.call(this))},finisher(O){O.createProperty(F.key,D)}}:{...F,finisher(O){O.createProperty(F.key,D)}},e=(D,F,O)=>{F.constructor.createProperty(O,D)};function n$1(D){return(F,O)=>void 0!==O?e(D,F,O):i$3(D,F)}/**
	 * @license
	 * Copyright 2017 Google LLC
	 * SPDX-License-Identifier: BSD-3-Clause
	 */let o=({finisher:D,descriptor:F})=>(O,U)=>{var G;if(void 0===U){let U=null!==(G=O.originalKey)&&void 0!==G?G:O.key,W=null!=F?{kind:"method",placement:"prototype",key:U,descriptor:F(O.key)}:{...O,key:U};return null!=D&&(W.finisher=function(F){D(F,U)}),W}{let G=O.constructor;void 0!==F&&Object.defineProperty(O,U,F(U)),null==D||D(G,U)}};/**
	 * @license
	 * Copyright 2017 Google LLC
	 * SPDX-License-Identifier: BSD-3-Clause
	 */function i$2(D,F){return o({descriptor:O=>{let U={get(){var F,O;return null!==(O=null===(F=this.renderRoot)||void 0===F?void 0:F.querySelector(D))&&void 0!==O?O:null},enumerable:!0,configurable:!0};if(F){let F="symbol"==typeof O?Symbol():"__"+O;U.get=function(){var O,U;return void 0===this[F]&&(this[F]=null!==(U=null===(O=this.renderRoot)||void 0===O?void 0:O.querySelector(D))&&void 0!==U?U:null),this[F]}}return U}})}null!=(null===(n=window.HTMLSlotElement)||void 0===n?void 0:n.prototype.assignedElements)||((D,F)=>D.assignedNodes(F).filter(D=>D.nodeType===Node.ELEMENT_NODE));var s$2,t,l,o$1,n,commonjsGlobal="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function getDefaultExportFromCjs(D){return D&&D.__esModule&&Object.prototype.hasOwnProperty.call(D,"default")?D.default:D}var lottie={exports:{}};!function(module,exports){"undefined"!=typeof navigator&&function(D,F){module.exports=F()}(0,function(){var svgNS="http://www.w3.org/2000/svg",locationHref="",_useWebWorker=!1,initialDefaultFrame=-999999,setWebWorker=function(D){_useWebWorker=!!D},getWebWorker=function(){return _useWebWorker},setLocationHref=function(D){locationHref=D},getLocationHref=function(){return locationHref};function createTag(D){return document.createElement(D)}function extendPrototype(D,F){var O,U,G=D.length;for(O=0;O<G;O+=1)for(var W in U=D[O].prototype)Object.prototype.hasOwnProperty.call(U,W)&&(F.prototype[W]=U[W])}function getDescriptor(D,F){return Object.getOwnPropertyDescriptor(D,F)}function createProxyFunction(D){function F(){}return F.prototype=D,F}var audioControllerFactory=function(){function D(D){this.audios=[],this.audioFactory=D,this._volume=1,this._isMuted=!1}return D.prototype={addAudio:function(D){this.audios.push(D)},pause:function(){var D,F=this.audios.length;for(D=0;D<F;D+=1)this.audios[D].pause()},resume:function(){var D,F=this.audios.length;for(D=0;D<F;D+=1)this.audios[D].resume()},setRate:function(D){var F,O=this.audios.length;for(F=0;F<O;F+=1)this.audios[F].setRate(D)},createAudio:function(D){return this.audioFactory?this.audioFactory(D):window.Howl?new window.Howl({src:[D]}):{isPlaying:!1,play:function(){this.isPlaying=!0},seek:function(){this.isPlaying=!1},playing:function(){},rate:function(){},setVolume:function(){}}},setAudioFactory:function(D){this.audioFactory=D},setVolume:function(D){this._volume=D,this._updateVolume()},mute:function(){this._isMuted=!0,this._updateVolume()},unmute:function(){this._isMuted=!1,this._updateVolume()},getVolume:function(){return this._volume},_updateVolume:function(){var D,F=this.audios.length;for(D=0;D<F;D+=1)this.audios[D].volume(this._volume*(this._isMuted?0:1))}},function(){return new D}}(),createTypedArray=function(){function D(D,F){var O,U=0,G=[];switch(D){case"int16":case"uint8c":O=1;break;default:O=1.1}for(U=0;U<F;U+=1)G.push(O);return G}function F(F,O){return"float32"===F?new Float32Array(O):"int16"===F?new Int16Array(O):"uint8c"===F?new Uint8ClampedArray(O):D(F,O)}return"function"==typeof Uint8ClampedArray&&"function"==typeof Float32Array?F:D}();function createSizedArray(D){return Array.apply(null,{length:D})}function _typeof$6(D){return(_typeof$6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(D){return typeof D}:function(D){return D&&"function"==typeof Symbol&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D})(D)}var subframeEnabled=!0,expressionsPlugin=null,expressionsInterfaces=null,idPrefix$1="",isSafari=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),bmPow=Math.pow,bmSqrt=Math.sqrt,bmFloor=Math.floor,bmMax=Math.max,bmMin=Math.min,BMMath={};!function(){var D,F=["abs","acos","acosh","asin","asinh","atan","atanh","atan2","ceil","cbrt","expm1","clz32","cos","cosh","exp","floor","fround","hypot","imul","log","log1p","log2","log10","max","min","pow","random","round","sign","sin","sinh","sqrt","tan","tanh","trunc","E","LN10","LN2","LOG10E","LOG2E","PI","SQRT1_2","SQRT2"],O=F.length;for(D=0;D<O;D+=1)BMMath[F[D]]=Math[F[D]]}(),BMMath.random=Math.random,BMMath.abs=function(D){if("object"===_typeof$6(D)&&D.length){var F,O=createSizedArray(D.length),U=D.length;for(F=0;F<U;F+=1)O[F]=Math.abs(D[F]);return O}return Math.abs(D)};var defaultCurveSegments=150,degToRads=Math.PI/180,roundCorner=.5519;function styleDiv(D){D.style.position="absolute",D.style.top=0,D.style.left=0,D.style.display="block",D.style.transformOrigin="0 0",D.style.webkitTransformOrigin="0 0",D.style.backfaceVisibility="visible",D.style.webkitBackfaceVisibility="visible",D.style.transformStyle="preserve-3d",D.style.webkitTransformStyle="preserve-3d",D.style.mozTransformStyle="preserve-3d"}function BMEnterFrameEvent(D,F,O,U){this.type=D,this.currentTime=F,this.totalTime=O,this.direction=U<0?-1:1}function BMCompleteEvent(D,F){this.type=D,this.direction=F<0?-1:1}function BMCompleteLoopEvent(D,F,O,U){this.type=D,this.currentLoop=O,this.totalLoops=F,this.direction=U<0?-1:1}function BMSegmentStartEvent(D,F,O){this.type=D,this.firstFrame=F,this.totalFrames=O}function BMDestroyEvent(D,F){this.type=D,this.target=F}function BMRenderFrameErrorEvent(D,F){this.type="renderFrameError",this.nativeError=D,this.currentTime=F}function BMConfigErrorEvent(D){this.type="configError",this.nativeError=D}var createElementID=function(){var D=0;return function(){return idPrefix$1+"__lottie_element_"+(D+=1)}}();function HSVtoRGB(D,F,O){var U,G,W,Y,X,J,K,Z;switch(Y=Math.floor(6*D),X=6*D-Y,J=O*(1-F),K=O*(1-X*F),Z=O*(1-(1-X)*F),Y%6){case 0:U=O,G=Z,W=J;break;case 1:U=K,G=O,W=J;break;case 2:U=J,G=O,W=Z;break;case 3:U=J,G=K,W=O;break;case 4:U=Z,G=J,W=O;break;case 5:U=O,G=J,W=K}return[U,G,W]}function RGBtoHSV(D,F,O){var U,G=Math.max(D,F,O),W=Math.min(D,F,O),Y=G-W,X=0===G?0:Y/G,J=G/255;switch(G){case W:U=0;break;case D:U=(F-O+Y*(F<O?6:0))/(6*Y);break;case F:U=(O-D+2*Y)/(6*Y);break;case O:U=(D-F+4*Y)/(6*Y)}return[U,X,J]}function addSaturationToRGB(D,F){var O=RGBtoHSV(255*D[0],255*D[1],255*D[2]);return O[1]+=F,O[1]>1?O[1]=1:O[1]<=0&&(O[1]=0),HSVtoRGB(O[0],O[1],O[2])}function addBrightnessToRGB(D,F){var O=RGBtoHSV(255*D[0],255*D[1],255*D[2]);return O[2]+=F,O[2]>1?O[2]=1:O[2]<0&&(O[2]=0),HSVtoRGB(O[0],O[1],O[2])}function addHueToRGB(D,F){var O=RGBtoHSV(255*D[0],255*D[1],255*D[2]);return O[0]+=F/360,O[0]>1?O[0]-=1:O[0]<0&&(O[0]+=1),HSVtoRGB(O[0],O[1],O[2])}var rgbToHex=function(){var D,F,O=[];for(D=0;D<256;D+=1)F=D.toString(16),O[D]=1===F.length?"0"+F:F;return function(D,F,U){return D<0&&(D=0),F<0&&(F=0),U<0&&(U=0),"#"+O[D]+O[F]+O[U]}}(),setSubframeEnabled=function(D){subframeEnabled=!!D},getSubframeEnabled=function(){return subframeEnabled},setExpressionsPlugin=function(D){expressionsPlugin=D},getExpressionsPlugin=function(){return expressionsPlugin},setExpressionInterfaces=function(D){expressionsInterfaces=D},getExpressionInterfaces=function(){return expressionsInterfaces},setDefaultCurveSegments=function(D){defaultCurveSegments=D},getDefaultCurveSegments=function(){return defaultCurveSegments},setIdPrefix=function(D){idPrefix$1=D};function createNS(D){return document.createElementNS(svgNS,D)}function _typeof$5(D){return(_typeof$5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(D){return typeof D}:function(D){return D&&"function"==typeof Symbol&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D})(D)}var dataManager=function(){var D,F,O=1,U=[],G={onmessage:function(){},postMessage:function(F){D({data:F})}},W={postMessage:function(D){G.onmessage({data:D})}};function Y(F){if(window.Worker&&window.Blob&&getWebWorker()){var O=new Blob(["var _workerSelf = self; self.onmessage = ",F.toString()],{type:"text/javascript"}),U=URL.createObjectURL(O);return new Worker(U)}return D=F,G}function X(){F||((F=Y(function(D){function F(){function D(F,O){var Y,X,J,K,Z,Q,tt=F.length;for(X=0;X<tt;X+=1)if("ks"in(Y=F[X])&&!Y.completed){if(Y.completed=!0,Y.hasMask){var tr=Y.masksProperties;for(J=0,K=tr.length;J<K;J+=1)if(tr[J].pt.k.i)W(tr[J].pt.k);else for(Z=0,Q=tr[J].pt.k.length;Z<Q;Z+=1)tr[J].pt.k[Z].s&&W(tr[J].pt.k[Z].s[0]),tr[J].pt.k[Z].e&&W(tr[J].pt.k[Z].e[0])}0===Y.ty?(Y.layers=U(Y.refId,O),D(Y.layers,O)):4===Y.ty?G(Y.shapes):5===Y.ty&&te(Y)}}function F(F,O){if(F){var G=0,W=F.length;for(G=0;G<W;G+=1)1===F[G].t&&(F[G].data.layers=U(F[G].data.refId,O),D(F[G].data.layers,O))}}function O(D,F){for(var O=0,U=F.length;O<U;){if(F[O].id===D)return F[O];O+=1}return null}function U(D,F){var U=O(D,F);return U?U.layers.__used?JSON.parse(JSON.stringify(U.layers)):(U.layers.__used=!0,U.layers):null}function G(D){var F,O,U;for(F=D.length-1;F>=0;F-=1)if("sh"===D[F].ty){if(D[F].ks.k.i)W(D[F].ks.k);else for(O=0,U=D[F].ks.k.length;O<U;O+=1)D[F].ks.k[O].s&&W(D[F].ks.k[O].s[0]),D[F].ks.k[O].e&&W(D[F].ks.k[O].e[0])}else"gr"===D[F].ty&&G(D[F].it)}function W(D){var F,O=D.i.length;for(F=0;F<O;F+=1)D.i[F][0]+=D.v[F][0],D.i[F][1]+=D.v[F][1],D.o[F][0]+=D.v[F][0],D.o[F][1]+=D.v[F][1]}function Y(D,F){var O=F?F.split("."):[100,100,100];return D[0]>O[0]||!(O[0]>D[0])&&(D[1]>O[1]||!(O[1]>D[1])&&(D[2]>O[2]||!(O[2]>D[2])&&null))}var X=function(){var D=[4,4,14];function F(D){var F=D.t.d;D.t.d={k:[{s:F,t:0}]}}function O(D){var O,U=D.length;for(O=0;O<U;O+=1)5===D[O].ty&&F(D[O])}return function(F){if(Y(D,F.v)&&(O(F.layers),F.assets)){var U,G=F.assets.length;for(U=0;U<G;U+=1)F.assets[U].layers&&O(F.assets[U].layers)}}}(),J=function(){var D=[4,7,99];return function(F){if(F.chars&&!Y(D,F.v)){var O,U=F.chars.length;for(O=0;O<U;O+=1){var W=F.chars[O];W.data&&W.data.shapes&&(G(W.data.shapes),W.data.ip=0,W.data.op=99999,W.data.st=0,W.data.sr=1,W.data.ks={p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0}},F.chars[O].t||(W.data.shapes.push({ty:"no"}),W.data.shapes[0].it.push({p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0},sk:{k:0,a:0},sa:{k:0,a:0},ty:"tr"})))}}}}(),K=function(){var D=[5,7,15];function F(D){var F=D.t.p;"number"==typeof F.a&&(F.a={a:0,k:F.a}),"number"==typeof F.p&&(F.p={a:0,k:F.p}),"number"==typeof F.r&&(F.r={a:0,k:F.r})}function O(D){var O,U=D.length;for(O=0;O<U;O+=1)5===D[O].ty&&F(D[O])}return function(F){if(Y(D,F.v)&&(O(F.layers),F.assets)){var U,G=F.assets.length;for(U=0;U<G;U+=1)F.assets[U].layers&&O(F.assets[U].layers)}}}(),Z=function(){var D=[4,1,9];function F(D){var O,U,G,W=D.length;for(O=0;O<W;O+=1)if("gr"===D[O].ty)F(D[O].it);else if("fl"===D[O].ty||"st"===D[O].ty){if(D[O].c.k&&D[O].c.k[0].i)for(U=0,G=D[O].c.k.length;U<G;U+=1)D[O].c.k[U].s&&(D[O].c.k[U].s[0]/=255,D[O].c.k[U].s[1]/=255,D[O].c.k[U].s[2]/=255,D[O].c.k[U].s[3]/=255),D[O].c.k[U].e&&(D[O].c.k[U].e[0]/=255,D[O].c.k[U].e[1]/=255,D[O].c.k[U].e[2]/=255,D[O].c.k[U].e[3]/=255);else D[O].c.k[0]/=255,D[O].c.k[1]/=255,D[O].c.k[2]/=255,D[O].c.k[3]/=255}}function O(D){var O,U=D.length;for(O=0;O<U;O+=1)4===D[O].ty&&F(D[O].shapes)}return function(F){if(Y(D,F.v)&&(O(F.layers),F.assets)){var U,G=F.assets.length;for(U=0;U<G;U+=1)F.assets[U].layers&&O(F.assets[U].layers)}}}(),Q=function(){var D=[4,4,18];function F(D){var O,U,G;for(O=D.length-1;O>=0;O-=1)if("sh"===D[O].ty){if(D[O].ks.k.i)D[O].ks.k.c=D[O].closed;else for(U=0,G=D[O].ks.k.length;U<G;U+=1)D[O].ks.k[U].s&&(D[O].ks.k[U].s[0].c=D[O].closed),D[O].ks.k[U].e&&(D[O].ks.k[U].e[0].c=D[O].closed)}else"gr"===D[O].ty&&F(D[O].it)}function O(D){var O,U,G,W,Y,X,J=D.length;for(U=0;U<J;U+=1){if((O=D[U]).hasMask){var K=O.masksProperties;for(G=0,W=K.length;G<W;G+=1)if(K[G].pt.k.i)K[G].pt.k.c=K[G].cl;else for(Y=0,X=K[G].pt.k.length;Y<X;Y+=1)K[G].pt.k[Y].s&&(K[G].pt.k[Y].s[0].c=K[G].cl),K[G].pt.k[Y].e&&(K[G].pt.k[Y].e[0].c=K[G].cl)}4===O.ty&&F(O.shapes)}}return function(F){if(Y(D,F.v)&&(O(F.layers),F.assets)){var U,G=F.assets.length;for(U=0;U<G;U+=1)F.assets[U].layers&&O(F.assets[U].layers)}}}();function tt(O){O.__complete||(Z(O),X(O),J(O),K(O),Q(O),D(O.layers,O.assets),F(O.chars,O.assets),O.__complete=!0)}function te(D){0===D.t.a.length&&D.t.p}var tr={};return tr.completeData=tt,tr.checkColors=Z,tr.checkChars=J,tr.checkPathProperties=K,tr.checkShapes=Q,tr.completeLayers=D,tr}if(W.dataManager||(W.dataManager=F()),W.assetLoader||(W.assetLoader=function(){function D(D){var F=D.getResponseHeader("content-type");return F&&"json"===D.responseType&&-1!==F.indexOf("json")||D.response&&"object"===_typeof$5(D.response)?D.response:D.response&&"string"==typeof D.response?JSON.parse(D.response):D.responseText?JSON.parse(D.responseText):null}return{load:function(F,O,U,G){var W,Y=new XMLHttpRequest;try{Y.responseType="json"}catch(D){}Y.onreadystatechange=function(){if(4===Y.readyState){if(200===Y.status)U(W=D(Y));else try{W=D(Y),U(W)}catch(D){G&&G(D)}}};try{Y.open("GET",F,!0)}catch(D){Y.open("GET",O+"/"+F,!0)}Y.send()}}}()),"loadAnimation"===D.data.type)W.assetLoader.load(D.data.path,D.data.fullPath,function(F){W.dataManager.completeData(F),W.postMessage({id:D.data.id,payload:F,status:"success"})},function(){W.postMessage({id:D.data.id,status:"error"})});else if("complete"===D.data.type){var O=D.data.animation;W.dataManager.completeData(O),W.postMessage({id:D.data.id,payload:O,status:"success"})}else"loadData"===D.data.type&&W.assetLoader.load(D.data.path,D.data.fullPath,function(F){W.postMessage({id:D.data.id,payload:F,status:"success"})},function(){W.postMessage({id:D.data.id,status:"error"})})})).onmessage=function(D){var F=D.data,O=F.id,G=U[O];U[O]=null,"success"===F.status?G.onComplete(F.payload):G.onError&&G.onError()})}function J(D,F){var G="processId_"+(O+=1);return U[G]={onComplete:D,onError:F},G}return{loadAnimation:function(D,O,U){X();var G=J(O,U);F.postMessage({type:"loadAnimation",path:D,fullPath:window.location.origin+window.location.pathname,id:G})},loadData:function(D,O,U){X();var G=J(O,U);F.postMessage({type:"loadData",path:D,fullPath:window.location.origin+window.location.pathname,id:G})},completeAnimation:function(D,O,U){X();var G=J(O,U);F.postMessage({type:"complete",animation:D,id:G})}}}(),ImagePreloader=function(){var D=function(){var D=createTag("canvas");D.width=1,D.height=1;var F=D.getContext("2d");return F.fillStyle="rgba(0,0,0,0)",F.fillRect(0,0,1,1),D}();function F(){this.loadedAssets+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function O(){this.loadedFootagesCount+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function U(D,F,O){var U="";if(D.e)U=D.p;else if(F){var G=D.p;-1!==G.indexOf("images/")&&(G=G.split("/")[1]),U=F+G}else U=O+(D.u?D.u:"")+D.p;return U}function G(D){var F=0,O=setInterval((function(){(D.getBBox().width||F>500)&&(this._imageLoaded(),clearInterval(O)),F+=1}).bind(this),50)}function W(F){var O=U(F,this.assetsPath,this.path),G=createNS("image");isSafari?this.testImageLoaded(G):G.addEventListener("load",this._imageLoaded,!1),G.addEventListener("error",(function(){W.img=D,this._imageLoaded()}).bind(this),!1),G.setAttributeNS("http://www.w3.org/1999/xlink","href",O),this._elementHelper.append?this._elementHelper.append(G):this._elementHelper.appendChild(G);var W={img:G,assetData:F};return W}function Y(F){var O=U(F,this.assetsPath,this.path),G=createTag("img");G.crossOrigin="anonymous",G.addEventListener("load",this._imageLoaded,!1),G.addEventListener("error",(function(){W.img=D,this._imageLoaded()}).bind(this),!1),G.src=O;var W={img:G,assetData:F};return W}function X(D){var F={assetData:D},O=U(D,this.assetsPath,this.path);return dataManager.loadData(O,(function(D){F.img=D,this._footageLoaded()}).bind(this),(function(){F.img={},this._footageLoaded()}).bind(this)),F}function J(D,F){this.imagesLoadedCb=F;var O,U=D.length;for(O=0;O<U;O+=1)D[O].layers||(D[O].t&&"seq"!==D[O].t?3===D[O].t&&(this.totalFootages+=1,this.images.push(this.createFootageData(D[O]))):(this.totalImages+=1,this.images.push(this._createImageData(D[O]))))}function K(D){this.path=D||""}function Z(D){this.assetsPath=D||""}function Q(D){for(var F=0,O=this.images.length;F<O;){if(this.images[F].assetData===D)return this.images[F].img;F+=1}return null}function tt(){this.imagesLoadedCb=null,this.images.length=0}function te(){return this.totalImages===this.loadedAssets}function tr(){return this.totalFootages===this.loadedFootagesCount}function ts(D,F){"svg"===D?(this._elementHelper=F,this._createImageData=this.createImageData.bind(this)):this._createImageData=this.createImgData.bind(this)}function tn(){this._imageLoaded=F.bind(this),this._footageLoaded=O.bind(this),this.testImageLoaded=G.bind(this),this.createFootageData=X.bind(this),this.assetsPath="",this.path="",this.totalImages=0,this.totalFootages=0,this.loadedAssets=0,this.loadedFootagesCount=0,this.imagesLoadedCb=null,this.images=[]}return tn.prototype={loadAssets:J,setAssetsPath:Z,setPath:K,loadedImages:te,loadedFootages:tr,destroy:tt,getAsset:Q,createImgData:Y,createImageData:W,imageLoaded:F,footageLoaded:O,setCacheType:ts},tn}();function BaseEvent(){}BaseEvent.prototype={triggerEvent:function(D,F){if(this._cbs[D])for(var O=this._cbs[D],U=0;U<O.length;U+=1)O[U](F)},addEventListener:function(D,F){return this._cbs[D]||(this._cbs[D]=[]),this._cbs[D].push(F),(function(){this.removeEventListener(D,F)}).bind(this)},removeEventListener:function(D,F){if(F){if(this._cbs[D]){for(var O=0,U=this._cbs[D].length;O<U;)this._cbs[D][O]===F&&(this._cbs[D].splice(O,1),O-=1,U-=1),O+=1;this._cbs[D].length||(this._cbs[D]=null)}}else this._cbs[D]=null}};var markerParser=function(){function D(D){for(var F,O=D.split("\r\n"),U={},G=0,W=0;W<O.length;W+=1)2===(F=O[W].split(":")).length&&(U[F[0]]=F[1].trim(),G+=1);if(0===G)throw Error();return U}return function(F){for(var O=[],U=0;U<F.length;U+=1){var G=F[U],W={time:G.tm,duration:G.dr};try{W.payload=JSON.parse(F[U].cm)}catch(O){try{W.payload=D(F[U].cm)}catch(D){W.payload={name:F[U].cm}}}O.push(W)}return O}}(),ProjectInterface=function(){function D(D){this.compositions.push(D)}return function(){function F(D){for(var F=0,O=this.compositions.length;F<O;){if(this.compositions[F].data&&this.compositions[F].data.nm===D)return this.compositions[F].prepareFrame&&this.compositions[F].data.xt&&this.compositions[F].prepareFrame(this.currentFrame),this.compositions[F].compInterface;F+=1}return null}return F.compositions=[],F.currentFrame=0,F.registerComposition=D,F}}(),renderers={},registerRenderer=function(D,F){renderers[D]=F};function getRenderer(D){return renderers[D]}function getRegisteredRenderer(){if(renderers.canvas)return"canvas";for(var D in renderers)if(renderers[D])return D;return""}function _typeof$4(D){return(_typeof$4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(D){return typeof D}:function(D){return D&&"function"==typeof Symbol&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D})(D)}var AnimationItem=function(){this._cbs=[],this.name="",this.path="",this.isLoaded=!1,this.currentFrame=0,this.currentRawFrame=0,this.firstFrame=0,this.totalFrames=0,this.frameRate=0,this.frameMult=0,this.playSpeed=1,this.playDirection=1,this.playCount=0,this.animationData={},this.assets=[],this.isPaused=!0,this.autoplay=!1,this.loop=!0,this.renderer=null,this.animationID=createElementID(),this.assetsPath="",this.timeCompleted=0,this.segmentPos=0,this.isSubframeEnabled=getSubframeEnabled(),this.segments=[],this._idle=!0,this._completedLoop=!1,this.projectInterface=ProjectInterface(),this.imagePreloader=new ImagePreloader,this.audioController=audioControllerFactory(),this.markers=[],this.configAnimation=this.configAnimation.bind(this),this.onSetupError=this.onSetupError.bind(this),this.onSegmentComplete=this.onSegmentComplete.bind(this),this.drawnFrameEvent=new BMEnterFrameEvent("drawnFrame",0,0,0),this.expressionsPlugin=getExpressionsPlugin()};extendPrototype([BaseEvent],AnimationItem),AnimationItem.prototype.setParams=function(D){(D.wrapper||D.container)&&(this.wrapper=D.wrapper||D.container);var F="svg";D.animType?F=D.animType:D.renderer&&(F=D.renderer);var O=getRenderer(F);this.renderer=new O(this,D.rendererSettings),this.imagePreloader.setCacheType(F,this.renderer.globalData.defs),this.renderer.setProjectInterface(this.projectInterface),this.animType=F,""===D.loop||null===D.loop||void 0===D.loop||!0===D.loop?this.loop=!0:!1===D.loop?this.loop=!1:this.loop=parseInt(D.loop,10),this.autoplay=!("autoplay"in D)||D.autoplay,this.name=D.name?D.name:"",this.autoloadSegments=!Object.prototype.hasOwnProperty.call(D,"autoloadSegments")||D.autoloadSegments,this.assetsPath=D.assetsPath,this.initialSegment=D.initialSegment,D.audioFactory&&this.audioController.setAudioFactory(D.audioFactory),D.animationData?this.setupAnimation(D.animationData):D.path&&(-1!==D.path.lastIndexOf("\\")?this.path=D.path.substr(0,D.path.lastIndexOf("\\")+1):this.path=D.path.substr(0,D.path.lastIndexOf("/")+1),this.fileName=D.path.substr(D.path.lastIndexOf("/")+1),this.fileName=this.fileName.substr(0,this.fileName.lastIndexOf(".json")),dataManager.loadAnimation(D.path,this.configAnimation,this.onSetupError))},AnimationItem.prototype.onSetupError=function(){this.trigger("data_failed")},AnimationItem.prototype.setupAnimation=function(D){dataManager.completeAnimation(D,this.configAnimation)},AnimationItem.prototype.setData=function(D,F){F&&"object"!==_typeof$4(F)&&(F=JSON.parse(F));var O={wrapper:D,animationData:F},U=D.attributes;O.path=U.getNamedItem("data-animation-path")?U.getNamedItem("data-animation-path").value:U.getNamedItem("data-bm-path")?U.getNamedItem("data-bm-path").value:U.getNamedItem("bm-path")?U.getNamedItem("bm-path").value:"",O.animType=U.getNamedItem("data-anim-type")?U.getNamedItem("data-anim-type").value:U.getNamedItem("data-bm-type")?U.getNamedItem("data-bm-type").value:U.getNamedItem("bm-type")?U.getNamedItem("bm-type").value:U.getNamedItem("data-bm-renderer")?U.getNamedItem("data-bm-renderer").value:U.getNamedItem("bm-renderer")?U.getNamedItem("bm-renderer").value:getRegisteredRenderer()||"canvas";var G=U.getNamedItem("data-anim-loop")?U.getNamedItem("data-anim-loop").value:U.getNamedItem("data-bm-loop")?U.getNamedItem("data-bm-loop").value:U.getNamedItem("bm-loop")?U.getNamedItem("bm-loop").value:"";"false"===G?O.loop=!1:"true"===G?O.loop=!0:""!==G&&(O.loop=parseInt(G,10));var W=U.getNamedItem("data-anim-autoplay")?U.getNamedItem("data-anim-autoplay").value:U.getNamedItem("data-bm-autoplay")?U.getNamedItem("data-bm-autoplay").value:!U.getNamedItem("bm-autoplay")||U.getNamedItem("bm-autoplay").value;O.autoplay="false"!==W,O.name=U.getNamedItem("data-name")?U.getNamedItem("data-name").value:U.getNamedItem("data-bm-name")?U.getNamedItem("data-bm-name").value:U.getNamedItem("bm-name")?U.getNamedItem("bm-name").value:"","false"===(U.getNamedItem("data-anim-prerender")?U.getNamedItem("data-anim-prerender").value:U.getNamedItem("data-bm-prerender")?U.getNamedItem("data-bm-prerender").value:U.getNamedItem("bm-prerender")?U.getNamedItem("bm-prerender").value:"")&&(O.prerender=!1),O.path?this.setParams(O):this.trigger("destroy")},AnimationItem.prototype.includeLayers=function(D){D.op>this.animationData.op&&(this.animationData.op=D.op,this.totalFrames=Math.floor(D.op-this.animationData.ip));var F,O,U=this.animationData.layers,G=U.length,W=D.layers,Y=W.length;for(O=0;O<Y;O+=1)for(F=0;F<G;){if(U[F].id===W[O].id){U[F]=W[O];break}F+=1}if((D.chars||D.fonts)&&(this.renderer.globalData.fontManager.addChars(D.chars),this.renderer.globalData.fontManager.addFonts(D.fonts,this.renderer.globalData.defs)),D.assets)for(F=0,G=D.assets.length;F<G;F+=1)this.animationData.assets.push(D.assets[F]);this.animationData.__complete=!1,dataManager.completeAnimation(this.animationData,this.onSegmentComplete)},AnimationItem.prototype.onSegmentComplete=function(D){this.animationData=D;var F=getExpressionsPlugin();F&&F.initExpressions(this),this.loadNextSegment()},AnimationItem.prototype.loadNextSegment=function(){var D=this.animationData.segments;if(!D||0===D.length||!this.autoloadSegments){this.trigger("data_ready"),this.timeCompleted=this.totalFrames;return}var F=D.shift();this.timeCompleted=F.time*this.frameRate;var O=this.path+this.fileName+"_"+this.segmentPos+".json";this.segmentPos+=1,dataManager.loadData(O,this.includeLayers.bind(this),(function(){this.trigger("data_failed")}).bind(this))},AnimationItem.prototype.loadSegments=function(){this.animationData.segments||(this.timeCompleted=this.totalFrames),this.loadNextSegment()},AnimationItem.prototype.imagesLoaded=function(){this.trigger("loaded_images"),this.checkLoaded()},AnimationItem.prototype.preloadImages=function(){this.imagePreloader.setAssetsPath(this.assetsPath),this.imagePreloader.setPath(this.path),this.imagePreloader.loadAssets(this.animationData.assets,this.imagesLoaded.bind(this))},AnimationItem.prototype.configAnimation=function(D){if(this.renderer)try{this.animationData=D,this.initialSegment?(this.totalFrames=Math.floor(this.initialSegment[1]-this.initialSegment[0]),this.firstFrame=Math.round(this.initialSegment[0])):(this.totalFrames=Math.floor(this.animationData.op-this.animationData.ip),this.firstFrame=Math.round(this.animationData.ip)),this.renderer.configAnimation(D),D.assets||(D.assets=[]),this.assets=this.animationData.assets,this.frameRate=this.animationData.fr,this.frameMult=this.animationData.fr/1e3,this.renderer.searchExtraCompositions(D.assets),this.markers=markerParser(D.markers||[]),this.trigger("config_ready"),this.preloadImages(),this.loadSegments(),this.updaFrameModifier(),this.waitForFontsLoaded(),this.isPaused&&this.audioController.pause()}catch(D){this.triggerConfigError(D)}},AnimationItem.prototype.waitForFontsLoaded=function(){this.renderer&&(this.renderer.globalData.fontManager.isLoaded?this.checkLoaded():setTimeout(this.waitForFontsLoaded.bind(this),20))},AnimationItem.prototype.checkLoaded=function(){if(!this.isLoaded&&this.renderer.globalData.fontManager.isLoaded&&(this.imagePreloader.loadedImages()||"canvas"!==this.renderer.rendererType)&&this.imagePreloader.loadedFootages()){this.isLoaded=!0;var D=getExpressionsPlugin();D&&D.initExpressions(this),this.renderer.initItems(),setTimeout((function(){this.trigger("DOMLoaded")}).bind(this),0),this.gotoFrame(),this.autoplay&&this.play()}},AnimationItem.prototype.resize=function(D,F){var O="number"==typeof D?D:void 0,U="number"==typeof F?F:void 0;this.renderer.updateContainerSize(O,U)},AnimationItem.prototype.setSubframe=function(D){this.isSubframeEnabled=!!D},AnimationItem.prototype.gotoFrame=function(){this.currentFrame=this.isSubframeEnabled?this.currentRawFrame:~~this.currentRawFrame,this.timeCompleted!==this.totalFrames&&this.currentFrame>this.timeCompleted&&(this.currentFrame=this.timeCompleted),this.trigger("enterFrame"),this.renderFrame(),this.trigger("drawnFrame")},AnimationItem.prototype.renderFrame=function(){if(!1!==this.isLoaded&&this.renderer)try{this.expressionsPlugin&&this.expressionsPlugin.resetFrame(),this.renderer.renderFrame(this.currentFrame+this.firstFrame)}catch(D){this.triggerRenderFrameError(D)}},AnimationItem.prototype.play=function(D){(!D||this.name===D)&&!0===this.isPaused&&(this.isPaused=!1,this.trigger("_play"),this.audioController.resume(),this._idle&&(this._idle=!1,this.trigger("_active")))},AnimationItem.prototype.pause=function(D){D&&this.name!==D||!1!==this.isPaused||(this.isPaused=!0,this.trigger("_pause"),this._idle=!0,this.trigger("_idle"),this.audioController.pause())},AnimationItem.prototype.togglePause=function(D){D&&this.name!==D||(!0===this.isPaused?this.play():this.pause())},AnimationItem.prototype.stop=function(D){D&&this.name!==D||(this.pause(),this.playCount=0,this._completedLoop=!1,this.setCurrentRawFrameValue(0))},AnimationItem.prototype.getMarkerData=function(D){for(var F,O=0;O<this.markers.length;O+=1)if((F=this.markers[O]).payload&&F.payload.name===D)return F;return null},AnimationItem.prototype.goToAndStop=function(D,F,O){if(!O||this.name===O){if(isNaN(Number(D))){var U=this.getMarkerData(D);U&&this.goToAndStop(U.time,!0)}else F?this.setCurrentRawFrameValue(D):this.setCurrentRawFrameValue(D*this.frameModifier);this.pause()}},AnimationItem.prototype.goToAndPlay=function(D,F,O){if(!O||this.name===O){var U=Number(D);if(isNaN(U)){var G=this.getMarkerData(D);G&&(G.duration?this.playSegments([G.time,G.time+G.duration],!0):this.goToAndStop(G.time,!0))}else this.goToAndStop(U,F,O);this.play()}},AnimationItem.prototype.advanceTime=function(D){if(!0!==this.isPaused&&!1!==this.isLoaded){var F=this.currentRawFrame+D*this.frameModifier,O=!1;F>=this.totalFrames-1&&this.frameModifier>0?this.loop&&this.playCount!==this.loop?F>=this.totalFrames?(this.playCount+=1,this.checkSegments(F%this.totalFrames)||(this.setCurrentRawFrameValue(F%this.totalFrames),this._completedLoop=!0,this.trigger("loopComplete"))):this.setCurrentRawFrameValue(F):this.checkSegments(F>this.totalFrames?F%this.totalFrames:0)||(O=!0,F=this.totalFrames-1):F<0?this.checkSegments(F%this.totalFrames)||(this.loop&&!(this.playCount--<=0&&!0!==this.loop)?(this.setCurrentRawFrameValue(this.totalFrames+F%this.totalFrames),this._completedLoop?this.trigger("loopComplete"):this._completedLoop=!0):(O=!0,F=0)):this.setCurrentRawFrameValue(F),O&&(this.setCurrentRawFrameValue(F),this.pause(),this.trigger("complete"))}},AnimationItem.prototype.adjustSegment=function(D,F){this.playCount=0,D[1]<D[0]?(this.frameModifier>0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(-1)),this.totalFrames=D[0]-D[1],this.timeCompleted=this.totalFrames,this.firstFrame=D[1],this.setCurrentRawFrameValue(this.totalFrames-.001-F)):D[1]>D[0]&&(this.frameModifier<0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(1)),this.totalFrames=D[1]-D[0],this.timeCompleted=this.totalFrames,this.firstFrame=D[0],this.setCurrentRawFrameValue(.001+F)),this.trigger("segmentStart")},AnimationItem.prototype.setSegment=function(D,F){var O=-1;this.isPaused&&(this.currentRawFrame+this.firstFrame<D?O=D:this.currentRawFrame+this.firstFrame>F&&(O=F-D)),this.firstFrame=D,this.totalFrames=F-D,this.timeCompleted=this.totalFrames,-1!==O&&this.goToAndStop(O,!0)},AnimationItem.prototype.playSegments=function(D,F){if(F&&(this.segments.length=0),"object"===_typeof$4(D[0])){var O,U=D.length;for(O=0;O<U;O+=1)this.segments.push(D[O])}else this.segments.push(D);this.segments.length&&F&&this.adjustSegment(this.segments.shift(),0),this.isPaused&&this.play()},AnimationItem.prototype.resetSegments=function(D){this.segments.length=0,this.segments.push([this.animationData.ip,this.animationData.op]),D&&this.checkSegments(0)},AnimationItem.prototype.checkSegments=function(D){return!!this.segments.length&&(this.adjustSegment(this.segments.shift(),D),!0)},AnimationItem.prototype.destroy=function(D){(!D||this.name===D)&&this.renderer&&(this.renderer.destroy(),this.imagePreloader.destroy(),this.trigger("destroy"),this._cbs=null,this.onEnterFrame=null,this.onLoopComplete=null,this.onComplete=null,this.onSegmentStart=null,this.onDestroy=null,this.renderer=null,this.expressionsPlugin=null,this.imagePreloader=null,this.projectInterface=null)},AnimationItem.prototype.setCurrentRawFrameValue=function(D){this.currentRawFrame=D,this.gotoFrame()},AnimationItem.prototype.setSpeed=function(D){this.playSpeed=D,this.updaFrameModifier()},AnimationItem.prototype.setDirection=function(D){this.playDirection=D<0?-1:1,this.updaFrameModifier()},AnimationItem.prototype.setLoop=function(D){this.loop=D},AnimationItem.prototype.setVolume=function(D,F){F&&this.name!==F||this.audioController.setVolume(D)},AnimationItem.prototype.getVolume=function(){return this.audioController.getVolume()},AnimationItem.prototype.mute=function(D){D&&this.name!==D||this.audioController.mute()},AnimationItem.prototype.unmute=function(D){D&&this.name!==D||this.audioController.unmute()},AnimationItem.prototype.updaFrameModifier=function(){this.frameModifier=this.frameMult*this.playSpeed*this.playDirection,this.audioController.setRate(this.playSpeed*this.playDirection)},AnimationItem.prototype.getPath=function(){return this.path},AnimationItem.prototype.getAssetsPath=function(D){var F="";if(D.e)F=D.p;else if(this.assetsPath){var O=D.p;-1!==O.indexOf("images/")&&(O=O.split("/")[1]),F=this.assetsPath+O}else F=this.path+(D.u?D.u:"")+D.p;return F},AnimationItem.prototype.getAssetData=function(D){for(var F=0,O=this.assets.length;F<O;){if(D===this.assets[F].id)return this.assets[F];F+=1}return null},AnimationItem.prototype.hide=function(){this.renderer.hide()},AnimationItem.prototype.show=function(){this.renderer.show()},AnimationItem.prototype.getDuration=function(D){return D?this.totalFrames:this.totalFrames/this.frameRate},AnimationItem.prototype.updateDocumentData=function(D,F,O){try{this.renderer.getElementByPath(D).updateDocumentData(F,O)}catch(D){}},AnimationItem.prototype.trigger=function(D){if(this._cbs&&this._cbs[D])switch(D){case"enterFrame":this.triggerEvent(D,new BMEnterFrameEvent(D,this.currentFrame,this.totalFrames,this.frameModifier));break;case"drawnFrame":this.drawnFrameEvent.currentTime=this.currentFrame,this.drawnFrameEvent.totalTime=this.totalFrames,this.drawnFrameEvent.direction=this.frameModifier,this.triggerEvent(D,this.drawnFrameEvent);break;case"loopComplete":this.triggerEvent(D,new BMCompleteLoopEvent(D,this.loop,this.playCount,this.frameMult));break;case"complete":this.triggerEvent(D,new BMCompleteEvent(D,this.frameMult));break;case"segmentStart":this.triggerEvent(D,new BMSegmentStartEvent(D,this.firstFrame,this.totalFrames));break;case"destroy":this.triggerEvent(D,new BMDestroyEvent(D,this));break;default:this.triggerEvent(D)}"enterFrame"===D&&this.onEnterFrame&&this.onEnterFrame.call(this,new BMEnterFrameEvent(D,this.currentFrame,this.totalFrames,this.frameMult)),"loopComplete"===D&&this.onLoopComplete&&this.onLoopComplete.call(this,new BMCompleteLoopEvent(D,this.loop,this.playCount,this.frameMult)),"complete"===D&&this.onComplete&&this.onComplete.call(this,new BMCompleteEvent(D,this.frameMult)),"segmentStart"===D&&this.onSegmentStart&&this.onSegmentStart.call(this,new BMSegmentStartEvent(D,this.firstFrame,this.totalFrames)),"destroy"===D&&this.onDestroy&&this.onDestroy.call(this,new BMDestroyEvent(D,this))},AnimationItem.prototype.triggerRenderFrameError=function(D){var F=new BMRenderFrameErrorEvent(D,this.currentFrame);this.triggerEvent("error",F),this.onError&&this.onError.call(this,F)},AnimationItem.prototype.triggerConfigError=function(D){var F=new BMConfigErrorEvent(D,this.currentFrame);this.triggerEvent("error",F),this.onError&&this.onError.call(this,F)};var animationManager=function(){var D={},F=[],O=0,U=0,G=0,W=!0,Y=!1;function X(D){for(var O=0,G=D.target;O<U;)F[O].animation!==G||(F.splice(O,1),O-=1,U-=1,G.isPaused||Q()),O+=1}function J(D,O){if(!D)return null;for(var G=0;G<U;){if(F[G].elem===D&&null!==F[G].elem)return F[G].animation;G+=1}var W=new AnimationItem;return tt(W,D),W.setData(D,O),W}function K(){var D,O=F.length,U=[];for(D=0;D<O;D+=1)U.push(F[D].animation);return U}function Z(){G+=1,tb()}function Q(){G-=1}function tt(D,O){D.addEventListener("destroy",X),D.addEventListener("_active",Z),D.addEventListener("_idle",Q),F.push({elem:O,animation:D}),U+=1}function te(D){var F=new AnimationItem;return tt(F,null),F.setParams(D),F}function tr(D,O){var G;for(G=0;G<U;G+=1)F[G].animation.setSpeed(D,O)}function ts(D,O){var G;for(G=0;G<U;G+=1)F[G].animation.setDirection(D,O)}function tn(D){var O;for(O=0;O<U;O+=1)F[O].animation.play(D)}function ta(D){var X,J=D-O;for(X=0;X<U;X+=1)F[X].animation.advanceTime(J);O=D,G&&!Y?window.requestAnimationFrame(ta):W=!0}function th(D){O=D,window.requestAnimationFrame(ta)}function tp(D){var O;for(O=0;O<U;O+=1)F[O].animation.pause(D)}function tf(D,O,G){var W;for(W=0;W<U;W+=1)F[W].animation.goToAndStop(D,O,G)}function tu(D){var O;for(O=0;O<U;O+=1)F[O].animation.stop(D)}function tc(D){var O;for(O=0;O<U;O+=1)F[O].animation.togglePause(D)}function tm(D){var O;for(O=U-1;O>=0;O-=1)F[O].animation.destroy(D)}function tg(D,F,O){var U,G=[].concat([].slice.call(document.getElementsByClassName("lottie")),[].slice.call(document.getElementsByClassName("bodymovin"))),W=G.length;for(U=0;U<W;U+=1)O&&G[U].setAttribute("data-bm-type",O),J(G[U],D);if(F&&0===W){O||(O="svg");var Y=document.getElementsByTagName("body")[0];Y.innerText="";var X=createTag("div");X.style.width="100%",X.style.height="100%",X.setAttribute("data-bm-type",O),Y.appendChild(X),J(X,D)}}function tv(){var D;for(D=0;D<U;D+=1)F[D].animation.resize()}function tb(){!Y&&G&&W&&(window.requestAnimationFrame(th),W=!1)}function t_(){Y=!0}function tk(){Y=!1,tb()}function tw(D,O){var G;for(G=0;G<U;G+=1)F[G].animation.setVolume(D,O)}function tA(D){var O;for(O=0;O<U;O+=1)F[O].animation.mute(D)}function tP(D){var O;for(O=0;O<U;O+=1)F[O].animation.unmute(D)}return D.registerAnimation=J,D.loadAnimation=te,D.setSpeed=tr,D.setDirection=ts,D.play=tn,D.pause=tp,D.stop=tu,D.togglePause=tc,D.searchAnimations=tg,D.resize=tv,D.goToAndStop=tf,D.destroy=tm,D.freeze=t_,D.unfreeze=tk,D.setVolume=tw,D.mute=tA,D.unmute=tP,D.getRegisteredAnimations=K,D}(),BezierFactory=function(){var D={};D.getBezierEasing=O;var F={};function O(D,O,U,G,W){var Y=W||("bez_"+D+"_"+O+"_"+U+"_"+G).replace(/\./g,"p");if(F[Y])return F[Y];var X=new ta([D,O,U,G]);return F[Y]=X,X}var U=4,G=.001,W=1e-7,Y=10,X=11,J=.1,K="function"==typeof Float32Array;function Z(D,F){return 1-3*F+3*D}function Q(D,F){return 3*F-6*D}function tt(D){return 3*D}function te(D,F,O){return((Z(F,O)*D+Q(F,O))*D+tt(F))*D}function tr(D,F,O){return 3*Z(F,O)*D*D+2*Q(F,O)*D+tt(F)}function ts(D,F,O,U,G){var X,J,K=0;do(X=te(J=F+(O-F)/2,U,G)-D)>0?O=J:F=J;while(Math.abs(X)>W&&++K<Y);return J}function tn(D,F,O,G){for(var W=0;W<U;++W){var Y=tr(F,O,G);if(0===Y)break;var X=te(F,O,G)-D;F-=X/Y}return F}function ta(D){this._p=D,this._mSampleValues=K?new Float32Array(X):Array(X),this._precomputed=!1,this.get=this.get.bind(this)}return ta.prototype={get:function(D){var F=this._p[0],O=this._p[1],U=this._p[2],G=this._p[3];return(this._precomputed||this._precompute(),F===O&&U===G)?D:0===D?0:1===D?1:te(this._getTForX(D),O,G)},_precompute:function(){var D=this._p[0],F=this._p[1],O=this._p[2],U=this._p[3];this._precomputed=!0,(D!==F||O!==U)&&this._calcSampleValues()},_calcSampleValues:function(){for(var D=this._p[0],F=this._p[2],O=0;O<X;++O)this._mSampleValues[O]=te(O*J,D,F)},_getTForX:function(D){for(var F=this._p[0],O=this._p[2],U=this._mSampleValues,W=0,Y=1,K=X-1;Y!==K&&U[Y]<=D;++Y)W+=J;var Z=W+(D-U[--Y])/(U[Y+1]-U[Y])*J,Q=tr(Z,F,O);return Q>=G?tn(D,Z,F,O):0===Q?Z:ts(D,W,W+J,F,O)}},D}(),pooling=function(){return{double:function(D){return D.concat(createSizedArray(D.length))}}}(),poolFactory=function(){return function(D,F,O){var U=0,G=D,W=createSizedArray(G);return{newElement:function(){var D;return U?(U-=1,D=W[U]):D=F(),D},release:function(D){U===G&&(W=pooling.double(W),G*=2),O&&O(D),W[U]=D,U+=1}}}}(),bezierLengthPool=function(){return poolFactory(8,function(){return{addedLength:0,percents:createTypedArray("float32",getDefaultCurveSegments()),lengths:createTypedArray("float32",getDefaultCurveSegments())}})}(),segmentsLengthPool=function(){function D(D){var F,O=D.lengths.length;for(F=0;F<O;F+=1)bezierLengthPool.release(D.lengths[F]);D.lengths.length=0}return poolFactory(8,function(){return{lengths:[],totalLength:0}},D)}();function bezFunction(){var D=Math;function F(D,F,O,U,G,W){var Y=D*U+F*G+O*W-G*U-W*D-O*F;return Y>-.001&&Y<.001}function O(O,U,G,W,Y,X,J,K,Z){if(0===G&&0===X&&0===Z)return F(O,U,W,Y,J,K);var Q,tt=D.sqrt(D.pow(W-O,2)+D.pow(Y-U,2)+D.pow(X-G,2)),te=D.sqrt(D.pow(J-O,2)+D.pow(K-U,2)+D.pow(Z-G,2)),tr=D.sqrt(D.pow(J-W,2)+D.pow(K-Y,2)+D.pow(Z-X,2));return(Q=tt>te?tt>tr?tt-te-tr:tr-te-tt:tr>te?tr-te-tt:te-tt-tr)>-.0001&&Q<1e-4}var U=function(){return function(D,F,O,U){var G,W,Y,X,J,K,Z=getDefaultCurveSegments(),Q=0,tt=[],te=[],tr=bezierLengthPool.newElement();for(G=0,Y=O.length;G<Z;G+=1){for(W=0,J=G/(Z-1),K=0;W<Y;W+=1)X=bmPow(1-J,3)*D[W]+3*bmPow(1-J,2)*J*O[W]+3*(1-J)*bmPow(J,2)*U[W]+bmPow(J,3)*F[W],tt[W]=X,null!==te[W]&&(K+=bmPow(tt[W]-te[W],2)),te[W]=tt[W];K&&(Q+=K=bmSqrt(K)),tr.percents[G]=J,tr.lengths[G]=Q}return tr.addedLength=Q,tr}}();function G(D){var F,O=segmentsLengthPool.newElement(),G=D.c,W=D.v,Y=D.o,X=D.i,J=D._length,K=O.lengths,Z=0;for(F=0;F<J-1;F+=1)K[F]=U(W[F],W[F+1],Y[F],X[F+1]),Z+=K[F].addedLength;return G&&J&&(K[F]=U(W[F],W[0],Y[F],X[0]),Z+=K[F].addedLength),O.totalLength=Z,O}function W(D){this.segmentLength=0,this.points=Array(D)}function Y(D,F){this.partialLength=D,this.point=F}var X=function(){var D={};return function(O,U,G,X){var J=(O[0]+"_"+O[1]+"_"+U[0]+"_"+U[1]+"_"+G[0]+"_"+G[1]+"_"+X[0]+"_"+X[1]).replace(/\./g,"p");if(!D[J]){var K,Z,Q,tt,te,tr,ts,tn=getDefaultCurveSegments(),ta=0,th=null;2===O.length&&(O[0]!==U[0]||O[1]!==U[1])&&F(O[0],O[1],U[0],U[1],O[0]+G[0],O[1]+G[1])&&F(O[0],O[1],U[0],U[1],U[0]+X[0],U[1]+X[1])&&(tn=2);var tp=new W(tn);for(K=0,Q=G.length;K<tn;K+=1){for(Z=0,ts=createSizedArray(Q),te=K/(tn-1),tr=0;Z<Q;Z+=1)tt=bmPow(1-te,3)*O[Z]+3*bmPow(1-te,2)*te*(O[Z]+G[Z])+3*(1-te)*bmPow(te,2)*(U[Z]+X[Z])+bmPow(te,3)*U[Z],ts[Z]=tt,null!==th&&(tr+=bmPow(ts[Z]-th[Z],2));ta+=tr=bmSqrt(tr),tp.points[K]=new Y(tr,ts),th=ts}tp.segmentLength=ta,D[J]=tp}return D[J]}}();function J(D,F){var O=F.percents,U=F.lengths,G=O.length,W=bmFloor((G-1)*D),Y=D*F.addedLength,X=0;if(W===G-1||0===W||Y===U[W])return O[W];for(var J=U[W]>Y?-1:1,K=!0;K;)if(U[W]<=Y&&U[W+1]>Y?(X=(Y-U[W])/(U[W+1]-U[W]),K=!1):W+=J,W<0||W>=G-1){if(W===G-1)return O[W];K=!1}return O[W]+(O[W+1]-O[W])*X}function K(F,O,U,G,W,Y){var X=J(W,Y),K=1-X;return[D.round((K*K*K*F[0]+(X*K*K+K*X*K+K*K*X)*U[0]+(X*X*K+K*X*X+X*K*X)*G[0]+X*X*X*O[0])*1e3)/1e3,D.round((K*K*K*F[1]+(X*K*K+K*X*K+K*K*X)*U[1]+(X*X*K+K*X*X+X*K*X)*G[1]+X*X*X*O[1])*1e3)/1e3]}var Z=createTypedArray("float32",8);return{getSegmentsLength:G,getNewSegment:function(F,O,U,G,W,Y,X){W<0?W=0:W>1&&(W=1);var K,Q=J(W,X),tt=J(Y=Y>1?1:Y,X),te=F.length,tr=1-Q,ts=1-tt,tn=tr*tr*tr,ta=Q*tr*tr*3,th=Q*Q*tr*3,tp=Q*Q*Q,tf=tr*tr*ts,tu=Q*tr*ts+tr*Q*ts+tr*tr*tt,tc=Q*Q*ts+tr*Q*tt+Q*tr*tt,tm=Q*Q*tt,tg=tr*ts*ts,tv=Q*ts*ts+tr*tt*ts+tr*ts*tt,tb=Q*tt*ts+tr*tt*tt+Q*ts*tt,t_=Q*tt*tt,tk=ts*ts*ts,tw=tt*ts*ts+ts*tt*ts+ts*ts*tt,tA=tt*tt*ts+ts*tt*tt+tt*ts*tt,tP=tt*tt*tt;for(K=0;K<te;K+=1)Z[4*K]=D.round((tn*F[K]+ta*U[K]+th*G[K]+tp*O[K])*1e3)/1e3,Z[4*K+1]=D.round((tf*F[K]+tu*U[K]+tc*G[K]+tm*O[K])*1e3)/1e3,Z[4*K+2]=D.round((tg*F[K]+tv*U[K]+tb*G[K]+t_*O[K])*1e3)/1e3,Z[4*K+3]=D.round((tk*F[K]+tw*U[K]+tA*G[K]+tP*O[K])*1e3)/1e3;return Z},getPointInSegment:K,buildBezierData:X,pointOnLine2D:F,pointOnLine3D:O}}var bez=bezFunction(),initFrame=initialDefaultFrame,mathAbs=Math.abs;function interpolateValue(D,F){var O,U,G,W,Y,X=this.offsetTime;"multidimensional"===this.propType&&(tn=createTypedArray("float32",this.pv.length));for(var J=F.lastIndex,K=J,Z=this.keyframes.length-1,Q=!0;Q;){if(ta=this.keyframes[K],th=this.keyframes[K+1],K===Z-1&&D>=th.t-X){ta.h&&(ta=th),J=0;break}if(th.t-X>D){J=K;break}K<Z-1?K+=1:(J=0,Q=!1)}tp=this.keyframesMetadata[K]||{};var tt=th.t-X,te=ta.t-X;if(ta.to){tp.bezierData||(tp.bezierData=bez.buildBezierData(ta.s,th.s||ta.e,ta.to,ta.ti));var tr=tp.bezierData;if(D>=tt||D<te){var ts=D>=tt?tr.points.length-1:0;for(tf=0,tu=tr.points[ts].point.length;tf<tu;tf+=1)tn[tf]=tr.points[ts].point[tf]}else{tp.__fnct?tv=tp.__fnct:(tv=BezierFactory.getBezierEasing(ta.o.x,ta.o.y,ta.i.x,ta.i.y,ta.n).get,tp.__fnct=tv),tc=tv((D-te)/(tt-te));var tn,ta,th,tp,tf,tu,tc,tm,tg,tv,tb,t_,tk=tr.segmentLength*tc,tw=F.lastFrame<D&&F._lastKeyframeIndex===K?F._lastAddedLength:0;for(tg=F.lastFrame<D&&F._lastKeyframeIndex===K?F._lastPoint:0,Q=!0,tm=tr.points.length;Q;){if(tw+=tr.points[tg].partialLength,0===tk||0===tc||tg===tr.points.length-1){for(tf=0,tu=tr.points[tg].point.length;tf<tu;tf+=1)tn[tf]=tr.points[tg].point[tf];break}if(tk>=tw&&tk<tw+tr.points[tg+1].partialLength){for(tf=0,t_=(tk-tw)/tr.points[tg+1].partialLength,tu=tr.points[tg].point.length;tf<tu;tf+=1)tn[tf]=tr.points[tg].point[tf]+(tr.points[tg+1].point[tf]-tr.points[tg].point[tf])*t_;break}tg<tm-1?tg+=1:Q=!1}F._lastPoint=tg,F._lastAddedLength=tw-tr.points[tg].partialLength,F._lastKeyframeIndex=K}}else if(Z=ta.s.length,tb=th.s||ta.e,this.sh&&1!==ta.h)D>=tt?(tn[0]=tb[0],tn[1]=tb[1],tn[2]=tb[2]):D<=te?(tn[0]=ta.s[0],tn[1]=ta.s[1],tn[2]=ta.s[2]):quaternionToEuler(tn,slerp(createQuaternion(ta.s),createQuaternion(tb),(D-te)/(tt-te)));else for(K=0;K<Z;K+=1)1!==ta.h&&(D>=tt?tc=1:D<te?tc=0:(ta.o.x.constructor===Array?(tp.__fnct||(tp.__fnct=[]),tp.__fnct[K]?tv=tp.__fnct[K]:(O=void 0===ta.o.x[K]?ta.o.x[0]:ta.o.x[K],U=void 0===ta.o.y[K]?ta.o.y[0]:ta.o.y[K],G=void 0===ta.i.x[K]?ta.i.x[0]:ta.i.x[K],W=void 0===ta.i.y[K]?ta.i.y[0]:ta.i.y[K],tv=BezierFactory.getBezierEasing(O,U,G,W).get,tp.__fnct[K]=tv)):tp.__fnct?tv=tp.__fnct:(O=ta.o.x,U=ta.o.y,G=ta.i.x,W=ta.i.y,tv=BezierFactory.getBezierEasing(O,U,G,W).get,ta.keyframeMetadata=tv),tc=tv((D-te)/(tt-te)))),tb=th.s||ta.e,Y=1===ta.h?ta.s[K]:ta.s[K]+(tb[K]-ta.s[K])*tc,"multidimensional"===this.propType?tn[K]=Y:tn=Y;return F.lastIndex=J,tn}function slerp(D,F,O){var U,G,W,Y,X,J=[],K=D[0],Z=D[1],Q=D[2],tt=D[3],te=F[0],tr=F[1],ts=F[2],tn=F[3];return(G=K*te+Z*tr+Q*ts+tt*tn)<0&&(G=-G,te=-te,tr=-tr,ts=-ts,tn=-tn),1-G>1e-6?(W=Math.sin(U=Math.acos(G)),Y=Math.sin((1-O)*U)/W,X=Math.sin(O*U)/W):(Y=1-O,X=O),J[0]=Y*K+X*te,J[1]=Y*Z+X*tr,J[2]=Y*Q+X*ts,J[3]=Y*tt+X*tn,J}function quaternionToEuler(D,F){var O=F[0],U=F[1],G=F[2],W=F[3],Y=Math.atan2(2*U*W-2*O*G,1-2*U*U-2*G*G),X=Math.asin(2*O*U+2*G*W),J=Math.atan2(2*O*W-2*U*G,1-2*O*O-2*G*G);D[0]=Y/degToRads,D[1]=X/degToRads,D[2]=J/degToRads}function createQuaternion(D){var F=D[0]*degToRads,O=D[1]*degToRads,U=D[2]*degToRads,G=Math.cos(F/2),W=Math.cos(O/2),Y=Math.cos(U/2),X=Math.sin(F/2),J=Math.sin(O/2),K=Math.sin(U/2),Z=G*W*Y-X*J*K;return[X*J*Y+G*W*K,X*W*Y+G*J*K,G*J*Y-X*W*K,Z]}function getValueAtCurrentTime(){var D=this.comp.renderedFrame-this.offsetTime,F=this.keyframes[0].t-this.offsetTime,O=this.keyframes[this.keyframes.length-1].t-this.offsetTime;if(!(D===this._caching.lastFrame||this._caching.lastFrame!==initFrame&&(this._caching.lastFrame>=O&&D>=O||this._caching.lastFrame<F&&D<F))){this._caching.lastFrame>=D&&(this._caching._lastKeyframeIndex=-1,this._caching.lastIndex=0);var U=this.interpolateValue(D,this._caching);this.pv=U}return this._caching.lastFrame=D,this.pv}function setVValue(D){var F;if("unidimensional"===this.propType)F=D*this.mult,mathAbs(this.v-F)>1e-5&&(this.v=F,this._mdf=!0);else for(var O=0,U=this.v.length;O<U;)F=D[O]*this.mult,mathAbs(this.v[O]-F)>1e-5&&(this.v[O]=F,this._mdf=!0),O+=1}function processEffectsSequence(){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length){if(this.lock){this.setVValue(this.pv);return}this.lock=!0,this._mdf=this._isFirstFrame;var D,F=this.effectsSequence.length,O=this.kf?this.pv:this.data.k;for(D=0;D<F;D+=1)O=this.effectsSequence[D](O);this.setVValue(O),this._isFirstFrame=!1,this.lock=!1,this.frameId=this.elem.globalData.frameId}}function addEffect(D){this.effectsSequence.push(D),this.container.addDynamicProperty(this)}function ValueProperty(D,F,O,U){this.propType="unidimensional",this.mult=O||1,this.data=F,this.v=O?F.k*O:F.k,this.pv=F.k,this._mdf=!1,this.elem=D,this.container=U,this.comp=D.comp,this.k=!1,this.kf=!1,this.vel=0,this.effectsSequence=[],this._isFirstFrame=!0,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.addEffect=addEffect}function MultiDimensionalProperty(D,F,O,U){this.propType="multidimensional",this.mult=O||1,this.data=F,this._mdf=!1,this.elem=D,this.container=U,this.comp=D.comp,this.k=!1,this.kf=!1,this.frameId=-1;var G,W=F.k.length;for(G=0,this.v=createTypedArray("float32",W),this.pv=createTypedArray("float32",W),this.vel=createTypedArray("float32",W);G<W;G+=1)this.v[G]=F.k[G]*this.mult,this.pv[G]=F.k[G];this._isFirstFrame=!0,this.effectsSequence=[],this.getValue=processEffectsSequence,this.setVValue=setVValue,this.addEffect=addEffect}function KeyframedValueProperty(D,F,O,U){this.propType="unidimensional",this.keyframes=F.k,this.keyframesMetadata=[],this.offsetTime=D.data.st,this.frameId=-1,this._caching={lastFrame:initFrame,lastIndex:0,value:0,_lastKeyframeIndex:-1},this.k=!0,this.kf=!0,this.data=F,this.mult=O||1,this.elem=D,this.container=U,this.comp=D.comp,this.v=initFrame,this.pv=initFrame,this._isFirstFrame=!0,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.interpolateValue=interpolateValue,this.effectsSequence=[getValueAtCurrentTime.bind(this)],this.addEffect=addEffect}function KeyframedMultidimensionalProperty(D,F,O,U){this.propType="multidimensional";var G,W,Y,X,J,K=F.k.length;for(G=0;G<K-1;G+=1)F.k[G].to&&F.k[G].s&&F.k[G+1]&&F.k[G+1].s&&(W=F.k[G].s,Y=F.k[G+1].s,X=F.k[G].to,J=F.k[G].ti,(2===W.length&&!(W[0]===Y[0]&&W[1]===Y[1])&&bez.pointOnLine2D(W[0],W[1],Y[0],Y[1],W[0]+X[0],W[1]+X[1])&&bez.pointOnLine2D(W[0],W[1],Y[0],Y[1],Y[0]+J[0],Y[1]+J[1])||3===W.length&&!(W[0]===Y[0]&&W[1]===Y[1]&&W[2]===Y[2])&&bez.pointOnLine3D(W[0],W[1],W[2],Y[0],Y[1],Y[2],W[0]+X[0],W[1]+X[1],W[2]+X[2])&&bez.pointOnLine3D(W[0],W[1],W[2],Y[0],Y[1],Y[2],Y[0]+J[0],Y[1]+J[1],Y[2]+J[2]))&&(F.k[G].to=null,F.k[G].ti=null),W[0]===Y[0]&&W[1]===Y[1]&&0===X[0]&&0===X[1]&&0===J[0]&&0===J[1]&&(2===W.length||W[2]===Y[2]&&0===X[2]&&0===J[2])&&(F.k[G].to=null,F.k[G].ti=null));this.effectsSequence=[getValueAtCurrentTime.bind(this)],this.data=F,this.keyframes=F.k,this.keyframesMetadata=[],this.offsetTime=D.data.st,this.k=!0,this.kf=!0,this._isFirstFrame=!0,this.mult=O||1,this.elem=D,this.container=U,this.comp=D.comp,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.interpolateValue=interpolateValue,this.frameId=-1;var Z=F.k[0].s.length;for(G=0,this.v=createTypedArray("float32",Z),this.pv=createTypedArray("float32",Z);G<Z;G+=1)this.v[G]=initFrame,this.pv[G]=initFrame;this._caching={lastFrame:initFrame,lastIndex:0,value:createTypedArray("float32",Z)},this.addEffect=addEffect}var PropertyFactory=function(){return{getProp:function(D,F,O,U,G){var W;if(F.sid&&(F=D.globalData.slotManager.getProp(F)),F.k.length){if("number"==typeof F.k[0])W=new MultiDimensionalProperty(D,F,U,G);else switch(O){case 0:W=new KeyframedValueProperty(D,F,U,G);break;case 1:W=new KeyframedMultidimensionalProperty(D,F,U,G)}}else W=new ValueProperty(D,F,U,G);return W.effectsSequence.length&&G.addDynamicProperty(W),W}}}();function DynamicPropertyContainer(){}DynamicPropertyContainer.prototype={addDynamicProperty:function(D){-1===this.dynamicProperties.indexOf(D)&&(this.dynamicProperties.push(D),this.container.addDynamicProperty(this),this._isAnimated=!0)},iterateDynamicProperties:function(){this._mdf=!1;var D,F=this.dynamicProperties.length;for(D=0;D<F;D+=1)this.dynamicProperties[D].getValue(),this.dynamicProperties[D]._mdf&&(this._mdf=!0)},initDynamicPropertyContainer:function(D){this.container=D,this.dynamicProperties=[],this._mdf=!1,this._isAnimated=!1}};var pointPool=function(){return poolFactory(8,function(){return createTypedArray("float32",2)})}();function ShapePath(){this.c=!1,this._length=0,this._maxLength=8,this.v=createSizedArray(this._maxLength),this.o=createSizedArray(this._maxLength),this.i=createSizedArray(this._maxLength)}ShapePath.prototype.setPathData=function(D,F){this.c=D,this.setLength(F);for(var O=0;O<F;)this.v[O]=pointPool.newElement(),this.o[O]=pointPool.newElement(),this.i[O]=pointPool.newElement(),O+=1},ShapePath.prototype.setLength=function(D){for(;this._maxLength<D;)this.doubleArrayLength();this._length=D},ShapePath.prototype.doubleArrayLength=function(){this.v=this.v.concat(createSizedArray(this._maxLength)),this.i=this.i.concat(createSizedArray(this._maxLength)),this.o=this.o.concat(createSizedArray(this._maxLength)),this._maxLength*=2},ShapePath.prototype.setXYAt=function(D,F,O,U,G){var W;switch(this._length=Math.max(this._length,U+1),this._length>=this._maxLength&&this.doubleArrayLength(),O){case"v":W=this.v;break;case"i":W=this.i;break;case"o":W=this.o;break;default:W=[]}W[U]&&(!W[U]||G)||(W[U]=pointPool.newElement()),W[U][0]=D,W[U][1]=F},ShapePath.prototype.setTripleAt=function(D,F,O,U,G,W,Y,X){this.setXYAt(D,F,"v",Y,X),this.setXYAt(O,U,"o",Y,X),this.setXYAt(G,W,"i",Y,X)},ShapePath.prototype.reverse=function(){var D,F=new ShapePath;F.setPathData(this.c,this._length);var O=this.v,U=this.o,G=this.i,W=0;this.c&&(F.setTripleAt(O[0][0],O[0][1],G[0][0],G[0][1],U[0][0],U[0][1],0,!1),W=1);var Y=this._length-1,X=this._length;for(D=W;D<X;D+=1)F.setTripleAt(O[Y][0],O[Y][1],G[Y][0],G[Y][1],U[Y][0],U[Y][1],D,!1),Y-=1;return F},ShapePath.prototype.length=function(){return this._length};var shapePool=function(){function D(D){var F,O=D._length;for(F=0;F<O;F+=1)pointPool.release(D.v[F]),pointPool.release(D.i[F]),pointPool.release(D.o[F]),D.v[F]=null,D.i[F]=null,D.o[F]=null;D._length=0,D.c=!1}function F(D){var F,U=O.newElement(),G=void 0===D._length?D.v.length:D._length;for(U.setLength(G),U.c=D.c,F=0;F<G;F+=1)U.setTripleAt(D.v[F][0],D.v[F][1],D.o[F][0],D.o[F][1],D.i[F][0],D.i[F][1],F);return U}var O=poolFactory(4,function(){return new ShapePath},D);return O.clone=F,O}();function ShapeCollection(){this._length=0,this._maxLength=4,this.shapes=createSizedArray(this._maxLength)}ShapeCollection.prototype.addShape=function(D){this._length===this._maxLength&&(this.shapes=this.shapes.concat(createSizedArray(this._maxLength)),this._maxLength*=2),this.shapes[this._length]=D,this._length+=1},ShapeCollection.prototype.releaseShapes=function(){var D;for(D=0;D<this._length;D+=1)shapePool.release(this.shapes[D]);this._length=0};var shapeCollectionPool=function(){var D={newShapeCollection:G,release:W},F=0,O=4,U=createSizedArray(4);function G(){var D;return F?(F-=1,D=U[F]):D=new ShapeCollection,D}function W(D){var G,W=D._length;for(G=0;G<W;G+=1)shapePool.release(D.shapes[G]);D._length=0,F===O&&(U=pooling.double(U),O*=2),U[F]=D,F+=1}return D}(),ShapePropertyFactory=function(){var D=-999999;function F(D,F,O){var U=O.lastIndex,G=this.keyframes;if(D<G[0].t-this.offsetTime)W=G[0].s[0],X=!0,U=0;else if(D>=G[G.length-1].t-this.offsetTime)W=G[G.length-1].s?G[G.length-1].s[0]:G[G.length-2].e[0],X=!0;else{for(var W,Y,X,J,K,Z,Q,tt,te,tr,ts,tn,ta,th=U,tp=G.length-1,tf=!0;tf&&(tr=G[th],!((ts=G[th+1]).t-this.offsetTime>D));)th<tp-1?th+=1:tf=!1;tn=this.keyframesMetadata[th]||{},X=1===tr.h,U=th,X||(D>=ts.t-this.offsetTime?tt=1:D<tr.t-this.offsetTime?tt=0:(tn.__fnct?ta=tn.__fnct:(ta=BezierFactory.getBezierEasing(tr.o.x,tr.o.y,tr.i.x,tr.i.y).get,tn.__fnct=ta),tt=ta((D-(tr.t-this.offsetTime))/(ts.t-this.offsetTime-(tr.t-this.offsetTime)))),Y=ts.s?ts.s[0]:tr.e[0]),W=tr.s[0]}for(J=0,Z=F._length,Q=W.i[0].length,O.lastIndex=U;J<Z;J+=1)for(K=0;K<Q;K+=1)te=X?W.i[J][K]:W.i[J][K]+(Y.i[J][K]-W.i[J][K])*tt,F.i[J][K]=te,te=X?W.o[J][K]:W.o[J][K]+(Y.o[J][K]-W.o[J][K])*tt,F.o[J][K]=te,te=X?W.v[J][K]:W.v[J][K]+(Y.v[J][K]-W.v[J][K])*tt,F.v[J][K]=te}function O(){var F=this.comp.renderedFrame-this.offsetTime,O=this.keyframes[0].t-this.offsetTime,U=this.keyframes[this.keyframes.length-1].t-this.offsetTime,G=this._caching.lastFrame;return G!==D&&(G<O&&F<O||G>U&&F>U)||(this._caching.lastIndex=G<F?this._caching.lastIndex:0,this.interpolateShape(F,this.pv,this._caching)),this._caching.lastFrame=F,this.pv}function U(){this.paths=this.localShapeCollection}function G(D,F){if(D._length!==F._length||D.c!==F.c)return!1;var O,U=D._length;for(O=0;O<U;O+=1)if(D.v[O][0]!==F.v[O][0]||D.v[O][1]!==F.v[O][1]||D.o[O][0]!==F.o[O][0]||D.o[O][1]!==F.o[O][1]||D.i[O][0]!==F.i[O][0]||D.i[O][1]!==F.i[O][1])return!1;return!0}function W(D){G(this.v,D)||(this.v=shapePool.clone(D),this.localShapeCollection.releaseShapes(),this.localShapeCollection.addShape(this.v),this._mdf=!0,this.paths=this.localShapeCollection)}function Y(){if(this.elem.globalData.frameId!==this.frameId){if(!this.effectsSequence.length){this._mdf=!1;return}if(this.lock){this.setVValue(this.pv);return}this.lock=!0,this._mdf=!1,D=this.kf?this.pv:this.data.ks?this.data.ks.k:this.data.pt.k;var D,F,O=this.effectsSequence.length;for(F=0;F<O;F+=1)D=this.effectsSequence[F](D);this.setVValue(D),this.lock=!1,this.frameId=this.elem.globalData.frameId}}function X(D,F,O){this.propType="shape",this.comp=D.comp,this.container=D,this.elem=D,this.data=F,this.k=!1,this.kf=!1,this._mdf=!1;var G=3===O?F.pt.k:F.ks.k;this.v=shapePool.clone(G),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.reset=U,this.effectsSequence=[]}function J(D){this.effectsSequence.push(D),this.container.addDynamicProperty(this)}function K(F,G,W){this.propType="shape",this.comp=F.comp,this.elem=F,this.container=F,this.offsetTime=F.data.st,this.keyframes=3===W?G.pt.k:G.ks.k,this.keyframesMetadata=[],this.k=!0,this.kf=!0;var Y=this.keyframes[0].s[0].i.length;this.v=shapePool.newElement(),this.v.setPathData(this.keyframes[0].s[0].c,Y),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.lastFrame=D,this.reset=U,this._caching={lastFrame:D,lastIndex:0},this.effectsSequence=[O.bind(this)]}X.prototype.interpolateShape=F,X.prototype.getValue=Y,X.prototype.setVValue=W,X.prototype.addEffect=J,K.prototype.getValue=Y,K.prototype.interpolateShape=F,K.prototype.setVValue=W,K.prototype.addEffect=J;var Z=function(){var D=roundCorner;function F(D,F){this.v=shapePool.newElement(),this.v.setPathData(!0,4),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.localShapeCollection.addShape(this.v),this.d=F.d,this.elem=D,this.comp=D.comp,this.frameId=-1,this.initDynamicPropertyContainer(D),this.p=PropertyFactory.getProp(D,F.p,1,0,this),this.s=PropertyFactory.getProp(D,F.s,1,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertEllToPath())}return F.prototype={reset:U,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertEllToPath())},convertEllToPath:function(){var F=this.p.v[0],O=this.p.v[1],U=this.s.v[0]/2,G=this.s.v[1]/2,W=3!==this.d,Y=this.v;Y.v[0][0]=F,Y.v[0][1]=O-G,Y.v[1][0]=W?F+U:F-U,Y.v[1][1]=O,Y.v[2][0]=F,Y.v[2][1]=O+G,Y.v[3][0]=W?F-U:F+U,Y.v[3][1]=O,Y.i[0][0]=W?F-U*D:F+U*D,Y.i[0][1]=O-G,Y.i[1][0]=W?F+U:F-U,Y.i[1][1]=O-G*D,Y.i[2][0]=W?F+U*D:F-U*D,Y.i[2][1]=O+G,Y.i[3][0]=W?F-U:F+U,Y.i[3][1]=O+G*D,Y.o[0][0]=W?F+U*D:F-U*D,Y.o[0][1]=O-G,Y.o[1][0]=W?F+U:F-U,Y.o[1][1]=O+G*D,Y.o[2][0]=W?F-U*D:F+U*D,Y.o[2][1]=O+G,Y.o[3][0]=W?F-U:F+U,Y.o[3][1]=O-G*D}},extendPrototype([DynamicPropertyContainer],F),F}(),Q=function(){function D(D,F){this.v=shapePool.newElement(),this.v.setPathData(!0,0),this.elem=D,this.comp=D.comp,this.data=F,this.frameId=-1,this.d=F.d,this.initDynamicPropertyContainer(D),1===F.sy?(this.ir=PropertyFactory.getProp(D,F.ir,0,0,this),this.is=PropertyFactory.getProp(D,F.is,0,.01,this),this.convertToPath=this.convertStarToPath):this.convertToPath=this.convertPolygonToPath,this.pt=PropertyFactory.getProp(D,F.pt,0,0,this),this.p=PropertyFactory.getProp(D,F.p,1,0,this),this.r=PropertyFactory.getProp(D,F.r,0,degToRads,this),this.or=PropertyFactory.getProp(D,F.or,0,0,this),this.os=PropertyFactory.getProp(D,F.os,0,.01,this),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertToPath())}return D.prototype={reset:U,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertToPath())},convertStarToPath:function(){var D,F,O,U,G=2*Math.floor(this.pt.v),W=2*Math.PI/G,Y=!0,X=this.or.v,J=this.ir.v,K=this.os.v,Z=this.is.v,Q=2*Math.PI*X/(2*G),tt=2*Math.PI*J/(2*G),te=-Math.PI/2;te+=this.r.v;var tr=3===this.data.d?-1:1;for(D=0,this.v._length=0;D<G;D+=1){F=Y?X:J,O=Y?K:Z,U=Y?Q:tt;var ts=F*Math.cos(te),tn=F*Math.sin(te),ta=0===ts&&0===tn?0:tn/Math.sqrt(ts*ts+tn*tn),th=0===ts&&0===tn?0:-ts/Math.sqrt(ts*ts+tn*tn);ts+=+this.p.v[0],tn+=+this.p.v[1],this.v.setTripleAt(ts,tn,ts-ta*U*O*tr,tn-th*U*O*tr,ts+ta*U*O*tr,tn+th*U*O*tr,D,!0),Y=!Y,te+=W*tr}},convertPolygonToPath:function(){var D,F=Math.floor(this.pt.v),O=2*Math.PI/F,U=this.or.v,G=this.os.v,W=2*Math.PI*U/(4*F),Y=-(.5*Math.PI),X=3===this.data.d?-1:1;for(Y+=this.r.v,this.v._length=0,D=0;D<F;D+=1){var J=U*Math.cos(Y),K=U*Math.sin(Y),Z=0===J&&0===K?0:K/Math.sqrt(J*J+K*K),Q=0===J&&0===K?0:-J/Math.sqrt(J*J+K*K);J+=+this.p.v[0],K+=+this.p.v[1],this.v.setTripleAt(J,K,J-Z*W*G*X,K-Q*W*G*X,J+Z*W*G*X,K+Q*W*G*X,D,!0),Y+=O*X}this.paths.length=0,this.paths[0]=this.v}},extendPrototype([DynamicPropertyContainer],D),D}(),tt=function(){function D(D,F){this.v=shapePool.newElement(),this.v.c=!0,this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.elem=D,this.comp=D.comp,this.frameId=-1,this.d=F.d,this.initDynamicPropertyContainer(D),this.p=PropertyFactory.getProp(D,F.p,1,0,this),this.s=PropertyFactory.getProp(D,F.s,1,0,this),this.r=PropertyFactory.getProp(D,F.r,0,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertRectToPath())}return D.prototype={convertRectToPath:function(){var D=this.p.v[0],F=this.p.v[1],O=this.s.v[0]/2,U=this.s.v[1]/2,G=bmMin(O,U,this.r.v),W=G*(1-roundCorner);this.v._length=0,2===this.d||1===this.d?(this.v.setTripleAt(D+O,F-U+G,D+O,F-U+G,D+O,F-U+W,0,!0),this.v.setTripleAt(D+O,F+U-G,D+O,F+U-W,D+O,F+U-G,1,!0),0!==G?(this.v.setTripleAt(D+O-G,F+U,D+O-G,F+U,D+O-W,F+U,2,!0),this.v.setTripleAt(D-O+G,F+U,D-O+W,F+U,D-O+G,F+U,3,!0),this.v.setTripleAt(D-O,F+U-G,D-O,F+U-G,D-O,F+U-W,4,!0),this.v.setTripleAt(D-O,F-U+G,D-O,F-U+W,D-O,F-U+G,5,!0),this.v.setTripleAt(D-O+G,F-U,D-O+G,F-U,D-O+W,F-U,6,!0),this.v.setTripleAt(D+O-G,F-U,D+O-W,F-U,D+O-G,F-U,7,!0)):(this.v.setTripleAt(D-O,F+U,D-O+W,F+U,D-O,F+U,2),this.v.setTripleAt(D-O,F-U,D-O,F-U+W,D-O,F-U,3))):(this.v.setTripleAt(D+O,F-U+G,D+O,F-U+W,D+O,F-U+G,0,!0),0!==G?(this.v.setTripleAt(D+O-G,F-U,D+O-G,F-U,D+O-W,F-U,1,!0),this.v.setTripleAt(D-O+G,F-U,D-O+W,F-U,D-O+G,F-U,2,!0),this.v.setTripleAt(D-O,F-U+G,D-O,F-U+G,D-O,F-U+W,3,!0),this.v.setTripleAt(D-O,F+U-G,D-O,F+U-W,D-O,F+U-G,4,!0),this.v.setTripleAt(D-O+G,F+U,D-O+G,F+U,D-O+W,F+U,5,!0),this.v.setTripleAt(D+O-G,F+U,D+O-W,F+U,D+O-G,F+U,6,!0),this.v.setTripleAt(D+O,F+U-G,D+O,F+U-G,D+O,F+U-W,7,!0)):(this.v.setTripleAt(D-O,F-U,D-O+W,F-U,D-O,F-U,1,!0),this.v.setTripleAt(D-O,F+U,D-O,F+U-W,D-O,F+U,2,!0),this.v.setTripleAt(D+O,F+U,D+O-W,F+U,D+O,F+U,3,!0)))},getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertRectToPath())},reset:U},extendPrototype([DynamicPropertyContainer],D),D}();function te(D,F,O){var U;return 3===O||4===O?U=(3===O?F.pt:F.ks).k.length?new K(D,F,O):new X(D,F,O):5===O?U=new tt(D,F):6===O?U=new Z(D,F):7===O&&(U=new Q(D,F)),U.k&&D.addDynamicProperty(U),U}function tr(){return X}function ts(){return K}var tn={};return tn.getShapeProp=te,tn.getConstructorFunction=tr,tn.getKeyframedConstructorFunction=ts,tn}(),Matrix=function(){var D=Math.cos,F=Math.sin,O=Math.tan,U=Math.round;function G(){return this.props[0]=1,this.props[1]=0,this.props[2]=0,this.props[3]=0,this.props[4]=0,this.props[5]=1,this.props[6]=0,this.props[7]=0,this.props[8]=0,this.props[9]=0,this.props[10]=1,this.props[11]=0,this.props[12]=0,this.props[13]=0,this.props[14]=0,this.props[15]=1,this}function W(O){if(0===O)return this;var U=D(O),G=F(O);return this._t(U,-G,0,0,G,U,0,0,0,0,1,0,0,0,0,1)}function Y(O){if(0===O)return this;var U=D(O),G=F(O);return this._t(1,0,0,0,0,U,-G,0,0,G,U,0,0,0,0,1)}function X(O){if(0===O)return this;var U=D(O),G=F(O);return this._t(U,0,G,0,0,1,0,0,-G,0,U,0,0,0,0,1)}function J(O){if(0===O)return this;var U=D(O),G=F(O);return this._t(U,-G,0,0,G,U,0,0,0,0,1,0,0,0,0,1)}function K(D,F){return this._t(1,F,D,1,0,0)}function Z(D,F){return this.shear(O(D),O(F))}function Q(U,G){var W=D(G),Y=F(G);return this._t(W,Y,0,0,-Y,W,0,0,0,0,1,0,0,0,0,1)._t(1,0,0,0,O(U),1,0,0,0,0,1,0,0,0,0,1)._t(W,-Y,0,0,Y,W,0,0,0,0,1,0,0,0,0,1)}function tt(D,F,O){return(O||0===O||(O=1),1===D&&1===F&&1===O)?this:this._t(D,0,0,0,0,F,0,0,0,0,O,0,0,0,0,1)}function te(D,F,O,U,G,W,Y,X,J,K,Z,Q,tt,te,tr,ts){return this.props[0]=D,this.props[1]=F,this.props[2]=O,this.props[3]=U,this.props[4]=G,this.props[5]=W,this.props[6]=Y,this.props[7]=X,this.props[8]=J,this.props[9]=K,this.props[10]=Z,this.props[11]=Q,this.props[12]=tt,this.props[13]=te,this.props[14]=tr,this.props[15]=ts,this}function tr(D,F,O){return(O=O||0,0!==D||0!==F||0!==O)?this._t(1,0,0,0,0,1,0,0,0,0,1,0,D,F,O,1):this}function ts(D,F,O,U,G,W,Y,X,J,K,Z,Q,tt,te,tr,ts){var tn=this.props;if(1===D&&0===F&&0===O&&0===U&&0===G&&1===W&&0===Y&&0===X&&0===J&&0===K&&1===Z&&0===Q)return tn[12]=tn[12]*D+tn[15]*tt,tn[13]=tn[13]*W+tn[15]*te,tn[14]=tn[14]*Z+tn[15]*tr,tn[15]*=ts,this._identityCalculated=!1,this;var ta=tn[0],th=tn[1],tp=tn[2],tf=tn[3],tu=tn[4],tc=tn[5],tm=tn[6],tg=tn[7],tv=tn[8],tb=tn[9],t_=tn[10],tk=tn[11],tw=tn[12],tA=tn[13],tP=tn[14],tC=tn[15];return tn[0]=ta*D+th*G+tp*J+tf*tt,tn[1]=ta*F+th*W+tp*K+tf*te,tn[2]=ta*O+th*Y+tp*Z+tf*tr,tn[3]=ta*U+th*X+tp*Q+tf*ts,tn[4]=tu*D+tc*G+tm*J+tg*tt,tn[5]=tu*F+tc*W+tm*K+tg*te,tn[6]=tu*O+tc*Y+tm*Z+tg*tr,tn[7]=tu*U+tc*X+tm*Q+tg*ts,tn[8]=tv*D+tb*G+t_*J+tk*tt,tn[9]=tv*F+tb*W+t_*K+tk*te,tn[10]=tv*O+tb*Y+t_*Z+tk*tr,tn[11]=tv*U+tb*X+t_*Q+tk*ts,tn[12]=tw*D+tA*G+tP*J+tC*tt,tn[13]=tw*F+tA*W+tP*K+tC*te,tn[14]=tw*O+tA*Y+tP*Z+tC*tr,tn[15]=tw*U+tA*X+tP*Q+tC*ts,this._identityCalculated=!1,this}function tn(D){var F=D.props;return this.transform(F[0],F[1],F[2],F[3],F[4],F[5],F[6],F[7],F[8],F[9],F[10],F[11],F[12],F[13],F[14],F[15])}function ta(){return this._identityCalculated||(this._identity=!(1!==this.props[0]||0!==this.props[1]||0!==this.props[2]||0!==this.props[3]||0!==this.props[4]||1!==this.props[5]||0!==this.props[6]||0!==this.props[7]||0!==this.props[8]||0!==this.props[9]||1!==this.props[10]||0!==this.props[11]||0!==this.props[12]||0!==this.props[13]||0!==this.props[14]||1!==this.props[15]),this._identityCalculated=!0),this._identity}function th(D){for(var F=0;F<16;){if(D.props[F]!==this.props[F])return!1;F+=1}return!0}function tp(D){var F;for(F=0;F<16;F+=1)D.props[F]=this.props[F];return D}function tf(D){var F;for(F=0;F<16;F+=1)this.props[F]=D[F]}function tu(D,F,O){return{x:D*this.props[0]+F*this.props[4]+O*this.props[8]+this.props[12],y:D*this.props[1]+F*this.props[5]+O*this.props[9]+this.props[13],z:D*this.props[2]+F*this.props[6]+O*this.props[10]+this.props[14]}}function tc(D,F,O){return D*this.props[0]+F*this.props[4]+O*this.props[8]+this.props[12]}function tm(D,F,O){return D*this.props[1]+F*this.props[5]+O*this.props[9]+this.props[13]}function tg(D,F,O){return D*this.props[2]+F*this.props[6]+O*this.props[10]+this.props[14]}function tv(){var D=this.props[0]*this.props[5]-this.props[1]*this.props[4],F=this.props[5]/D,O=-this.props[1]/D,U=-this.props[4]/D,G=this.props[0]/D,W=(this.props[4]*this.props[13]-this.props[5]*this.props[12])/D,Y=-(this.props[0]*this.props[13]-this.props[1]*this.props[12])/D,X=new Matrix;return X.props[0]=F,X.props[1]=O,X.props[4]=U,X.props[5]=G,X.props[12]=W,X.props[13]=Y,X}function tb(D){return this.getInverseMatrix().applyToPointArray(D[0],D[1],D[2]||0)}function t_(D){var F,O=D.length,U=[];for(F=0;F<O;F+=1)U[F]=tb(D[F]);return U}function tk(D,F,O){var U=createTypedArray("float32",6);if(this.isIdentity())U[0]=D[0],U[1]=D[1],U[2]=F[0],U[3]=F[1],U[4]=O[0],U[5]=O[1];else{var G=this.props[0],W=this.props[1],Y=this.props[4],X=this.props[5],J=this.props[12],K=this.props[13];U[0]=D[0]*G+D[1]*Y+J,U[1]=D[0]*W+D[1]*X+K,U[2]=F[0]*G+F[1]*Y+J,U[3]=F[0]*W+F[1]*X+K,U[4]=O[0]*G+O[1]*Y+J,U[5]=O[0]*W+O[1]*X+K}return U}function tw(D,F,O){return this.isIdentity()?[D,F,O]:[D*this.props[0]+F*this.props[4]+O*this.props[8]+this.props[12],D*this.props[1]+F*this.props[5]+O*this.props[9]+this.props[13],D*this.props[2]+F*this.props[6]+O*this.props[10]+this.props[14]]}function tA(D,F){if(this.isIdentity())return D+","+F;var O=this.props;return Math.round((D*O[0]+F*O[4]+O[12])*100)/100+","+Math.round((D*O[1]+F*O[5]+O[13])*100)/100}function tP(){for(var D=0,F=this.props,O="matrix3d(",G=1e4;D<16;)O+=U(F[D]*G)/G+(15===D?")":","),D+=1;return O}function tC(D){var F=1e4;return D<1e-6&&D>0||D>-.000001&&D<0?U(D*F)/F:D}function tS(){var D=this.props;return"matrix("+tC(D[0])+","+tC(D[1])+","+tC(D[4])+","+tC(D[5])+","+tC(D[12])+","+tC(D[13])+")"}return function(){this.reset=G,this.rotate=W,this.rotateX=Y,this.rotateY=X,this.rotateZ=J,this.skew=Z,this.skewFromAxis=Q,this.shear=K,this.scale=tt,this.setTransform=te,this.translate=tr,this.transform=ts,this.multiply=tn,this.applyToPoint=tu,this.applyToX=tc,this.applyToY=tm,this.applyToZ=tg,this.applyToPointArray=tw,this.applyToTriplePoints=tk,this.applyToPointStringified=tA,this.toCSS=tP,this.to2dCSS=tS,this.clone=tp,this.cloneFromProps=tf,this.equals=th,this.inversePoints=t_,this.inversePoint=tb,this.getInverseMatrix=tv,this._t=this.transform,this.isIdentity=ta,this._identity=!0,this._identityCalculated=!1,this.props=createTypedArray("float32",16),this.reset()}}();function _typeof$3(D){return(_typeof$3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(D){return typeof D}:function(D){return D&&"function"==typeof Symbol&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D})(D)}var lottie={};function setLocation(D){setLocationHref(D)}function searchAnimations(){animationManager.searchAnimations()}function setSubframeRendering(D){setSubframeEnabled(D)}function setPrefix(D){setIdPrefix(D)}function loadAnimation(D){return animationManager.loadAnimation(D)}function setQuality(D){if("string"==typeof D)switch(D){case"high":setDefaultCurveSegments(200);break;default:case"medium":setDefaultCurveSegments(50);break;case"low":setDefaultCurveSegments(10)}else!isNaN(D)&&D>1&&setDefaultCurveSegments(D)}function inBrowser(){return"undefined"!=typeof navigator}function installPlugin(D,F){"expressions"===D&&setExpressionsPlugin(F)}function getFactory(D){switch(D){case"propertyFactory":return PropertyFactory;case"shapePropertyFactory":return ShapePropertyFactory;case"matrix":return Matrix;default:return null}}function checkReady(){"complete"===document.readyState&&(clearInterval(readyStateCheckInterval),searchAnimations())}function getQueryVariable(D){for(var F=queryString.split("&"),O=0;O<F.length;O+=1){var U=F[O].split("=");if(decodeURIComponent(U[0])==D)return decodeURIComponent(U[1])}return null}lottie.play=animationManager.play,lottie.pause=animationManager.pause,lottie.setLocationHref=setLocation,lottie.togglePause=animationManager.togglePause,lottie.setSpeed=animationManager.setSpeed,lottie.setDirection=animationManager.setDirection,lottie.stop=animationManager.stop,lottie.searchAnimations=searchAnimations,lottie.registerAnimation=animationManager.registerAnimation,lottie.loadAnimation=loadAnimation,lottie.setSubframeRendering=setSubframeRendering,lottie.resize=animationManager.resize,lottie.goToAndStop=animationManager.goToAndStop,lottie.destroy=animationManager.destroy,lottie.setQuality=setQuality,lottie.inBrowser=inBrowser,lottie.installPlugin=installPlugin,lottie.freeze=animationManager.freeze,lottie.unfreeze=animationManager.unfreeze,lottie.setVolume=animationManager.setVolume,lottie.mute=animationManager.mute,lottie.unmute=animationManager.unmute,lottie.getRegisteredAnimations=animationManager.getRegisteredAnimations,lottie.useWebWorker=setWebWorker,lottie.setIDPrefix=setPrefix,lottie.__getFactory=getFactory,lottie.version="5.12.2";var queryString="",scripts=document.getElementsByTagName("script"),index=scripts.length-1,myScript=scripts[index]||{src:""};queryString=myScript.src?myScript.src.replace(/^[^\?]+\??/,""):"",getQueryVariable("renderer");var readyStateCheckInterval=setInterval(checkReady,100);try{"object"!==_typeof$3(exports)&&(window.bodymovin=lottie)}catch(err){}var ShapeModifiers=function(){var D={},F={};function O(D,O){F[D]||(F[D]=O)}function U(D,O,U){return new F[D](O,U)}return D.registerModifier=O,D.getModifier=U,D}();function ShapeModifier(){}function TrimModifier(){}function PuckerAndBloatModifier(){}ShapeModifier.prototype.initModifierProperties=function(){},ShapeModifier.prototype.addShapeToModifier=function(){},ShapeModifier.prototype.addShape=function(D){if(!this.closed){D.sh.container.addDynamicProperty(D.sh);var F={shape:D.sh,data:D,localShapeCollection:shapeCollectionPool.newShapeCollection()};this.shapes.push(F),this.addShapeToModifier(F),this._isAnimated&&D.setAsAnimated()}},ShapeModifier.prototype.init=function(D,F){this.shapes=[],this.elem=D,this.initDynamicPropertyContainer(D),this.initModifierProperties(D,F),this.frameId=initialDefaultFrame,this.closed=!1,this.k=!1,this.dynamicProperties.length?this.k=!0:this.getValue(!0)},ShapeModifier.prototype.processKeys=function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties())},extendPrototype([DynamicPropertyContainer],ShapeModifier),extendPrototype([ShapeModifier],TrimModifier),TrimModifier.prototype.initModifierProperties=function(D,F){this.s=PropertyFactory.getProp(D,F.s,0,.01,this),this.e=PropertyFactory.getProp(D,F.e,0,.01,this),this.o=PropertyFactory.getProp(D,F.o,0,0,this),this.sValue=0,this.eValue=0,this.getValue=this.processKeys,this.m=F.m,this._isAnimated=!!this.s.effectsSequence.length||!!this.e.effectsSequence.length||!!this.o.effectsSequence.length},TrimModifier.prototype.addShapeToModifier=function(D){D.pathsData=[]},TrimModifier.prototype.calculateShapeEdges=function(D,F,O,U,G){var W,Y,X=[];F<=1?X.push({s:D,e:F}):D>=1?X.push({s:D-1,e:F-1}):(X.push({s:D,e:1}),X.push({s:0,e:F-1}));var J=[],K=X.length;for(W=0;W<K;W+=1)(Y=X[W]).e*G<U||Y.s*G>U+O||J.push([Y.s*G<=U?0:(Y.s*G-U)/O,Y.e*G>=U+O?1:(Y.e*G-U)/O]);return J.length||J.push([0,0]),J},TrimModifier.prototype.releasePathsData=function(D){var F,O=D.length;for(F=0;F<O;F+=1)segmentsLengthPool.release(D[F]);return D.length=0,D},TrimModifier.prototype.processShapes=function(D){if(this._mdf||D){var F=this.o.v%360/360;if(F<0&&(F+=1),(W=this.s.v>1?1+F:this.s.v<0?0+F:this.s.v+F)>(Y=this.e.v>1?1+F:this.e.v<0?0+F:this.e.v+F)){var O=W;W=Y,Y=O}W=1e-4*Math.round(1e4*W),Y=1e-4*Math.round(1e4*Y),this.sValue=W,this.eValue=Y}else W=this.sValue,Y=this.eValue;var U=this.shapes.length,G=0;if(Y===W)for(J=0;J<U;J+=1)this.shapes[J].localShapeCollection.releaseShapes(),this.shapes[J].shape._mdf=!0,this.shapes[J].shape.paths=this.shapes[J].localShapeCollection,this._mdf&&(this.shapes[J].pathsData.length=0);else if(1===Y&&0===W||0===Y&&1===W){if(this._mdf)for(J=0;J<U;J+=1)this.shapes[J].pathsData.length=0,this.shapes[J].shape._mdf=!0}else{var W,Y,X,J,K,Z,Q,tt,te,tr,ts,tn,ta=[];for(J=0;J<U;J+=1)if((tr=this.shapes[J]).shape._mdf||this._mdf||D||2===this.m){if(Z=(X=tr.shape.paths)._length,te=0,!tr.shape._mdf&&tr.pathsData.length)te=tr.totalShapeLength;else{for(K=0,Q=this.releasePathsData(tr.pathsData);K<Z;K+=1)tt=bez.getSegmentsLength(X.shapes[K]),Q.push(tt),te+=tt.totalLength;tr.totalShapeLength=te,tr.pathsData=Q}G+=te,tr.shape._mdf=!0}else tr.shape.paths=tr.localShapeCollection;var th=W,tp=Y,tf=0;for(J=U-1;J>=0;J-=1)if((tr=this.shapes[J]).shape._mdf){for((ts=tr.localShapeCollection).releaseShapes(),2===this.m&&U>1?(tn=this.calculateShapeEdges(W,Y,tr.totalShapeLength,tf,G),tf+=tr.totalShapeLength):tn=[[th,tp]],Z=tn.length,K=0;K<Z;K+=1){th=tn[K][0],tp=tn[K][1],ta.length=0,tp<=1?ta.push({s:tr.totalShapeLength*th,e:tr.totalShapeLength*tp}):th>=1?ta.push({s:tr.totalShapeLength*(th-1),e:tr.totalShapeLength*(tp-1)}):(ta.push({s:tr.totalShapeLength*th,e:tr.totalShapeLength}),ta.push({s:0,e:tr.totalShapeLength*(tp-1)}));var tu=this.addShapes(tr,ta[0]);if(ta[0].s!==ta[0].e){if(ta.length>1){if(tr.shape.paths.shapes[tr.shape.paths._length-1].c){var tc=tu.pop();this.addPaths(tu,ts),tu=this.addShapes(tr,ta[1],tc)}else this.addPaths(tu,ts),tu=this.addShapes(tr,ta[1])}this.addPaths(tu,ts)}}tr.shape.paths=ts}}},TrimModifier.prototype.addPaths=function(D,F){var O,U=D.length;for(O=0;O<U;O+=1)F.addShape(D[O])},TrimModifier.prototype.addSegment=function(D,F,O,U,G,W,Y){G.setXYAt(F[0],F[1],"o",W),G.setXYAt(O[0],O[1],"i",W+1),Y&&G.setXYAt(D[0],D[1],"v",W),G.setXYAt(U[0],U[1],"v",W+1)},TrimModifier.prototype.addSegmentFromArray=function(D,F,O,U){F.setXYAt(D[1],D[5],"o",O),F.setXYAt(D[2],D[6],"i",O+1),U&&F.setXYAt(D[0],D[4],"v",O),F.setXYAt(D[3],D[7],"v",O+1)},TrimModifier.prototype.addShapes=function(D,F,O){var U,G,W,Y,X,J,K,Z,Q=D.pathsData,tt=D.shape.paths.shapes,te=D.shape.paths._length,tr=0,ts=[],tn=!0;for(O?(X=O._length,Z=O._length):(O=shapePool.newElement(),X=0,Z=0),ts.push(O),U=0;U<te;U+=1){for(G=1,J=Q[U].lengths,O.c=tt[U].c,W=tt[U].c?J.length:J.length+1;G<W;G+=1)if(tr+(Y=J[G-1]).addedLength<F.s)tr+=Y.addedLength,O.c=!1;else if(tr>F.e){O.c=!1;break}else F.s<=tr&&F.e>=tr+Y.addedLength?(this.addSegment(tt[U].v[G-1],tt[U].o[G-1],tt[U].i[G],tt[U].v[G],O,X,tn),tn=!1):(K=bez.getNewSegment(tt[U].v[G-1],tt[U].v[G],tt[U].o[G-1],tt[U].i[G],(F.s-tr)/Y.addedLength,(F.e-tr)/Y.addedLength,J[G-1]),this.addSegmentFromArray(K,O,X,tn),tn=!1,O.c=!1),tr+=Y.addedLength,X+=1;if(tt[U].c&&J.length){if(Y=J[G-1],tr<=F.e){var ta=J[G-1].addedLength;F.s<=tr&&F.e>=tr+ta?(this.addSegment(tt[U].v[G-1],tt[U].o[G-1],tt[U].i[0],tt[U].v[0],O,X,tn),tn=!1):(K=bez.getNewSegment(tt[U].v[G-1],tt[U].v[0],tt[U].o[G-1],tt[U].i[0],(F.s-tr)/ta,(F.e-tr)/ta,J[G-1]),this.addSegmentFromArray(K,O,X,tn),tn=!1,O.c=!1)}else O.c=!1;tr+=Y.addedLength,X+=1}if(O._length&&(O.setXYAt(O.v[Z][0],O.v[Z][1],"i",Z),O.setXYAt(O.v[O._length-1][0],O.v[O._length-1][1],"o",O._length-1)),tr>F.e)break;U<te-1&&(O=shapePool.newElement(),tn=!0,ts.push(O),X=0)}return ts},extendPrototype([ShapeModifier],PuckerAndBloatModifier),PuckerAndBloatModifier.prototype.initModifierProperties=function(D,F){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(D,F.a,0,null,this),this._isAnimated=!!this.amount.effectsSequence.length},PuckerAndBloatModifier.prototype.processPath=function(D,F){var O,U,G,W,Y,X,J=F/100,K=[0,0],Z=D._length,Q=0;for(Q=0;Q<Z;Q+=1)K[0]+=D.v[Q][0],K[1]+=D.v[Q][1];K[0]/=Z,K[1]/=Z;var tt=shapePool.newElement();for(Q=0,tt.c=D.c;Q<Z;Q+=1)O=D.v[Q][0]+(K[0]-D.v[Q][0])*J,U=D.v[Q][1]+(K[1]-D.v[Q][1])*J,G=D.o[Q][0]+-((K[0]-D.o[Q][0])*J),W=D.o[Q][1]+-((K[1]-D.o[Q][1])*J),Y=D.i[Q][0]+-((K[0]-D.i[Q][0])*J),X=D.i[Q][1]+-((K[1]-D.i[Q][1])*J),tt.setTripleAt(O,U,G,W,Y,X,Q);return tt},PuckerAndBloatModifier.prototype.processShapes=function(D){var F,O,U,G,W,Y,X=this.shapes.length,J=this.amount.v;if(0!==J)for(O=0;O<X;O+=1){if(Y=(W=this.shapes[O]).localShapeCollection,!(!W.shape._mdf&&!this._mdf&&!D))for(Y.releaseShapes(),W.shape._mdf=!0,F=W.shape.paths.shapes,G=W.shape.paths._length,U=0;U<G;U+=1)Y.addShape(this.processPath(F[U],J));W.shape.paths=W.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var TransformPropertyFactory=function(){var D=[0,0];function F(D){var F=this._mdf;this.iterateDynamicProperties(),this._mdf=this._mdf||F,this.a&&D.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.s&&D.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&D.skewFromAxis(-this.sk.v,this.sa.v),this.r?D.rotate(-this.r.v):D.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.data.p.s?this.data.p.z?D.translate(this.px.v,this.py.v,-this.pz.v):D.translate(this.px.v,this.py.v,0):D.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}function O(F){if(this.elem.globalData.frameId!==this.frameId){if(this._isDirty&&(this.precalculateMatrix(),this._isDirty=!1),this.iterateDynamicProperties(),this._mdf||F){var O;if(this.v.cloneFromProps(this.pre.props),this.appliedTransformations<1&&this.v.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations<2&&this.v.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&this.appliedTransformations<3&&this.v.skewFromAxis(-this.sk.v,this.sa.v),this.r&&this.appliedTransformations<4?this.v.rotate(-this.r.v):!this.r&&this.appliedTransformations<4&&this.v.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.autoOriented){if(O=this.elem.globalData.frameRate,this.p&&this.p.keyframes&&this.p.getValueAtTime)this.p._caching.lastFrame+this.p.offsetTime<=this.p.keyframes[0].t?(U=this.p.getValueAtTime((this.p.keyframes[0].t+.01)/O,0),G=this.p.getValueAtTime(this.p.keyframes[0].t/O,0)):this.p._caching.lastFrame+this.p.offsetTime>=this.p.keyframes[this.p.keyframes.length-1].t?(U=this.p.getValueAtTime(this.p.keyframes[this.p.keyframes.length-1].t/O,0),G=this.p.getValueAtTime((this.p.keyframes[this.p.keyframes.length-1].t-.05)/O,0)):(U=this.p.pv,G=this.p.getValueAtTime((this.p._caching.lastFrame+this.p.offsetTime-.01)/O,this.p.offsetTime));else if(this.px&&this.px.keyframes&&this.py.keyframes&&this.px.getValueAtTime&&this.py.getValueAtTime){U=[],G=[];var U,G,W=this.px,Y=this.py;W._caching.lastFrame+W.offsetTime<=W.keyframes[0].t?(U[0]=W.getValueAtTime((W.keyframes[0].t+.01)/O,0),U[1]=Y.getValueAtTime((Y.keyframes[0].t+.01)/O,0),G[0]=W.getValueAtTime(W.keyframes[0].t/O,0),G[1]=Y.getValueAtTime(Y.keyframes[0].t/O,0)):W._caching.lastFrame+W.offsetTime>=W.keyframes[W.keyframes.length-1].t?(U[0]=W.getValueAtTime(W.keyframes[W.keyframes.length-1].t/O,0),U[1]=Y.getValueAtTime(Y.keyframes[Y.keyframes.length-1].t/O,0),G[0]=W.getValueAtTime((W.keyframes[W.keyframes.length-1].t-.01)/O,0),G[1]=Y.getValueAtTime((Y.keyframes[Y.keyframes.length-1].t-.01)/O,0)):(U=[W.pv,Y.pv],G[0]=W.getValueAtTime((W._caching.lastFrame+W.offsetTime-.01)/O,W.offsetTime),G[1]=Y.getValueAtTime((Y._caching.lastFrame+Y.offsetTime-.01)/O,Y.offsetTime))}else U=G=D;this.v.rotate(-Math.atan2(U[1]-G[1],U[0]-G[0]))}this.data.p&&this.data.p.s?this.data.p.z?this.v.translate(this.px.v,this.py.v,-this.pz.v):this.v.translate(this.px.v,this.py.v,0):this.v.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}this.frameId=this.elem.globalData.frameId}}function U(){if(this.appliedTransformations=0,this.pre.reset(),!this.a.effectsSequence.length&&(this.pre.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations=1,!this.s.effectsSequence.length)){if(this.pre.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.appliedTransformations=2,this.sk){if(this.sk.effectsSequence.length||this.sa.effectsSequence.length)return;this.pre.skewFromAxis(-this.sk.v,this.sa.v),this.appliedTransformations=3}this.r?this.r.effectsSequence.length||(this.pre.rotate(-this.r.v),this.appliedTransformations=4):this.rz.effectsSequence.length||this.ry.effectsSequence.length||this.rx.effectsSequence.length||this.or.effectsSequence.length||(this.pre.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.appliedTransformations=4)}}function G(){}function W(D){this._addDynamicProperty(D),this.elem.addDynamicProperty(D),this._isDirty=!0}function Y(D,F,O){if(this.elem=D,this.frameId=-1,this.propType="transform",this.data=F,this.v=new Matrix,this.pre=new Matrix,this.appliedTransformations=0,this.initDynamicPropertyContainer(O||D),F.p&&F.p.s?(this.px=PropertyFactory.getProp(D,F.p.x,0,0,this),this.py=PropertyFactory.getProp(D,F.p.y,0,0,this),F.p.z&&(this.pz=PropertyFactory.getProp(D,F.p.z,0,0,this))):this.p=PropertyFactory.getProp(D,F.p||{k:[0,0,0]},1,0,this),F.rx){if(this.rx=PropertyFactory.getProp(D,F.rx,0,degToRads,this),this.ry=PropertyFactory.getProp(D,F.ry,0,degToRads,this),this.rz=PropertyFactory.getProp(D,F.rz,0,degToRads,this),F.or.k[0].ti){var U,G=F.or.k.length;for(U=0;U<G;U+=1)F.or.k[U].to=null,F.or.k[U].ti=null}this.or=PropertyFactory.getProp(D,F.or,1,degToRads,this),this.or.sh=!0}else this.r=PropertyFactory.getProp(D,F.r||{k:0},0,degToRads,this);F.sk&&(this.sk=PropertyFactory.getProp(D,F.sk,0,degToRads,this),this.sa=PropertyFactory.getProp(D,F.sa,0,degToRads,this)),this.a=PropertyFactory.getProp(D,F.a||{k:[0,0,0]},1,0,this),this.s=PropertyFactory.getProp(D,F.s||{k:[100,100,100]},1,.01,this),F.o?this.o=PropertyFactory.getProp(D,F.o,0,.01,D):this.o={_mdf:!1,v:1},this._isDirty=!0,this.dynamicProperties.length||this.getValue(!0)}return Y.prototype={applyToMatrix:F,getValue:O,precalculateMatrix:U,autoOrient:G},extendPrototype([DynamicPropertyContainer],Y),Y.prototype.addDynamicProperty=W,Y.prototype._addDynamicProperty=DynamicPropertyContainer.prototype.addDynamicProperty,{getTransformProperty:function(D,F,O){return new Y(D,F,O)}}}();function RepeaterModifier(){}function RoundCornersModifier(){}function floatEqual(D,F){return 1e5*Math.abs(D-F)<=Math.min(Math.abs(D),Math.abs(F))}function floatZero(D){return 1e-5>=Math.abs(D)}function lerp(D,F,O){return D*(1-O)+F*O}function lerpPoint(D,F,O){return[lerp(D[0],F[0],O),lerp(D[1],F[1],O)]}function quadRoots(D,F,O){if(0===D)return[];var U=F*F-4*D*O;if(U<0)return[];var G=-F/(2*D);if(0===U)return[G];var W=Math.sqrt(U)/(2*D);return[G-W,G+W]}function polynomialCoefficients(D,F,O,U){return[-D+3*F-3*O+U,3*D-6*F+3*O,-3*D+3*F,D]}function singlePoint(D){return new PolynomialBezier(D,D,D,D,!1)}function PolynomialBezier(D,F,O,U,G){G&&pointEqual(D,F)&&(F=lerpPoint(D,U,1/3)),G&&pointEqual(O,U)&&(O=lerpPoint(D,U,2/3));var W=polynomialCoefficients(D[0],F[0],O[0],U[0]),Y=polynomialCoefficients(D[1],F[1],O[1],U[1]);this.a=[W[0],Y[0]],this.b=[W[1],Y[1]],this.c=[W[2],Y[2]],this.d=[W[3],Y[3]],this.points=[D,F,O,U]}function extrema(D,F){var O=D.points[0][F],U=D.points[D.points.length-1][F];if(O>U){var G=U;U=O,O=G}for(var W=quadRoots(3*D.a[F],2*D.b[F],D.c[F]),Y=0;Y<W.length;Y+=1)if(W[Y]>0&&W[Y]<1){var X=D.point(W[Y])[F];X<O?O=X:X>U&&(U=X)}return{min:O,max:U}}function intersectData(D,F,O){var U=D.boundingBox();return{cx:U.cx,cy:U.cy,width:U.width,height:U.height,bez:D,t:(F+O)/2,t1:F,t2:O}}function splitData(D){var F=D.bez.split(.5);return[intersectData(F[0],D.t1,D.t),intersectData(F[1],D.t,D.t2)]}function boxIntersect(D,F){return 2*Math.abs(D.cx-F.cx)<D.width+F.width&&2*Math.abs(D.cy-F.cy)<D.height+F.height}function intersectsImpl(D,F,O,U,G,W){if(boxIntersect(D,F)){if(O>=W||D.width<=U&&D.height<=U&&F.width<=U&&F.height<=U){G.push([D.t,F.t]);return}var Y=splitData(D),X=splitData(F);intersectsImpl(Y[0],X[0],O+1,U,G,W),intersectsImpl(Y[0],X[1],O+1,U,G,W),intersectsImpl(Y[1],X[0],O+1,U,G,W),intersectsImpl(Y[1],X[1],O+1,U,G,W)}}function crossProduct(D,F){return[D[1]*F[2]-D[2]*F[1],D[2]*F[0]-D[0]*F[2],D[0]*F[1]-D[1]*F[0]]}function lineIntersection(D,F,O,U){var G=[D[0],D[1],1],W=[F[0],F[1],1],Y=[O[0],O[1],1],X=[U[0],U[1],1],J=crossProduct(crossProduct(G,W),crossProduct(Y,X));return floatZero(J[2])?null:[J[0]/J[2],J[1]/J[2]]}function polarOffset(D,F,O){return[D[0]+Math.cos(F)*O,D[1]-Math.sin(F)*O]}function pointDistance(D,F){return Math.hypot(D[0]-F[0],D[1]-F[1])}function pointEqual(D,F){return floatEqual(D[0],F[0])&&floatEqual(D[1],F[1])}function ZigZagModifier(){}function setPoint(D,F,O,U,G,W,Y){var X=O-Math.PI/2,J=O+Math.PI/2,K=F[0]+Math.cos(O)*U*G,Z=F[1]-Math.sin(O)*U*G;D.setTripleAt(K,Z,K+Math.cos(X)*W,Z-Math.sin(X)*W,K+Math.cos(J)*Y,Z-Math.sin(J)*Y,D.length())}function getPerpendicularVector(D,F){var O=[F[0]-D[0],F[1]-D[1]],U=-(.5*Math.PI);return[Math.cos(U)*O[0]-Math.sin(U)*O[1],Math.sin(U)*O[0]+Math.cos(U)*O[1]]}function getProjectingAngle(D,F){var O=0===F?D.length()-1:F-1,U=(F+1)%D.length(),G=getPerpendicularVector(D.v[O],D.v[U]);return Math.atan2(0,1)-Math.atan2(G[1],G[0])}function zigZagCorner(D,F,O,U,G,W,Y){var X=getProjectingAngle(F,O),J=F.v[O%F._length],K=F.v[0===O?F._length-1:O-1],Z=F.v[(O+1)%F._length],Q=2===W?Math.sqrt(Math.pow(J[0]-K[0],2)+Math.pow(J[1]-K[1],2)):0,tt=2===W?Math.sqrt(Math.pow(J[0]-Z[0],2)+Math.pow(J[1]-Z[1],2)):0;setPoint(D,F.v[O%F._length],X,Y,U,tt/((G+1)*2),Q/((G+1)*2))}function zigZagSegment(D,F,O,U,G,W){for(var Y=0;Y<U;Y+=1){var X=(Y+1)/(U+1),J=2===G?Math.sqrt(Math.pow(F.points[3][0]-F.points[0][0],2)+Math.pow(F.points[3][1]-F.points[0][1],2)):0,K=F.normalAngle(X);setPoint(D,F.point(X),K,W,O,J/((U+1)*2),J/((U+1)*2)),W=-W}return W}function linearOffset(D,F,O){var U=Math.atan2(F[0]-D[0],F[1]-D[1]);return[polarOffset(D,U,O),polarOffset(F,U,O)]}function offsetSegment(D,F){O=(J=linearOffset(D.points[0],D.points[1],F))[0],U=J[1],G=(J=linearOffset(D.points[1],D.points[2],F))[0],W=J[1],Y=(J=linearOffset(D.points[2],D.points[3],F))[0],X=J[1];var O,U,G,W,Y,X,J,K=lineIntersection(O,U,G,W);null===K&&(K=U);var Z=lineIntersection(Y,X,G,W);return null===Z&&(Z=Y),new PolynomialBezier(O,K,Z,X)}function joinLines(D,F,O,U,G){var W=F.points[3],Y=O.points[0];if(3===U||pointEqual(W,Y))return W;if(2===U){var X=-F.tangentAngle(1),J=-O.tangentAngle(0)+Math.PI,K=lineIntersection(W,polarOffset(W,X+Math.PI/2,100),Y,polarOffset(Y,X+Math.PI/2,100)),Z=K?pointDistance(K,W):pointDistance(W,Y)/2,Q=polarOffset(W,X,2*Z*roundCorner);return D.setXYAt(Q[0],Q[1],"o",D.length()-1),Q=polarOffset(Y,J,2*Z*roundCorner),D.setTripleAt(Y[0],Y[1],Y[0],Y[1],Q[0],Q[1],D.length()),Y}var tt=pointEqual(W,F.points[2])?F.points[0]:F.points[2],te=pointEqual(Y,O.points[1])?O.points[3]:O.points[1],tr=lineIntersection(tt,W,Y,te);return tr&&pointDistance(tr,W)<G?(D.setTripleAt(tr[0],tr[1],tr[0],tr[1],tr[0],tr[1],D.length()),tr):W}function getIntersection(D,F){var O=D.intersections(F);return(O.length&&floatEqual(O[0][0],1)&&O.shift(),O.length)?O[0]:null}function pruneSegmentIntersection(D,F){var O=D.slice(),U=F.slice(),G=getIntersection(D[D.length-1],F[0]);return(G&&(O[D.length-1]=D[D.length-1].split(G[0])[0],U[0]=F[0].split(G[1])[1]),D.length>1&&F.length>1&&(G=getIntersection(D[0],F[F.length-1])))?[[D[0].split(G[0])[0]],[F[F.length-1].split(G[1])[1]]]:[O,U]}function pruneIntersections(D){for(var F,O=1;O<D.length;O+=1)F=pruneSegmentIntersection(D[O-1],D[O]),D[O-1]=F[0],D[O]=F[1];return D.length>1&&(F=pruneSegmentIntersection(D[D.length-1],D[0]),D[D.length-1]=F[0],D[0]=F[1]),D}function offsetSegmentSplit(D,F){var O,U,G,W,Y=D.inflectionPoints();if(0===Y.length)return[offsetSegment(D,F)];if(1===Y.length||floatEqual(Y[1],1))return O=(G=D.split(Y[0]))[0],U=G[1],[offsetSegment(O,F),offsetSegment(U,F)];O=(G=D.split(Y[0]))[0];var X=(Y[1]-Y[0])/(1-Y[0]);return W=(G=G[1].split(X))[0],U=G[1],[offsetSegment(O,F),offsetSegment(W,F),offsetSegment(U,F)]}function OffsetPathModifier(){}function getFontProperties(D){for(var F=D.fStyle?D.fStyle.split(" "):[],O="normal",U="normal",G=F.length,W=0;W<G;W+=1)switch(F[W].toLowerCase()){case"italic":U="italic";break;case"bold":O="700";break;case"black":O="900";break;case"medium":O="500";break;case"regular":case"normal":O="400";break;case"light":case"thin":O="200"}return{style:U,weight:D.fWeight||O}}extendPrototype([ShapeModifier],RepeaterModifier),RepeaterModifier.prototype.initModifierProperties=function(D,F){this.getValue=this.processKeys,this.c=PropertyFactory.getProp(D,F.c,0,null,this),this.o=PropertyFactory.getProp(D,F.o,0,null,this),this.tr=TransformPropertyFactory.getTransformProperty(D,F.tr,this),this.so=PropertyFactory.getProp(D,F.tr.so,0,.01,this),this.eo=PropertyFactory.getProp(D,F.tr.eo,0,.01,this),this.data=F,this.dynamicProperties.length||this.getValue(!0),this._isAnimated=!!this.dynamicProperties.length,this.pMatrix=new Matrix,this.rMatrix=new Matrix,this.sMatrix=new Matrix,this.tMatrix=new Matrix,this.matrix=new Matrix},RepeaterModifier.prototype.applyTransforms=function(D,F,O,U,G,W){var Y=W?-1:1,X=U.s.v[0]+(1-U.s.v[0])*(1-G),J=U.s.v[1]+(1-U.s.v[1])*(1-G);D.translate(U.p.v[0]*Y*G,U.p.v[1]*Y*G,U.p.v[2]),F.translate(-U.a.v[0],-U.a.v[1],U.a.v[2]),F.rotate(-U.r.v*Y*G),F.translate(U.a.v[0],U.a.v[1],U.a.v[2]),O.translate(-U.a.v[0],-U.a.v[1],U.a.v[2]),O.scale(W?1/X:X,W?1/J:J),O.translate(U.a.v[0],U.a.v[1],U.a.v[2])},RepeaterModifier.prototype.init=function(D,F,O,U){for(this.elem=D,this.arr=F,this.pos=O,this.elemsData=U,this._currentCopies=0,this._elements=[],this._groups=[],this.frameId=-1,this.initDynamicPropertyContainer(D),this.initModifierProperties(D,F[O]);O>0;)O-=1,this._elements.unshift(F[O]);this.dynamicProperties.length?this.k=!0:this.getValue(!0)},RepeaterModifier.prototype.resetElements=function(D){var F,O=D.length;for(F=0;F<O;F+=1)D[F]._processed=!1,"gr"===D[F].ty&&this.resetElements(D[F].it)},RepeaterModifier.prototype.cloneElements=function(D){var F=JSON.parse(JSON.stringify(D));return this.resetElements(F),F},RepeaterModifier.prototype.changeGroupRender=function(D,F){var O,U=D.length;for(O=0;O<U;O+=1)D[O]._render=F,"gr"===D[O].ty&&this.changeGroupRender(D[O].it,F)},RepeaterModifier.prototype.processShapes=function(D){var F=!1;if(this._mdf||D){var O,U,G,W,Y,X,J,K,Z=Math.ceil(this.c.v);if(this._groups.length<Z){for(;this._groups.length<Z;){var Q={it:this.cloneElements(this._elements),ty:"gr"};Q.it.push({a:{a:0,ix:1,k:[0,0]},nm:"Transform",o:{a:0,ix:7,k:100},p:{a:0,ix:2,k:[0,0]},r:{a:1,ix:6,k:[{s:0,e:0,t:0},{s:0,e:0,t:1}]},s:{a:0,ix:3,k:[100,100]},sa:{a:0,ix:5,k:0},sk:{a:0,ix:4,k:0},ty:"tr"}),this.arr.splice(0,0,Q),this._groups.splice(0,0,Q),this._currentCopies+=1}this.elem.reloadShapes(),F=!0}for(G=0,Y=0;G<=this._groups.length-1;G+=1){if(X=Y<Z,this._groups[G]._render=X,this.changeGroupRender(this._groups[G].it,X),!X){var tt=this.elemsData[G].it,te=tt[tt.length-1];0!==te.transform.op.v?(te.transform.op._mdf=!0,te.transform.op.v=0):te.transform.op._mdf=!1}Y+=1}this._currentCopies=Z;var tr=this.o.v,ts=tr%1,tn=tr>0?Math.floor(tr):Math.ceil(tr),ta=this.pMatrix.props,th=this.rMatrix.props,tp=this.sMatrix.props;this.pMatrix.reset(),this.rMatrix.reset(),this.sMatrix.reset(),this.tMatrix.reset(),this.matrix.reset();var tf=0;if(tr>0){for(;tf<tn;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),tf+=1;ts&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,ts,!1),tf+=ts)}else if(tr<0){for(;tf>tn;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!0),tf-=1;ts&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,-ts,!0),tf-=ts)}for(G=1===this.data.m?0:this._currentCopies-1,W=1===this.data.m?1:-1,Y=this._currentCopies;Y;){if(K=(U=(O=this.elemsData[G].it)[O.length-1].transform.mProps.v.props).length,O[O.length-1].transform.mProps._mdf=!0,O[O.length-1].transform.op._mdf=!0,O[O.length-1].transform.op.v=1===this._currentCopies?this.so.v:this.so.v+(this.eo.v-this.so.v)*(G/(this._currentCopies-1)),0!==tf){for((0!==G&&1===W||G!==this._currentCopies-1&&-1===W)&&this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),this.matrix.transform(th[0],th[1],th[2],th[3],th[4],th[5],th[6],th[7],th[8],th[9],th[10],th[11],th[12],th[13],th[14],th[15]),this.matrix.transform(tp[0],tp[1],tp[2],tp[3],tp[4],tp[5],tp[6],tp[7],tp[8],tp[9],tp[10],tp[11],tp[12],tp[13],tp[14],tp[15]),this.matrix.transform(ta[0],ta[1],ta[2],ta[3],ta[4],ta[5],ta[6],ta[7],ta[8],ta[9],ta[10],ta[11],ta[12],ta[13],ta[14],ta[15]),J=0;J<K;J+=1)U[J]=this.matrix.props[J];this.matrix.reset()}else for(this.matrix.reset(),J=0;J<K;J+=1)U[J]=this.matrix.props[J];tf+=1,Y-=1,G+=W}}else for(Y=this._currentCopies,G=0,W=1;Y;)U=(O=this.elemsData[G].it)[O.length-1].transform.mProps.v.props,O[O.length-1].transform.mProps._mdf=!1,O[O.length-1].transform.op._mdf=!1,Y-=1,G+=W;return F},RepeaterModifier.prototype.addShape=function(){},extendPrototype([ShapeModifier],RoundCornersModifier),RoundCornersModifier.prototype.initModifierProperties=function(D,F){this.getValue=this.processKeys,this.rd=PropertyFactory.getProp(D,F.r,0,null,this),this._isAnimated=!!this.rd.effectsSequence.length},RoundCornersModifier.prototype.processPath=function(D,F){var O,U,G,W,Y,X,J,K,Z,Q,tt,te,tr,ts=shapePool.newElement();ts.c=D.c;var tn=D._length,ta=0;for(O=0;O<tn;O+=1)U=D.v[O],W=D.o[O],G=D.i[O],U[0]===W[0]&&U[1]===W[1]&&U[0]===G[0]&&U[1]===G[1]?0!==O&&O!==tn-1||D.c?(Y=0===O?D.v[tn-1]:D.v[O-1],J=(X=Math.sqrt(Math.pow(U[0]-Y[0],2)+Math.pow(U[1]-Y[1],2)))?Math.min(X/2,F)/X:0,K=te=U[0]+(Y[0]-U[0])*J,Z=tr=U[1]-(U[1]-Y[1])*J,Q=K-(K-U[0])*roundCorner,tt=Z-(Z-U[1])*roundCorner,ts.setTripleAt(K,Z,Q,tt,te,tr,ta),ta+=1,Y=O===tn-1?D.v[0]:D.v[O+1],J=(X=Math.sqrt(Math.pow(U[0]-Y[0],2)+Math.pow(U[1]-Y[1],2)))?Math.min(X/2,F)/X:0,K=Q=U[0]+(Y[0]-U[0])*J,Z=tt=U[1]+(Y[1]-U[1])*J,te=K-(K-U[0])*roundCorner,tr=Z-(Z-U[1])*roundCorner,ts.setTripleAt(K,Z,Q,tt,te,tr,ta),ta+=1):(ts.setTripleAt(U[0],U[1],W[0],W[1],G[0],G[1],ta),ta+=1):(ts.setTripleAt(D.v[O][0],D.v[O][1],D.o[O][0],D.o[O][1],D.i[O][0],D.i[O][1],ta),ta+=1);return ts},RoundCornersModifier.prototype.processShapes=function(D){var F,O,U,G,W,Y,X=this.shapes.length,J=this.rd.v;if(0!==J)for(O=0;O<X;O+=1){if(Y=(W=this.shapes[O]).localShapeCollection,!(!W.shape._mdf&&!this._mdf&&!D))for(Y.releaseShapes(),W.shape._mdf=!0,F=W.shape.paths.shapes,G=W.shape.paths._length,U=0;U<G;U+=1)Y.addShape(this.processPath(F[U],J));W.shape.paths=W.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},PolynomialBezier.prototype.point=function(D){return[((this.a[0]*D+this.b[0])*D+this.c[0])*D+this.d[0],((this.a[1]*D+this.b[1])*D+this.c[1])*D+this.d[1]]},PolynomialBezier.prototype.derivative=function(D){return[(3*D*this.a[0]+2*this.b[0])*D+this.c[0],(3*D*this.a[1]+2*this.b[1])*D+this.c[1]]},PolynomialBezier.prototype.tangentAngle=function(D){var F=this.derivative(D);return Math.atan2(F[1],F[0])},PolynomialBezier.prototype.normalAngle=function(D){var F=this.derivative(D);return Math.atan2(F[0],F[1])},PolynomialBezier.prototype.inflectionPoints=function(){var D=this.a[1]*this.b[0]-this.a[0]*this.b[1];if(floatZero(D))return[];var F=-.5*(this.a[1]*this.c[0]-this.a[0]*this.c[1])/D,O=F*F-1/3*(this.b[1]*this.c[0]-this.b[0]*this.c[1])/D;if(O<0)return[];var U=Math.sqrt(O);return floatZero(U)?U>0&&U<1?[F]:[]:[F-U,F+U].filter(function(D){return D>0&&D<1})},PolynomialBezier.prototype.split=function(D){if(D<=0)return[singlePoint(this.points[0]),this];if(D>=1)return[this,singlePoint(this.points[this.points.length-1])];var F=lerpPoint(this.points[0],this.points[1],D),O=lerpPoint(this.points[1],this.points[2],D),U=lerpPoint(this.points[2],this.points[3],D),G=lerpPoint(F,O,D),W=lerpPoint(O,U,D),Y=lerpPoint(G,W,D);return[new PolynomialBezier(this.points[0],F,G,Y,!0),new PolynomialBezier(Y,W,U,this.points[3],!0)]},PolynomialBezier.prototype.bounds=function(){return{x:extrema(this,0),y:extrema(this,1)}},PolynomialBezier.prototype.boundingBox=function(){var D=this.bounds();return{left:D.x.min,right:D.x.max,top:D.y.min,bottom:D.y.max,width:D.x.max-D.x.min,height:D.y.max-D.y.min,cx:(D.x.max+D.x.min)/2,cy:(D.y.max+D.y.min)/2}},PolynomialBezier.prototype.intersections=function(D,F,O){void 0===F&&(F=2),void 0===O&&(O=7);var U=[];return intersectsImpl(intersectData(this,0,1),intersectData(D,0,1),0,F,U,O),U},PolynomialBezier.shapeSegment=function(D,F){var O=(F+1)%D.length();return new PolynomialBezier(D.v[F],D.o[F],D.i[O],D.v[O],!0)},PolynomialBezier.shapeSegmentInverted=function(D,F){var O=(F+1)%D.length();return new PolynomialBezier(D.v[O],D.i[O],D.o[F],D.v[F],!0)},extendPrototype([ShapeModifier],ZigZagModifier),ZigZagModifier.prototype.initModifierProperties=function(D,F){this.getValue=this.processKeys,this.amplitude=PropertyFactory.getProp(D,F.s,0,null,this),this.frequency=PropertyFactory.getProp(D,F.r,0,null,this),this.pointsType=PropertyFactory.getProp(D,F.pt,0,null,this),this._isAnimated=0!==this.amplitude.effectsSequence.length||0!==this.frequency.effectsSequence.length||0!==this.pointsType.effectsSequence.length},ZigZagModifier.prototype.processPath=function(D,F,O,U){var G=D._length,W=shapePool.newElement();if(W.c=D.c,D.c||(G-=1),0===G)return W;var Y=-1,X=PolynomialBezier.shapeSegment(D,0);zigZagCorner(W,D,0,F,O,U,Y);for(var J=0;J<G;J+=1)Y=zigZagSegment(W,X,F,O,U,-Y),X=J!==G-1||D.c?PolynomialBezier.shapeSegment(D,(J+1)%G):null,zigZagCorner(W,D,J+1,F,O,U,Y);return W},ZigZagModifier.prototype.processShapes=function(D){var F,O,U,G,W,Y,X=this.shapes.length,J=this.amplitude.v,K=Math.max(0,Math.round(this.frequency.v)),Z=this.pointsType.v;if(0!==J)for(O=0;O<X;O+=1){if(Y=(W=this.shapes[O]).localShapeCollection,!(!W.shape._mdf&&!this._mdf&&!D))for(Y.releaseShapes(),W.shape._mdf=!0,F=W.shape.paths.shapes,G=W.shape.paths._length,U=0;U<G;U+=1)Y.addShape(this.processPath(F[U],J,K,Z));W.shape.paths=W.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},extendPrototype([ShapeModifier],OffsetPathModifier),OffsetPathModifier.prototype.initModifierProperties=function(D,F){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(D,F.a,0,null,this),this.miterLimit=PropertyFactory.getProp(D,F.ml,0,null,this),this.lineJoin=F.lj,this._isAnimated=0!==this.amount.effectsSequence.length},OffsetPathModifier.prototype.processPath=function(D,F,O,U){var G,W,Y,X=shapePool.newElement();X.c=D.c;var J=D.length();D.c||(J-=1);var K=[];for(G=0;G<J;G+=1)Y=PolynomialBezier.shapeSegment(D,G),K.push(offsetSegmentSplit(Y,F));if(!D.c)for(G=J-1;G>=0;G-=1)Y=PolynomialBezier.shapeSegmentInverted(D,G),K.push(offsetSegmentSplit(Y,F));K=pruneIntersections(K);var Z=null,Q=null;for(G=0;G<K.length;G+=1){var tt=K[G];for(Q&&(Z=joinLines(X,Q,tt[0],O,U)),Q=tt[tt.length-1],W=0;W<tt.length;W+=1)Y=tt[W],Z&&pointEqual(Y.points[0],Z)?X.setXYAt(Y.points[1][0],Y.points[1][1],"o",X.length()-1):X.setTripleAt(Y.points[0][0],Y.points[0][1],Y.points[1][0],Y.points[1][1],Y.points[0][0],Y.points[0][1],X.length()),X.setTripleAt(Y.points[3][0],Y.points[3][1],Y.points[3][0],Y.points[3][1],Y.points[2][0],Y.points[2][1],X.length()),Z=Y.points[3]}return K.length&&joinLines(X,Q,K[0][0],O,U),X},OffsetPathModifier.prototype.processShapes=function(D){var F,O,U,G,W,Y,X=this.shapes.length,J=this.amount.v,K=this.miterLimit.v,Z=this.lineJoin;if(0!==J)for(O=0;O<X;O+=1){if(Y=(W=this.shapes[O]).localShapeCollection,!(!W.shape._mdf&&!this._mdf&&!D))for(Y.releaseShapes(),W.shape._mdf=!0,F=W.shape.paths.shapes,G=W.shape.paths._length,U=0;U<G;U+=1)Y.addShape(this.processPath(F[U],J,Z,K));W.shape.paths=W.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var FontManager=function(){var D=5e3,F={w:0,size:0,shapes:[],data:{shapes:[]}},O=[];O=O.concat([2304,2305,2306,2307,2362,2363,2364,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2387,2388,2389,2390,2391,2402,2403]);var U=127988,G=917631,W=917601,Y=917626,X=65039,J=8205,K=127462,Z=127487,Q=["d83cdffb","d83cdffc","d83cdffd","d83cdffe","d83cdfff"];function tt(D){var F,O=D.split(","),U=O.length,G=[];for(F=0;F<U;F+=1)"sans-serif"!==O[F]&&"monospace"!==O[F]&&G.push(O[F]);return G.join(",")}function te(D,F){var O=createTag("span");O.setAttribute("aria-hidden",!0),O.style.fontFamily=F;var U=createTag("span");U.innerText="giItT1WQy@!-/#",O.style.position="absolute",O.style.left="-10000px",O.style.top="-10000px",O.style.fontSize="300px",O.style.fontVariant="normal",O.style.fontStyle="normal",O.style.fontWeight="normal",O.style.letterSpacing="0",O.appendChild(U),document.body.appendChild(O);var G=U.offsetWidth;return U.style.fontFamily=tt(D)+", "+F,{node:U,w:G,parent:O}}function tr(){var F,O,U,G=this.fonts.length,W=G;for(F=0;F<G;F+=1)this.fonts[F].loaded?W-=1:"n"===this.fonts[F].fOrigin||0===this.fonts[F].origin?this.fonts[F].loaded=!0:(O=this.fonts[F].monoCase.node,U=this.fonts[F].monoCase.w,O.offsetWidth!==U?(W-=1,this.fonts[F].loaded=!0):(O=this.fonts[F].sansCase.node,U=this.fonts[F].sansCase.w,O.offsetWidth!==U&&(W-=1,this.fonts[F].loaded=!0)),this.fonts[F].loaded&&(this.fonts[F].sansCase.parent.parentNode.removeChild(this.fonts[F].sansCase.parent),this.fonts[F].monoCase.parent.parentNode.removeChild(this.fonts[F].monoCase.parent)));0!==W&&Date.now()-this.initTime<D?setTimeout(this.checkLoadedFontsBinded,20):setTimeout(this.setIsLoadedBinded,10)}function ts(D,F){var O,U=document.body&&F?"svg":"canvas",G=getFontProperties(D);if("svg"===U){var W=createNS("text");W.style.fontSize="100px",W.setAttribute("font-family",D.fFamily),W.setAttribute("font-style",G.style),W.setAttribute("font-weight",G.weight),W.textContent="1",D.fClass?(W.style.fontFamily="inherit",W.setAttribute("class",D.fClass)):W.style.fontFamily=D.fFamily,F.appendChild(W),O=W}else{var Y=new OffscreenCanvas(500,500).getContext("2d");Y.font=G.style+" "+G.weight+" 100px "+D.fFamily,O=Y}return{measureText:function(D){return"svg"===U?(O.textContent=D,O.getComputedTextLength()):O.measureText(D).width}}}function tn(D,F){if(!D){this.isLoaded=!0;return}if(this.chars){this.isLoaded=!0,this.fonts=D.list;return}if(!document.body){this.isLoaded=!0,D.list.forEach(function(D){D.helper=ts(D),D.cache={}}),this.fonts=D.list;return}var O=D.list,U=O.length,G=U;for(W=0;W<U;W+=1){var W,Y,X,J=!0;if(O[W].loaded=!1,O[W].monoCase=te(O[W].fFamily,"monospace"),O[W].sansCase=te(O[W].fFamily,"sans-serif"),O[W].fPath){if("p"===O[W].fOrigin||3===O[W].origin){if((Y=document.querySelectorAll('style[f-forigin="p"][f-family="'+O[W].fFamily+'"], style[f-origin="3"][f-family="'+O[W].fFamily+'"]')).length>0&&(J=!1),J){var K=createTag("style");K.setAttribute("f-forigin",O[W].fOrigin),K.setAttribute("f-origin",O[W].origin),K.setAttribute("f-family",O[W].fFamily),K.type="text/css",K.innerText="@font-face {font-family: "+O[W].fFamily+"; font-style: normal; src: url('"+O[W].fPath+"');}",F.appendChild(K)}}else if("g"===O[W].fOrigin||1===O[W].origin){for(X=0,Y=document.querySelectorAll('link[f-forigin="g"], link[f-origin="1"]');X<Y.length;X+=1)-1!==Y[X].href.indexOf(O[W].fPath)&&(J=!1);if(J){var Z=createTag("link");Z.setAttribute("f-forigin",O[W].fOrigin),Z.setAttribute("f-origin",O[W].origin),Z.type="text/css",Z.rel="stylesheet",Z.href=O[W].fPath,document.body.appendChild(Z)}}else if("t"===O[W].fOrigin||2===O[W].origin){for(X=0,Y=document.querySelectorAll('script[f-forigin="t"], script[f-origin="2"]');X<Y.length;X+=1)O[W].fPath===Y[X].src&&(J=!1);if(J){var Q=createTag("link");Q.setAttribute("f-forigin",O[W].fOrigin),Q.setAttribute("f-origin",O[W].origin),Q.setAttribute("rel","stylesheet"),Q.setAttribute("href",O[W].fPath),F.appendChild(Q)}}}else O[W].loaded=!0,G-=1;O[W].helper=ts(O[W],F),O[W].cache={},this.fonts.push(O[W])}0===G?this.isLoaded=!0:setTimeout(this.checkLoadedFonts.bind(this),100)}function ta(D){if(D){this.chars||(this.chars=[]);var F,O,U,G=D.length,W=this.chars.length;for(F=0;F<G;F+=1){for(O=0,U=!1;O<W;)this.chars[O].style===D[F].style&&this.chars[O].fFamily===D[F].fFamily&&this.chars[O].ch===D[F].ch&&(U=!0),O+=1;U||(this.chars.push(D[F]),W+=1)}}}function th(D,O,U){for(var G=0,W=this.chars.length;G<W;){if(this.chars[G].ch===D&&this.chars[G].style===O&&this.chars[G].fFamily===U)return this.chars[G];G+=1}return("string"==typeof D&&13!==D.charCodeAt(0)||!D)&&console&&console.warn&&!this._warned&&(this._warned=!0,console.warn("Missing character from exported characters list: ",D,O,U)),F}function tp(D,F,O){var U=this.getFontByName(F),G=D;if(!U.cache[G]){var W=U.helper;if(" "===D){var Y=W.measureText("|"+D+"|"),X=W.measureText("||");U.cache[G]=(Y-X)/100}else U.cache[G]=W.measureText(D)/100}return U.cache[G]*O}function tf(D){for(var F=0,O=this.fonts.length;F<O;){if(this.fonts[F].fName===D)return this.fonts[F];F+=1}return this.fonts[0]}function tu(D){var F=0,O=D.charCodeAt(0);if(O>=55296&&O<=56319){var U=D.charCodeAt(1);U>=56320&&U<=57343&&(F=(O-55296)*1024+U-56320+65536)}return F}function tc(D,F){var O=D.toString(16)+F.toString(16);return -1!==Q.indexOf(O)}function tm(D){return D===J}function tg(D){return D===X}function tv(D){var F=tu(D);return F>=K&&F<=Z}function tb(D){return tv(D.substr(0,2))&&tv(D.substr(2,2))}function t_(D){return -1!==O.indexOf(D)}function tk(D,F){var O=tu(D.substr(F,2));if(O!==U)return!1;var X=0;for(F+=2;X<5;){if((O=tu(D.substr(F,2)))<W||O>Y)return!1;X+=1,F+=2}return tu(D.substr(F,2))===G}function tw(){this.isLoaded=!0}var tA=function(){this.fonts=[],this.chars=null,this.typekitLoaded=0,this.isLoaded=!1,this._warned=!1,this.initTime=Date.now(),this.setIsLoadedBinded=this.setIsLoaded.bind(this),this.checkLoadedFontsBinded=this.checkLoadedFonts.bind(this)};tA.isModifier=tc,tA.isZeroWidthJoiner=tm,tA.isFlagEmoji=tb,tA.isRegionalCode=tv,tA.isCombinedCharacter=t_,tA.isRegionalFlag=tk,tA.isVariationSelector=tg,tA.BLACK_FLAG_CODE_POINT=U;var tP={addChars:ta,addFonts:tn,getCharData:th,getFontByName:tf,measureText:tp,checkLoadedFonts:tr,setIsLoaded:tw};return tA.prototype=tP,tA}();function SlotManager(D){this.animationData=D}function slotFactory(D){return new SlotManager(D)}function RenderableElement(){}SlotManager.prototype.getProp=function(D){return this.animationData.slots&&this.animationData.slots[D.sid]?Object.assign(D,this.animationData.slots[D.sid].p):D},RenderableElement.prototype={initRenderable:function(){this.isInRange=!1,this.hidden=!1,this.isTransparent=!1,this.renderableComponents=[]},addRenderableComponent:function(D){-1===this.renderableComponents.indexOf(D)&&this.renderableComponents.push(D)},removeRenderableComponent:function(D){-1!==this.renderableComponents.indexOf(D)&&this.renderableComponents.splice(this.renderableComponents.indexOf(D),1)},prepareRenderableFrame:function(D){this.checkLayerLimits(D)},checkTransparency:function(){this.finalTransform.mProp.o.v<=0?!this.isTransparent&&this.globalData.renderConfig.hideOnTransparent&&(this.isTransparent=!0,this.hide()):this.isTransparent&&(this.isTransparent=!1,this.show())},checkLayerLimits:function(D){this.data.ip-this.data.st<=D&&this.data.op-this.data.st>D?!0!==this.isInRange&&(this.globalData._mdf=!0,this._mdf=!0,this.isInRange=!0,this.show()):!1!==this.isInRange&&(this.globalData._mdf=!0,this.isInRange=!1,this.hide())},renderRenderable:function(){var D,F=this.renderableComponents.length;for(D=0;D<F;D+=1)this.renderableComponents[D].renderFrame(this._isFirstFrame)},sourceRectAtTime:function(){return{top:0,left:0,width:100,height:100}},getLayerSize:function(){return 5===this.data.ty?{w:this.data.textData.width,h:this.data.textData.height}:{w:this.data.width,h:this.data.height}}};var getBlendMode=function(){var D={0:"source-over",1:"multiply",2:"screen",3:"overlay",4:"darken",5:"lighten",6:"color-dodge",7:"color-burn",8:"hard-light",9:"soft-light",10:"difference",11:"exclusion",12:"hue",13:"saturation",14:"color",15:"luminosity"};return function(F){return D[F]||""}}();function SliderEffect(D,F,O){this.p=PropertyFactory.getProp(F,D.v,0,0,O)}function AngleEffect(D,F,O){this.p=PropertyFactory.getProp(F,D.v,0,0,O)}function ColorEffect(D,F,O){this.p=PropertyFactory.getProp(F,D.v,1,0,O)}function PointEffect(D,F,O){this.p=PropertyFactory.getProp(F,D.v,1,0,O)}function LayerIndexEffect(D,F,O){this.p=PropertyFactory.getProp(F,D.v,0,0,O)}function MaskIndexEffect(D,F,O){this.p=PropertyFactory.getProp(F,D.v,0,0,O)}function CheckboxEffect(D,F,O){this.p=PropertyFactory.getProp(F,D.v,0,0,O)}function NoValueEffect(){this.p={}}function EffectsManager(D,F){var O,U,G=D.ef||[];this.effectElements=[];var W=G.length;for(O=0;O<W;O+=1)U=new GroupEffect(G[O],F),this.effectElements.push(U)}function GroupEffect(D,F){this.init(D,F)}function BaseElement(){}function FrameElement(){}function FootageElement(D,F,O){this.initFrame(),this.initRenderable(),this.assetData=F.getAssetData(D.refId),this.footageData=F.imageLoader.getAsset(this.assetData),this.initBaseData(D,F,O)}function AudioElement(D,F,O){this.initFrame(),this.initRenderable(),this.assetData=F.getAssetData(D.refId),this.initBaseData(D,F,O),this._isPlaying=!1,this._canPlay=!1;var U=this.globalData.getAssetsPath(this.assetData);this.audio=this.globalData.audioController.createAudio(U),this._currentTime=0,this.globalData.audioController.addAudio(this),this._volumeMultiplier=1,this._volume=1,this._previousVolume=null,this.tm=D.tm?PropertyFactory.getProp(this,D.tm,0,F.frameRate,this):{_placeholder:!0},this.lv=PropertyFactory.getProp(this,D.au&&D.au.lv?D.au.lv:{k:[100]},1,.01,this)}function BaseRenderer(){}extendPrototype([DynamicPropertyContainer],GroupEffect),GroupEffect.prototype.getValue=GroupEffect.prototype.iterateDynamicProperties,GroupEffect.prototype.init=function(D,F){this.data=D,this.effectElements=[],this.initDynamicPropertyContainer(F);var O,U,G=this.data.ef.length,W=this.data.ef;for(O=0;O<G;O+=1){switch(U=null,W[O].ty){case 0:U=new SliderEffect(W[O],F,this);break;case 1:U=new AngleEffect(W[O],F,this);break;case 2:U=new ColorEffect(W[O],F,this);break;case 3:U=new PointEffect(W[O],F,this);break;case 4:case 7:U=new CheckboxEffect(W[O],F,this);break;case 10:U=new LayerIndexEffect(W[O],F,this);break;case 11:U=new MaskIndexEffect(W[O],F,this);break;case 5:U=new EffectsManager(W[O],F);break;default:U=new NoValueEffect(W[O])}U&&this.effectElements.push(U)}},BaseElement.prototype={checkMasks:function(){if(!this.data.hasMask)return!1;for(var D=0,F=this.data.masksProperties.length;D<F;){if("n"!==this.data.masksProperties[D].mode&&!1!==this.data.masksProperties[D].cl)return!0;D+=1}return!1},initExpressions:function(){var D=getExpressionInterfaces();if(D){var F=D("layer"),O=D("effects"),U=D("shape"),G=D("text"),W=D("comp");this.layerInterface=F(this),this.data.hasMask&&this.maskManager&&this.layerInterface.registerMaskInterface(this.maskManager);var Y=O.createEffectsInterface(this,this.layerInterface);this.layerInterface.registerEffectsInterface(Y),0===this.data.ty||this.data.xt?this.compInterface=W(this):4===this.data.ty?(this.layerInterface.shapeInterface=U(this.shapesData,this.itemsData,this.layerInterface),this.layerInterface.content=this.layerInterface.shapeInterface):5===this.data.ty&&(this.layerInterface.textInterface=G(this),this.layerInterface.text=this.layerInterface.textInterface)}},setBlendMode:function(){var D=getBlendMode(this.data.bm);(this.baseElement||this.layerElement).style["mix-blend-mode"]=D},initBaseData:function(D,F,O){this.globalData=F,this.comp=O,this.data=D,this.layerId=createElementID(),this.data.sr||(this.data.sr=1),this.effectsManager=new EffectsManager(this.data,this,this.dynamicProperties)},getType:function(){return this.type},sourceRectAtTime:function(){}},FrameElement.prototype={initFrame:function(){this._isFirstFrame=!1,this.dynamicProperties=[],this._mdf=!1},prepareProperties:function(D,F){var O,U=this.dynamicProperties.length;for(O=0;O<U;O+=1)(F||this._isParent&&"transform"===this.dynamicProperties[O].propType)&&(this.dynamicProperties[O].getValue(),this.dynamicProperties[O]._mdf&&(this.globalData._mdf=!0,this._mdf=!0))},addDynamicProperty:function(D){-1===this.dynamicProperties.indexOf(D)&&this.dynamicProperties.push(D)}},FootageElement.prototype.prepareFrame=function(){},extendPrototype([RenderableElement,BaseElement,FrameElement],FootageElement),FootageElement.prototype.getBaseElement=function(){return null},FootageElement.prototype.renderFrame=function(){},FootageElement.prototype.destroy=function(){},FootageElement.prototype.initExpressions=function(){var D=getExpressionInterfaces();if(D){var F=D("footage");this.layerInterface=F(this)}},FootageElement.prototype.getFootageData=function(){return this.footageData},AudioElement.prototype.prepareFrame=function(D){if(this.prepareRenderableFrame(D,!0),this.prepareProperties(D,!0),this.tm._placeholder)this._currentTime=D/this.data.sr;else{var F=this.tm.v;this._currentTime=F}this._volume=this.lv.v[0];var O=this._volume*this._volumeMultiplier;this._previousVolume!==O&&(this._previousVolume=O,this.audio.volume(O))},extendPrototype([RenderableElement,BaseElement,FrameElement],AudioElement),AudioElement.prototype.renderFrame=function(){this.isInRange&&this._canPlay&&(this._isPlaying?(!this.audio.playing()||Math.abs(this._currentTime/this.globalData.frameRate-this.audio.seek())>.1)&&this.audio.seek(this._currentTime/this.globalData.frameRate):(this.audio.play(),this.audio.seek(this._currentTime/this.globalData.frameRate),this._isPlaying=!0))},AudioElement.prototype.show=function(){},AudioElement.prototype.hide=function(){this.audio.pause(),this._isPlaying=!1},AudioElement.prototype.pause=function(){this.audio.pause(),this._isPlaying=!1,this._canPlay=!1},AudioElement.prototype.resume=function(){this._canPlay=!0},AudioElement.prototype.setRate=function(D){this.audio.rate(D)},AudioElement.prototype.volume=function(D){this._volumeMultiplier=D,this._previousVolume=D*this._volume,this.audio.volume(this._previousVolume)},AudioElement.prototype.getBaseElement=function(){return null},AudioElement.prototype.destroy=function(){},AudioElement.prototype.sourceRectAtTime=function(){},AudioElement.prototype.initExpressions=function(){},BaseRenderer.prototype.checkLayers=function(D){var F,O,U=this.layers.length;for(this.completeLayers=!0,F=U-1;F>=0;F-=1)!this.elements[F]&&(O=this.layers[F]).ip-O.st<=D-this.layers[F].st&&O.op-O.st>D-this.layers[F].st&&this.buildItem(F),this.completeLayers=!!this.elements[F]&&this.completeLayers;this.checkPendingElements()},BaseRenderer.prototype.createItem=function(D){switch(D.ty){case 2:return this.createImage(D);case 0:return this.createComp(D);case 1:return this.createSolid(D);case 3:default:return this.createNull(D);case 4:return this.createShape(D);case 5:return this.createText(D);case 6:return this.createAudio(D);case 13:return this.createCamera(D);case 15:return this.createFootage(D)}},BaseRenderer.prototype.createCamera=function(){throw Error("You're using a 3d camera. Try the html renderer.")},BaseRenderer.prototype.createAudio=function(D){return new AudioElement(D,this.globalData,this)},BaseRenderer.prototype.createFootage=function(D){return new FootageElement(D,this.globalData,this)},BaseRenderer.prototype.buildAllItems=function(){var D,F=this.layers.length;for(D=0;D<F;D+=1)this.buildItem(D);this.checkPendingElements()},BaseRenderer.prototype.includeLayers=function(D){this.completeLayers=!1;var F,O,U=D.length,G=this.layers.length;for(F=0;F<U;F+=1)for(O=0;O<G;){if(this.layers[O].id===D[F].id){this.layers[O]=D[F];break}O+=1}},BaseRenderer.prototype.setProjectInterface=function(D){this.globalData.projectInterface=D},BaseRenderer.prototype.initItems=function(){this.globalData.progressiveLoad||this.buildAllItems()},BaseRenderer.prototype.buildElementParenting=function(D,F,O){for(var U=this.elements,G=this.layers,W=0,Y=G.length;W<Y;)G[W].ind==F&&(U[W]&&!0!==U[W]?(O.push(U[W]),U[W].setAsParent(),void 0!==G[W].parent?this.buildElementParenting(D,G[W].parent,O):D.setHierarchy(O)):(this.buildItem(W),this.addPendingElement(D))),W+=1},BaseRenderer.prototype.addPendingElement=function(D){this.pendingElements.push(D)},BaseRenderer.prototype.searchExtraCompositions=function(D){var F,O=D.length;for(F=0;F<O;F+=1)if(D[F].xt){var U=this.createComp(D[F]);U.initExpressions(),this.globalData.projectInterface.registerComposition(U)}},BaseRenderer.prototype.getElementById=function(D){var F,O=this.elements.length;for(F=0;F<O;F+=1)if(this.elements[F].data.ind===D)return this.elements[F];return null},BaseRenderer.prototype.getElementByPath=function(D){var F=D.shift();if("number"==typeof F)O=this.elements[F];else{var O,U,G=this.elements.length;for(U=0;U<G;U+=1)if(this.elements[U].data.nm===F){O=this.elements[U];break}}return 0===D.length?O:O.getElementByPath(D)},BaseRenderer.prototype.setupGlobalData=function(D,F){this.globalData.fontManager=new FontManager,this.globalData.slotManager=slotFactory(D),this.globalData.fontManager.addChars(D.chars),this.globalData.fontManager.addFonts(D.fonts,F),this.globalData.getAssetData=this.animationItem.getAssetData.bind(this.animationItem),this.globalData.getAssetsPath=this.animationItem.getAssetsPath.bind(this.animationItem),this.globalData.imageLoader=this.animationItem.imagePreloader,this.globalData.audioController=this.animationItem.audioController,this.globalData.frameId=0,this.globalData.frameRate=D.fr,this.globalData.nm=D.nm,this.globalData.compSize={w:D.w,h:D.h}};var effectTypes={TRANSFORM_EFFECT:"transformEFfect"};function TransformElement(){}function MaskElement(D,F,O){this.data=D,this.element=F,this.globalData=O,this.storedData=[],this.masksProperties=this.data.masksProperties||[],this.maskElement=null;var U=this.globalData.defs,G=this.masksProperties?this.masksProperties.length:0;this.viewData=createSizedArray(G),this.solidPath="";var W=this.masksProperties,Y=0,X=[],J=createElementID(),K="clipPath",Z="clip-path";for(Q=0;Q<G;Q+=1)if(("a"!==W[Q].mode&&"n"!==W[Q].mode||W[Q].inv||100!==W[Q].o.k||W[Q].o.x)&&(K="mask",Z="mask"),("s"===W[Q].mode||"i"===W[Q].mode)&&0===Y?((ts=createNS("rect")).setAttribute("fill","#ffffff"),ts.setAttribute("width",this.element.comp.data.w||0),ts.setAttribute("height",this.element.comp.data.h||0),X.push(ts)):ts=null,tt=createNS("path"),"n"===W[Q].mode)this.viewData[Q]={op:PropertyFactory.getProp(this.element,W[Q].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,W[Q],3),elem:tt,lastPath:""},U.appendChild(tt);else{if(Y+=1,tt.setAttribute("fill","s"===W[Q].mode?"#000000":"#ffffff"),tt.setAttribute("clip-rule","nonzero"),0!==W[Q].x.k?(K="mask",Z="mask",th=PropertyFactory.getProp(this.element,W[Q].x,0,null,this.element),tp=createElementID(),(tn=createNS("filter")).setAttribute("id",tp),(ta=createNS("feMorphology")).setAttribute("operator","erode"),ta.setAttribute("in","SourceGraphic"),ta.setAttribute("radius","0"),tn.appendChild(ta),U.appendChild(tn),tt.setAttribute("stroke","s"===W[Q].mode?"#000000":"#ffffff")):(ta=null,th=null),this.storedData[Q]={elem:tt,x:th,expan:ta,lastPath:"",lastOperator:"",filterId:tp,lastRadius:0},"i"===W[Q].mode){tr=X.length;var Q,tt,te,tr,ts,tn,ta,th,tp,tf=createNS("g");for(te=0;te<tr;te+=1)tf.appendChild(X[te]);var tu=createNS("mask");tu.setAttribute("mask-type","alpha"),tu.setAttribute("id",J+"_"+Y),tu.appendChild(tt),U.appendChild(tu),tf.setAttribute("mask","url("+getLocationHref()+"#"+J+"_"+Y+")"),X.length=0,X.push(tf)}else X.push(tt);W[Q].inv&&!this.solidPath&&(this.solidPath=this.createLayerSolidPath()),this.viewData[Q]={elem:tt,lastPath:"",op:PropertyFactory.getProp(this.element,W[Q].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,W[Q],3),invRect:ts},this.viewData[Q].prop.k||this.drawPath(W[Q],this.viewData[Q].prop.v,this.viewData[Q])}for(Q=0,this.maskElement=createNS(K),G=X.length;Q<G;Q+=1)this.maskElement.appendChild(X[Q]);Y>0&&(this.maskElement.setAttribute("id",J),this.element.maskedElement.setAttribute(Z,"url("+getLocationHref()+"#"+J+")"),U.appendChild(this.maskElement)),this.viewData.length&&this.element.addRenderableComponent(this)}TransformElement.prototype={initTransform:function(){var D=new Matrix;this.finalTransform={mProp:this.data.ks?TransformPropertyFactory.getTransformProperty(this,this.data.ks,this):{o:0},_matMdf:!1,_localMatMdf:!1,_opMdf:!1,mat:D,localMat:D,localOpacity:1},this.data.ao&&(this.finalTransform.mProp.autoOriented=!0),this.data.ty},renderTransform:function(){if(this.finalTransform._opMdf=this.finalTransform.mProp.o._mdf||this._isFirstFrame,this.finalTransform._matMdf=this.finalTransform.mProp._mdf||this._isFirstFrame,this.hierarchy){var D,F=this.finalTransform.mat,O=0,U=this.hierarchy.length;if(!this.finalTransform._matMdf)for(;O<U;){if(this.hierarchy[O].finalTransform.mProp._mdf){this.finalTransform._matMdf=!0;break}O+=1}if(this.finalTransform._matMdf)for(D=this.finalTransform.mProp.v.props,F.cloneFromProps(D),O=0;O<U;O+=1)F.multiply(this.hierarchy[O].finalTransform.mProp.v)}this.finalTransform._matMdf&&(this.finalTransform._localMatMdf=this.finalTransform._matMdf),this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v)},renderLocalTransform:function(){if(this.localTransforms){var D=0,F=this.localTransforms.length;if(this.finalTransform._localMatMdf=this.finalTransform._matMdf,!this.finalTransform._localMatMdf||!this.finalTransform._opMdf)for(;D<F;)this.localTransforms[D]._mdf&&(this.finalTransform._localMatMdf=!0),this.localTransforms[D]._opMdf&&!this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v,this.finalTransform._opMdf=!0),D+=1;if(this.finalTransform._localMatMdf){var O=this.finalTransform.localMat;for(this.localTransforms[0].matrix.clone(O),D=1;D<F;D+=1){var U=this.localTransforms[D].matrix;O.multiply(U)}O.multiply(this.finalTransform.mat)}if(this.finalTransform._opMdf){var G=this.finalTransform.localOpacity;for(D=0;D<F;D+=1)G*=.01*this.localTransforms[D].opacity;this.finalTransform.localOpacity=G}}},searchEffectTransforms:function(){if(this.renderableEffectsManager){var D=this.renderableEffectsManager.getEffects(effectTypes.TRANSFORM_EFFECT);if(D.length){this.localTransforms=[],this.finalTransform.localMat=new Matrix;var F=0,O=D.length;for(F=0;F<O;F+=1)this.localTransforms.push(D[F])}}},globalToLocal:function(D){var F,O,U=[];U.push(this.finalTransform);for(var G=!0,W=this.comp;G;)W.finalTransform?(W.data.hasMask&&U.splice(0,0,W.finalTransform),W=W.comp):G=!1;var Y=U.length;for(F=0;F<Y;F+=1)O=U[F].mat.applyToPointArray(0,0,0),D=[D[0]-O[0],D[1]-O[1],0];return D},mHelper:new Matrix},MaskElement.prototype.getMaskProperty=function(D){return this.viewData[D].prop},MaskElement.prototype.renderFrame=function(D){var F,O=this.element.finalTransform.mat,U=this.masksProperties.length;for(F=0;F<U;F+=1)if((this.viewData[F].prop._mdf||D)&&this.drawPath(this.masksProperties[F],this.viewData[F].prop.v,this.viewData[F]),(this.viewData[F].op._mdf||D)&&this.viewData[F].elem.setAttribute("fill-opacity",this.viewData[F].op.v),"n"!==this.masksProperties[F].mode&&(this.viewData[F].invRect&&(this.element.finalTransform.mProp._mdf||D)&&this.viewData[F].invRect.setAttribute("transform",O.getInverseMatrix().to2dCSS()),this.storedData[F].x&&(this.storedData[F].x._mdf||D))){var G=this.storedData[F].expan;this.storedData[F].x.v<0?("erode"!==this.storedData[F].lastOperator&&(this.storedData[F].lastOperator="erode",this.storedData[F].elem.setAttribute("filter","url("+getLocationHref()+"#"+this.storedData[F].filterId+")")),G.setAttribute("radius",-this.storedData[F].x.v)):("dilate"!==this.storedData[F].lastOperator&&(this.storedData[F].lastOperator="dilate",this.storedData[F].elem.setAttribute("filter",null)),this.storedData[F].elem.setAttribute("stroke-width",2*this.storedData[F].x.v))}},MaskElement.prototype.getMaskelement=function(){return this.maskElement},MaskElement.prototype.createLayerSolidPath=function(){return"M0,0 "+(" h"+this.globalData.compSize.w+" v"+this.globalData.compSize.h+" h-"+this.globalData.compSize.w+" v-"+this.globalData.compSize.h)+" "},MaskElement.prototype.drawPath=function(D,F,O){var U,G,W=" M"+F.v[0][0]+","+F.v[0][1];for(U=1,G=F._length;U<G;U+=1)W+=" C"+F.o[U-1][0]+","+F.o[U-1][1]+" "+F.i[U][0]+","+F.i[U][1]+" "+F.v[U][0]+","+F.v[U][1];if(F.c&&G>1&&(W+=" C"+F.o[U-1][0]+","+F.o[U-1][1]+" "+F.i[0][0]+","+F.i[0][1]+" "+F.v[0][0]+","+F.v[0][1]),O.lastPath!==W){var Y="";O.elem&&(F.c&&(Y=D.inv?this.solidPath+W:W),O.elem.setAttribute("d",Y)),O.lastPath=W}},MaskElement.prototype.destroy=function(){this.element=null,this.globalData=null,this.maskElement=null,this.data=null,this.masksProperties=null};var filtersFactory=function(){var D={};function F(D,F){var O=createNS("filter");return O.setAttribute("id",D),!0!==F&&(O.setAttribute("filterUnits","objectBoundingBox"),O.setAttribute("x","0%"),O.setAttribute("y","0%"),O.setAttribute("width","100%"),O.setAttribute("height","100%")),O}function O(){var D=createNS("feColorMatrix");return D.setAttribute("type","matrix"),D.setAttribute("color-interpolation-filters","sRGB"),D.setAttribute("values","0 0 0 1 0  0 0 0 1 0  0 0 0 1 0  0 0 0 1 1"),D}return D.createFilter=F,D.createAlphaToLuminanceFilter=O,D}(),featureSupport=function(){var D={maskType:!0,svgLumaHidden:!0,offscreenCanvas:"undefined"!=typeof OffscreenCanvas};return(/MSIE 10/i.test(navigator.userAgent)||/MSIE 9/i.test(navigator.userAgent)||/rv:11.0/i.test(navigator.userAgent)||/Edge\/\d./i.test(navigator.userAgent))&&(D.maskType=!1),/firefox/i.test(navigator.userAgent)&&(D.svgLumaHidden=!1),D}(),registeredEffects$1={},idPrefix="filter_result_";function SVGEffects(D){var F,O,U="SourceGraphic",G=D.data.ef?D.data.ef.length:0,W=createElementID(),Y=filtersFactory.createFilter(W,!0),X=0;for(F=0,this.filters=[];F<G;F+=1){O=null;var J=D.data.ef[F].ty;registeredEffects$1[J]&&(O=new registeredEffects$1[J].effect(Y,D.effectsManager.effectElements[F],D,idPrefix+X,U),U=idPrefix+X,registeredEffects$1[J].countsAsEffect&&(X+=1)),O&&this.filters.push(O)}X&&(D.globalData.defs.appendChild(Y),D.layerElement.setAttribute("filter","url("+getLocationHref()+"#"+W+")")),this.filters.length&&D.addRenderableComponent(this)}function registerEffect$1(D,F,O){registeredEffects$1[D]={effect:F,countsAsEffect:O}}function SVGBaseElement(){}function HierarchyElement(){}function RenderableDOMElement(){}function IImageElement(D,F,O){this.assetData=F.getAssetData(D.refId),this.assetData&&this.assetData.sid&&(this.assetData=F.slotManager.getProp(this.assetData)),this.initElement(D,F,O),this.sourceRect={top:0,left:0,width:this.assetData.w,height:this.assetData.h}}function ProcessedElement(D,F){this.elem=D,this.pos=F}function IShapeElement(){}SVGEffects.prototype.renderFrame=function(D){var F,O=this.filters.length;for(F=0;F<O;F+=1)this.filters[F].renderFrame(D)},SVGEffects.prototype.getEffects=function(D){var F,O=this.filters.length,U=[];for(F=0;F<O;F+=1)this.filters[F].type===D&&U.push(this.filters[F]);return U},SVGBaseElement.prototype={initRendererElement:function(){this.layerElement=createNS("g")},createContainerElements:function(){this.matteElement=createNS("g"),this.transformedElement=this.layerElement,this.maskedElement=this.layerElement,this._sizeChanged=!1;var D=null;if(this.data.td){this.matteMasks={};var F=createNS("g");F.setAttribute("id",this.layerId),F.appendChild(this.layerElement),D=F,this.globalData.defs.appendChild(F)}else this.data.tt?(this.matteElement.appendChild(this.layerElement),D=this.matteElement,this.baseElement=this.matteElement):this.baseElement=this.layerElement;if(this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0===this.data.ty&&!this.data.hd){var O=createNS("clipPath"),U=createNS("path");U.setAttribute("d","M0,0 L"+this.data.w+",0 L"+this.data.w+","+this.data.h+" L0,"+this.data.h+"z");var G=createElementID();if(O.setAttribute("id",G),O.appendChild(U),this.globalData.defs.appendChild(O),this.checkMasks()){var W=createNS("g");W.setAttribute("clip-path","url("+getLocationHref()+"#"+G+")"),W.appendChild(this.layerElement),this.transformedElement=W,D?D.appendChild(this.transformedElement):this.baseElement=this.transformedElement}else this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+G+")")}0!==this.data.bm&&this.setBlendMode()},renderElement:function(){this.finalTransform._localMatMdf&&this.transformedElement.setAttribute("transform",this.finalTransform.localMat.to2dCSS()),this.finalTransform._opMdf&&this.transformedElement.setAttribute("opacity",this.finalTransform.localOpacity)},destroyBaseElement:function(){this.layerElement=null,this.matteElement=null,this.maskManager.destroy()},getBaseElement:function(){return this.data.hd?null:this.baseElement},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData),this.renderableEffectsManager=new SVGEffects(this),this.searchEffectTransforms()},getMatte:function(D){if(this.matteMasks||(this.matteMasks={}),!this.matteMasks[D]){var F,O,U,G,W=this.layerId+"_"+D;if(1===D||3===D){var Y=createNS("mask");Y.setAttribute("id",W),Y.setAttribute("mask-type",3===D?"luminance":"alpha"),(U=createNS("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),Y.appendChild(U),this.globalData.defs.appendChild(Y),featureSupport.maskType||1!==D||(Y.setAttribute("mask-type","luminance"),F=createElementID(),O=filtersFactory.createFilter(F),this.globalData.defs.appendChild(O),O.appendChild(filtersFactory.createAlphaToLuminanceFilter()),(G=createNS("g")).appendChild(U),Y.appendChild(G),G.setAttribute("filter","url("+getLocationHref()+"#"+F+")"))}else if(2===D){var X=createNS("mask");X.setAttribute("id",W),X.setAttribute("mask-type","alpha");var J=createNS("g");X.appendChild(J),F=createElementID(),O=filtersFactory.createFilter(F);var K=createNS("feComponentTransfer");K.setAttribute("in","SourceGraphic"),O.appendChild(K);var Z=createNS("feFuncA");Z.setAttribute("type","table"),Z.setAttribute("tableValues","1.0 0.0"),K.appendChild(Z),this.globalData.defs.appendChild(O);var Q=createNS("rect");Q.setAttribute("width",this.comp.data.w),Q.setAttribute("height",this.comp.data.h),Q.setAttribute("x","0"),Q.setAttribute("y","0"),Q.setAttribute("fill","#ffffff"),Q.setAttribute("opacity","0"),J.setAttribute("filter","url("+getLocationHref()+"#"+F+")"),J.appendChild(Q),(U=createNS("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),J.appendChild(U),featureSupport.maskType||(X.setAttribute("mask-type","luminance"),O.appendChild(filtersFactory.createAlphaToLuminanceFilter()),G=createNS("g"),J.appendChild(Q),G.appendChild(this.layerElement),J.appendChild(G)),this.globalData.defs.appendChild(X)}this.matteMasks[D]=W}return this.matteMasks[D]},setMatte:function(D){this.matteElement&&this.matteElement.setAttribute("mask","url("+getLocationHref()+"#"+D+")")}},HierarchyElement.prototype={initHierarchy:function(){this.hierarchy=[],this._isParent=!1,this.checkParenting()},setHierarchy:function(D){this.hierarchy=D},setAsParent:function(){this._isParent=!0},checkParenting:function(){void 0!==this.data.parent&&this.comp.buildElementParenting(this,this.data.parent,[])}},function(){extendPrototype([RenderableElement,createProxyFunction({initElement:function(D,F,O){this.initFrame(),this.initBaseData(D,F,O),this.initTransform(D,F,O),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide()},hide:function(){this.hidden||this.isInRange&&!this.isTransparent||((this.baseElement||this.layerElement).style.display="none",this.hidden=!0)},show:function(){this.isInRange&&!this.isTransparent&&(this.data.hd||((this.baseElement||this.layerElement).style.display="block"),this.hidden=!1,this._isFirstFrame=!0)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},renderInnerContent:function(){},prepareFrame:function(D){this._mdf=!1,this.prepareRenderableFrame(D),this.prepareProperties(D,this.isInRange),this.checkTransparency()},destroy:function(){this.innerElem=null,this.destroyBaseElement()}})],RenderableDOMElement)}(),extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],IImageElement),IImageElement.prototype.createContent=function(){var D=this.globalData.getAssetsPath(this.assetData);this.innerElem=createNS("image"),this.innerElem.setAttribute("width",this.assetData.w+"px"),this.innerElem.setAttribute("height",this.assetData.h+"px"),this.innerElem.setAttribute("preserveAspectRatio",this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio),this.innerElem.setAttributeNS("http://www.w3.org/1999/xlink","href",D),this.layerElement.appendChild(this.innerElem)},IImageElement.prototype.sourceRectAtTime=function(){return this.sourceRect},IShapeElement.prototype={addShapeToModifiers:function(D){var F,O=this.shapeModifiers.length;for(F=0;F<O;F+=1)this.shapeModifiers[F].addShape(D)},isShapeInAnimatedModifiers:function(D){for(var F=0,O=this.shapeModifiers.length;F<O;)if(this.shapeModifiers[F].isAnimatedWithShape(D))return!0;return!1},renderModifiers:function(){if(this.shapeModifiers.length){var D,F=this.shapes.length;for(D=0;D<F;D+=1)this.shapes[D].sh.reset();for(D=(F=this.shapeModifiers.length)-1;D>=0&&!this.shapeModifiers[D].processShapes(this._isFirstFrame);D-=1);}},searchProcessedElement:function(D){for(var F=this.processedElements,O=0,U=F.length;O<U;){if(F[O].elem===D)return F[O].pos;O+=1}return 0},addProcessedElement:function(D,F){for(var O=this.processedElements,U=O.length;U;)if(O[U-=1].elem===D){O[U].pos=F;return}O.push(new ProcessedElement(D,F))},prepareFrame:function(D){this.prepareRenderableFrame(D),this.prepareProperties(D,this.isInRange)}};var lineCapEnum={1:"butt",2:"round",3:"square"},lineJoinEnum={1:"miter",2:"round",3:"bevel"};function SVGShapeData(D,F,O){this.caches=[],this.styles=[],this.transformers=D,this.lStr="",this.sh=O,this.lvl=F,this._isAnimated=!!O.k;for(var U=0,G=D.length;U<G;){if(D[U].mProps.dynamicProperties.length){this._isAnimated=!0;break}U+=1}}function SVGStyleData(D,F){this.data=D,this.type=D.ty,this.d="",this.lvl=F,this._mdf=!1,this.closed=!0===D.hd,this.pElem=createNS("path"),this.msElem=null}function DashProperty(D,F,O,U){this.elem=D,this.frameId=-1,this.dataProps=createSizedArray(F.length),this.renderer=O,this.k=!1,this.dashStr="",this.dashArray=createTypedArray("float32",F.length?F.length-1:0),this.dashoffset=createTypedArray("float32",1),this.initDynamicPropertyContainer(U);var G,W,Y=F.length||0;for(G=0;G<Y;G+=1)W=PropertyFactory.getProp(D,F[G].v,0,0,this),this.k=W.k||this.k,this.dataProps[G]={n:F[G].n,p:W};this.k||this.getValue(!0),this._isAnimated=this.k}function SVGStrokeStyleData(D,F,O){this.initDynamicPropertyContainer(D),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(D,F.o,0,.01,this),this.w=PropertyFactory.getProp(D,F.w,0,null,this),this.d=new DashProperty(D,F.d||{},"svg",this),this.c=PropertyFactory.getProp(D,F.c,1,255,this),this.style=O,this._isAnimated=!!this._isAnimated}function SVGFillStyleData(D,F,O){this.initDynamicPropertyContainer(D),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(D,F.o,0,.01,this),this.c=PropertyFactory.getProp(D,F.c,1,255,this),this.style=O}function SVGNoStyleData(D,F,O){this.initDynamicPropertyContainer(D),this.getValue=this.iterateDynamicProperties,this.style=O}function GradientProperty(D,F,O){this.data=F,this.c=createTypedArray("uint8c",4*F.p);var U=F.k.k[0].s?F.k.k[0].s.length-4*F.p:F.k.k.length-4*F.p;this.o=createTypedArray("float32",U),this._cmdf=!1,this._omdf=!1,this._collapsable=this.checkCollapsable(),this._hasOpacity=U,this.initDynamicPropertyContainer(O),this.prop=PropertyFactory.getProp(D,F.k,1,null,this),this.k=this.prop.k,this.getValue(!0)}function SVGGradientFillStyleData(D,F,O){this.initDynamicPropertyContainer(D),this.getValue=this.iterateDynamicProperties,this.initGradientData(D,F,O)}function SVGGradientStrokeStyleData(D,F,O){this.initDynamicPropertyContainer(D),this.getValue=this.iterateDynamicProperties,this.w=PropertyFactory.getProp(D,F.w,0,null,this),this.d=new DashProperty(D,F.d||{},"svg",this),this.initGradientData(D,F,O),this._isAnimated=!!this._isAnimated}function ShapeGroupData(){this.it=[],this.prevViewData=[],this.gr=createNS("g")}function SVGTransformData(D,F,O){this.transform={mProps:D,op:F,container:O},this.elements=[],this._isAnimated=this.transform.mProps.dynamicProperties.length||this.transform.op.effectsSequence.length}SVGShapeData.prototype.setAsAnimated=function(){this._isAnimated=!0},SVGStyleData.prototype.reset=function(){this.d="",this._mdf=!1},DashProperty.prototype.getValue=function(D){if((this.elem.globalData.frameId!==this.frameId||D)&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf=this._mdf||D,this._mdf)){var F=0,O=this.dataProps.length;for("svg"===this.renderer&&(this.dashStr=""),F=0;F<O;F+=1)"o"!==this.dataProps[F].n?"svg"===this.renderer?this.dashStr+=" "+this.dataProps[F].p.v:this.dashArray[F]=this.dataProps[F].p.v:this.dashoffset[0]=this.dataProps[F].p.v}},extendPrototype([DynamicPropertyContainer],DashProperty),extendPrototype([DynamicPropertyContainer],SVGStrokeStyleData),extendPrototype([DynamicPropertyContainer],SVGFillStyleData),extendPrototype([DynamicPropertyContainer],SVGNoStyleData),GradientProperty.prototype.comparePoints=function(D,F){for(var O=0,U=this.o.length/2;O<U;){if(Math.abs(D[4*O]-D[4*F+2*O])>.01)return!1;O+=1}return!0},GradientProperty.prototype.checkCollapsable=function(){if(this.o.length/2!=this.c.length/4)return!1;if(this.data.k.k[0].s)for(var D=0,F=this.data.k.k.length;D<F;){if(!this.comparePoints(this.data.k.k[D].s,this.data.p))return!1;D+=1}else if(!this.comparePoints(this.data.k.k,this.data.p))return!1;return!0},GradientProperty.prototype.getValue=function(D){if(this.prop.getValue(),this._mdf=!1,this._cmdf=!1,this._omdf=!1,this.prop._mdf||D){var F,O,U,G=4*this.data.p;for(F=0;F<G;F+=1)O=F%4==0?100:255,U=Math.round(this.prop.v[F]*O),this.c[F]!==U&&(this.c[F]=U,this._cmdf=!D);if(this.o.length)for(G=this.prop.v.length,F=4*this.data.p;F<G;F+=1)O=F%2==0?100:1,U=F%2==0?Math.round(100*this.prop.v[F]):this.prop.v[F],this.o[F-4*this.data.p]!==U&&(this.o[F-4*this.data.p]=U,this._omdf=!D);this._mdf=!D}},extendPrototype([DynamicPropertyContainer],GradientProperty),SVGGradientFillStyleData.prototype.initGradientData=function(D,F,O){this.o=PropertyFactory.getProp(D,F.o,0,.01,this),this.s=PropertyFactory.getProp(D,F.s,1,null,this),this.e=PropertyFactory.getProp(D,F.e,1,null,this),this.h=PropertyFactory.getProp(D,F.h||{k:0},0,.01,this),this.a=PropertyFactory.getProp(D,F.a||{k:0},0,degToRads,this),this.g=new GradientProperty(D,F.g,this),this.style=O,this.stops=[],this.setGradientData(O.pElem,F),this.setGradientOpacity(F,O),this._isAnimated=!!this._isAnimated},SVGGradientFillStyleData.prototype.setGradientData=function(D,F){var O,U,G,W=createElementID(),Y=createNS(1===F.t?"linearGradient":"radialGradient");Y.setAttribute("id",W),Y.setAttribute("spreadMethod","pad"),Y.setAttribute("gradientUnits","userSpaceOnUse");var X=[];for(U=0,G=4*F.g.p;U<G;U+=4)O=createNS("stop"),Y.appendChild(O),X.push(O);D.setAttribute("gf"===F.ty?"fill":"stroke","url("+getLocationHref()+"#"+W+")"),this.gf=Y,this.cst=X},SVGGradientFillStyleData.prototype.setGradientOpacity=function(D,F){if(this.g._hasOpacity&&!this.g._collapsable){var O,U,G,W=createNS("mask"),Y=createNS("path");W.appendChild(Y);var X=createElementID(),J=createElementID();W.setAttribute("id",J);var K=createNS(1===D.t?"linearGradient":"radialGradient");K.setAttribute("id",X),K.setAttribute("spreadMethod","pad"),K.setAttribute("gradientUnits","userSpaceOnUse"),G=D.g.k.k[0].s?D.g.k.k[0].s.length:D.g.k.k.length;var Z=this.stops;for(U=4*D.g.p;U<G;U+=2)(O=createNS("stop")).setAttribute("stop-color","rgb(255,255,255)"),K.appendChild(O),Z.push(O);Y.setAttribute("gf"===D.ty?"fill":"stroke","url("+getLocationHref()+"#"+X+")"),"gs"===D.ty&&(Y.setAttribute("stroke-linecap",lineCapEnum[D.lc||2]),Y.setAttribute("stroke-linejoin",lineJoinEnum[D.lj||2]),1===D.lj&&Y.setAttribute("stroke-miterlimit",D.ml)),this.of=K,this.ms=W,this.ost=Z,this.maskId=J,F.msElem=Y}},extendPrototype([DynamicPropertyContainer],SVGGradientFillStyleData),extendPrototype([SVGGradientFillStyleData,DynamicPropertyContainer],SVGGradientStrokeStyleData);var buildShapeString=function(D,F,O,U){if(0===F)return"";var G,W=D.o,Y=D.i,X=D.v,J=" M"+U.applyToPointStringified(X[0][0],X[0][1]);for(G=1;G<F;G+=1)J+=" C"+U.applyToPointStringified(W[G-1][0],W[G-1][1])+" "+U.applyToPointStringified(Y[G][0],Y[G][1])+" "+U.applyToPointStringified(X[G][0],X[G][1]);return O&&F&&(J+=" C"+U.applyToPointStringified(W[G-1][0],W[G-1][1])+" "+U.applyToPointStringified(Y[0][0],Y[0][1])+" "+U.applyToPointStringified(X[0][0],X[0][1])+"z"),J},SVGElementsRenderer=function(){var D=new Matrix,F=new Matrix;function O(D,F,O){(O||F.transform.op._mdf)&&F.transform.container.setAttribute("opacity",F.transform.op.v),(O||F.transform.mProps._mdf)&&F.transform.container.setAttribute("transform",F.transform.mProps.v.to2dCSS())}function U(){}function G(O,U,G){var W,Y,X,J,K,Z,Q,tt,te,tr,ts=U.styles.length,tn=U.lvl;for(Z=0;Z<ts;Z+=1){if(J=U.sh._mdf||G,U.styles[Z].lvl<tn){for(tt=F.reset(),te=tn-U.styles[Z].lvl,tr=U.transformers.length-1;!J&&te>0;)J=U.transformers[tr].mProps._mdf||J,te-=1,tr-=1;if(J)for(te=tn-U.styles[Z].lvl,tr=U.transformers.length-1;te>0;)tt.multiply(U.transformers[tr].mProps.v),te-=1,tr-=1}else tt=D;if(Y=(Q=U.sh.paths)._length,J){for(W=0,X="";W<Y;W+=1)(K=Q.shapes[W])&&K._length&&(X+=buildShapeString(K,K._length,K.c,tt));U.caches[Z]=X}else X=U.caches[Z];U.styles[Z].d+=!0===O.hd?"":X,U.styles[Z]._mdf=J||U.styles[Z]._mdf}}function W(D,F,O){var U=F.style;(F.c._mdf||O)&&U.pElem.setAttribute("fill","rgb("+bmFloor(F.c.v[0])+","+bmFloor(F.c.v[1])+","+bmFloor(F.c.v[2])+")"),(F.o._mdf||O)&&U.pElem.setAttribute("fill-opacity",F.o.v)}function Y(D,F,O){X(D,F,O),J(D,F,O)}function X(D,F,O){var U,G,W,Y,X,J=F.gf,K=F.g._hasOpacity,Z=F.s.v,Q=F.e.v;if(F.o._mdf||O){var tt="gf"===D.ty?"fill-opacity":"stroke-opacity";F.style.pElem.setAttribute(tt,F.o.v)}if(F.s._mdf||O){var te=1===D.t?"x1":"cx",tr="x1"===te?"y1":"cy";J.setAttribute(te,Z[0]),J.setAttribute(tr,Z[1]),K&&!F.g._collapsable&&(F.of.setAttribute(te,Z[0]),F.of.setAttribute(tr,Z[1]))}if(F.g._cmdf||O){U=F.cst;var ts=F.g.c;for(G=0,W=U.length;G<W;G+=1)(Y=U[G]).setAttribute("offset",ts[4*G]+"%"),Y.setAttribute("stop-color","rgb("+ts[4*G+1]+","+ts[4*G+2]+","+ts[4*G+3]+")")}if(K&&(F.g._omdf||O)){var tn=F.g.o;for(G=0,W=(U=F.g._collapsable?F.cst:F.ost).length;G<W;G+=1)Y=U[G],F.g._collapsable||Y.setAttribute("offset",tn[2*G]+"%"),Y.setAttribute("stop-opacity",tn[2*G+1])}if(1===D.t)(F.e._mdf||O)&&(J.setAttribute("x2",Q[0]),J.setAttribute("y2",Q[1]),K&&!F.g._collapsable&&(F.of.setAttribute("x2",Q[0]),F.of.setAttribute("y2",Q[1])));else if((F.s._mdf||F.e._mdf||O)&&(X=Math.sqrt(Math.pow(Z[0]-Q[0],2)+Math.pow(Z[1]-Q[1],2)),J.setAttribute("r",X),K&&!F.g._collapsable&&F.of.setAttribute("r",X)),F.e._mdf||F.h._mdf||F.a._mdf||O){X||(X=Math.sqrt(Math.pow(Z[0]-Q[0],2)+Math.pow(Z[1]-Q[1],2)));var ta=Math.atan2(Q[1]-Z[1],Q[0]-Z[0]),th=F.h.v;th>=1?th=.99:th<=-1&&(th=-.99);var tp=X*th,tf=Math.cos(ta+F.a.v)*tp+Z[0],tu=Math.sin(ta+F.a.v)*tp+Z[1];J.setAttribute("fx",tf),J.setAttribute("fy",tu),K&&!F.g._collapsable&&(F.of.setAttribute("fx",tf),F.of.setAttribute("fy",tu))}}function J(D,F,O){var U=F.style,G=F.d;G&&(G._mdf||O)&&G.dashStr&&(U.pElem.setAttribute("stroke-dasharray",G.dashStr),U.pElem.setAttribute("stroke-dashoffset",G.dashoffset[0])),F.c&&(F.c._mdf||O)&&U.pElem.setAttribute("stroke","rgb("+bmFloor(F.c.v[0])+","+bmFloor(F.c.v[1])+","+bmFloor(F.c.v[2])+")"),(F.o._mdf||O)&&U.pElem.setAttribute("stroke-opacity",F.o.v),(F.w._mdf||O)&&(U.pElem.setAttribute("stroke-width",F.w.v),U.msElem&&U.msElem.setAttribute("stroke-width",F.w.v))}return{createRenderFunction:function(D){switch(D.ty){case"fl":return W;case"gf":return X;case"gs":return Y;case"st":return J;case"sh":case"el":case"rc":case"sr":return G;case"tr":return O;case"no":return U;default:return null}}}}();function SVGShapeElement(D,F,O){this.shapes=[],this.shapesData=D.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.initElement(D,F,O),this.prevViewData=[]}function LetterProps(D,F,O,U,G,W){this.o=D,this.sw=F,this.sc=O,this.fc=U,this.m=G,this.p=W,this._mdf={o:!0,sw:!!F,sc:!!O,fc:!!U,m:!0,p:!0}}function TextProperty(D,F){this._frameId=initialDefaultFrame,this.pv="",this.v="",this.kf=!1,this._isFirstFrame=!0,this._mdf=!1,F.d&&F.d.sid&&(F.d=D.globalData.slotManager.getProp(F.d)),this.data=F,this.elem=D,this.comp=this.elem.comp,this.keysIndex=0,this.canResize=!1,this.minimumFontSize=1,this.effectsSequence=[],this.currentData={ascent:0,boxWidth:this.defaultBoxWidth,f:"",fStyle:"",fWeight:"",fc:"",j:"",justifyOffset:"",l:[],lh:0,lineWidths:[],ls:"",of:"",s:"",sc:"",sw:0,t:0,tr:0,sz:0,ps:null,fillColorAnim:!1,strokeColorAnim:!1,strokeWidthAnim:!1,yOffset:0,finalSize:0,finalText:[],finalLineHeight:0,__complete:!1},this.copyData(this.currentData,this.data.d.k[0].s),this.searchProperty()||this.completeTextData(this.currentData)}extendPrototype([BaseElement,TransformElement,SVGBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableDOMElement],SVGShapeElement),SVGShapeElement.prototype.initSecondaryElement=function(){},SVGShapeElement.prototype.identityMatrix=new Matrix,SVGShapeElement.prototype.buildExpressionInterface=function(){},SVGShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes()},SVGShapeElement.prototype.filterUniqueShapes=function(){var D,F,O,U,G=this.shapes.length,W=this.stylesList.length,Y=[],X=!1;for(O=0;O<W;O+=1){for(D=0,U=this.stylesList[O],X=!1,Y.length=0;D<G;D+=1)-1!==(F=this.shapes[D]).styles.indexOf(U)&&(Y.push(F),X=F._isAnimated||X);Y.length>1&&X&&this.setShapesAsAnimated(Y)}},SVGShapeElement.prototype.setShapesAsAnimated=function(D){var F,O=D.length;for(F=0;F<O;F+=1)D[F].setAsAnimated()},SVGShapeElement.prototype.createStyleElement=function(D,F){var O,U=new SVGStyleData(D,F),G=U.pElem;return"st"===D.ty?O=new SVGStrokeStyleData(this,D,U):"fl"===D.ty?O=new SVGFillStyleData(this,D,U):"gf"===D.ty||"gs"===D.ty?(O=new("gf"===D.ty?SVGGradientFillStyleData:SVGGradientStrokeStyleData)(this,D,U),this.globalData.defs.appendChild(O.gf),O.maskId&&(this.globalData.defs.appendChild(O.ms),this.globalData.defs.appendChild(O.of),G.setAttribute("mask","url("+getLocationHref()+"#"+O.maskId+")"))):"no"===D.ty&&(O=new SVGNoStyleData(this,D,U)),("st"===D.ty||"gs"===D.ty)&&(G.setAttribute("stroke-linecap",lineCapEnum[D.lc||2]),G.setAttribute("stroke-linejoin",lineJoinEnum[D.lj||2]),G.setAttribute("fill-opacity","0"),1===D.lj&&G.setAttribute("stroke-miterlimit",D.ml)),2===D.r&&G.setAttribute("fill-rule","evenodd"),D.ln&&G.setAttribute("id",D.ln),D.cl&&G.setAttribute("class",D.cl),D.bm&&(G.style["mix-blend-mode"]=getBlendMode(D.bm)),this.stylesList.push(U),this.addToAnimatedContents(D,O),O},SVGShapeElement.prototype.createGroupElement=function(D){var F=new ShapeGroupData;return D.ln&&F.gr.setAttribute("id",D.ln),D.cl&&F.gr.setAttribute("class",D.cl),D.bm&&(F.gr.style["mix-blend-mode"]=getBlendMode(D.bm)),F},SVGShapeElement.prototype.createTransformElement=function(D,F){var O=TransformPropertyFactory.getTransformProperty(this,D,this),U=new SVGTransformData(O,O.o,F);return this.addToAnimatedContents(D,U),U},SVGShapeElement.prototype.createShapeElement=function(D,F,O){var U=4;"rc"===D.ty?U=5:"el"===D.ty?U=6:"sr"===D.ty&&(U=7);var G=ShapePropertyFactory.getShapeProp(this,D,U,this),W=new SVGShapeData(F,O,G);return this.shapes.push(W),this.addShapeToModifiers(W),this.addToAnimatedContents(D,W),W},SVGShapeElement.prototype.addToAnimatedContents=function(D,F){for(var O=0,U=this.animatedContents.length;O<U;){if(this.animatedContents[O].element===F)return;O+=1}this.animatedContents.push({fn:SVGElementsRenderer.createRenderFunction(D),element:F,data:D})},SVGShapeElement.prototype.setElementStyles=function(D){var F,O=D.styles,U=this.stylesList.length;for(F=0;F<U;F+=1)this.stylesList[F].closed||O.push(this.stylesList[F])},SVGShapeElement.prototype.reloadShapes=function(){this._isFirstFrame=!0;var D,F=this.itemsData.length;for(D=0;D<F;D+=1)this.prevViewData[D]=this.itemsData[D];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes(),F=this.dynamicProperties.length,D=0;D<F;D+=1)this.dynamicProperties[D].getValue();this.renderModifiers()},SVGShapeElement.prototype.searchShapes=function(D,F,O,U,G,W,Y){var X,J,K,Z,Q,tt,te=[].concat(W),tr=D.length-1,ts=[],tn=[];for(X=tr;X>=0;X-=1){if((tt=this.searchProcessedElement(D[X]))?F[X]=O[tt-1]:D[X]._render=Y,"fl"===D[X].ty||"st"===D[X].ty||"gf"===D[X].ty||"gs"===D[X].ty||"no"===D[X].ty)tt?F[X].style.closed=!1:F[X]=this.createStyleElement(D[X],G),D[X]._render&&F[X].style.pElem.parentNode!==U&&U.appendChild(F[X].style.pElem),ts.push(F[X].style);else if("gr"===D[X].ty){if(tt)for(J=0,K=F[X].it.length;J<K;J+=1)F[X].prevViewData[J]=F[X].it[J];else F[X]=this.createGroupElement(D[X]);this.searchShapes(D[X].it,F[X].it,F[X].prevViewData,F[X].gr,G+1,te,Y),D[X]._render&&F[X].gr.parentNode!==U&&U.appendChild(F[X].gr)}else"tr"===D[X].ty?(tt||(F[X]=this.createTransformElement(D[X],U)),Z=F[X].transform,te.push(Z)):"sh"===D[X].ty||"rc"===D[X].ty||"el"===D[X].ty||"sr"===D[X].ty?(tt||(F[X]=this.createShapeElement(D[X],te,G)),this.setElementStyles(F[X])):"tm"===D[X].ty||"rd"===D[X].ty||"ms"===D[X].ty||"pb"===D[X].ty||"zz"===D[X].ty||"op"===D[X].ty?(tt?(Q=F[X]).closed=!1:((Q=ShapeModifiers.getModifier(D[X].ty)).init(this,D[X]),F[X]=Q,this.shapeModifiers.push(Q)),tn.push(Q)):"rp"===D[X].ty&&(tt?(Q=F[X]).closed=!0:(Q=ShapeModifiers.getModifier(D[X].ty),F[X]=Q,Q.init(this,D,X,F),this.shapeModifiers.push(Q),Y=!1),tn.push(Q));this.addProcessedElement(D[X],X+1)}for(X=0,tr=ts.length;X<tr;X+=1)ts[X].closed=!0;for(X=0,tr=tn.length;X<tr;X+=1)tn[X].closed=!0},SVGShapeElement.prototype.renderInnerContent=function(){this.renderModifiers();var D,F=this.stylesList.length;for(D=0;D<F;D+=1)this.stylesList[D].reset();for(this.renderShape(),D=0;D<F;D+=1)(this.stylesList[D]._mdf||this._isFirstFrame)&&(this.stylesList[D].msElem&&(this.stylesList[D].msElem.setAttribute("d",this.stylesList[D].d),this.stylesList[D].d="M0 0"+this.stylesList[D].d),this.stylesList[D].pElem.setAttribute("d",this.stylesList[D].d||"M0 0"))},SVGShapeElement.prototype.renderShape=function(){var D,F,O=this.animatedContents.length;for(D=0;D<O;D+=1)F=this.animatedContents[D],(this._isFirstFrame||F.element._isAnimated)&&!0!==F.data&&F.fn(F.data,F.element,this._isFirstFrame)},SVGShapeElement.prototype.destroy=function(){this.destroyBaseElement(),this.shapesData=null,this.itemsData=null},LetterProps.prototype.update=function(D,F,O,U,G,W){this._mdf.o=!1,this._mdf.sw=!1,this._mdf.sc=!1,this._mdf.fc=!1,this._mdf.m=!1,this._mdf.p=!1;var Y=!1;return this.o!==D&&(this.o=D,this._mdf.o=!0,Y=!0),this.sw!==F&&(this.sw=F,this._mdf.sw=!0,Y=!0),this.sc!==O&&(this.sc=O,this._mdf.sc=!0,Y=!0),this.fc!==U&&(this.fc=U,this._mdf.fc=!0,Y=!0),this.m!==G&&(this.m=G,this._mdf.m=!0,Y=!0),W.length&&(this.p[0]!==W[0]||this.p[1]!==W[1]||this.p[4]!==W[4]||this.p[5]!==W[5]||this.p[12]!==W[12]||this.p[13]!==W[13])&&(this.p=W,this._mdf.p=!0,Y=!0),Y},TextProperty.prototype.defaultBoxWidth=[0,0],TextProperty.prototype.copyData=function(D,F){for(var O in F)Object.prototype.hasOwnProperty.call(F,O)&&(D[O]=F[O]);return D},TextProperty.prototype.setCurrentData=function(D){D.__complete||this.completeTextData(D),this.currentData=D,this.currentData.boxWidth=this.currentData.boxWidth||this.defaultBoxWidth,this._mdf=!0},TextProperty.prototype.searchProperty=function(){return this.searchKeyframes()},TextProperty.prototype.searchKeyframes=function(){return this.kf=this.data.d.k.length>1,this.kf&&this.addEffect(this.getKeyframeValue.bind(this)),this.kf},TextProperty.prototype.addEffect=function(D){this.effectsSequence.push(D),this.elem.addDynamicProperty(this)},TextProperty.prototype.getValue=function(D){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length||D){this.currentData.t=this.data.d.k[this.keysIndex].s.t;var F,O=this.currentData,U=this.keysIndex;if(this.lock){this.setCurrentData(this.currentData);return}this.lock=!0,this._mdf=!1;var G=this.effectsSequence.length,W=D||this.data.d.k[this.keysIndex].s;for(F=0;F<G;F+=1)W=U!==this.keysIndex?this.effectsSequence[F](W,W.t):this.effectsSequence[F](this.currentData,W.t);O!==W&&this.setCurrentData(W),this.v=this.currentData,this.pv=this.v,this.lock=!1,this.frameId=this.elem.globalData.frameId}},TextProperty.prototype.getKeyframeValue=function(){for(var D=this.data.d.k,F=this.elem.comp.renderedFrame,O=0,U=D.length;O<=U-1&&O!==U-1&&!(D[O+1].t>F);)O+=1;return this.keysIndex!==O&&(this.keysIndex=O),this.data.d.k[this.keysIndex].s},TextProperty.prototype.buildFinalText=function(D){for(var F,O,U=[],G=0,W=D.length,Y=!1,X=!1,J="";G<W;)Y=X,X=!1,F=D.charCodeAt(G),J=D.charAt(G),FontManager.isCombinedCharacter(F)?Y=!0:F>=55296&&F<=56319?FontManager.isRegionalFlag(D,G)?J=D.substr(G,14):(O=D.charCodeAt(G+1))>=56320&&O<=57343&&(FontManager.isModifier(F,O)?(J=D.substr(G,2),Y=!0):J=FontManager.isFlagEmoji(D.substr(G,4))?D.substr(G,4):D.substr(G,2)):F>56319?(O=D.charCodeAt(G+1),FontManager.isVariationSelector(F)&&(Y=!0)):FontManager.isZeroWidthJoiner(F)&&(Y=!0,X=!0),Y?(U[U.length-1]+=J,Y=!1):U.push(J),G+=J.length;return U},TextProperty.prototype.completeTextData=function(D){D.__complete=!0;var F=this.elem.globalData.fontManager,O=this.data,U=[],G=0,W=O.m.g,Y=0,X=0,J=0,K=[],Z=0,Q=0,tt=F.getFontByName(D.f),te=0,tr=getFontProperties(tt);D.fWeight=tr.weight,D.fStyle=tr.style,D.finalSize=D.s,D.finalText=this.buildFinalText(D.t),ta=D.finalText.length,D.finalLineHeight=D.lh;var ts=D.tr/1e3*D.finalSize;if(D.sz)for(var tn,ta,th,tp,tf,tu,tc,tm,tg,tv,tb=!0,t_=D.sz[0],tk=D.sz[1];tb;){tv=this.buildFinalText(D.t),tg=0,Z=0,ta=tv.length,ts=D.tr/1e3*D.finalSize;var tw=-1;for(tn=0;tn<ta;tn+=1)tm=tv[tn].charCodeAt(0),th=!1," "===tv[tn]?tw=tn:(13===tm||3===tm)&&(Z=0,th=!0,tg+=D.finalLineHeight||1.2*D.finalSize),F.chars?(tc=F.getCharData(tv[tn],tt.fStyle,tt.fFamily),te=th?0:tc.w*D.finalSize/100):te=F.measureText(tv[tn],D.f,D.finalSize),Z+te>t_&&" "!==tv[tn]?(-1===tw?ta+=1:tn=tw,tg+=D.finalLineHeight||1.2*D.finalSize,tv.splice(tn,tw===tn?1:0,"\r"),tw=-1,Z=0):Z+=te+ts;tg+=tt.ascent*D.finalSize/100,this.canResize&&D.finalSize>this.minimumFontSize&&tk<tg?(D.finalSize-=1,D.finalLineHeight=D.finalSize*D.lh/D.s):(D.finalText=tv,ta=D.finalText.length,tb=!1)}Z=-ts,te=0;var tA=0;for(tn=0;tn<ta;tn+=1)if(th=!1,13===(tm=(tS=D.finalText[tn]).charCodeAt(0))||3===tm?(tA=0,K.push(Z),Q=Z>Q?Z:Q,Z=-2*ts,tp="",th=!0,J+=1):tp=tS,F.chars?(tc=F.getCharData(tS,tt.fStyle,F.getFontByName(D.f).fFamily),te=th?0:tc.w*D.finalSize/100):te=F.measureText(tp,D.f,D.finalSize)," "===tS?tA+=te+ts:(Z+=te+ts+tA,tA=0),U.push({l:te,an:te,add:Y,n:th,anIndexes:[],val:tp,line:J,animatorJustifyOffset:0}),2==W){if(Y+=te,""===tp||" "===tp||tn===ta-1){for((""===tp||" "===tp)&&(Y-=te);X<=tn;)U[X].an=Y,U[X].ind=G,U[X].extra=te,X+=1;G+=1,Y=0}}else if(3==W){if(Y+=te,""===tp||tn===ta-1){for(""===tp&&(Y-=te);X<=tn;)U[X].an=Y,U[X].ind=G,U[X].extra=te,X+=1;Y=0,G+=1}}else U[G].ind=G,U[G].extra=0,G+=1;if(D.l=U,Q=Z>Q?Z:Q,K.push(Z),D.sz)D.boxWidth=D.sz[0],D.justifyOffset=0;else switch(D.boxWidth=Q,D.j){case 1:D.justifyOffset=-D.boxWidth;break;case 2:D.justifyOffset=-D.boxWidth/2;break;default:D.justifyOffset=0}D.lineWidths=K;var tP=O.a;tu=tP.length;var tC=[];for(tf=0;tf<tu;tf+=1){for((tE=tP[tf]).a.sc&&(D.strokeColorAnim=!0),tE.a.sw&&(D.strokeWidthAnim=!0),(tE.a.fc||tE.a.fh||tE.a.fs||tE.a.fb)&&(D.fillColorAnim=!0),tM=0,tD=tE.s.b,tn=0;tn<ta;tn+=1)(tT=U[tn]).anIndexes[tf]=tM,(1==tD&&""!==tT.val||2==tD&&""!==tT.val&&" "!==tT.val||3==tD&&(tT.n||" "==tT.val||tn==ta-1)||4==tD&&(tT.n||tn==ta-1))&&(1===tE.s.rn&&tC.push(tM),tM+=1);O.a[tf].s.totalChars=tM;var tS,tE,tT,tD,tM,tF,tI=-1;if(1===tE.s.rn)for(tn=0;tn<ta;tn+=1)tI!=(tT=U[tn]).anIndexes[tf]&&(tI=tT.anIndexes[tf],tF=tC.splice(Math.floor(Math.random()*tC.length),1)[0]),tT.anIndexes[tf]=tF}D.yOffset=D.finalLineHeight||1.2*D.finalSize,D.ls=D.ls||0,D.ascent=tt.ascent*D.finalSize/100},TextProperty.prototype.updateDocumentData=function(D,F){F=void 0===F?this.keysIndex:F;var O=this.copyData({},this.data.d.k[F].s);O=this.copyData(O,D),this.data.d.k[F].s=O,this.recalculate(F),this.setCurrentData(O),this.elem.addDynamicProperty(this)},TextProperty.prototype.recalculate=function(D){var F=this.data.d.k[D].s;F.__complete=!1,this.keysIndex=0,this._isFirstFrame=!0,this.getValue(F)},TextProperty.prototype.canResizeFont=function(D){this.canResize=D,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)},TextProperty.prototype.setMinimumFontSize=function(D){this.minimumFontSize=Math.floor(D)||1,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)};var TextSelectorProp=function(){var D=Math.max,F=Math.min,O=Math.floor;function U(D,F){this._currentTextLength=-1,this.k=!1,this.data=F,this.elem=D,this.comp=D.comp,this.finalS=0,this.finalE=0,this.initDynamicPropertyContainer(D),this.s=PropertyFactory.getProp(D,F.s||{k:0},0,0,this),"e"in F?this.e=PropertyFactory.getProp(D,F.e,0,0,this):this.e={v:100},this.o=PropertyFactory.getProp(D,F.o||{k:0},0,0,this),this.xe=PropertyFactory.getProp(D,F.xe||{k:0},0,0,this),this.ne=PropertyFactory.getProp(D,F.ne||{k:0},0,0,this),this.sm=PropertyFactory.getProp(D,F.sm||{k:100},0,0,this),this.a=PropertyFactory.getProp(D,F.a,0,.01,this),this.dynamicProperties.length||this.getValue()}return U.prototype={getMult:function(U){this._currentTextLength!==this.elem.textProperty.currentData.l.length&&this.getValue();var G=0,W=0,Y=1,X=1;this.ne.v>0?G=this.ne.v/100:W=-this.ne.v/100,this.xe.v>0?Y=1-this.xe.v/100:X=1+this.xe.v/100;var J=BezierFactory.getBezierEasing(G,W,Y,X).get,K=0,Z=this.finalS,Q=this.finalE,tt=this.data.sh;if(2===tt)K=J(K=Q===Z?U>=Q?1:0:D(0,F(.5/(Q-Z)+(U-Z)/(Q-Z),1)));else if(3===tt)K=J(K=Q===Z?U>=Q?0:1:1-D(0,F(.5/(Q-Z)+(U-Z)/(Q-Z),1)));else if(4===tt)Q===Z?K=0:(K=D(0,F(.5/(Q-Z)+(U-Z)/(Q-Z),1)))<.5?K*=2:K=1-2*(K-.5),K=J(K);else if(5===tt){if(Q===Z)K=0;else{var te=Q-Z,tr=-te/2+(U=F(D(0,U+.5-Z),Q-Z)),ts=te/2;K=Math.sqrt(1-tr*tr/(ts*ts))}K=J(K)}else 6===tt?K=J(K=Q===Z?0:(1+Math.cos(Math.PI+2*Math.PI*(U=F(D(0,U+.5-Z),Q-Z))/(Q-Z)))/2):(U>=O(Z)&&(K=U-Z<0?D(0,F(F(Q,1)-(Z-U),1)):D(0,F(Q-U,1))),K=J(K));if(100!==this.sm.v){var tn=.01*this.sm.v;0===tn&&(tn=1e-8);var ta=.5-.5*tn;K<ta?K=0:(K=(K-ta)/tn)>1&&(K=1)}return K*this.a.v},getValue:function(D){this.iterateDynamicProperties(),this._mdf=D||this._mdf,this._currentTextLength=this.elem.textProperty.currentData.l.length||0,D&&2===this.data.r&&(this.e.v=this._currentTextLength);var F=2===this.data.r?1:100/this.data.totalChars,O=this.o.v/F,U=this.s.v/F+O,G=this.e.v/F+O;if(U>G){var W=U;U=G,G=W}this.finalS=U,this.finalE=G}},extendPrototype([DynamicPropertyContainer],U),{getTextSelectorProp:function(D,F,O){return new U(D,F)}}}();function TextAnimatorDataProperty(D,F,O){var U={propType:!1},G=PropertyFactory.getProp,W=F.a;this.a={r:W.r?G(D,W.r,0,degToRads,O):U,rx:W.rx?G(D,W.rx,0,degToRads,O):U,ry:W.ry?G(D,W.ry,0,degToRads,O):U,sk:W.sk?G(D,W.sk,0,degToRads,O):U,sa:W.sa?G(D,W.sa,0,degToRads,O):U,s:W.s?G(D,W.s,1,.01,O):U,a:W.a?G(D,W.a,1,0,O):U,o:W.o?G(D,W.o,0,.01,O):U,p:W.p?G(D,W.p,1,0,O):U,sw:W.sw?G(D,W.sw,0,0,O):U,sc:W.sc?G(D,W.sc,1,0,O):U,fc:W.fc?G(D,W.fc,1,0,O):U,fh:W.fh?G(D,W.fh,0,0,O):U,fs:W.fs?G(D,W.fs,0,.01,O):U,fb:W.fb?G(D,W.fb,0,.01,O):U,t:W.t?G(D,W.t,0,0,O):U},this.s=TextSelectorProp.getTextSelectorProp(D,F.s,O),this.s.t=F.s.t}function TextAnimatorProperty(D,F,O){this._isFirstFrame=!0,this._hasMaskedPath=!1,this._frameId=-1,this._textData=D,this._renderType=F,this._elem=O,this._animatorsData=createSizedArray(this._textData.a.length),this._pathData={},this._moreOptions={alignment:{}},this.renderedLetters=[],this.lettersChangedFlag=!1,this.initDynamicPropertyContainer(O)}function ITextElement(){}TextAnimatorProperty.prototype.searchProperties=function(){var D,F,O=this._textData.a.length,U=PropertyFactory.getProp;for(D=0;D<O;D+=1)F=this._textData.a[D],this._animatorsData[D]=new TextAnimatorDataProperty(this._elem,F,this);this._textData.p&&"m"in this._textData.p?(this._pathData={a:U(this._elem,this._textData.p.a,0,0,this),f:U(this._elem,this._textData.p.f,0,0,this),l:U(this._elem,this._textData.p.l,0,0,this),r:U(this._elem,this._textData.p.r,0,0,this),p:U(this._elem,this._textData.p.p,0,0,this),m:this._elem.maskManager.getMaskProperty(this._textData.p.m)},this._hasMaskedPath=!0):this._hasMaskedPath=!1,this._moreOptions.alignment=U(this._elem,this._textData.m.a,1,0,this)},TextAnimatorProperty.prototype.getMeasures=function(D,F){if(this.lettersChangedFlag=F,this._mdf||this._isFirstFrame||F||this._hasMaskedPath&&this._pathData.m._mdf){this._isFirstFrame=!1;var O,U,G,W,Y,X,J,K,Z,Q,tt,te,tr,ts,tn,ta,th,tp,tf=this._moreOptions.alignment.v,tu=this._animatorsData,tc=this._textData,tm=this.mHelper,tg=this._renderType,tv=this.renderedLetters.length,tb=D.l;if(this._hasMaskedPath){if(tV=this._pathData.m,!this._pathData.n||this._pathData._mdf){var t_,tk,tw,tA,tP,tC,tS,tE,tT,tD,tM,tF,tI,tL,tB,tO,tR,tV,tN,t$=tV.v;for(this._pathData.r.v&&(t$=t$.reverse()),tP={tLength:0,segments:[]},tA=t$._length-1,tO=0,tw=0;tw<tA;tw+=1)tN=bez.buildBezierData(t$.v[tw],t$.v[tw+1],[t$.o[tw][0]-t$.v[tw][0],t$.o[tw][1]-t$.v[tw][1]],[t$.i[tw+1][0]-t$.v[tw+1][0],t$.i[tw+1][1]-t$.v[tw+1][1]]),tP.tLength+=tN.segmentLength,tP.segments.push(tN),tO+=tN.segmentLength;tw=tA,tV.v.c&&(tN=bez.buildBezierData(t$.v[tw],t$.v[0],[t$.o[tw][0]-t$.v[tw][0],t$.o[tw][1]-t$.v[tw][1]],[t$.i[0][0]-t$.v[0][0],t$.i[0][1]-t$.v[0][1]]),tP.tLength+=tN.segmentLength,tP.segments.push(tN),tO+=tN.segmentLength),this._pathData.pi=tP}if(tP=this._pathData.pi,tC=this._pathData.f.v,tM=0,tD=1,tE=0,tT=!0,tL=tP.segments,tC<0&&tV.v.c)for(tP.tLength<Math.abs(tC)&&(tC=-Math.abs(tC)%tP.tLength),tM=tL.length-1,tD=(tI=tL[tM].points).length-1;tC<0;)tC+=tI[tD].partialLength,(tD-=1)<0&&(tM-=1,tD=(tI=tL[tM].points).length-1);tF=(tI=tL[tM].points)[tD-1],tB=(tS=tI[tD]).partialLength}tA=tb.length,t_=0,tk=0;var tj=1.2*D.finalSize*.714,tU=!0;W=tu.length;var tG=-1,tq=tC,tW=tM,tH=tD,tY=-1,tX="",tJ=this.defaultPropsArray;if(2===D.j||1===D.j){var tK=0,tZ=0,tQ=2===D.j?-.5:-1,t3=0,t5=!0;for(tw=0;tw<tA;tw+=1)if(tb[tw].n){for(tK&&(tK+=tZ);t3<tw;)tb[t3].animatorJustifyOffset=tK,t3+=1;tK=0,t5=!0}else{for(G=0;G<W;G+=1)(O=tu[G].a).t.propType&&(t5&&2===D.j&&(tZ+=O.t.v*tQ),(X=(U=tu[G].s).getMult(tb[tw].anIndexes[G],tc.a[G].s.totalChars)).length?tK+=O.t.v*X[0]*tQ:tK+=O.t.v*X*tQ);t5=!1}for(tK&&(tK+=tZ);t3<tw;)tb[t3].animatorJustifyOffset=tK,t3+=1}for(tw=0;tw<tA;tw+=1){if(tm.reset(),Q=1,tb[tw].n)t_=0,tk+=D.yOffset+(tU?1:0),tC=tq,tU=!1,this._hasMaskedPath&&(tM=tW,tD=tH,tF=(tI=tL[tM].points)[tD-1],tB=(tS=tI[tD]).partialLength,tE=0),tX="",th="",tn="",tp="",tJ=this.defaultPropsArray;else{if(this._hasMaskedPath){if(tY!==tb[tw].line){switch(D.j){case 1:tC+=tO-D.lineWidths[tb[tw].line];break;case 2:tC+=(tO-D.lineWidths[tb[tw].line])/2}tY=tb[tw].line}tG!==tb[tw].ind&&(tb[tG]&&(tC+=tb[tG].extra),tC+=tb[tw].an/2,tG=tb[tw].ind),tC+=tf[0]*tb[tw].an*.005;var t4=0;for(G=0;G<W;G+=1)(O=tu[G].a).p.propType&&((X=(U=tu[G].s).getMult(tb[tw].anIndexes[G],tc.a[G].s.totalChars)).length?t4+=O.p.v[0]*X[0]:t4+=O.p.v[0]*X),O.a.propType&&((X=(U=tu[G].s).getMult(tb[tw].anIndexes[G],tc.a[G].s.totalChars)).length?t4+=O.a.v[0]*X[0]:t4+=O.a.v[0]*X);for(tT=!0,this._pathData.a.v&&(tC=.5*tb[0].an+(tO-this._pathData.f.v-.5*tb[0].an-.5*tb[tb.length-1].an)*tG/(tA-1)+this._pathData.f.v);tT;)tE+tB>=tC+t4||!tI?(tR=(tC+t4-tE)/tS.partialLength,K=tF.point[0]+(tS.point[0]-tF.point[0])*tR,Z=tF.point[1]+(tS.point[1]-tF.point[1])*tR,tm.translate(-tf[0]*tb[tw].an*.005,-(.01*(tf[1]*tj))),tT=!1):tI&&(tE+=tS.partialLength,(tD+=1)>=tI.length&&(tD=0,tL[tM+=1]?tI=tL[tM].points:tV.v.c?(tD=0,tI=tL[tM=0].points):(tE-=tS.partialLength,tI=null)),tI&&(tF=tS,tB=(tS=tI[tD]).partialLength));J=tb[tw].an/2-tb[tw].add,tm.translate(-J,0,0)}else J=tb[tw].an/2-tb[tw].add,tm.translate(-J,0,0),tm.translate(-tf[0]*tb[tw].an*.005,-tf[1]*tj*.01,0);for(G=0;G<W;G+=1)(O=tu[G].a).t.propType&&(X=(U=tu[G].s).getMult(tb[tw].anIndexes[G],tc.a[G].s.totalChars),(0!==t_||0!==D.j)&&(this._hasMaskedPath?X.length?tC+=O.t.v*X[0]:tC+=O.t.v*X:X.length?t_+=O.t.v*X[0]:t_+=O.t.v*X));for(D.strokeWidthAnim&&(te=D.sw||0),D.strokeColorAnim&&(tt=D.sc?[D.sc[0],D.sc[1],D.sc[2]]:[0,0,0]),D.fillColorAnim&&D.fc&&(tr=[D.fc[0],D.fc[1],D.fc[2]]),G=0;G<W;G+=1)(O=tu[G].a).a.propType&&((X=(U=tu[G].s).getMult(tb[tw].anIndexes[G],tc.a[G].s.totalChars)).length?tm.translate(-O.a.v[0]*X[0],-O.a.v[1]*X[1],O.a.v[2]*X[2]):tm.translate(-O.a.v[0]*X,-O.a.v[1]*X,O.a.v[2]*X));for(G=0;G<W;G+=1)(O=tu[G].a).s.propType&&((X=(U=tu[G].s).getMult(tb[tw].anIndexes[G],tc.a[G].s.totalChars)).length?tm.scale(1+(O.s.v[0]-1)*X[0],1+(O.s.v[1]-1)*X[1],1):tm.scale(1+(O.s.v[0]-1)*X,1+(O.s.v[1]-1)*X,1));for(G=0;G<W;G+=1){if(O=tu[G].a,X=(U=tu[G].s).getMult(tb[tw].anIndexes[G],tc.a[G].s.totalChars),O.sk.propType&&(X.length?tm.skewFromAxis(-O.sk.v*X[0],O.sa.v*X[1]):tm.skewFromAxis(-O.sk.v*X,O.sa.v*X)),O.r.propType&&(X.length?tm.rotateZ(-O.r.v*X[2]):tm.rotateZ(-O.r.v*X)),O.ry.propType&&(X.length?tm.rotateY(O.ry.v*X[1]):tm.rotateY(O.ry.v*X)),O.rx.propType&&(X.length?tm.rotateX(O.rx.v*X[0]):tm.rotateX(O.rx.v*X)),O.o.propType&&(X.length?Q+=(O.o.v*X[0]-Q)*X[0]:Q+=(O.o.v*X-Q)*X),D.strokeWidthAnim&&O.sw.propType&&(X.length?te+=O.sw.v*X[0]:te+=O.sw.v*X),D.strokeColorAnim&&O.sc.propType)for(ts=0;ts<3;ts+=1)X.length?tt[ts]+=(O.sc.v[ts]-tt[ts])*X[0]:tt[ts]+=(O.sc.v[ts]-tt[ts])*X;if(D.fillColorAnim&&D.fc){if(O.fc.propType)for(ts=0;ts<3;ts+=1)X.length?tr[ts]+=(O.fc.v[ts]-tr[ts])*X[0]:tr[ts]+=(O.fc.v[ts]-tr[ts])*X;O.fh.propType&&(tr=X.length?addHueToRGB(tr,O.fh.v*X[0]):addHueToRGB(tr,O.fh.v*X)),O.fs.propType&&(tr=X.length?addSaturationToRGB(tr,O.fs.v*X[0]):addSaturationToRGB(tr,O.fs.v*X)),O.fb.propType&&(tr=X.length?addBrightnessToRGB(tr,O.fb.v*X[0]):addBrightnessToRGB(tr,O.fb.v*X))}}for(G=0;G<W;G+=1)(O=tu[G].a).p.propType&&(X=(U=tu[G].s).getMult(tb[tw].anIndexes[G],tc.a[G].s.totalChars),this._hasMaskedPath?X.length?tm.translate(0,O.p.v[1]*X[0],-O.p.v[2]*X[1]):tm.translate(0,O.p.v[1]*X,-O.p.v[2]*X):X.length?tm.translate(O.p.v[0]*X[0],O.p.v[1]*X[1],-O.p.v[2]*X[2]):tm.translate(O.p.v[0]*X,O.p.v[1]*X,-O.p.v[2]*X));if(D.strokeWidthAnim&&(tn=te<0?0:te),D.strokeColorAnim&&(ta="rgb("+Math.round(255*tt[0])+","+Math.round(255*tt[1])+","+Math.round(255*tt[2])+")"),D.fillColorAnim&&D.fc&&(th="rgb("+Math.round(255*tr[0])+","+Math.round(255*tr[1])+","+Math.round(255*tr[2])+")"),this._hasMaskedPath){if(tm.translate(0,-D.ls),tm.translate(0,tf[1]*tj*.01+tk,0),this._pathData.p.v){var t6=180*Math.atan((tS.point[1]-tF.point[1])/(tS.point[0]-tF.point[0]))/Math.PI;tS.point[0]<tF.point[0]&&(t6+=180),tm.rotate(-t6*Math.PI/180)}tm.translate(K,Z,0),tC-=tf[0]*tb[tw].an*.005,tb[tw+1]&&tG!==tb[tw+1].ind&&(tC+=tb[tw].an/2+.001*D.tr*D.finalSize)}else{switch(tm.translate(t_,tk,0),D.ps&&tm.translate(D.ps[0],D.ps[1]+D.ascent,0),D.j){case 1:tm.translate(tb[tw].animatorJustifyOffset+D.justifyOffset+(D.boxWidth-D.lineWidths[tb[tw].line]),0,0);break;case 2:tm.translate(tb[tw].animatorJustifyOffset+D.justifyOffset+(D.boxWidth-D.lineWidths[tb[tw].line])/2,0,0)}tm.translate(0,-D.ls),tm.translate(J,0,0),tm.translate(tf[0]*tb[tw].an*.005,tf[1]*tj*.01,0),t_+=tb[tw].l+.001*D.tr*D.finalSize}"html"===tg?tX=tm.toCSS():"svg"===tg?tX=tm.to2dCSS():tJ=[tm.props[0],tm.props[1],tm.props[2],tm.props[3],tm.props[4],tm.props[5],tm.props[6],tm.props[7],tm.props[8],tm.props[9],tm.props[10],tm.props[11],tm.props[12],tm.props[13],tm.props[14],tm.props[15]],tp=Q}tv<=tw?(Y=new LetterProps(tp,tn,ta,th,tX,tJ),this.renderedLetters.push(Y),tv+=1,this.lettersChangedFlag=!0):(Y=this.renderedLetters[tw],this.lettersChangedFlag=Y.update(tp,tn,ta,th,tX,tJ)||this.lettersChangedFlag)}}},TextAnimatorProperty.prototype.getValue=function(){this._elem.globalData.frameId!==this._frameId&&(this._frameId=this._elem.globalData.frameId,this.iterateDynamicProperties())},TextAnimatorProperty.prototype.mHelper=new Matrix,TextAnimatorProperty.prototype.defaultPropsArray=[],extendPrototype([DynamicPropertyContainer],TextAnimatorProperty),ITextElement.prototype.initElement=function(D,F,O){this.lettersChangedFlag=!0,this.initFrame(),this.initBaseData(D,F,O),this.textProperty=new TextProperty(this,D.t,this.dynamicProperties),this.textAnimator=new TextAnimatorProperty(D.t,this.renderType,this),this.initTransform(D,F,O),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide(),this.textAnimator.searchProperties(this.dynamicProperties)},ITextElement.prototype.prepareFrame=function(D){this._mdf=!1,this.prepareRenderableFrame(D),this.prepareProperties(D,this.isInRange)},ITextElement.prototype.createPathShape=function(D,F){var O,U,G=F.length,W="";for(O=0;O<G;O+=1)"sh"===F[O].ty&&(W+=buildShapeString(U=F[O].ks.k,U.i.length,!0,D));return W},ITextElement.prototype.updateDocumentData=function(D,F){this.textProperty.updateDocumentData(D,F)},ITextElement.prototype.canResizeFont=function(D){this.textProperty.canResizeFont(D)},ITextElement.prototype.setMinimumFontSize=function(D){this.textProperty.setMinimumFontSize(D)},ITextElement.prototype.applyTextPropertiesToMatrix=function(D,F,O,U,G){switch(D.ps&&F.translate(D.ps[0],D.ps[1]+D.ascent,0),F.translate(0,-D.ls,0),D.j){case 1:F.translate(D.justifyOffset+(D.boxWidth-D.lineWidths[O]),0,0);break;case 2:F.translate(D.justifyOffset+(D.boxWidth-D.lineWidths[O])/2,0,0)}F.translate(U,G,0)},ITextElement.prototype.buildColor=function(D){return"rgb("+Math.round(255*D[0])+","+Math.round(255*D[1])+","+Math.round(255*D[2])+")"},ITextElement.prototype.emptyProp=new LetterProps,ITextElement.prototype.destroy=function(){},ITextElement.prototype.validateText=function(){(this.textProperty._mdf||this.textProperty._isFirstFrame)&&(this.buildNewText(),this.textProperty._isFirstFrame=!1,this.textProperty._mdf=!1)};var emptyShapeData={shapes:[]};function SVGTextLottieElement(D,F,O){this.textSpans=[],this.renderType="svg",this.initElement(D,F,O)}function ISolidElement(D,F,O){this.initElement(D,F,O)}function NullElement(D,F,O){this.initFrame(),this.initBaseData(D,F,O),this.initFrame(),this.initTransform(D,F,O),this.initHierarchy()}function SVGRendererBase(){}function ICompElement(){}function SVGCompElement(D,F,O){this.layers=D.layers,this.supports3d=!0,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(D,F,O),this.tm=D.tm?PropertyFactory.getProp(this,D.tm,0,F.frameRate,this):{_placeholder:!0}}function SVGRenderer(D,F){this.animationItem=D,this.layers=null,this.renderedFrame=-1,this.svgElement=createNS("svg");var O="";if(F&&F.title){var U=createNS("title"),G=createElementID();U.setAttribute("id",G),U.textContent=F.title,this.svgElement.appendChild(U),O+=G}if(F&&F.description){var W=createNS("desc"),Y=createElementID();W.setAttribute("id",Y),W.textContent=F.description,this.svgElement.appendChild(W),O+=" "+Y}O&&this.svgElement.setAttribute("aria-labelledby",O);var X=createNS("defs");this.svgElement.appendChild(X);var J=createNS("g");this.svgElement.appendChild(J),this.layerElement=J,this.renderConfig={preserveAspectRatio:F&&F.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:F&&F.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:F&&F.contentVisibility||"visible",progressiveLoad:F&&F.progressiveLoad||!1,hideOnTransparent:!(F&&!1===F.hideOnTransparent),viewBoxOnly:F&&F.viewBoxOnly||!1,viewBoxSize:F&&F.viewBoxSize||!1,className:F&&F.className||"",id:F&&F.id||"",focusable:F&&F.focusable,filterSize:{width:F&&F.filterSize&&F.filterSize.width||"100%",height:F&&F.filterSize&&F.filterSize.height||"100%",x:F&&F.filterSize&&F.filterSize.x||"0%",y:F&&F.filterSize&&F.filterSize.y||"0%"},width:F&&F.width,height:F&&F.height,runExpressions:!F||void 0===F.runExpressions||F.runExpressions},this.globalData={_mdf:!1,frameNum:-1,defs:X,renderConfig:this.renderConfig},this.elements=[],this.pendingElements=[],this.destroyed=!1,this.rendererType="svg"}function ShapeTransformManager(){this.sequences={},this.sequenceList=[],this.transform_key_count=0}extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],SVGTextLottieElement),SVGTextLottieElement.prototype.createContent=function(){this.data.singleShape&&!this.globalData.fontManager.chars&&(this.textContainer=createNS("text"))},SVGTextLottieElement.prototype.buildTextContents=function(D){for(var F=0,O=D.length,U=[],G="";F<O;)"\r"===D[F]||"\x03"===D[F]?(U.push(G),G=""):G+=D[F],F+=1;return U.push(G),U},SVGTextLottieElement.prototype.buildShapeData=function(D,F){if(D.shapes&&D.shapes.length){var O=D.shapes[0];if(O.it){var U=O.it[O.it.length-1];U.s&&(U.s.k[0]=F,U.s.k[1]=F)}}return D},SVGTextLottieElement.prototype.buildNewText=function(){this.addDynamicProperty(this);var D=this.textProperty.currentData;this.renderedLetters=createSizedArray(D?D.l.length:0),D.fc?this.layerElement.setAttribute("fill",this.buildColor(D.fc)):this.layerElement.setAttribute("fill","rgba(0,0,0,0)"),D.sc&&(this.layerElement.setAttribute("stroke",this.buildColor(D.sc)),this.layerElement.setAttribute("stroke-width",D.sw)),this.layerElement.setAttribute("font-size",D.finalSize);var F=this.globalData.fontManager.getFontByName(D.f);if(F.fClass)this.layerElement.setAttribute("class",F.fClass);else{this.layerElement.setAttribute("font-family",F.fFamily);var O=D.fWeight,U=D.fStyle;this.layerElement.setAttribute("font-style",U),this.layerElement.setAttribute("font-weight",O)}this.layerElement.setAttribute("aria-label",D.t);var G=D.l||[],W=!!this.globalData.fontManager.chars;tn=G.length;var Y=this.mHelper,X="",J=this.data.singleShape,K=0,Z=0,Q=!0,tt=.001*D.tr*D.finalSize;if(!J||W||D.sz){var te=this.textSpans.length;for(ts=0;ts<tn;ts+=1){if(this.textSpans[ts]||(this.textSpans[ts]={span:null,childSpan:null,glyph:null}),!W||!J||0===ts){if(ta=te>ts?this.textSpans[ts].span:createNS(W?"g":"text"),te<=ts){if(ta.setAttribute("stroke-linecap","butt"),ta.setAttribute("stroke-linejoin","round"),ta.setAttribute("stroke-miterlimit","4"),this.textSpans[ts].span=ta,W){var tr=createNS("g");ta.appendChild(tr),this.textSpans[ts].childSpan=tr}this.textSpans[ts].span=ta,this.layerElement.appendChild(ta)}ta.style.display="inherit"}if(Y.reset(),J&&(G[ts].n&&(K=-tt,Z+=D.yOffset+(Q?1:0),Q=!1),this.applyTextPropertiesToMatrix(D,Y,G[ts].line,K,Z),K+=(G[ts].l||0)+tt),W){if(1===(th=this.globalData.fontManager.getCharData(D.finalText[ts],F.fStyle,this.globalData.fontManager.getFontByName(D.f).fFamily)).t)tp=new SVGCompElement(th.data,this.globalData,this);else{var ts,tn,ta,th,tp,tf=emptyShapeData;th.data&&th.data.shapes&&(tf=this.buildShapeData(th.data,D.finalSize)),tp=new SVGShapeElement(tf,this.globalData,this)}if(this.textSpans[ts].glyph){var tu=this.textSpans[ts].glyph;this.textSpans[ts].childSpan.removeChild(tu.layerElement),tu.destroy()}this.textSpans[ts].glyph=tp,tp._debug=!0,tp.prepareFrame(0),tp.renderFrame(),this.textSpans[ts].childSpan.appendChild(tp.layerElement),1===th.t&&this.textSpans[ts].childSpan.setAttribute("transform","scale("+D.finalSize/100+","+D.finalSize/100+")")}else J&&ta.setAttribute("transform","translate("+Y.props[12]+","+Y.props[13]+")"),ta.textContent=G[ts].val,ta.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve")}J&&ta&&ta.setAttribute("d",X)}else{var tc=this.textContainer,tm="start";switch(D.j){case 1:tm="end";break;case 2:tm="middle";break;default:tm="start"}tc.setAttribute("text-anchor",tm),tc.setAttribute("letter-spacing",tt);var tg=this.buildTextContents(D.finalText);for(ts=0,tn=tg.length,Z=D.ps?D.ps[1]+D.ascent:0;ts<tn;ts+=1)(ta=this.textSpans[ts].span||createNS("tspan")).textContent=tg[ts],ta.setAttribute("x",0),ta.setAttribute("y",Z),ta.style.display="inherit",tc.appendChild(ta),this.textSpans[ts]||(this.textSpans[ts]={span:null,glyph:null}),this.textSpans[ts].span=ta,Z+=D.finalLineHeight;this.layerElement.appendChild(tc)}for(;ts<this.textSpans.length;)this.textSpans[ts].span.style.display="none",ts+=1;this._sizeChanged=!0},SVGTextLottieElement.prototype.sourceRectAtTime=function(){if(this.prepareFrame(this.comp.renderedFrame-this.data.st),this.renderInnerContent(),this._sizeChanged){this._sizeChanged=!1;var D=this.layerElement.getBBox();this.bbox={top:D.y,left:D.x,width:D.width,height:D.height}}return this.bbox},SVGTextLottieElement.prototype.getValue=function(){var D,F,O=this.textSpans.length;for(D=0,this.renderedFrame=this.comp.renderedFrame;D<O;D+=1)(F=this.textSpans[D].glyph)&&(F.prepareFrame(this.comp.renderedFrame-this.data.st),F._mdf&&(this._mdf=!0))},SVGTextLottieElement.prototype.renderInnerContent=function(){if(this.validateText(),(!this.data.singleShape||this._mdf)&&(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag)){this._sizeChanged=!0;var D,F,O,U,G,W=this.textAnimator.renderedLetters,Y=this.textProperty.currentData.l;for(D=0,F=Y.length;D<F;D+=1)!Y[D].n&&(O=W[D],U=this.textSpans[D].span,(G=this.textSpans[D].glyph)&&G.renderFrame(),O._mdf.m&&U.setAttribute("transform",O.m),O._mdf.o&&U.setAttribute("opacity",O.o),O._mdf.sw&&U.setAttribute("stroke-width",O.sw),O._mdf.sc&&U.setAttribute("stroke",O.sc),O._mdf.fc&&U.setAttribute("fill",O.fc))}},extendPrototype([IImageElement],ISolidElement),ISolidElement.prototype.createContent=function(){var D=createNS("rect");D.setAttribute("width",this.data.sw),D.setAttribute("height",this.data.sh),D.setAttribute("fill",this.data.sc),this.layerElement.appendChild(D)},NullElement.prototype.prepareFrame=function(D){this.prepareProperties(D,!0)},NullElement.prototype.renderFrame=function(){},NullElement.prototype.getBaseElement=function(){return null},NullElement.prototype.destroy=function(){},NullElement.prototype.sourceRectAtTime=function(){},NullElement.prototype.hide=function(){},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement],NullElement),extendPrototype([BaseRenderer],SVGRendererBase),SVGRendererBase.prototype.createNull=function(D){return new NullElement(D,this.globalData,this)},SVGRendererBase.prototype.createShape=function(D){return new SVGShapeElement(D,this.globalData,this)},SVGRendererBase.prototype.createText=function(D){return new SVGTextLottieElement(D,this.globalData,this)},SVGRendererBase.prototype.createImage=function(D){return new IImageElement(D,this.globalData,this)},SVGRendererBase.prototype.createSolid=function(D){return new ISolidElement(D,this.globalData,this)},SVGRendererBase.prototype.configAnimation=function(D){this.svgElement.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.svgElement.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),this.renderConfig.viewBoxSize?this.svgElement.setAttribute("viewBox",this.renderConfig.viewBoxSize):this.svgElement.setAttribute("viewBox","0 0 "+D.w+" "+D.h),this.renderConfig.viewBoxOnly||(this.svgElement.setAttribute("width",D.w),this.svgElement.setAttribute("height",D.h),this.svgElement.style.width="100%",this.svgElement.style.height="100%",this.svgElement.style.transform="translate3d(0,0,0)",this.svgElement.style.contentVisibility=this.renderConfig.contentVisibility),this.renderConfig.width&&this.svgElement.setAttribute("width",this.renderConfig.width),this.renderConfig.height&&this.svgElement.setAttribute("height",this.renderConfig.height),this.renderConfig.className&&this.svgElement.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.svgElement.setAttribute("id",this.renderConfig.id),void 0!==this.renderConfig.focusable&&this.svgElement.setAttribute("focusable",this.renderConfig.focusable),this.svgElement.setAttribute("preserveAspectRatio",this.renderConfig.preserveAspectRatio),this.animationItem.wrapper.appendChild(this.svgElement);var F=this.globalData.defs;this.setupGlobalData(D,F),this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.data=D;var O=createNS("clipPath"),U=createNS("rect");U.setAttribute("width",D.w),U.setAttribute("height",D.h),U.setAttribute("x",0),U.setAttribute("y",0);var G=createElementID();O.setAttribute("id",G),O.appendChild(U),this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+G+")"),F.appendChild(O),this.layers=D.layers,this.elements=createSizedArray(D.layers.length)},SVGRendererBase.prototype.destroy=function(){this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.layerElement=null,this.globalData.defs=null;var D,F=this.layers?this.layers.length:0;for(D=0;D<F;D+=1)this.elements[D]&&this.elements[D].destroy&&this.elements[D].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},SVGRendererBase.prototype.updateContainerSize=function(){},SVGRendererBase.prototype.findIndexByInd=function(D){var F=0,O=this.layers.length;for(F=0;F<O;F+=1)if(this.layers[F].ind===D)return F;return -1},SVGRendererBase.prototype.buildItem=function(D){var F=this.elements;if(!F[D]&&99!==this.layers[D].ty){F[D]=!0;var O=this.createItem(this.layers[D]);if(F[D]=O,getExpressionsPlugin()&&(0===this.layers[D].ty&&this.globalData.projectInterface.registerComposition(O),O.initExpressions()),this.appendElementInPos(O,D),this.layers[D].tt){var U="tp"in this.layers[D]?this.findIndexByInd(this.layers[D].tp):D-1;if(-1===U)return;if(this.elements[U]&&!0!==this.elements[U]){var G=F[U].getMatte(this.layers[D].tt);O.setMatte(G)}else this.buildItem(U),this.addPendingElement(O)}}},SVGRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var D=this.pendingElements.pop();if(D.checkParenting(),D.data.tt)for(var F=0,O=this.elements.length;F<O;){if(this.elements[F]===D){var U="tp"in D.data?this.findIndexByInd(D.data.tp):F-1,G=this.elements[U].getMatte(this.layers[F].tt);D.setMatte(G);break}F+=1}}},SVGRendererBase.prototype.renderFrame=function(D){if(this.renderedFrame!==D&&!this.destroyed){null===D?D=this.renderedFrame:this.renderedFrame=D,this.globalData.frameNum=D,this.globalData.frameId+=1,this.globalData.projectInterface.currentFrame=D,this.globalData._mdf=!1;var F,O=this.layers.length;for(this.completeLayers||this.checkLayers(D),F=O-1;F>=0;F-=1)(this.completeLayers||this.elements[F])&&this.elements[F].prepareFrame(D-this.layers[F].st);if(this.globalData._mdf)for(F=0;F<O;F+=1)(this.completeLayers||this.elements[F])&&this.elements[F].renderFrame()}},SVGRendererBase.prototype.appendElementInPos=function(D,F){var O,U=D.getBaseElement();if(U){for(var G=0;G<F;)this.elements[G]&&!0!==this.elements[G]&&this.elements[G].getBaseElement()&&(O=this.elements[G].getBaseElement()),G+=1;O?this.layerElement.insertBefore(U,O):this.layerElement.appendChild(U)}},SVGRendererBase.prototype.hide=function(){this.layerElement.style.display="none"},SVGRendererBase.prototype.show=function(){this.layerElement.style.display="block"},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement,RenderableDOMElement],ICompElement),ICompElement.prototype.initElement=function(D,F,O){this.initFrame(),this.initBaseData(D,F,O),this.initTransform(D,F,O),this.initRenderable(),this.initHierarchy(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),(this.data.xt||!F.progressiveLoad)&&this.buildAllItems(),this.hide()},ICompElement.prototype.prepareFrame=function(D){if(this._mdf=!1,this.prepareRenderableFrame(D),this.prepareProperties(D,this.isInRange),this.isInRange||this.data.xt){if(this.tm._placeholder)this.renderedFrame=D/this.data.sr;else{var F,O=this.tm.v;O===this.data.op&&(O=this.data.op-1),this.renderedFrame=O}var U=this.elements.length;for(this.completeLayers||this.checkLayers(this.renderedFrame),F=U-1;F>=0;F-=1)(this.completeLayers||this.elements[F])&&(this.elements[F].prepareFrame(this.renderedFrame-this.layers[F].st),this.elements[F]._mdf&&(this._mdf=!0))}},ICompElement.prototype.renderInnerContent=function(){var D,F=this.layers.length;for(D=0;D<F;D+=1)(this.completeLayers||this.elements[D])&&this.elements[D].renderFrame()},ICompElement.prototype.setElements=function(D){this.elements=D},ICompElement.prototype.getElements=function(){return this.elements},ICompElement.prototype.destroyElements=function(){var D,F=this.layers.length;for(D=0;D<F;D+=1)this.elements[D]&&this.elements[D].destroy()},ICompElement.prototype.destroy=function(){this.destroyElements(),this.destroyBaseElement()},extendPrototype([SVGRendererBase,ICompElement,SVGBaseElement],SVGCompElement),SVGCompElement.prototype.createComp=function(D){return new SVGCompElement(D,this.globalData,this)},extendPrototype([SVGRendererBase],SVGRenderer),SVGRenderer.prototype.createComp=function(D){return new SVGCompElement(D,this.globalData,this)},ShapeTransformManager.prototype={addTransformSequence:function(D){var F,O=D.length,U="_";for(F=0;F<O;F+=1)U+=D[F].transform.key+"_";var G=this.sequences[U];return G||(G={transforms:[].concat(D),finalTransform:new Matrix,_mdf:!1},this.sequences[U]=G,this.sequenceList.push(G)),G},processSequence:function(D,F){for(var O=0,U=D.transforms.length,G=F;O<U&&!F;){if(D.transforms[O].transform.mProps._mdf){G=!0;break}O+=1}if(G)for(D.finalTransform.reset(),O=U-1;O>=0;O-=1)D.finalTransform.multiply(D.transforms[O].transform.mProps.v);D._mdf=G},processSequences:function(D){var F,O=this.sequenceList.length;for(F=0;F<O;F+=1)this.processSequence(this.sequenceList[F],D)},getNewKey:function(){return this.transform_key_count+=1,"_"+this.transform_key_count}};var lumaLoader=function(){var D="__lottie_element_luma_buffer",F=null,O=null,U=null;function G(){var F=createNS("svg"),O=createNS("filter"),U=createNS("feColorMatrix");return O.setAttribute("id",D),U.setAttribute("type","matrix"),U.setAttribute("color-interpolation-filters","sRGB"),U.setAttribute("values","0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0"),O.appendChild(U),F.appendChild(O),F.setAttribute("id",D+"_svg"),featureSupport.svgLumaHidden&&(F.style.display="none"),F}function W(){F||(U=G(),document.body.appendChild(U),(O=(F=createTag("canvas")).getContext("2d")).filter="url(#"+D+")",O.fillStyle="rgba(0,0,0,0)",O.fillRect(0,0,1,1))}function Y(U){return F||W(),F.width=U.width,F.height=U.height,O.filter="url(#"+D+")",F}return{load:W,get:Y}};function createCanvas(D,F){if(featureSupport.offscreenCanvas)return new OffscreenCanvas(D,F);var O=createTag("canvas");return O.width=D,O.height=F,O}var assetLoader=function(){return{loadLumaCanvas:lumaLoader.load,getLumaCanvas:lumaLoader.get,createCanvas:createCanvas}}(),registeredEffects={};function CVEffects(D){var F,O,U=D.data.ef?D.data.ef.length:0;for(F=0,this.filters=[];F<U;F+=1){O=null;var G=D.data.ef[F].ty;registeredEffects[G]&&(O=new registeredEffects[G].effect(D.effectsManager.effectElements[F],D)),O&&this.filters.push(O)}this.filters.length&&D.addRenderableComponent(this)}function registerEffect(D,F){registeredEffects[D]={effect:F}}function CVMaskElement(D,F){this.data=D,this.element=F,this.masksProperties=this.data.masksProperties||[],this.viewData=createSizedArray(this.masksProperties.length);var O,U=this.masksProperties.length,G=!1;for(O=0;O<U;O+=1)"n"!==this.masksProperties[O].mode&&(G=!0),this.viewData[O]=ShapePropertyFactory.getShapeProp(this.element,this.masksProperties[O],3);this.hasMasks=G,G&&this.element.addRenderableComponent(this)}function CVBaseElement(){}CVEffects.prototype.renderFrame=function(D){var F,O=this.filters.length;for(F=0;F<O;F+=1)this.filters[F].renderFrame(D)},CVEffects.prototype.getEffects=function(D){var F,O=this.filters.length,U=[];for(F=0;F<O;F+=1)this.filters[F].type===D&&U.push(this.filters[F]);return U},CVMaskElement.prototype.renderFrame=function(){if(this.hasMasks){var D=this.element.finalTransform.mat,F=this.element.canvasContext,O=this.masksProperties.length;for(F.beginPath(),U=0;U<O;U+=1)if("n"!==this.masksProperties[U].mode){this.masksProperties[U].inv&&(F.moveTo(0,0),F.lineTo(this.element.globalData.compSize.w,0),F.lineTo(this.element.globalData.compSize.w,this.element.globalData.compSize.h),F.lineTo(0,this.element.globalData.compSize.h),F.lineTo(0,0)),Y=this.viewData[U].v,G=D.applyToPointArray(Y.v[0][0],Y.v[0][1],0),F.moveTo(G[0],G[1]);var U,G,W,Y,X,J=Y._length;for(X=1;X<J;X+=1)W=D.applyToTriplePoints(Y.o[X-1],Y.i[X],Y.v[X]),F.bezierCurveTo(W[0],W[1],W[2],W[3],W[4],W[5]);W=D.applyToTriplePoints(Y.o[X-1],Y.i[0],Y.v[0]),F.bezierCurveTo(W[0],W[1],W[2],W[3],W[4],W[5])}this.element.globalData.renderer.save(!0),F.clip()}},CVMaskElement.prototype.getMaskProperty=MaskElement.prototype.getMaskProperty,CVMaskElement.prototype.destroy=function(){this.element=null};var operationsMap={1:"source-in",2:"source-out",3:"source-in",4:"source-out"};function CVShapeData(D,F,O,U){this.styledShapes=[],this.tr=[0,0,0,0,0,0];var G,W,Y=4;"rc"===F.ty?Y=5:"el"===F.ty?Y=6:"sr"===F.ty&&(Y=7),this.sh=ShapePropertyFactory.getShapeProp(D,F,Y,D);var X=O.length;for(G=0;G<X;G+=1)O[G].closed||(W={transforms:U.addTransformSequence(O[G].transforms),trNodes:[]},this.styledShapes.push(W),O[G].elements.push(W))}function CVShapeElement(D,F,O){this.shapes=[],this.shapesData=D.shapes,this.stylesList=[],this.itemsData=[],this.prevViewData=[],this.shapeModifiers=[],this.processedElements=[],this.transformsManager=new ShapeTransformManager,this.initElement(D,F,O)}function CVTextElement(D,F,O){this.textSpans=[],this.yOffset=0,this.fillColorAnim=!1,this.strokeColorAnim=!1,this.strokeWidthAnim=!1,this.stroke=!1,this.fill=!1,this.justifyOffset=0,this.currentRender=null,this.renderType="canvas",this.values={fill:"rgba(0,0,0,0)",stroke:"rgba(0,0,0,0)",sWidth:0,fValue:""},this.initElement(D,F,O)}function CVImageElement(D,F,O){this.assetData=F.getAssetData(D.refId),this.img=F.imageLoader.getAsset(this.assetData),this.initElement(D,F,O)}function CVSolidElement(D,F,O){this.initElement(D,F,O)}function CanvasRendererBase(){}function CanvasContext(){this.opacity=-1,this.transform=createTypedArray("float32",16),this.fillStyle="",this.strokeStyle="",this.lineWidth="",this.lineCap="",this.lineJoin="",this.miterLimit="",this.id=Math.random()}function CVContextData(){this.stack=[],this.cArrPos=0,this.cTr=new Matrix;var D,F=15;for(D=0;D<F;D+=1){var O=new CanvasContext;this.stack[D]=O}this._length=F,this.nativeContext=null,this.transformMat=new Matrix,this.currentOpacity=1,this.currentFillStyle="",this.appliedFillStyle="",this.currentStrokeStyle="",this.appliedStrokeStyle="",this.currentLineWidth="",this.appliedLineWidth="",this.currentLineCap="",this.appliedLineCap="",this.currentLineJoin="",this.appliedLineJoin="",this.appliedMiterLimit="",this.currentMiterLimit=""}function CVCompElement(D,F,O){this.completeLayers=!1,this.layers=D.layers,this.pendingElements=[],this.elements=createSizedArray(this.layers.length),this.initElement(D,F,O),this.tm=D.tm?PropertyFactory.getProp(this,D.tm,0,F.frameRate,this):{_placeholder:!0}}function CanvasRenderer(D,F){this.animationItem=D,this.renderConfig={clearCanvas:!F||void 0===F.clearCanvas||F.clearCanvas,context:F&&F.context||null,progressiveLoad:F&&F.progressiveLoad||!1,preserveAspectRatio:F&&F.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:F&&F.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:F&&F.contentVisibility||"visible",className:F&&F.className||"",id:F&&F.id||"",runExpressions:!F||void 0===F.runExpressions||F.runExpressions},this.renderConfig.dpr=F&&F.dpr||1,this.animationItem.wrapper&&(this.renderConfig.dpr=F&&F.dpr||window.devicePixelRatio||1),this.renderedFrame=-1,this.globalData={frameNum:-1,_mdf:!1,renderConfig:this.renderConfig,currentGlobalAlpha:-1},this.contextData=new CVContextData,this.elements=[],this.pendingElements=[],this.transformMat=new Matrix,this.completeLayers=!1,this.rendererType="canvas",this.renderConfig.clearCanvas&&(this.ctxTransform=this.contextData.transform.bind(this.contextData),this.ctxOpacity=this.contextData.opacity.bind(this.contextData),this.ctxFillStyle=this.contextData.fillStyle.bind(this.contextData),this.ctxStrokeStyle=this.contextData.strokeStyle.bind(this.contextData),this.ctxLineWidth=this.contextData.lineWidth.bind(this.contextData),this.ctxLineCap=this.contextData.lineCap.bind(this.contextData),this.ctxLineJoin=this.contextData.lineJoin.bind(this.contextData),this.ctxMiterLimit=this.contextData.miterLimit.bind(this.contextData),this.ctxFill=this.contextData.fill.bind(this.contextData),this.ctxFillRect=this.contextData.fillRect.bind(this.contextData),this.ctxStroke=this.contextData.stroke.bind(this.contextData),this.save=this.contextData.save.bind(this.contextData))}function HBaseElement(){}function HSolidElement(D,F,O){this.initElement(D,F,O)}function HShapeElement(D,F,O){this.shapes=[],this.shapesData=D.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.shapesContainer=createNS("g"),this.initElement(D,F,O),this.prevViewData=[],this.currentBBox={x:999999,y:-999999,h:0,w:0}}function HTextElement(D,F,O){this.textSpans=[],this.textPaths=[],this.currentBBox={x:999999,y:-999999,h:0,w:0},this.renderType="svg",this.isMasked=!1,this.initElement(D,F,O)}function HCameraElement(D,F,O){this.initFrame(),this.initBaseData(D,F,O),this.initHierarchy();var U=PropertyFactory.getProp;if(this.pe=U(this,D.pe,0,0,this),D.ks.p.s?(this.px=U(this,D.ks.p.x,1,0,this),this.py=U(this,D.ks.p.y,1,0,this),this.pz=U(this,D.ks.p.z,1,0,this)):this.p=U(this,D.ks.p,1,0,this),D.ks.a&&(this.a=U(this,D.ks.a,1,0,this)),D.ks.or.k.length&&D.ks.or.k[0].to){var G,W=D.ks.or.k.length;for(G=0;G<W;G+=1)D.ks.or.k[G].to=null,D.ks.or.k[G].ti=null}this.or=U(this,D.ks.or,1,degToRads,this),this.or.sh=!0,this.rx=U(this,D.ks.rx,0,degToRads,this),this.ry=U(this,D.ks.ry,0,degToRads,this),this.rz=U(this,D.ks.rz,0,degToRads,this),this.mat=new Matrix,this._prevMat=new Matrix,this._isFirstFrame=!0,this.finalTransform={mProp:this}}function HImageElement(D,F,O){this.assetData=F.getAssetData(D.refId),this.initElement(D,F,O)}function HybridRendererBase(D,F){this.animationItem=D,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:F&&F.className||"",imagePreserveAspectRatio:F&&F.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(F&&!1===F.hideOnTransparent),filterSize:{width:F&&F.filterSize&&F.filterSize.width||"400%",height:F&&F.filterSize&&F.filterSize.height||"400%",x:F&&F.filterSize&&F.filterSize.x||"-100%",y:F&&F.filterSize&&F.filterSize.y||"-100%"}},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}function HCompElement(D,F,O){this.layers=D.layers,this.supports3d=!D.hasMask,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(D,F,O),this.tm=D.tm?PropertyFactory.getProp(this,D.tm,0,F.frameRate,this):{_placeholder:!0}}function HybridRenderer(D,F){this.animationItem=D,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:F&&F.className||"",imagePreserveAspectRatio:F&&F.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(F&&!1===F.hideOnTransparent),filterSize:{width:F&&F.filterSize&&F.filterSize.width||"400%",height:F&&F.filterSize&&F.filterSize.height||"400%",x:F&&F.filterSize&&F.filterSize.x||"-100%",y:F&&F.filterSize&&F.filterSize.y||"-100%"},runExpressions:!F||void 0===F.runExpressions||F.runExpressions},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}CVBaseElement.prototype={createElements:function(){},initRendererElement:function(){},createContainerElements:function(){if(this.data.tt>=1){this.buffers=[];var D=this.globalData.canvasContext,F=assetLoader.createCanvas(D.canvas.width,D.canvas.height);this.buffers.push(F);var O=assetLoader.createCanvas(D.canvas.width,D.canvas.height);this.buffers.push(O),this.data.tt>=3&&!document._isProxy&&assetLoader.loadLumaCanvas()}this.canvasContext=this.globalData.canvasContext,this.transformCanvas=this.globalData.transformCanvas,this.renderableEffectsManager=new CVEffects(this),this.searchEffectTransforms()},createContent:function(){},setBlendMode:function(){var D=this.globalData;if(D.blendMode!==this.data.bm){D.blendMode=this.data.bm;var F=getBlendMode(this.data.bm);D.canvasContext.globalCompositeOperation=F}},createRenderableComponents:function(){this.maskManager=new CVMaskElement(this.data,this),this.transformEffects=this.renderableEffectsManager.getEffects(effectTypes.TRANSFORM_EFFECT)},hideElement:function(){this.hidden||this.isInRange&&!this.isTransparent||(this.hidden=!0)},showElement:function(){this.isInRange&&!this.isTransparent&&(this.hidden=!1,this._isFirstFrame=!0,this.maskManager._isFirstFrame=!0)},clearCanvas:function(D){D.clearRect(this.transformCanvas.tx,this.transformCanvas.ty,this.transformCanvas.w*this.transformCanvas.sx,this.transformCanvas.h*this.transformCanvas.sy)},prepareLayer:function(){if(this.data.tt>=1){var D=this.buffers[0].getContext("2d");this.clearCanvas(D),D.drawImage(this.canvasContext.canvas,0,0),this.currentTransform=this.canvasContext.getTransform(),this.canvasContext.setTransform(1,0,0,1,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.setTransform(this.currentTransform)}},exitLayer:function(){if(this.data.tt>=1){var D=this.buffers[1],F=D.getContext("2d");if(this.clearCanvas(F),F.drawImage(this.canvasContext.canvas,0,0),this.canvasContext.setTransform(1,0,0,1,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.setTransform(this.currentTransform),this.comp.getElementById("tp"in this.data?this.data.tp:this.data.ind-1).renderFrame(!0),this.canvasContext.setTransform(1,0,0,1,0,0),this.data.tt>=3&&!document._isProxy){var O=assetLoader.getLumaCanvas(this.canvasContext.canvas);O.getContext("2d").drawImage(this.canvasContext.canvas,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.drawImage(O,0,0)}this.canvasContext.globalCompositeOperation=operationsMap[this.data.tt],this.canvasContext.drawImage(D,0,0),this.canvasContext.globalCompositeOperation="destination-over",this.canvasContext.drawImage(this.buffers[0],0,0),this.canvasContext.setTransform(this.currentTransform),this.canvasContext.globalCompositeOperation="source-over"}},renderFrame:function(D){if(!this.hidden&&!this.data.hd&&(1!==this.data.td||D)){this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.setBlendMode();var F=0===this.data.ty;this.prepareLayer(),this.globalData.renderer.save(F),this.globalData.renderer.ctxTransform(this.finalTransform.localMat.props),this.globalData.renderer.ctxOpacity(this.finalTransform.localOpacity),this.renderInnerContent(),this.globalData.renderer.restore(F),this.exitLayer(),this.maskManager.hasMasks&&this.globalData.renderer.restore(!0),this._isFirstFrame&&(this._isFirstFrame=!1)}},destroy:function(){this.canvasContext=null,this.data=null,this.globalData=null,this.maskManager.destroy()},mHelper:new Matrix},CVBaseElement.prototype.hide=CVBaseElement.prototype.hideElement,CVBaseElement.prototype.show=CVBaseElement.prototype.showElement,CVShapeData.prototype.setAsAnimated=SVGShapeData.prototype.setAsAnimated,extendPrototype([BaseElement,TransformElement,CVBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableElement],CVShapeElement),CVShapeElement.prototype.initElement=RenderableDOMElement.prototype.initElement,CVShapeElement.prototype.transformHelper={opacity:1,_opMdf:!1},CVShapeElement.prototype.dashResetter=[],CVShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[])},CVShapeElement.prototype.createStyleElement=function(D,F){var O={data:D,type:D.ty,preTransforms:this.transformsManager.addTransformSequence(F),transforms:[],elements:[],closed:!0===D.hd},U={};if("fl"===D.ty||"st"===D.ty?(U.c=PropertyFactory.getProp(this,D.c,1,255,this),U.c.k||(O.co="rgb("+bmFloor(U.c.v[0])+","+bmFloor(U.c.v[1])+","+bmFloor(U.c.v[2])+")")):("gf"===D.ty||"gs"===D.ty)&&(U.s=PropertyFactory.getProp(this,D.s,1,null,this),U.e=PropertyFactory.getProp(this,D.e,1,null,this),U.h=PropertyFactory.getProp(this,D.h||{k:0},0,.01,this),U.a=PropertyFactory.getProp(this,D.a||{k:0},0,degToRads,this),U.g=new GradientProperty(this,D.g,this)),U.o=PropertyFactory.getProp(this,D.o,0,.01,this),"st"===D.ty||"gs"===D.ty){if(O.lc=lineCapEnum[D.lc||2],O.lj=lineJoinEnum[D.lj||2],1==D.lj&&(O.ml=D.ml),U.w=PropertyFactory.getProp(this,D.w,0,null,this),U.w.k||(O.wi=U.w.v),D.d){var G=new DashProperty(this,D.d,"canvas",this);U.d=G,U.d.k||(O.da=U.d.dashArray,O.do=U.d.dashoffset[0])}}else O.r=2===D.r?"evenodd":"nonzero";return this.stylesList.push(O),U.style=O,U},CVShapeElement.prototype.createGroupElement=function(){return{it:[],prevViewData:[]}},CVShapeElement.prototype.createTransformElement=function(D){return{transform:{opacity:1,_opMdf:!1,key:this.transformsManager.getNewKey(),op:PropertyFactory.getProp(this,D.o,0,.01,this),mProps:TransformPropertyFactory.getTransformProperty(this,D,this)}}},CVShapeElement.prototype.createShapeElement=function(D){var F=new CVShapeData(this,D,this.stylesList,this.transformsManager);return this.shapes.push(F),this.addShapeToModifiers(F),F},CVShapeElement.prototype.reloadShapes=function(){this._isFirstFrame=!0;var D,F=this.itemsData.length;for(D=0;D<F;D+=1)this.prevViewData[D]=this.itemsData[D];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[]),F=this.dynamicProperties.length,D=0;D<F;D+=1)this.dynamicProperties[D].getValue();this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame)},CVShapeElement.prototype.addTransformToStyleList=function(D){var F,O=this.stylesList.length;for(F=0;F<O;F+=1)this.stylesList[F].closed||this.stylesList[F].transforms.push(D)},CVShapeElement.prototype.removeTransformFromStyleList=function(){var D,F=this.stylesList.length;for(D=0;D<F;D+=1)this.stylesList[D].closed||this.stylesList[D].transforms.pop()},CVShapeElement.prototype.closeStyles=function(D){var F,O=D.length;for(F=0;F<O;F+=1)D[F].closed=!0},CVShapeElement.prototype.searchShapes=function(D,F,O,U,G){var W,Y,X,J,K,Z,Q=D.length-1,tt=[],te=[],tr=[].concat(G);for(W=Q;W>=0;W-=1){if((J=this.searchProcessedElement(D[W]))?F[W]=O[J-1]:D[W]._shouldRender=U,"fl"===D[W].ty||"st"===D[W].ty||"gf"===D[W].ty||"gs"===D[W].ty)J?F[W].style.closed=!1:F[W]=this.createStyleElement(D[W],tr),tt.push(F[W].style);else if("gr"===D[W].ty){if(J)for(Y=0,X=F[W].it.length;Y<X;Y+=1)F[W].prevViewData[Y]=F[W].it[Y];else F[W]=this.createGroupElement(D[W]);this.searchShapes(D[W].it,F[W].it,F[W].prevViewData,U,tr)}else"tr"===D[W].ty?(J||(Z=this.createTransformElement(D[W]),F[W]=Z),tr.push(F[W]),this.addTransformToStyleList(F[W])):"sh"===D[W].ty||"rc"===D[W].ty||"el"===D[W].ty||"sr"===D[W].ty?J||(F[W]=this.createShapeElement(D[W])):"tm"===D[W].ty||"rd"===D[W].ty||"pb"===D[W].ty||"zz"===D[W].ty||"op"===D[W].ty?(J?(K=F[W]).closed=!1:((K=ShapeModifiers.getModifier(D[W].ty)).init(this,D[W]),F[W]=K,this.shapeModifiers.push(K)),te.push(K)):"rp"===D[W].ty&&(J?(K=F[W]).closed=!0:(K=ShapeModifiers.getModifier(D[W].ty),F[W]=K,K.init(this,D,W,F),this.shapeModifiers.push(K),U=!1),te.push(K));this.addProcessedElement(D[W],W+1)}for(this.removeTransformFromStyleList(),this.closeStyles(tt),Q=te.length,W=0;W<Q;W+=1)te[W].closed=!0},CVShapeElement.prototype.renderInnerContent=function(){this.transformHelper.opacity=1,this.transformHelper._opMdf=!1,this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame),this.renderShape(this.transformHelper,this.shapesData,this.itemsData,!0)},CVShapeElement.prototype.renderShapeTransform=function(D,F){(D._opMdf||F.op._mdf||this._isFirstFrame)&&(F.opacity=D.opacity,F.opacity*=F.op.v,F._opMdf=!0)},CVShapeElement.prototype.drawLayer=function(){var D,F,O,U,G,W,Y,X,J,K=this.stylesList.length,Z=this.globalData.renderer,Q=this.globalData.canvasContext;for(D=0;D<K;D+=1)if(!(("st"===(X=(J=this.stylesList[D]).type)||"gs"===X)&&0===J.wi||!J.data._shouldRender||0===J.coOp||0===this.globalData.currentGlobalAlpha)){for(Z.save(),W=J.elements,"st"===X||"gs"===X?(Z.ctxStrokeStyle("st"===X?J.co:J.grd),Z.ctxLineWidth(J.wi),Z.ctxLineCap(J.lc),Z.ctxLineJoin(J.lj),Z.ctxMiterLimit(J.ml||0)):Z.ctxFillStyle("fl"===X?J.co:J.grd),Z.ctxOpacity(J.coOp),"st"!==X&&"gs"!==X&&Q.beginPath(),Z.ctxTransform(J.preTransforms.finalTransform.props),O=W.length,F=0;F<O;F+=1){for(("st"===X||"gs"===X)&&(Q.beginPath(),J.da&&(Q.setLineDash(J.da),Q.lineDashOffset=J.do)),G=(Y=W[F].trNodes).length,U=0;U<G;U+=1)"m"===Y[U].t?Q.moveTo(Y[U].p[0],Y[U].p[1]):"c"===Y[U].t?Q.bezierCurveTo(Y[U].pts[0],Y[U].pts[1],Y[U].pts[2],Y[U].pts[3],Y[U].pts[4],Y[U].pts[5]):Q.closePath();("st"===X||"gs"===X)&&(Z.ctxStroke(),J.da&&Q.setLineDash(this.dashResetter))}"st"!==X&&"gs"!==X&&this.globalData.renderer.ctxFill(J.r),Z.restore()}},CVShapeElement.prototype.renderShape=function(D,F,O,U){var G,W,Y=F.length-1;for(W=D,G=Y;G>=0;G-=1)"tr"===F[G].ty?(W=O[G].transform,this.renderShapeTransform(D,W)):"sh"===F[G].ty||"el"===F[G].ty||"rc"===F[G].ty||"sr"===F[G].ty?this.renderPath(F[G],O[G]):"fl"===F[G].ty?this.renderFill(F[G],O[G],W):"st"===F[G].ty?this.renderStroke(F[G],O[G],W):"gf"===F[G].ty||"gs"===F[G].ty?this.renderGradientFill(F[G],O[G],W):"gr"===F[G].ty?this.renderShape(W,F[G].it,O[G].it):F[G].ty;U&&this.drawLayer()},CVShapeElement.prototype.renderStyledShape=function(D,F){if(this._isFirstFrame||F._mdf||D.transforms._mdf){var O,U,G,W=D.trNodes,Y=F.paths,X=Y._length;W.length=0;var J=D.transforms.finalTransform;for(G=0;G<X;G+=1){var K=Y.shapes[G];if(K&&K.v){for(O=1,U=K._length;O<U;O+=1)1===O&&W.push({t:"m",p:J.applyToPointArray(K.v[0][0],K.v[0][1],0)}),W.push({t:"c",pts:J.applyToTriplePoints(K.o[O-1],K.i[O],K.v[O])});1===U&&W.push({t:"m",p:J.applyToPointArray(K.v[0][0],K.v[0][1],0)}),K.c&&U&&(W.push({t:"c",pts:J.applyToTriplePoints(K.o[O-1],K.i[0],K.v[0])}),W.push({t:"z"}))}}D.trNodes=W}},CVShapeElement.prototype.renderPath=function(D,F){if(!0!==D.hd&&D._shouldRender){var O,U=F.styledShapes.length;for(O=0;O<U;O+=1)this.renderStyledShape(F.styledShapes[O],F.sh)}},CVShapeElement.prototype.renderFill=function(D,F,O){var U=F.style;(F.c._mdf||this._isFirstFrame)&&(U.co="rgb("+bmFloor(F.c.v[0])+","+bmFloor(F.c.v[1])+","+bmFloor(F.c.v[2])+")"),(F.o._mdf||O._opMdf||this._isFirstFrame)&&(U.coOp=F.o.v*O.opacity)},CVShapeElement.prototype.renderGradientFill=function(D,F,O){var U=F.style;if(!U.grd||F.g._mdf||F.s._mdf||F.e._mdf||1!==D.t&&(F.h._mdf||F.a._mdf)){var G,W,Y=this.globalData.canvasContext,X=F.s.v,J=F.e.v;if(1===D.t)G=Y.createLinearGradient(X[0],X[1],J[0],J[1]);else{var K=Math.sqrt(Math.pow(X[0]-J[0],2)+Math.pow(X[1]-J[1],2)),Z=Math.atan2(J[1]-X[1],J[0]-X[0]),Q=F.h.v;Q>=1?Q=.99:Q<=-1&&(Q=-.99);var tt=K*Q,te=Math.cos(Z+F.a.v)*tt+X[0],tr=Math.sin(Z+F.a.v)*tt+X[1];G=Y.createRadialGradient(te,tr,0,X[0],X[1],K)}var ts=D.g.p,tn=F.g.c,ta=1;for(W=0;W<ts;W+=1)F.g._hasOpacity&&F.g._collapsable&&(ta=F.g.o[2*W+1]),G.addColorStop(tn[4*W]/100,"rgba("+tn[4*W+1]+","+tn[4*W+2]+","+tn[4*W+3]+","+ta+")");U.grd=G}U.coOp=F.o.v*O.opacity},CVShapeElement.prototype.renderStroke=function(D,F,O){var U=F.style,G=F.d;G&&(G._mdf||this._isFirstFrame)&&(U.da=G.dashArray,U.do=G.dashoffset[0]),(F.c._mdf||this._isFirstFrame)&&(U.co="rgb("+bmFloor(F.c.v[0])+","+bmFloor(F.c.v[1])+","+bmFloor(F.c.v[2])+")"),(F.o._mdf||O._opMdf||this._isFirstFrame)&&(U.coOp=F.o.v*O.opacity),(F.w._mdf||this._isFirstFrame)&&(U.wi=F.w.v)},CVShapeElement.prototype.destroy=function(){this.shapesData=null,this.globalData=null,this.canvasContext=null,this.stylesList.length=0,this.itemsData.length=0},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement,ITextElement],CVTextElement),CVTextElement.prototype.tHelper=createTag("canvas").getContext("2d"),CVTextElement.prototype.buildNewText=function(){var D,F,O,U,G,W,Y,X,J,K,Z,Q,tt=this.textProperty.currentData;this.renderedLetters=createSizedArray(tt.l?tt.l.length:0);var te=!1;tt.fc?(te=!0,this.values.fill=this.buildColor(tt.fc)):this.values.fill="rgba(0,0,0,0)",this.fill=te;var tr=!1;tt.sc&&(tr=!0,this.values.stroke=this.buildColor(tt.sc),this.values.sWidth=tt.sw);var ts=this.globalData.fontManager.getFontByName(tt.f),tn=tt.l,ta=this.mHelper;this.stroke=tr,this.values.fValue=tt.finalSize+"px "+this.globalData.fontManager.getFontByName(tt.f).fFamily,F=tt.finalText.length;var th=this.data.singleShape,tp=.001*tt.tr*tt.finalSize,tf=0,tu=0,tc=!0,tm=0;for(D=0;D<F;D+=1){U=(O=this.globalData.fontManager.getCharData(tt.finalText[D],ts.fStyle,this.globalData.fontManager.getFontByName(tt.f).fFamily))&&O.data||{},ta.reset(),th&&tn[D].n&&(tf=-tp,tu+=tt.yOffset+(tc?1:0),tc=!1),J=(Y=U.shapes?U.shapes[0].it:[]).length,ta.scale(tt.finalSize/100,tt.finalSize/100),th&&this.applyTextPropertiesToMatrix(tt,ta,tn[D].line,tf,tu),Z=createSizedArray(J-1);var tg=0;for(X=0;X<J;X+=1)if("sh"===Y[X].ty){for(G=1,W=Y[X].ks.k.i.length,K=Y[X].ks.k,Q=[];G<W;G+=1)1===G&&Q.push(ta.applyToX(K.v[0][0],K.v[0][1],0),ta.applyToY(K.v[0][0],K.v[0][1],0)),Q.push(ta.applyToX(K.o[G-1][0],K.o[G-1][1],0),ta.applyToY(K.o[G-1][0],K.o[G-1][1],0),ta.applyToX(K.i[G][0],K.i[G][1],0),ta.applyToY(K.i[G][0],K.i[G][1],0),ta.applyToX(K.v[G][0],K.v[G][1],0),ta.applyToY(K.v[G][0],K.v[G][1],0));Q.push(ta.applyToX(K.o[G-1][0],K.o[G-1][1],0),ta.applyToY(K.o[G-1][0],K.o[G-1][1],0),ta.applyToX(K.i[0][0],K.i[0][1],0),ta.applyToY(K.i[0][0],K.i[0][1],0),ta.applyToX(K.v[0][0],K.v[0][1],0),ta.applyToY(K.v[0][0],K.v[0][1],0)),Z[tg]=Q,tg+=1}th&&(tf+=tn[D].l+tp),this.textSpans[tm]?this.textSpans[tm].elem=Z:this.textSpans[tm]={elem:Z},tm+=1}},CVTextElement.prototype.renderInnerContent=function(){this.validateText(),this.canvasContext.font=this.values.fValue,this.globalData.renderer.ctxLineCap("butt"),this.globalData.renderer.ctxLineJoin("miter"),this.globalData.renderer.ctxMiterLimit(4),this.data.singleShape||this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag);var D,F,O,U,G,W,Y,X,J,K=this.textAnimator.renderedLetters,Z=this.textProperty.currentData.l;F=Z.length;var Q=null,tt=null,te=null,tr=this.globalData.renderer;for(D=0;D<F;D+=1)if(!Z[D].n){if((Y=K[D])&&(tr.save(),tr.ctxTransform(Y.p),tr.ctxOpacity(Y.o)),this.fill){for(Y&&Y.fc?Q!==Y.fc&&(tr.ctxFillStyle(Y.fc),Q=Y.fc):Q!==this.values.fill&&(Q=this.values.fill,tr.ctxFillStyle(this.values.fill)),U=(X=this.textSpans[D].elem).length,this.globalData.canvasContext.beginPath(),O=0;O<U;O+=1)for(W=(J=X[O]).length,this.globalData.canvasContext.moveTo(J[0],J[1]),G=2;G<W;G+=6)this.globalData.canvasContext.bezierCurveTo(J[G],J[G+1],J[G+2],J[G+3],J[G+4],J[G+5]);this.globalData.canvasContext.closePath(),tr.ctxFill()}if(this.stroke){for(Y&&Y.sw?te!==Y.sw&&(te=Y.sw,tr.ctxLineWidth(Y.sw)):te!==this.values.sWidth&&(te=this.values.sWidth,tr.ctxLineWidth(this.values.sWidth)),Y&&Y.sc?tt!==Y.sc&&(tt=Y.sc,tr.ctxStrokeStyle(Y.sc)):tt!==this.values.stroke&&(tt=this.values.stroke,tr.ctxStrokeStyle(this.values.stroke)),U=(X=this.textSpans[D].elem).length,this.globalData.canvasContext.beginPath(),O=0;O<U;O+=1)for(W=(J=X[O]).length,this.globalData.canvasContext.moveTo(J[0],J[1]),G=2;G<W;G+=6)this.globalData.canvasContext.bezierCurveTo(J[G],J[G+1],J[G+2],J[G+3],J[G+4],J[G+5]);this.globalData.canvasContext.closePath(),tr.ctxStroke()}Y&&this.globalData.renderer.restore()}},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVImageElement),CVImageElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVImageElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVImageElement.prototype.createContent=function(){if(this.img.width&&(this.assetData.w!==this.img.width||this.assetData.h!==this.img.height)){var D,F,O=createTag("canvas");O.width=this.assetData.w,O.height=this.assetData.h;var U=O.getContext("2d"),G=this.img.width,W=this.img.height,Y=G/W,X=this.assetData.w/this.assetData.h,J=this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio;Y>X&&"xMidYMid slice"===J||Y<X&&"xMidYMid slice"!==J?D=(F=W)*X:F=(D=G)/X,U.drawImage(this.img,(G-D)/2,(W-F)/2,D,F,0,0,this.assetData.w,this.assetData.h),this.img=O}},CVImageElement.prototype.renderInnerContent=function(){this.canvasContext.drawImage(this.img,0,0)},CVImageElement.prototype.destroy=function(){this.img=null},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVSolidElement),CVSolidElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVSolidElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVSolidElement.prototype.renderInnerContent=function(){this.globalData.renderer.ctxFillStyle(this.data.sc),this.globalData.renderer.ctxFillRect(0,0,this.data.sw,this.data.sh)},extendPrototype([BaseRenderer],CanvasRendererBase),CanvasRendererBase.prototype.createShape=function(D){return new CVShapeElement(D,this.globalData,this)},CanvasRendererBase.prototype.createText=function(D){return new CVTextElement(D,this.globalData,this)},CanvasRendererBase.prototype.createImage=function(D){return new CVImageElement(D,this.globalData,this)},CanvasRendererBase.prototype.createSolid=function(D){return new CVSolidElement(D,this.globalData,this)},CanvasRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,CanvasRendererBase.prototype.ctxTransform=function(D){(1!==D[0]||0!==D[1]||0!==D[4]||1!==D[5]||0!==D[12]||0!==D[13])&&this.canvasContext.transform(D[0],D[1],D[4],D[5],D[12],D[13])},CanvasRendererBase.prototype.ctxOpacity=function(D){this.canvasContext.globalAlpha*=D<0?0:D},CanvasRendererBase.prototype.ctxFillStyle=function(D){this.canvasContext.fillStyle=D},CanvasRendererBase.prototype.ctxStrokeStyle=function(D){this.canvasContext.strokeStyle=D},CanvasRendererBase.prototype.ctxLineWidth=function(D){this.canvasContext.lineWidth=D},CanvasRendererBase.prototype.ctxLineCap=function(D){this.canvasContext.lineCap=D},CanvasRendererBase.prototype.ctxLineJoin=function(D){this.canvasContext.lineJoin=D},CanvasRendererBase.prototype.ctxMiterLimit=function(D){this.canvasContext.miterLimit=D},CanvasRendererBase.prototype.ctxFill=function(D){this.canvasContext.fill(D)},CanvasRendererBase.prototype.ctxFillRect=function(D,F,O,U){this.canvasContext.fillRect(D,F,O,U)},CanvasRendererBase.prototype.ctxStroke=function(){this.canvasContext.stroke()},CanvasRendererBase.prototype.reset=function(){if(!this.renderConfig.clearCanvas){this.canvasContext.restore();return}this.contextData.reset()},CanvasRendererBase.prototype.save=function(){this.canvasContext.save()},CanvasRendererBase.prototype.restore=function(D){if(!this.renderConfig.clearCanvas){this.canvasContext.restore();return}D&&(this.globalData.blendMode="source-over"),this.contextData.restore(D)},CanvasRendererBase.prototype.configAnimation=function(D){if(this.animationItem.wrapper){this.animationItem.container=createTag("canvas");var F=this.animationItem.container.style;F.width="100%",F.height="100%";var O="0px 0px 0px";F.transformOrigin=O,F.mozTransformOrigin=O,F.webkitTransformOrigin=O,F["-webkit-transform"]=O,F.contentVisibility=this.renderConfig.contentVisibility,this.animationItem.wrapper.appendChild(this.animationItem.container),this.canvasContext=this.animationItem.container.getContext("2d"),this.renderConfig.className&&this.animationItem.container.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.animationItem.container.setAttribute("id",this.renderConfig.id)}else this.canvasContext=this.renderConfig.context;this.contextData.setContext(this.canvasContext),this.data=D,this.layers=D.layers,this.transformCanvas={w:D.w,h:D.h,sx:0,sy:0,tx:0,ty:0},this.setupGlobalData(D,document.body),this.globalData.canvasContext=this.canvasContext,this.globalData.renderer=this,this.globalData.isDashed=!1,this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.globalData.transformCanvas=this.transformCanvas,this.elements=createSizedArray(D.layers.length),this.updateContainerSize()},CanvasRendererBase.prototype.updateContainerSize=function(D,F){if(this.reset(),D?(O=D,U=F,this.canvasContext.canvas.width=O,this.canvasContext.canvas.height=U):(this.animationItem.wrapper&&this.animationItem.container?(O=this.animationItem.wrapper.offsetWidth,U=this.animationItem.wrapper.offsetHeight):(O=this.canvasContext.canvas.width,U=this.canvasContext.canvas.height),this.canvasContext.canvas.width=O*this.renderConfig.dpr,this.canvasContext.canvas.height=U*this.renderConfig.dpr),-1!==this.renderConfig.preserveAspectRatio.indexOf("meet")||-1!==this.renderConfig.preserveAspectRatio.indexOf("slice")){var O,U,G,W,Y=this.renderConfig.preserveAspectRatio.split(" "),X=Y[1]||"meet",J=Y[0]||"xMidYMid",K=J.substr(0,4),Z=J.substr(4);G=O/U,(W=this.transformCanvas.w/this.transformCanvas.h)>G&&"meet"===X||W<G&&"slice"===X?(this.transformCanvas.sx=O/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=O/(this.transformCanvas.w/this.renderConfig.dpr)):(this.transformCanvas.sx=U/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.sy=U/(this.transformCanvas.h/this.renderConfig.dpr)),"xMid"===K&&(W<G&&"meet"===X||W>G&&"slice"===X)?this.transformCanvas.tx=(O-this.transformCanvas.w*(U/this.transformCanvas.h))/2*this.renderConfig.dpr:"xMax"===K&&(W<G&&"meet"===X||W>G&&"slice"===X)?this.transformCanvas.tx=(O-this.transformCanvas.w*(U/this.transformCanvas.h))*this.renderConfig.dpr:this.transformCanvas.tx=0,"YMid"===Z&&(W>G&&"meet"===X||W<G&&"slice"===X)?this.transformCanvas.ty=(U-this.transformCanvas.h*(O/this.transformCanvas.w))/2*this.renderConfig.dpr:"YMax"===Z&&(W>G&&"meet"===X||W<G&&"slice"===X)?this.transformCanvas.ty=(U-this.transformCanvas.h*(O/this.transformCanvas.w))*this.renderConfig.dpr:this.transformCanvas.ty=0}else"none"===this.renderConfig.preserveAspectRatio?(this.transformCanvas.sx=O/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=U/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.tx=0,this.transformCanvas.ty=0):(this.transformCanvas.sx=this.renderConfig.dpr,this.transformCanvas.sy=this.renderConfig.dpr,this.transformCanvas.tx=0,this.transformCanvas.ty=0);this.transformCanvas.props=[this.transformCanvas.sx,0,0,0,0,this.transformCanvas.sy,0,0,0,0,1,0,this.transformCanvas.tx,this.transformCanvas.ty,0,1],this.ctxTransform(this.transformCanvas.props),this.canvasContext.beginPath(),this.canvasContext.rect(0,0,this.transformCanvas.w,this.transformCanvas.h),this.canvasContext.closePath(),this.canvasContext.clip(),this.renderFrame(this.renderedFrame,!0)},CanvasRendererBase.prototype.destroy=function(){var D;for(this.renderConfig.clearCanvas&&this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),D=(this.layers?this.layers.length:0)-1;D>=0;D-=1)this.elements[D]&&this.elements[D].destroy&&this.elements[D].destroy();this.elements.length=0,this.globalData.canvasContext=null,this.animationItem.container=null,this.destroyed=!0},CanvasRendererBase.prototype.renderFrame=function(D,F){if((this.renderedFrame!==D||!0!==this.renderConfig.clearCanvas||F)&&!this.destroyed&&-1!==D){this.renderedFrame=D,this.globalData.frameNum=D-this.animationItem._isFirstFrame,this.globalData.frameId+=1,this.globalData._mdf=!this.renderConfig.clearCanvas||F,this.globalData.projectInterface.currentFrame=D;var O,U=this.layers.length;for(this.completeLayers||this.checkLayers(D),O=U-1;O>=0;O-=1)(this.completeLayers||this.elements[O])&&this.elements[O].prepareFrame(D-this.layers[O].st);if(this.globalData._mdf){for(!0===this.renderConfig.clearCanvas?this.canvasContext.clearRect(0,0,this.transformCanvas.w,this.transformCanvas.h):this.save(),O=U-1;O>=0;O-=1)(this.completeLayers||this.elements[O])&&this.elements[O].renderFrame();!0!==this.renderConfig.clearCanvas&&this.restore()}}},CanvasRendererBase.prototype.buildItem=function(D){var F=this.elements;if(!F[D]&&99!==this.layers[D].ty){var O=this.createItem(this.layers[D],this,this.globalData);F[D]=O,O.initExpressions()}},CanvasRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;)this.pendingElements.pop().checkParenting()},CanvasRendererBase.prototype.hide=function(){this.animationItem.container.style.display="none"},CanvasRendererBase.prototype.show=function(){this.animationItem.container.style.display="block"},CVContextData.prototype.duplicate=function(){var D=2*this._length,F=0;for(F=this._length;F<D;F+=1)this.stack[F]=new CanvasContext;this._length=D},CVContextData.prototype.reset=function(){this.cArrPos=0,this.cTr.reset(),this.stack[this.cArrPos].opacity=1},CVContextData.prototype.restore=function(D){this.cArrPos-=1;var F,O=this.stack[this.cArrPos],U=O.transform,G=this.cTr.props;for(F=0;F<16;F+=1)G[F]=U[F];if(D){this.nativeContext.restore();var W=this.stack[this.cArrPos+1];this.appliedFillStyle=W.fillStyle,this.appliedStrokeStyle=W.strokeStyle,this.appliedLineWidth=W.lineWidth,this.appliedLineCap=W.lineCap,this.appliedLineJoin=W.lineJoin,this.appliedMiterLimit=W.miterLimit}this.nativeContext.setTransform(U[0],U[1],U[4],U[5],U[12],U[13]),(D||-1!==O.opacity&&this.currentOpacity!==O.opacity)&&(this.nativeContext.globalAlpha=O.opacity,this.currentOpacity=O.opacity),this.currentFillStyle=O.fillStyle,this.currentStrokeStyle=O.strokeStyle,this.currentLineWidth=O.lineWidth,this.currentLineCap=O.lineCap,this.currentLineJoin=O.lineJoin,this.currentMiterLimit=O.miterLimit},CVContextData.prototype.save=function(D){D&&this.nativeContext.save();var F,O=this.cTr.props;this._length<=this.cArrPos&&this.duplicate();var U=this.stack[this.cArrPos];for(F=0;F<16;F+=1)U.transform[F]=O[F];this.cArrPos+=1;var G=this.stack[this.cArrPos];G.opacity=U.opacity,G.fillStyle=U.fillStyle,G.strokeStyle=U.strokeStyle,G.lineWidth=U.lineWidth,G.lineCap=U.lineCap,G.lineJoin=U.lineJoin,G.miterLimit=U.miterLimit},CVContextData.prototype.setOpacity=function(D){this.stack[this.cArrPos].opacity=D},CVContextData.prototype.setContext=function(D){this.nativeContext=D},CVContextData.prototype.fillStyle=function(D){this.stack[this.cArrPos].fillStyle!==D&&(this.currentFillStyle=D,this.stack[this.cArrPos].fillStyle=D)},CVContextData.prototype.strokeStyle=function(D){this.stack[this.cArrPos].strokeStyle!==D&&(this.currentStrokeStyle=D,this.stack[this.cArrPos].strokeStyle=D)},CVContextData.prototype.lineWidth=function(D){this.stack[this.cArrPos].lineWidth!==D&&(this.currentLineWidth=D,this.stack[this.cArrPos].lineWidth=D)},CVContextData.prototype.lineCap=function(D){this.stack[this.cArrPos].lineCap!==D&&(this.currentLineCap=D,this.stack[this.cArrPos].lineCap=D)},CVContextData.prototype.lineJoin=function(D){this.stack[this.cArrPos].lineJoin!==D&&(this.currentLineJoin=D,this.stack[this.cArrPos].lineJoin=D)},CVContextData.prototype.miterLimit=function(D){this.stack[this.cArrPos].miterLimit!==D&&(this.currentMiterLimit=D,this.stack[this.cArrPos].miterLimit=D)},CVContextData.prototype.transform=function(D){this.transformMat.cloneFromProps(D);var F=this.cTr;this.transformMat.multiply(F),F.cloneFromProps(this.transformMat.props);var O=F.props;this.nativeContext.setTransform(O[0],O[1],O[4],O[5],O[12],O[13])},CVContextData.prototype.opacity=function(D){var F=this.stack[this.cArrPos].opacity;F*=D<0?0:D,this.stack[this.cArrPos].opacity!==F&&(this.currentOpacity!==D&&(this.nativeContext.globalAlpha=D,this.currentOpacity=D),this.stack[this.cArrPos].opacity=F)},CVContextData.prototype.fill=function(D){this.appliedFillStyle!==this.currentFillStyle&&(this.appliedFillStyle=this.currentFillStyle,this.nativeContext.fillStyle=this.appliedFillStyle),this.nativeContext.fill(D)},CVContextData.prototype.fillRect=function(D,F,O,U){this.appliedFillStyle!==this.currentFillStyle&&(this.appliedFillStyle=this.currentFillStyle,this.nativeContext.fillStyle=this.appliedFillStyle),this.nativeContext.fillRect(D,F,O,U)},CVContextData.prototype.stroke=function(){this.appliedStrokeStyle!==this.currentStrokeStyle&&(this.appliedStrokeStyle=this.currentStrokeStyle,this.nativeContext.strokeStyle=this.appliedStrokeStyle),this.appliedLineWidth!==this.currentLineWidth&&(this.appliedLineWidth=this.currentLineWidth,this.nativeContext.lineWidth=this.appliedLineWidth),this.appliedLineCap!==this.currentLineCap&&(this.appliedLineCap=this.currentLineCap,this.nativeContext.lineCap=this.appliedLineCap),this.appliedLineJoin!==this.currentLineJoin&&(this.appliedLineJoin=this.currentLineJoin,this.nativeContext.lineJoin=this.appliedLineJoin),this.appliedMiterLimit!==this.currentMiterLimit&&(this.appliedMiterLimit=this.currentMiterLimit,this.nativeContext.miterLimit=this.appliedMiterLimit),this.nativeContext.stroke()},extendPrototype([CanvasRendererBase,ICompElement,CVBaseElement],CVCompElement),CVCompElement.prototype.renderInnerContent=function(){var D,F=this.canvasContext;for(F.beginPath(),F.moveTo(0,0),F.lineTo(this.data.w,0),F.lineTo(this.data.w,this.data.h),F.lineTo(0,this.data.h),F.lineTo(0,0),F.clip(),D=this.layers.length-1;D>=0;D-=1)(this.completeLayers||this.elements[D])&&this.elements[D].renderFrame()},CVCompElement.prototype.destroy=function(){var D;for(D=this.layers.length-1;D>=0;D-=1)this.elements[D]&&this.elements[D].destroy();this.layers=null,this.elements=null},CVCompElement.prototype.createComp=function(D){return new CVCompElement(D,this.globalData,this)},extendPrototype([CanvasRendererBase],CanvasRenderer),CanvasRenderer.prototype.createComp=function(D){return new CVCompElement(D,this.globalData,this)},HBaseElement.prototype={checkBlendMode:function(){},initRendererElement:function(){this.baseElement=createTag(this.data.tg||"div"),this.data.hasMask?(this.svgElement=createNS("svg"),this.layerElement=createNS("g"),this.maskedElement=this.layerElement,this.svgElement.appendChild(this.layerElement),this.baseElement.appendChild(this.svgElement)):this.layerElement=this.baseElement,styleDiv(this.baseElement)},createContainerElements:function(){this.renderableEffectsManager=new CVEffects(this),this.transformedElement=this.baseElement,this.maskedElement=this.layerElement,this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0!==this.data.bm&&this.setBlendMode()},renderElement:function(){var D=this.transformedElement?this.transformedElement.style:{};if(this.finalTransform._matMdf){var F=this.finalTransform.mat.toCSS();D.transform=F,D.webkitTransform=F}this.finalTransform._opMdf&&(D.opacity=this.finalTransform.mProp.o.v)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},destroy:function(){this.layerElement=null,this.transformedElement=null,this.matteElement&&(this.matteElement=null),this.maskManager&&(this.maskManager.destroy(),this.maskManager=null)},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData)},addEffects:function(){},setMatte:function(){}},HBaseElement.prototype.getBaseElement=SVGBaseElement.prototype.getBaseElement,HBaseElement.prototype.destroyBaseElement=HBaseElement.prototype.destroy,HBaseElement.prototype.buildElementParenting=BaseRenderer.prototype.buildElementParenting,extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],HSolidElement),HSolidElement.prototype.createContent=function(){var D;this.data.hasMask?((D=createNS("rect")).setAttribute("width",this.data.sw),D.setAttribute("height",this.data.sh),D.setAttribute("fill",this.data.sc),this.svgElement.setAttribute("width",this.data.sw),this.svgElement.setAttribute("height",this.data.sh)):((D=createTag("div")).style.width=this.data.sw+"px",D.style.height=this.data.sh+"px",D.style.backgroundColor=this.data.sc),this.layerElement.appendChild(D)},extendPrototype([BaseElement,TransformElement,HSolidElement,SVGShapeElement,HBaseElement,HierarchyElement,FrameElement,RenderableElement],HShapeElement),HShapeElement.prototype._renderShapeFrame=HShapeElement.prototype.renderInnerContent,HShapeElement.prototype.createContent=function(){var D;if(this.baseElement.style.fontSize=0,this.data.hasMask)this.layerElement.appendChild(this.shapesContainer),D=this.svgElement;else{D=createNS("svg");var F=this.comp.data?this.comp.data:this.globalData.compSize;D.setAttribute("width",F.w),D.setAttribute("height",F.h),D.appendChild(this.shapesContainer),this.layerElement.appendChild(D)}this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.shapesContainer,0,[],!0),this.filterUniqueShapes(),this.shapeCont=D},HShapeElement.prototype.getTransformedPoint=function(D,F){var O,U=D.length;for(O=0;O<U;O+=1)F=D[O].mProps.v.applyToPointArray(F[0],F[1],0);return F},HShapeElement.prototype.calculateShapeBoundingBox=function(D,F){var O,U,G,W,Y,X=D.sh.v,J=D.transformers,K=X._length;if(!(K<=1)){for(O=0;O<K-1;O+=1)U=this.getTransformedPoint(J,X.v[O]),G=this.getTransformedPoint(J,X.o[O]),W=this.getTransformedPoint(J,X.i[O+1]),Y=this.getTransformedPoint(J,X.v[O+1]),this.checkBounds(U,G,W,Y,F);X.c&&(U=this.getTransformedPoint(J,X.v[O]),G=this.getTransformedPoint(J,X.o[O]),W=this.getTransformedPoint(J,X.i[0]),Y=this.getTransformedPoint(J,X.v[0]),this.checkBounds(U,G,W,Y,F))}},HShapeElement.prototype.checkBounds=function(D,F,O,U,G){this.getBoundsOfCurve(D,F,O,U);var W=this.shapeBoundingBox;G.x=bmMin(W.left,G.x),G.xMax=bmMax(W.right,G.xMax),G.y=bmMin(W.top,G.y),G.yMax=bmMax(W.bottom,G.yMax)},HShapeElement.prototype.shapeBoundingBox={left:0,right:0,top:0,bottom:0},HShapeElement.prototype.tempBoundingBox={x:0,xMax:0,y:0,yMax:0,width:0,height:0},HShapeElement.prototype.getBoundsOfCurve=function(D,F,O,U){for(var G,W,Y,X,J,K,Z,Q=[[D[0],U[0]],[D[1],U[1]]],tt=0;tt<2;++tt)W=6*D[tt]-12*F[tt]+6*O[tt],G=-3*D[tt]+9*F[tt]-9*O[tt]+3*U[tt],Y=3*F[tt]-3*D[tt],W|=0,Y|=0,0==(G|=0)&&0===W||(0===G?(X=-Y/W)>0&&X<1&&Q[tt].push(this.calculateF(X,D,F,O,U,tt)):(J=W*W-4*Y*G)>=0&&((K=(-W+bmSqrt(J))/(2*G))>0&&K<1&&Q[tt].push(this.calculateF(K,D,F,O,U,tt)),(Z=(-W-bmSqrt(J))/(2*G))>0&&Z<1&&Q[tt].push(this.calculateF(Z,D,F,O,U,tt))));this.shapeBoundingBox.left=bmMin.apply(null,Q[0]),this.shapeBoundingBox.top=bmMin.apply(null,Q[1]),this.shapeBoundingBox.right=bmMax.apply(null,Q[0]),this.shapeBoundingBox.bottom=bmMax.apply(null,Q[1])},HShapeElement.prototype.calculateF=function(D,F,O,U,G,W){return bmPow(1-D,3)*F[W]+3*bmPow(1-D,2)*D*O[W]+3*(1-D)*bmPow(D,2)*U[W]+bmPow(D,3)*G[W]},HShapeElement.prototype.calculateBoundingBox=function(D,F){var O,U=D.length;for(O=0;O<U;O+=1)D[O]&&D[O].sh?this.calculateShapeBoundingBox(D[O],F):D[O]&&D[O].it?this.calculateBoundingBox(D[O].it,F):D[O]&&D[O].style&&D[O].w&&this.expandStrokeBoundingBox(D[O].w,F)},HShapeElement.prototype.expandStrokeBoundingBox=function(D,F){var O=0;if(D.keyframes){for(var U=0;U<D.keyframes.length;U+=1){var G=D.keyframes[U].s;G>O&&(O=G)}O*=D.mult}else O=D.v*D.mult;F.x-=O,F.xMax+=O,F.y-=O,F.yMax+=O},HShapeElement.prototype.currentBoxContains=function(D){return this.currentBBox.x<=D.x&&this.currentBBox.y<=D.y&&this.currentBBox.width+this.currentBBox.x>=D.x+D.width&&this.currentBBox.height+this.currentBBox.y>=D.y+D.height},HShapeElement.prototype.renderInnerContent=function(){if(this._renderShapeFrame(),!this.hidden&&(this._isFirstFrame||this._mdf)){var D=this.tempBoundingBox,F=999999;if(D.x=F,D.xMax=-F,D.y=F,D.yMax=-F,this.calculateBoundingBox(this.itemsData,D),D.width=D.xMax<D.x?0:D.xMax-D.x,D.height=D.yMax<D.y?0:D.yMax-D.y,!this.currentBoxContains(D)){var O=!1;if(this.currentBBox.w!==D.width&&(this.currentBBox.w=D.width,this.shapeCont.setAttribute("width",D.width),O=!0),this.currentBBox.h!==D.height&&(this.currentBBox.h=D.height,this.shapeCont.setAttribute("height",D.height),O=!0),O||this.currentBBox.x!==D.x||this.currentBBox.y!==D.y){this.currentBBox.w=D.width,this.currentBBox.h=D.height,this.currentBBox.x=D.x,this.currentBBox.y=D.y,this.shapeCont.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h);var U=this.shapeCont.style,G="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";U.transform=G,U.webkitTransform=G}}}},extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],HTextElement),HTextElement.prototype.createContent=function(){if(this.isMasked=this.checkMasks(),this.isMasked){this.renderType="svg",this.compW=this.comp.data.w,this.compH=this.comp.data.h,this.svgElement.setAttribute("width",this.compW),this.svgElement.setAttribute("height",this.compH);var D=createNS("g");this.maskedElement.appendChild(D),this.innerElem=D}else this.renderType="html",this.innerElem=this.layerElement;this.checkParenting()},HTextElement.prototype.buildNewText=function(){var D=this.textProperty.currentData;this.renderedLetters=createSizedArray(D.l?D.l.length:0);var F=this.innerElem.style,O=D.fc?this.buildColor(D.fc):"rgba(0,0,0,0)";F.fill=O,F.color=O,D.sc&&(F.stroke=this.buildColor(D.sc),F.strokeWidth=D.sw+"px");var U=this.globalData.fontManager.getFontByName(D.f);if(!this.globalData.fontManager.chars){if(F.fontSize=D.finalSize+"px",F.lineHeight=D.finalSize+"px",U.fClass)this.innerElem.className=U.fClass;else{F.fontFamily=U.fFamily;var G=D.fWeight,W=D.fStyle;F.fontStyle=W,F.fontWeight=G}}var Y=D.l;Q=Y.length;var X=this.mHelper,J="",K=0;for(Z=0;Z<Q;Z+=1){if(this.globalData.fontManager.chars?(this.textPaths[K]?tt=this.textPaths[K]:((tt=createNS("path")).setAttribute("stroke-linecap",lineCapEnum[1]),tt.setAttribute("stroke-linejoin",lineJoinEnum[2]),tt.setAttribute("stroke-miterlimit","4")),this.isMasked||(this.textSpans[K]?tr=(te=this.textSpans[K]).children[0]:((te=createTag("div")).style.lineHeight=0,(tr=createNS("svg")).appendChild(tt),styleDiv(te)))):this.isMasked?tt=this.textPaths[K]?this.textPaths[K]:createNS("text"):this.textSpans[K]?(te=this.textSpans[K],tt=this.textPaths[K]):(styleDiv(te=createTag("span")),styleDiv(tt=createTag("span")),te.appendChild(tt)),this.globalData.fontManager.chars){var Z,Q,tt,te,tr,ts,tn,ta=this.globalData.fontManager.getCharData(D.finalText[Z],U.fStyle,this.globalData.fontManager.getFontByName(D.f).fFamily);if(tn=ta?ta.data:null,X.reset(),tn&&tn.shapes&&tn.shapes.length&&(ts=tn.shapes[0].it,X.scale(D.finalSize/100,D.finalSize/100),J=this.createPathShape(X,ts),tt.setAttribute("d",J)),this.isMasked)this.innerElem.appendChild(tt);else{if(this.innerElem.appendChild(te),tn&&tn.shapes){document.body.appendChild(tr);var th=tr.getBBox();tr.setAttribute("width",th.width+2),tr.setAttribute("height",th.height+2),tr.setAttribute("viewBox",th.x-1+" "+(th.y-1)+" "+(th.width+2)+" "+(th.height+2));var tp=tr.style,tf="translate("+(th.x-1)+"px,"+(th.y-1)+"px)";tp.transform=tf,tp.webkitTransform=tf,Y[Z].yOffset=th.y-1}else tr.setAttribute("width",1),tr.setAttribute("height",1);te.appendChild(tr)}}else if(tt.textContent=Y[Z].val,tt.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),this.isMasked)this.innerElem.appendChild(tt);else{this.innerElem.appendChild(te);var tu=tt.style,tc="translate3d(0,"+-D.finalSize/1.2+"px,0)";tu.transform=tc,tu.webkitTransform=tc}this.isMasked?this.textSpans[K]=tt:this.textSpans[K]=te,this.textSpans[K].style.display="block",this.textPaths[K]=tt,K+=1}for(;K<this.textSpans.length;)this.textSpans[K].style.display="none",K+=1},HTextElement.prototype.renderInnerContent=function(){if(this.validateText(),this.data.singleShape){if(!this._isFirstFrame&&!this.lettersChangedFlag)return;if(this.isMasked&&this.finalTransform._matMdf){this.svgElement.setAttribute("viewBox",-this.finalTransform.mProp.p.v[0]+" "+-this.finalTransform.mProp.p.v[1]+" "+this.compW+" "+this.compH),D=this.svgElement.style;var D,F,O,U,G,W,Y="translate("+-this.finalTransform.mProp.p.v[0]+"px,"+-this.finalTransform.mProp.p.v[1]+"px)";D.transform=Y,D.webkitTransform=Y}}if(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag){var X=0,J=this.textAnimator.renderedLetters,K=this.textProperty.currentData.l;for(F=0,O=K.length;F<O;F+=1)K[F].n?X+=1:(G=this.textSpans[F],W=this.textPaths[F],U=J[X],X+=1,U._mdf.m&&(this.isMasked?G.setAttribute("transform",U.m):(G.style.webkitTransform=U.m,G.style.transform=U.m)),G.style.opacity=U.o,U.sw&&U._mdf.sw&&W.setAttribute("stroke-width",U.sw),U.sc&&U._mdf.sc&&W.setAttribute("stroke",U.sc),U.fc&&U._mdf.fc&&(W.setAttribute("fill",U.fc),W.style.color=U.fc));if(this.innerElem.getBBox&&!this.hidden&&(this._isFirstFrame||this._mdf)){var Z=this.innerElem.getBBox();this.currentBBox.w!==Z.width&&(this.currentBBox.w=Z.width,this.svgElement.setAttribute("width",Z.width)),this.currentBBox.h!==Z.height&&(this.currentBBox.h=Z.height,this.svgElement.setAttribute("height",Z.height));var Q=1;if(this.currentBBox.w!==Z.width+2*Q||this.currentBBox.h!==Z.height+2*Q||this.currentBBox.x!==Z.x-Q||this.currentBBox.y!==Z.y-Q){this.currentBBox.w=Z.width+2*Q,this.currentBBox.h=Z.height+2*Q,this.currentBBox.x=Z.x-Q,this.currentBBox.y=Z.y-Q,this.svgElement.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h),D=this.svgElement.style;var tt="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";D.transform=tt,D.webkitTransform=tt}}}},extendPrototype([BaseElement,FrameElement,HierarchyElement],HCameraElement),HCameraElement.prototype.setup=function(){var D,F,O,U,G=this.comp.threeDElements.length;for(D=0;D<G;D+=1)if("3d"===(F=this.comp.threeDElements[D]).type){O=F.perspectiveElem.style,U=F.container.style;var W=this.pe.v+"px",Y="0px 0px 0px",X="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";O.perspective=W,O.webkitPerspective=W,U.transformOrigin=Y,U.mozTransformOrigin=Y,U.webkitTransformOrigin=Y,O.transform=X,O.webkitTransform=X}},HCameraElement.prototype.createElements=function(){},HCameraElement.prototype.hide=function(){},HCameraElement.prototype.renderFrame=function(){var D=this._isFirstFrame;if(this.hierarchy)for(O=0,U=this.hierarchy.length;O<U;O+=1)D=this.hierarchy[O].finalTransform.mProp._mdf||D;if(D||this.pe._mdf||this.p&&this.p._mdf||this.px&&(this.px._mdf||this.py._mdf||this.pz._mdf)||this.rx._mdf||this.ry._mdf||this.rz._mdf||this.or._mdf||this.a&&this.a._mdf){if(this.mat.reset(),this.hierarchy)for(O=U=this.hierarchy.length-1;O>=0;O-=1){var F=this.hierarchy[O].finalTransform.mProp;this.mat.translate(-F.p.v[0],-F.p.v[1],F.p.v[2]),this.mat.rotateX(-F.or.v[0]).rotateY(-F.or.v[1]).rotateZ(F.or.v[2]),this.mat.rotateX(-F.rx.v).rotateY(-F.ry.v).rotateZ(F.rz.v),this.mat.scale(1/F.s.v[0],1/F.s.v[1],1/F.s.v[2]),this.mat.translate(F.a.v[0],F.a.v[1],F.a.v[2])}if(this.p?this.mat.translate(-this.p.v[0],-this.p.v[1],this.p.v[2]):this.mat.translate(-this.px.v,-this.py.v,this.pz.v),this.a){var O,U,G,W=Math.sqrt(Math.pow((G=this.p?[this.p.v[0]-this.a.v[0],this.p.v[1]-this.a.v[1],this.p.v[2]-this.a.v[2]]:[this.px.v-this.a.v[0],this.py.v-this.a.v[1],this.pz.v-this.a.v[2]])[0],2)+Math.pow(G[1],2)+Math.pow(G[2],2)),Y=[G[0]/W,G[1]/W,G[2]/W],X=Math.sqrt(Y[2]*Y[2]+Y[0]*Y[0]),J=Math.atan2(Y[1],X),K=Math.atan2(Y[0],-Y[2]);this.mat.rotateY(K).rotateX(-J)}this.mat.rotateX(-this.rx.v).rotateY(-this.ry.v).rotateZ(this.rz.v),this.mat.rotateX(-this.or.v[0]).rotateY(-this.or.v[1]).rotateZ(this.or.v[2]),this.mat.translate(this.globalData.compSize.w/2,this.globalData.compSize.h/2,0),this.mat.translate(0,0,this.pe.v);var Z=!this._prevMat.equals(this.mat);if((Z||this.pe._mdf)&&this.comp.threeDElements){for(O=0,U=this.comp.threeDElements.length;O<U;O+=1)if("3d"===(Q=this.comp.threeDElements[O]).type){if(Z){var Q,tt,te,tr=this.mat.toCSS();(te=Q.container.style).transform=tr,te.webkitTransform=tr}this.pe._mdf&&((tt=Q.perspectiveElem.style).perspective=this.pe.v+"px",tt.webkitPerspective=this.pe.v+"px")}this.mat.clone(this._prevMat)}}this._isFirstFrame=!1},HCameraElement.prototype.prepareFrame=function(D){this.prepareProperties(D,!0)},HCameraElement.prototype.destroy=function(){},HCameraElement.prototype.getBaseElement=function(){return null},extendPrototype([BaseElement,TransformElement,HBaseElement,HSolidElement,HierarchyElement,FrameElement,RenderableElement],HImageElement),HImageElement.prototype.createContent=function(){var D=this.globalData.getAssetsPath(this.assetData),F=new Image;this.data.hasMask?(this.imageElem=createNS("image"),this.imageElem.setAttribute("width",this.assetData.w+"px"),this.imageElem.setAttribute("height",this.assetData.h+"px"),this.imageElem.setAttributeNS("http://www.w3.org/1999/xlink","href",D),this.layerElement.appendChild(this.imageElem),this.baseElement.setAttribute("width",this.assetData.w),this.baseElement.setAttribute("height",this.assetData.h)):this.layerElement.appendChild(F),F.crossOrigin="anonymous",F.src=D,this.data.ln&&this.baseElement.setAttribute("id",this.data.ln)},extendPrototype([BaseRenderer],HybridRendererBase),HybridRendererBase.prototype.buildItem=SVGRenderer.prototype.buildItem,HybridRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;)this.pendingElements.pop().checkParenting()},HybridRendererBase.prototype.appendElementInPos=function(D,F){var O=D.getBaseElement();if(O){var U=this.layers[F];if(U.ddd&&this.supports3d)this.addTo3dContainer(O,F);else if(this.threeDElements)this.addTo3dContainer(O,F);else{for(var G,W,Y=0;Y<F;)this.elements[Y]&&!0!==this.elements[Y]&&this.elements[Y].getBaseElement&&(W=this.elements[Y],G=(this.layers[Y].ddd?this.getThreeDContainerByPos(Y):W.getBaseElement())||G),Y+=1;G?U.ddd&&this.supports3d||this.layerElement.insertBefore(O,G):U.ddd&&this.supports3d||this.layerElement.appendChild(O)}}},HybridRendererBase.prototype.createShape=function(D){return this.supports3d?new HShapeElement(D,this.globalData,this):new SVGShapeElement(D,this.globalData,this)},HybridRendererBase.prototype.createText=function(D){return this.supports3d?new HTextElement(D,this.globalData,this):new SVGTextLottieElement(D,this.globalData,this)},HybridRendererBase.prototype.createCamera=function(D){return this.camera=new HCameraElement(D,this.globalData,this),this.camera},HybridRendererBase.prototype.createImage=function(D){return this.supports3d?new HImageElement(D,this.globalData,this):new IImageElement(D,this.globalData,this)},HybridRendererBase.prototype.createSolid=function(D){return this.supports3d?new HSolidElement(D,this.globalData,this):new ISolidElement(D,this.globalData,this)},HybridRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,HybridRendererBase.prototype.getThreeDContainerByPos=function(D){for(var F=0,O=this.threeDElements.length;F<O;){if(this.threeDElements[F].startPos<=D&&this.threeDElements[F].endPos>=D)return this.threeDElements[F].perspectiveElem;F+=1}return null},HybridRendererBase.prototype.createThreeDContainer=function(D,F){var O,U,G=createTag("div");styleDiv(G);var W=createTag("div");if(styleDiv(W),"3d"===F){(O=G.style).width=this.globalData.compSize.w+"px",O.height=this.globalData.compSize.h+"px";var Y="50% 50%";O.webkitTransformOrigin=Y,O.mozTransformOrigin=Y,O.transformOrigin=Y;var X="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";(U=W.style).transform=X,U.webkitTransform=X}G.appendChild(W);var J={container:W,perspectiveElem:G,startPos:D,endPos:D,type:F};return this.threeDElements.push(J),J},HybridRendererBase.prototype.build3dContainers=function(){var D,F,O=this.layers.length,U="";for(D=0;D<O;D+=1)this.layers[D].ddd&&3!==this.layers[D].ty?("3d"!==U&&(U="3d",F=this.createThreeDContainer(D,"3d")),F.endPos=Math.max(F.endPos,D)):("2d"!==U&&(U="2d",F=this.createThreeDContainer(D,"2d")),F.endPos=Math.max(F.endPos,D));for(D=(O=this.threeDElements.length)-1;D>=0;D-=1)this.resizerElem.appendChild(this.threeDElements[D].perspectiveElem)},HybridRendererBase.prototype.addTo3dContainer=function(D,F){for(var O=0,U=this.threeDElements.length;O<U;){if(F<=this.threeDElements[O].endPos){for(var G,W=this.threeDElements[O].startPos;W<F;)this.elements[W]&&this.elements[W].getBaseElement&&(G=this.elements[W].getBaseElement()),W+=1;G?this.threeDElements[O].container.insertBefore(D,G):this.threeDElements[O].container.appendChild(D);break}O+=1}},HybridRendererBase.prototype.configAnimation=function(D){var F=createTag("div"),O=this.animationItem.wrapper,U=F.style;U.width=D.w+"px",U.height=D.h+"px",this.resizerElem=F,styleDiv(F),U.transformStyle="flat",U.mozTransformStyle="flat",U.webkitTransformStyle="flat",this.renderConfig.className&&F.setAttribute("class",this.renderConfig.className),O.appendChild(F),U.overflow="hidden";var G=createNS("svg");G.setAttribute("width","1"),G.setAttribute("height","1"),styleDiv(G),this.resizerElem.appendChild(G);var W=createNS("defs");G.appendChild(W),this.data=D,this.setupGlobalData(D,G),this.globalData.defs=W,this.layers=D.layers,this.layerElement=this.resizerElem,this.build3dContainers(),this.updateContainerSize()},HybridRendererBase.prototype.destroy=function(){this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.animationItem.container=null,this.globalData.defs=null;var D,F=this.layers?this.layers.length:0;for(D=0;D<F;D+=1)this.elements[D]&&this.elements[D].destroy&&this.elements[D].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},HybridRendererBase.prototype.updateContainerSize=function(){var D,F,O,U,G=this.animationItem.wrapper.offsetWidth,W=this.animationItem.wrapper.offsetHeight,Y=G/W;this.globalData.compSize.w/this.globalData.compSize.h>Y?(D=G/this.globalData.compSize.w,F=G/this.globalData.compSize.w,O=0,U=(W-this.globalData.compSize.h*(G/this.globalData.compSize.w))/2):(D=W/this.globalData.compSize.h,F=W/this.globalData.compSize.h,O=(G-this.globalData.compSize.w*(W/this.globalData.compSize.h))/2,U=0);var X=this.resizerElem.style;X.webkitTransform="matrix3d("+D+",0,0,0,0,"+F+",0,0,0,0,1,0,"+O+","+U+",0,1)",X.transform=X.webkitTransform},HybridRendererBase.prototype.renderFrame=SVGRenderer.prototype.renderFrame,HybridRendererBase.prototype.hide=function(){this.resizerElem.style.display="none"},HybridRendererBase.prototype.show=function(){this.resizerElem.style.display="block"},HybridRendererBase.prototype.initItems=function(){if(this.buildAllItems(),this.camera)this.camera.setup();else{var D,F=this.globalData.compSize.w,O=this.globalData.compSize.h,U=this.threeDElements.length;for(D=0;D<U;D+=1){var G=this.threeDElements[D].perspectiveElem.style;G.webkitPerspective=Math.sqrt(Math.pow(F,2)+Math.pow(O,2))+"px",G.perspective=G.webkitPerspective}}},HybridRendererBase.prototype.searchExtraCompositions=function(D){var F,O=D.length,U=createTag("div");for(F=0;F<O;F+=1)if(D[F].xt){var G=this.createComp(D[F],U,this.globalData.comp,null);G.initExpressions(),this.globalData.projectInterface.registerComposition(G)}},extendPrototype([HybridRendererBase,ICompElement,HBaseElement],HCompElement),HCompElement.prototype._createBaseContainerElements=HCompElement.prototype.createContainerElements,HCompElement.prototype.createContainerElements=function(){this._createBaseContainerElements(),this.data.hasMask?(this.svgElement.setAttribute("width",this.data.w),this.svgElement.setAttribute("height",this.data.h),this.transformedElement=this.baseElement):this.transformedElement=this.layerElement},HCompElement.prototype.addTo3dContainer=function(D,F){for(var O,U=0;U<F;)this.elements[U]&&this.elements[U].getBaseElement&&(O=this.elements[U].getBaseElement()),U+=1;O?this.layerElement.insertBefore(D,O):this.layerElement.appendChild(D)},HCompElement.prototype.createComp=function(D){return this.supports3d?new HCompElement(D,this.globalData,this):new SVGCompElement(D,this.globalData,this)},extendPrototype([HybridRendererBase],HybridRenderer),HybridRenderer.prototype.createComp=function(D){return this.supports3d?new HCompElement(D,this.globalData,this):new SVGCompElement(D,this.globalData,this)};var CompExpressionInterface=function(){return function(D){function F(F){for(var O=0,U=D.layers.length;O<U;){if(D.layers[O].nm===F||D.layers[O].ind===F)return D.elements[O].layerInterface;O+=1}return null}return Object.defineProperty(F,"_name",{value:D.data.nm}),F.layer=F,F.pixelAspect=1,F.height=D.data.h||D.globalData.compSize.h,F.width=D.data.w||D.globalData.compSize.w,F.pixelAspect=1,F.frameDuration=1/D.globalData.frameRate,F.displayStartTime=0,F.numLayers=D.layers.length,F}}();function _typeof$2(D){return(_typeof$2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(D){return typeof D}:function(D){return D&&"function"==typeof Symbol&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D})(D)}function seedRandom(D,F){var O=this,U=256,G=6,W=52,Y="random",X=F.pow(U,G),J=F.pow(2,W),K=2*J,Z=U-1;function Q(O,W,Z){var Q=[],th=ts(tr((W=!0===W?{entropy:!0}:W||{}).entropy?[O,ta(D)]:null===O?tn():O,3),Q),tp=new tt(Q),tf=function(){for(var D=tp.g(G),F=X,O=0;D<J;)D=(D+O)*U,F*=U,O=tp.g(1);for(;D>=K;)D/=2,F/=2,O>>>=1;return(D+O)/F};return tf.int32=function(){return 0|tp.g(4)},tf.quick=function(){return tp.g(4)/4294967296},tf.double=tf,ts(ta(tp.S),D),(W.pass||Z||function(D,O,U,G){return(G&&(G.S&&te(G,tp),D.state=function(){return te(tp,{})}),U)?(F[Y]=D,O):D})(tf,th,"global"in W?W.global:this==F,W.state)}function tt(D){var F,O=D.length,G=this,W=0,Y=G.i=G.j=0,X=G.S=[];for(O||(D=[O++]);W<U;)X[W]=W++;for(W=0;W<U;W++)X[W]=X[Y=Z&Y+D[W%O]+(F=X[W])],X[Y]=F;G.g=function(D){for(var F,O=0,W=G.i,Y=G.j,X=G.S;D--;)F=X[W=Z&W+1],O=O*U+X[Z&(X[W]=X[Y=Z&Y+F])+(X[Y]=F)];return G.i=W,G.j=Y,O}}function te(D,F){return F.i=D.i,F.j=D.j,F.S=D.S.slice(),F}function tr(D,F){var O,U=[],G=_typeof$2(D);if(F&&"object"==G)for(O in D)try{U.push(tr(D[O],F-1))}catch(D){}return U.length?U:"string"==G?D:D+"\x00"}function ts(D,F){for(var O,U=D+"",G=0;G<U.length;)F[Z&G]=Z&(O^=19*F[Z&G])+U.charCodeAt(G++);return ta(F)}function tn(){try{var F=new Uint8Array(U);return(O.crypto||O.msCrypto).getRandomValues(F),ta(F)}catch(F){var G=O.navigator,W=G&&G.plugins;return[+new Date,O,W,O.screen,ta(D)]}}function ta(D){return String.fromCharCode.apply(0,D)}F["seed"+Y]=Q,ts(F.random(),D)}function initialize$2(D){seedRandom([],D)}var propTypes={SHAPE:"shape"};function _typeof$1(D){return(_typeof$1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(D){return typeof D}:function(D){return D&&"function"==typeof Symbol&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D})(D)}var ExpressionManager=function(){var ob={},Math=BMMath,window=null,document=null,XMLHttpRequest=null,fetch=null,frames=null,_lottieGlobal={};function resetFrame(){_lottieGlobal={}}function $bm_isInstanceOfArray(D){return D.constructor===Array||D.constructor===Float32Array}function isNumerable(D,F){return"number"===D||F instanceof Number||"boolean"===D||"string"===D}function $bm_neg(D){var F=_typeof$1(D);if("number"===F||D instanceof Number||"boolean"===F)return-D;if($bm_isInstanceOfArray(D)){var O,U=D.length,G=[];for(O=0;O<U;O+=1)G[O]=-D[O];return G}return D.propType?D.v:-D}initialize$2(BMMath);var easeInBez=BezierFactory.getBezierEasing(.333,0,.833,.833,"easeIn").get,easeOutBez=BezierFactory.getBezierEasing(.167,.167,.667,1,"easeOut").get,easeInOutBez=BezierFactory.getBezierEasing(.33,0,.667,1,"easeInOut").get;function sum(D,F){var O=_typeof$1(D),U=_typeof$1(F);if(isNumerable(O,D)&&isNumerable(U,F)||"string"===O||"string"===U)return D+F;if($bm_isInstanceOfArray(D)&&isNumerable(U,F))return D=D.slice(0),D[0]+=F,D;if(isNumerable(O,D)&&$bm_isInstanceOfArray(F))return(F=F.slice(0))[0]=D+F[0],F;if($bm_isInstanceOfArray(D)&&$bm_isInstanceOfArray(F)){for(var G=0,W=D.length,Y=F.length,X=[];G<W||G<Y;)("number"==typeof D[G]||D[G]instanceof Number)&&("number"==typeof F[G]||F[G]instanceof Number)?X[G]=D[G]+F[G]:X[G]=void 0===F[G]?D[G]:D[G]||F[G],G+=1;return X}return 0}var add=sum;function sub(D,F){var O=_typeof$1(D),U=_typeof$1(F);if(isNumerable(O,D)&&isNumerable(U,F))return"string"===O&&(D=parseInt(D,10)),"string"===U&&(F=parseInt(F,10)),D-F;if($bm_isInstanceOfArray(D)&&isNumerable(U,F))return D=D.slice(0),D[0]-=F,D;if(isNumerable(O,D)&&$bm_isInstanceOfArray(F))return(F=F.slice(0))[0]=D-F[0],F;if($bm_isInstanceOfArray(D)&&$bm_isInstanceOfArray(F)){for(var G=0,W=D.length,Y=F.length,X=[];G<W||G<Y;)("number"==typeof D[G]||D[G]instanceof Number)&&("number"==typeof F[G]||F[G]instanceof Number)?X[G]=D[G]-F[G]:X[G]=void 0===F[G]?D[G]:D[G]||F[G],G+=1;return X}return 0}function mul(D,F){var O,U,G,W=_typeof$1(D),Y=_typeof$1(F);if(isNumerable(W,D)&&isNumerable(Y,F))return D*F;if($bm_isInstanceOfArray(D)&&isNumerable(Y,F)){for(U=0,O=createTypedArray("float32",G=D.length);U<G;U+=1)O[U]=D[U]*F;return O}if(isNumerable(W,D)&&$bm_isInstanceOfArray(F)){for(U=0,O=createTypedArray("float32",G=F.length);U<G;U+=1)O[U]=D*F[U];return O}return 0}function div(D,F){var O,U,G,W=_typeof$1(D),Y=_typeof$1(F);if(isNumerable(W,D)&&isNumerable(Y,F))return D/F;if($bm_isInstanceOfArray(D)&&isNumerable(Y,F)){for(U=0,O=createTypedArray("float32",G=D.length);U<G;U+=1)O[U]=D[U]/F;return O}if(isNumerable(W,D)&&$bm_isInstanceOfArray(F)){for(U=0,O=createTypedArray("float32",G=F.length);U<G;U+=1)O[U]=D/F[U];return O}return 0}function mod(D,F){return"string"==typeof D&&(D=parseInt(D,10)),"string"==typeof F&&(F=parseInt(F,10)),D%F}var $bm_sum=sum,$bm_sub=sub,$bm_mul=mul,$bm_div=div,$bm_mod=mod;function clamp(D,F,O){if(F>O){var U=O;O=F,F=U}return Math.min(Math.max(D,F),O)}function radiansToDegrees(D){return D/degToRads}var radians_to_degrees=radiansToDegrees;function degreesToRadians(D){return D*degToRads}var degrees_to_radians=radiansToDegrees,helperLengthArray=[0,0,0,0,0,0];function length(D,F){if("number"==typeof D||D instanceof Number)return F=F||0,Math.abs(D-F);F||(F=helperLengthArray);var O,U=Math.min(D.length,F.length),G=0;for(O=0;O<U;O+=1)G+=Math.pow(F[O]-D[O],2);return Math.sqrt(G)}function normalize(D){return div(D,length(D))}function rgbToHsl(D){var F,O,U=D[0],G=D[1],W=D[2],Y=Math.max(U,G,W),X=Math.min(U,G,W),J=(Y+X)/2;if(Y===X)F=0,O=0;else{var K=Y-X;switch(O=J>.5?K/(2-Y-X):K/(Y+X),Y){case U:F=(G-W)/K+(G<W?6:0);break;case G:F=(W-U)/K+2;break;case W:F=(U-G)/K+4}F/=6}return[F,O,J,D[3]]}function hue2rgb(D,F,O){return(O<0&&(O+=1),O>1&&(O-=1),O<1/6)?D+(F-D)*6*O:O<.5?F:O<2/3?D+(F-D)*(2/3-O)*6:D}function hslToRgb(D){var F,O,U,G=D[0],W=D[1],Y=D[2];if(0===W)F=Y,U=Y,O=Y;else{var X=Y<.5?Y*(1+W):Y+W-Y*W,J=2*Y-X;F=hue2rgb(J,X,G+1/3),O=hue2rgb(J,X,G),U=hue2rgb(J,X,G-1/3)}return[F,O,U,D[3]]}function linear(D,F,O,U,G){if((void 0===U||void 0===G)&&(U=F,G=O,F=0,O=1),O<F){var W,Y=O;O=F,F=Y}if(D<=F)return U;if(D>=O)return G;var X=O===F?0:(D-F)/(O-F);if(!U.length)return U+(G-U)*X;var J=U.length,K=createTypedArray("float32",J);for(W=0;W<J;W+=1)K[W]=U[W]+(G[W]-U[W])*X;return K}function random(D,F){if(void 0===F&&(void 0===D?(D=0,F=1):(F=D,D=void 0)),F.length){var O,U=F.length;D||(D=createTypedArray("float32",U));var G=createTypedArray("float32",U),W=BMMath.random();for(O=0;O<U;O+=1)G[O]=D[O]+W*(F[O]-D[O]);return G}return void 0===D&&(D=0),D+BMMath.random()*(F-D)}function createPath(D,F,O,U){var G,W,Y,X=D.length,J=shapePool.newElement();J.setPathData(!!U,X);var K=[0,0];for(G=0;G<X;G+=1)W=F&&F[G]?F[G]:K,Y=O&&O[G]?O[G]:K,J.setTripleAt(D[G][0],D[G][1],Y[0]+D[G][0],Y[1]+D[G][1],W[0]+D[G][0],W[1]+D[G][1],G,!0);return J}function initiateExpression(elem,data,property){function noOp(D){return D}if(!elem.globalData.renderConfig.runExpressions)return noOp;var transform,$bm_transform,content,effect,loopIn,loop_in,loopOut,loop_out,smooth,toWorld,fromWorld,fromComp,toComp,fromCompToSurface,position,rotation,anchorPoint,scale,thisLayer,thisComp,mask,valueAtTime,velocityAtTime,scoped_bm_rt,time,velocity,value,text,textIndex,textTotal,selectorValue,parent,val=data.x,needsVelocity=/velocity(?![\w\d])/.test(val),_needsRandom=-1!==val.indexOf("random"),elemType=elem.data.ty,thisProperty=property;thisProperty.valueAtTime=thisProperty.getValueAtTime,Object.defineProperty(thisProperty,"value",{get:function(){return thisProperty.v}}),elem.comp.frameDuration=1/elem.comp.globalData.frameRate,elem.comp.displayStartTime=0;var inPoint=elem.data.ip/elem.comp.globalData.frameRate,outPoint=elem.data.op/elem.comp.globalData.frameRate,width=elem.data.sw?elem.data.sw:0,height=elem.data.sh?elem.data.sh:0,name=elem.data.nm,expression_function=eval("[function _expression_function(){"+val+";scoped_bm_rt=$bm_rt}]")[0],numKeys=property.kf?data.k.length:0,active=!this.data||!0!==this.data.hd,wiggle=(function(D,F){var O,U,G=this.pv.length?this.pv.length:1,W=createTypedArray("float32",G);D=5;var Y=Math.floor(time*D);for(O=0,U=0;O<Y;){for(U=0;U<G;U+=1)W[U]+=-F+2*F*BMMath.random();O+=1}var X=time*D,J=X-Math.floor(X),K=createTypedArray("float32",G);if(G>1){for(U=0;U<G;U+=1)K[U]=this.pv[U]+W[U]+(-F+2*F*BMMath.random())*J;return K}return this.pv+W[0]+(-F+2*F*BMMath.random())*J}).bind(this);function loopInDuration(D,F){return loopIn(D,F,!0)}function loopOutDuration(D,F){return loopOut(D,F,!0)}thisProperty.loopIn&&(loop_in=loopIn=thisProperty.loopIn.bind(thisProperty)),thisProperty.loopOut&&(loop_out=loopOut=thisProperty.loopOut.bind(thisProperty)),thisProperty.smooth&&(smooth=thisProperty.smooth.bind(thisProperty)),this.getValueAtTime&&(valueAtTime=this.getValueAtTime.bind(this)),this.getVelocityAtTime&&(velocityAtTime=this.getVelocityAtTime.bind(this));var comp=elem.comp.globalData.projectInterface.bind(elem.comp.globalData.projectInterface);function lookAt(D,F){var O=[F[0]-D[0],F[1]-D[1],F[2]-D[2]],U=Math.atan2(O[0],Math.sqrt(O[1]*O[1]+O[2]*O[2]))/degToRads;return[-Math.atan2(O[1],O[2])/degToRads,U,0]}function easeOut(D,F,O,U,G){return applyEase(easeOutBez,D,F,O,U,G)}function easeIn(D,F,O,U,G){return applyEase(easeInBez,D,F,O,U,G)}function ease(D,F,O,U,G){return applyEase(easeInOutBez,D,F,O,U,G)}function applyEase(D,F,O,U,G,W){void 0===G?(G=O,W=U):F=(F-O)/(U-O),F>1?F=1:F<0&&(F=0);var Y=D(F);if($bm_isInstanceOfArray(G)){var X,J=G.length,K=createTypedArray("float32",J);for(X=0;X<J;X+=1)K[X]=(W[X]-G[X])*Y+G[X];return K}return(W-G)*Y+G}function nearestKey(D){var F,O,U,G=data.k.length;if(data.k.length&&"number"!=typeof data.k[0]){if(O=-1,(D*=elem.comp.globalData.frameRate)<data.k[0].t)O=1,U=data.k[0].t;else{for(F=0;F<G-1;F+=1){if(D===data.k[F].t){O=F+1,U=data.k[F].t;break}if(D>data.k[F].t&&D<data.k[F+1].t){D-data.k[F].t>data.k[F+1].t-D?(O=F+2,U=data.k[F+1].t):(O=F+1,U=data.k[F].t);break}}-1===O&&(O=F+1,U=data.k[F].t)}}else O=0,U=0;var W={};return W.index=O,W.time=U/elem.comp.globalData.frameRate,W}function key(D){if(!data.k.length||"number"==typeof data.k[0])throw Error("The property has no keyframe at index "+D);D-=1,F={time:data.k[D].t/elem.comp.globalData.frameRate,value:[]};var F,O,U,G=Object.prototype.hasOwnProperty.call(data.k[D],"s")?data.k[D].s:data.k[D-1].e;for(O=0,U=G.length;O<U;O+=1)F[O]=G[O],F.value[O]=G[O];return F}function framesToTime(D,F){return F||(F=elem.comp.globalData.frameRate),D/F}function timeToFrames(D,F){return D||0===D||(D=time),F||(F=elem.comp.globalData.frameRate),D*F}function seedRandom(D){BMMath.seedrandom(randSeed+D)}function sourceRectAtTime(){return elem.sourceRectAtTime()}function substring(D,F){return"string"==typeof value?void 0===F?value.substring(D):value.substring(D,F):""}function substr(D,F){return"string"==typeof value?void 0===F?value.substr(D):value.substr(D,F):""}function posterizeTime(D){value=valueAtTime(time=0===D?0:Math.floor(time*D)/D)}var index=elem.data.ind,hasParent=!!(elem.hierarchy&&elem.hierarchy.length),randSeed=Math.floor(1e6*Math.random()),globalData=elem.globalData;function executeExpression(D){return(value=D,this.frameExpressionId===elem.globalData.frameId&&"textSelector"!==this.propType)?value:("textSelector"===this.propType&&(textIndex=this.textIndex,textTotal=this.textTotal,selectorValue=this.selectorValue),thisLayer||(text=elem.layerInterface.text,thisLayer=elem.layerInterface,thisComp=elem.comp.compInterface,toWorld=thisLayer.toWorld.bind(thisLayer),fromWorld=thisLayer.fromWorld.bind(thisLayer),fromComp=thisLayer.fromComp.bind(thisLayer),toComp=thisLayer.toComp.bind(thisLayer),mask=thisLayer.mask?thisLayer.mask.bind(thisLayer):null,fromCompToSurface=fromComp),!transform&&($bm_transform=transform=elem.layerInterface("ADBE Transform Group"),transform&&(anchorPoint=transform.anchorPoint)),4!==elemType||content||(content=thisLayer("ADBE Root Vectors Group")),effect||(effect=thisLayer(4)),(hasParent=!!(elem.hierarchy&&elem.hierarchy.length))&&!parent&&(parent=elem.hierarchy[0].layerInterface),time=this.comp.renderedFrame/this.comp.globalData.frameRate,_needsRandom&&seedRandom(randSeed+time),needsVelocity&&(velocity=velocityAtTime(time)),expression_function(),this.frameExpressionId=elem.globalData.frameId,scoped_bm_rt=scoped_bm_rt.propType===propTypes.SHAPE?scoped_bm_rt.v:scoped_bm_rt)}return executeExpression.__preventDeadCodeRemoval=[$bm_transform,anchorPoint,time,velocity,inPoint,outPoint,width,height,name,loop_in,loop_out,smooth,toComp,fromCompToSurface,toWorld,fromWorld,mask,position,rotation,scale,thisComp,numKeys,active,wiggle,loopInDuration,loopOutDuration,comp,lookAt,easeOut,easeIn,ease,nearestKey,key,text,textIndex,textTotal,selectorValue,framesToTime,timeToFrames,sourceRectAtTime,substring,substr,posterizeTime,index,globalData],executeExpression}return ob.initiateExpression=initiateExpression,ob.__preventDeadCodeRemoval=[window,document,XMLHttpRequest,fetch,frames,$bm_neg,add,$bm_sum,$bm_sub,$bm_mul,$bm_div,$bm_mod,clamp,radians_to_degrees,degreesToRadians,degrees_to_radians,normalize,rgbToHsl,hslToRgb,linear,random,createPath,_lottieGlobal],ob.resetFrame=resetFrame,ob}(),Expressions=function(){var D={};function F(D){var F=0,O=[];function U(){F+=1}function G(){0==(F-=1)&&Y()}function W(D){-1===O.indexOf(D)&&O.push(D)}function Y(){var D,F=O.length;for(D=0;D<F;D+=1)O[D].release();O.length=0}D.renderer.compInterface=CompExpressionInterface(D.renderer),D.renderer.globalData.projectInterface.registerComposition(D.renderer),D.renderer.globalData.pushExpression=U,D.renderer.globalData.popExpression=G,D.renderer.globalData.registerExpressionProperty=W}return D.initExpressions=F,D.resetFrame=ExpressionManager.resetFrame,D}(),MaskManagerInterface=function(){function D(D,F){this._mask=D,this._data=F}return Object.defineProperty(D.prototype,"maskPath",{get:function(){return this._mask.prop.k&&this._mask.prop.getValue(),this._mask.prop}}),Object.defineProperty(D.prototype,"maskOpacity",{get:function(){return this._mask.op.k&&this._mask.op.getValue(),100*this._mask.op.v}}),function(F){var O,U=createSizedArray(F.viewData.length),G=F.viewData.length;for(O=0;O<G;O+=1)U[O]=new D(F.viewData[O],F.masksProperties[O]);return function(D){for(O=0;O<G;){if(F.masksProperties[O].nm===D)return U[O];O+=1}return null}}}(),ExpressionPropertyInterface=function(){var D={pv:0,v:0,mult:1},F={pv:[0,0,0],v:[0,0,0],mult:1};function O(D,F,O){Object.defineProperty(D,"velocity",{get:function(){return F.getVelocityAtTime(F.comp.currentFrame)}}),D.numKeys=F.keyframes?F.keyframes.length:0,D.key=function(U){if(!D.numKeys)return 0;var G="";G="s"in F.keyframes[U-1]?F.keyframes[U-1].s:"e"in F.keyframes[U-2]?F.keyframes[U-2].e:F.keyframes[U-2].s;var W="unidimensional"===O?new Number(G):Object.assign({},G);return W.time=F.keyframes[U-1].t/F.elem.comp.globalData.frameRate,W.value="unidimensional"===O?G[0]:G,W},D.valueAtTime=F.getValueAtTime,D.speedAtTime=F.getSpeedAtTime,D.velocityAtTime=F.getVelocityAtTime,D.propertyGroup=F.propertyGroup}function U(F){F&&"pv"in F||(F=D);var U=1/F.mult,G=F.pv*U,W=new Number(G);return W.value=G,O(W,F,"unidimensional"),function(){return F.k&&F.getValue(),G=F.v*U,W.value!==G&&((W=new Number(G)).value=G,O(W,F,"unidimensional")),W}}function G(D){D&&"pv"in D||(D=F);var U=1/D.mult,G=D.data&&D.data.l||D.pv.length,W=createTypedArray("float32",G),Y=createTypedArray("float32",G);return W.value=Y,O(W,D,"multidimensional"),function(){D.k&&D.getValue();for(var F=0;F<G;F+=1)Y[F]=D.v[F]*U,W[F]=Y[F];return W}}function W(){return D}return function(D){return D?"unidimensional"===D.propType?U(D):G(D):W}}(),TransformExpressionInterface=function(){return function(D){var F,O,U,G;function W(D){switch(D){case"scale":case"Scale":case"ADBE Scale":case 6:return W.scale;case"rotation":case"Rotation":case"ADBE Rotation":case"ADBE Rotate Z":case 10:return W.rotation;case"ADBE Rotate X":return W.xRotation;case"ADBE Rotate Y":return W.yRotation;case"position":case"Position":case"ADBE Position":case 2:return W.position;case"ADBE Position_0":return W.xPosition;case"ADBE Position_1":return W.yPosition;case"ADBE Position_2":return W.zPosition;case"anchorPoint":case"AnchorPoint":case"Anchor Point":case"ADBE AnchorPoint":case 1:return W.anchorPoint;case"opacity":case"Opacity":case 11:return W.opacity;default:return null}}return Object.defineProperty(W,"rotation",{get:ExpressionPropertyInterface(D.r||D.rz)}),Object.defineProperty(W,"zRotation",{get:ExpressionPropertyInterface(D.rz||D.r)}),Object.defineProperty(W,"xRotation",{get:ExpressionPropertyInterface(D.rx)}),Object.defineProperty(W,"yRotation",{get:ExpressionPropertyInterface(D.ry)}),Object.defineProperty(W,"scale",{get:ExpressionPropertyInterface(D.s)}),D.p?G=ExpressionPropertyInterface(D.p):(F=ExpressionPropertyInterface(D.px),O=ExpressionPropertyInterface(D.py),D.pz&&(U=ExpressionPropertyInterface(D.pz))),Object.defineProperty(W,"position",{get:function(){return D.p?G():[F(),O(),U?U():0]}}),Object.defineProperty(W,"xPosition",{get:ExpressionPropertyInterface(D.px)}),Object.defineProperty(W,"yPosition",{get:ExpressionPropertyInterface(D.py)}),Object.defineProperty(W,"zPosition",{get:ExpressionPropertyInterface(D.pz)}),Object.defineProperty(W,"anchorPoint",{get:ExpressionPropertyInterface(D.a)}),Object.defineProperty(W,"opacity",{get:ExpressionPropertyInterface(D.o)}),Object.defineProperty(W,"skew",{get:ExpressionPropertyInterface(D.sk)}),Object.defineProperty(W,"skewAxis",{get:ExpressionPropertyInterface(D.sa)}),Object.defineProperty(W,"orientation",{get:ExpressionPropertyInterface(D.or)}),W}}(),LayerExpressionInterface=function(){function D(D){var F=new Matrix;return void 0!==D?this._elem.finalTransform.mProp.getValueAtTime(D).clone(F):this._elem.finalTransform.mProp.applyToMatrix(F),F}function F(D,F){var O=this.getMatrix(F);return O.props[12]=0,O.props[13]=0,O.props[14]=0,this.applyPoint(O,D)}function O(D,F){var O=this.getMatrix(F);return this.applyPoint(O,D)}function U(D,F){var O=this.getMatrix(F);return O.props[12]=0,O.props[13]=0,O.props[14]=0,this.invertPoint(O,D)}function G(D,F){var O=this.getMatrix(F);return this.invertPoint(O,D)}function W(D,F){if(this._elem.hierarchy&&this._elem.hierarchy.length){var O,U=this._elem.hierarchy.length;for(O=0;O<U;O+=1)this._elem.hierarchy[O].finalTransform.mProp.applyToMatrix(D)}return D.applyToPointArray(F[0],F[1],F[2]||0)}function Y(D,F){if(this._elem.hierarchy&&this._elem.hierarchy.length){var O,U=this._elem.hierarchy.length;for(O=0;O<U;O+=1)this._elem.hierarchy[O].finalTransform.mProp.applyToMatrix(D)}return D.inversePoint(F)}function X(D){var F=new Matrix;if(F.reset(),this._elem.finalTransform.mProp.applyToMatrix(F),this._elem.hierarchy&&this._elem.hierarchy.length){var O,U=this._elem.hierarchy.length;for(O=0;O<U;O+=1)this._elem.hierarchy[O].finalTransform.mProp.applyToMatrix(F)}return F.inversePoint(D)}function J(){return[1,1,1,1]}return function(K){function Z(D){tt.mask=new MaskManagerInterface(D,K)}function Q(D){tt.effect=D}function tt(D){switch(D){case"ADBE Root Vectors Group":case"Contents":case 2:return tt.shapeInterface;case 1:case 6:case"Transform":case"transform":case"ADBE Transform Group":return te;case 4:case"ADBE Effect Parade":case"effects":case"Effects":return tt.effect;case"ADBE Text Properties":return tt.textInterface;default:return null}}tt.getMatrix=D,tt.invertPoint=Y,tt.applyPoint=W,tt.toWorld=O,tt.toWorldVec=F,tt.fromWorld=G,tt.fromWorldVec=U,tt.toComp=O,tt.fromComp=X,tt.sampleImage=J,tt.sourceRectAtTime=K.sourceRectAtTime.bind(K),tt._elem=K;var te,tr=getDescriptor(te=TransformExpressionInterface(K.finalTransform.mProp),"anchorPoint");return Object.defineProperties(tt,{hasParent:{get:function(){return K.hierarchy.length}},parent:{get:function(){return K.hierarchy[0].layerInterface}},rotation:getDescriptor(te,"rotation"),scale:getDescriptor(te,"scale"),position:getDescriptor(te,"position"),opacity:getDescriptor(te,"opacity"),anchorPoint:tr,anchor_point:tr,transform:{get:function(){return te}},active:{get:function(){return K.isInRange}}}),tt.startTime=K.data.st,tt.index=K.data.ind,tt.source=K.data.refId,tt.height=0===K.data.ty?K.data.h:100,tt.width=0===K.data.ty?K.data.w:100,tt.inPoint=K.data.ip/K.comp.globalData.frameRate,tt.outPoint=K.data.op/K.comp.globalData.frameRate,tt._name=K.data.nm,tt.registerMaskInterface=Z,tt.registerEffectsInterface=Q,tt}}(),propertyGroupFactory=function(){return function(D,F){return function(O){return(O=void 0===O?1:O)<=0?D:F(O-1)}}}(),PropertyInterface=function(){return function(D,F){var O={_name:D};return function(D){return(D=void 0===D?1:D)<=0?O:F(D-1)}}}(),EffectsExpressionInterface=function(){function D(O,U,G,W){function Y(D){for(var F=O.ef,U=0,G=F.length;U<G;){if(D===F[U].nm||D===F[U].mn||D===F[U].ix){if(5===F[U].ty)return K[U];return K[U]()}U+=1}throw Error()}var X,J=propertyGroupFactory(Y,G),K=[],Z=O.ef.length;for(X=0;X<Z;X+=1)5===O.ef[X].ty?K.push(D(O.ef[X],U.effectElements[X],U.effectElements[X].propertyGroup,W)):K.push(F(U.effectElements[X],O.ef[X].ty,W,J));return"ADBE Color Control"===O.mn&&Object.defineProperty(Y,"color",{get:function(){return K[0]()}}),Object.defineProperties(Y,{numProperties:{get:function(){return O.np}},_name:{value:O.nm},propertyGroup:{value:J}}),Y.enabled=0!==O.en,Y.active=Y.enabled,Y}function F(D,F,O,U){var G=ExpressionPropertyInterface(D.p);function W(){return 10===F?O.comp.compInterface(D.p.v):G()}return D.p.setGroupProperty&&D.p.setGroupProperty(PropertyInterface("",U)),W}return{createEffectsInterface:function(F,O){if(F.effectsManager){var U,G=[],W=F.data.ef,Y=F.effectsManager.effectElements.length;for(U=0;U<Y;U+=1)G.push(D(W[U],F.effectsManager.effectElements[U],O,F));var X=F.data.ef||[],J=function(D){for(U=0,Y=X.length;U<Y;){if(D===X[U].nm||D===X[U].mn||D===X[U].ix)return G[U];U+=1}return null};return Object.defineProperty(J,"numProperties",{get:function(){return X.length}}),J}return null}}}(),ShapePathInterface=function(){return function(D,F,O){var U=F.sh;function G(D){return"Shape"===D||"shape"===D||"Path"===D||"path"===D||"ADBE Vector Shape"===D||2===D?G.path:null}var W=propertyGroupFactory(G,O);return U.setGroupProperty(PropertyInterface("Path",W)),Object.defineProperties(G,{path:{get:function(){return U.k&&U.getValue(),U}},shape:{get:function(){return U.k&&U.getValue(),U}},_name:{value:D.nm},ix:{value:D.ix},propertyIndex:{value:D.ix},mn:{value:D.mn},propertyGroup:{value:O}}),G}}(),ShapeExpressionInterface=function(){function D(D,F,J){var tr,ts=[],tn=D?D.length:0;for(tr=0;tr<tn;tr+=1)"gr"===D[tr].ty?ts.push(O(D[tr],F[tr],J)):"fl"===D[tr].ty?ts.push(U(D[tr],F[tr],J)):"st"===D[tr].ty?ts.push(Y(D[tr],F[tr],J)):"tm"===D[tr].ty?ts.push(X(D[tr],F[tr],J)):"tr"===D[tr].ty||("el"===D[tr].ty?ts.push(K(D[tr],F[tr],J)):"sr"===D[tr].ty?ts.push(Z(D[tr],F[tr],J)):"sh"===D[tr].ty?ts.push(ShapePathInterface(D[tr],F[tr],J)):"rc"===D[tr].ty?ts.push(Q(D[tr],F[tr],J)):"rd"===D[tr].ty?ts.push(tt(D[tr],F[tr],J)):"rp"===D[tr].ty?ts.push(te(D[tr],F[tr],J)):"gf"===D[tr].ty?ts.push(G(D[tr],F[tr],J)):ts.push(W(D[tr],F[tr])));return ts}function F(F,O,U){var G,W=function(D){for(var F=0,O=G.length;F<O;){if(G[F]._name===D||G[F].mn===D||G[F].propertyIndex===D||G[F].ix===D||G[F].ind===D)return G[F];F+=1}return"number"==typeof D?G[D-1]:null};W.propertyGroup=propertyGroupFactory(W,U),G=D(F.it,O.it,W.propertyGroup),W.numProperties=G.length;var Y=J(F.it[F.it.length-1],O.it[O.it.length-1],W.propertyGroup);return W.transform=Y,W.propertyIndex=F.cix,W._name=F.nm,W}function O(D,O,U){var G=function(D){switch(D){case"ADBE Vectors Group":case"Contents":case 2:return G.content;default:return G.transform}};G.propertyGroup=propertyGroupFactory(G,U);var W=F(D,O,G.propertyGroup),Y=J(D.it[D.it.length-1],O.it[O.it.length-1],G.propertyGroup);return G.content=W,G.transform=Y,Object.defineProperty(G,"_name",{get:function(){return D.nm}}),G.numProperties=D.np,G.propertyIndex=D.ix,G.nm=D.nm,G.mn=D.mn,G}function U(D,F,O){function U(D){return"Color"===D||"color"===D?U.color:"Opacity"===D||"opacity"===D?U.opacity:null}return Object.defineProperties(U,{color:{get:ExpressionPropertyInterface(F.c)},opacity:{get:ExpressionPropertyInterface(F.o)},_name:{value:D.nm},mn:{value:D.mn}}),F.c.setGroupProperty(PropertyInterface("Color",O)),F.o.setGroupProperty(PropertyInterface("Opacity",O)),U}function G(D,F,O){function U(D){return"Start Point"===D||"start point"===D?U.startPoint:"End Point"===D||"end point"===D?U.endPoint:"Opacity"===D||"opacity"===D?U.opacity:null}return Object.defineProperties(U,{startPoint:{get:ExpressionPropertyInterface(F.s)},endPoint:{get:ExpressionPropertyInterface(F.e)},opacity:{get:ExpressionPropertyInterface(F.o)},type:{get:function(){return"a"}},_name:{value:D.nm},mn:{value:D.mn}}),F.s.setGroupProperty(PropertyInterface("Start Point",O)),F.e.setGroupProperty(PropertyInterface("End Point",O)),F.o.setGroupProperty(PropertyInterface("Opacity",O)),U}function W(){return function(){return null}}function Y(D,F,O){var U,G=propertyGroupFactory(K,O),W=propertyGroupFactory(J,G);function Y(O){Object.defineProperty(J,D.d[O].nm,{get:ExpressionPropertyInterface(F.d.dataProps[O].p)})}var X=D.d?D.d.length:0,J={};for(U=0;U<X;U+=1)Y(U),F.d.dataProps[U].p.setGroupProperty(W);function K(D){return"Color"===D||"color"===D?K.color:"Opacity"===D||"opacity"===D?K.opacity:"Stroke Width"===D||"stroke width"===D?K.strokeWidth:null}return Object.defineProperties(K,{color:{get:ExpressionPropertyInterface(F.c)},opacity:{get:ExpressionPropertyInterface(F.o)},strokeWidth:{get:ExpressionPropertyInterface(F.w)},dash:{get:function(){return J}},_name:{value:D.nm},mn:{value:D.mn}}),F.c.setGroupProperty(PropertyInterface("Color",G)),F.o.setGroupProperty(PropertyInterface("Opacity",G)),F.w.setGroupProperty(PropertyInterface("Stroke Width",G)),K}function X(D,F,O){function U(F){return F===D.e.ix||"End"===F||"end"===F?U.end:F===D.s.ix?U.start:F===D.o.ix?U.offset:null}var G=propertyGroupFactory(U,O);return U.propertyIndex=D.ix,F.s.setGroupProperty(PropertyInterface("Start",G)),F.e.setGroupProperty(PropertyInterface("End",G)),F.o.setGroupProperty(PropertyInterface("Offset",G)),U.propertyIndex=D.ix,U.propertyGroup=O,Object.defineProperties(U,{start:{get:ExpressionPropertyInterface(F.s)},end:{get:ExpressionPropertyInterface(F.e)},offset:{get:ExpressionPropertyInterface(F.o)},_name:{value:D.nm}}),U.mn=D.mn,U}function J(D,F,O){function U(F){return D.a.ix===F||"Anchor Point"===F?U.anchorPoint:D.o.ix===F||"Opacity"===F?U.opacity:D.p.ix===F||"Position"===F?U.position:D.r.ix===F||"Rotation"===F||"ADBE Vector Rotation"===F?U.rotation:D.s.ix===F||"Scale"===F?U.scale:D.sk&&D.sk.ix===F||"Skew"===F?U.skew:D.sa&&D.sa.ix===F||"Skew Axis"===F?U.skewAxis:null}var G=propertyGroupFactory(U,O);return F.transform.mProps.o.setGroupProperty(PropertyInterface("Opacity",G)),F.transform.mProps.p.setGroupProperty(PropertyInterface("Position",G)),F.transform.mProps.a.setGroupProperty(PropertyInterface("Anchor Point",G)),F.transform.mProps.s.setGroupProperty(PropertyInterface("Scale",G)),F.transform.mProps.r.setGroupProperty(PropertyInterface("Rotation",G)),F.transform.mProps.sk&&(F.transform.mProps.sk.setGroupProperty(PropertyInterface("Skew",G)),F.transform.mProps.sa.setGroupProperty(PropertyInterface("Skew Angle",G))),F.transform.op.setGroupProperty(PropertyInterface("Opacity",G)),Object.defineProperties(U,{opacity:{get:ExpressionPropertyInterface(F.transform.mProps.o)},position:{get:ExpressionPropertyInterface(F.transform.mProps.p)},anchorPoint:{get:ExpressionPropertyInterface(F.transform.mProps.a)},scale:{get:ExpressionPropertyInterface(F.transform.mProps.s)},rotation:{get:ExpressionPropertyInterface(F.transform.mProps.r)},skew:{get:ExpressionPropertyInterface(F.transform.mProps.sk)},skewAxis:{get:ExpressionPropertyInterface(F.transform.mProps.sa)},_name:{value:D.nm}}),U.ty="tr",U.mn=D.mn,U.propertyGroup=O,U}function K(D,F,O){function U(F){return D.p.ix===F?U.position:D.s.ix===F?U.size:null}var G=propertyGroupFactory(U,O);U.propertyIndex=D.ix;var W="tm"===F.sh.ty?F.sh.prop:F.sh;return W.s.setGroupProperty(PropertyInterface("Size",G)),W.p.setGroupProperty(PropertyInterface("Position",G)),Object.defineProperties(U,{size:{get:ExpressionPropertyInterface(W.s)},position:{get:ExpressionPropertyInterface(W.p)},_name:{value:D.nm}}),U.mn=D.mn,U}function Z(D,F,O){function U(F){return D.p.ix===F?U.position:D.r.ix===F?U.rotation:D.pt.ix===F?U.points:D.or.ix===F||"ADBE Vector Star Outer Radius"===F?U.outerRadius:D.os.ix===F?U.outerRoundness:D.ir&&(D.ir.ix===F||"ADBE Vector Star Inner Radius"===F)?U.innerRadius:D.is&&D.is.ix===F?U.innerRoundness:null}var G=propertyGroupFactory(U,O),W="tm"===F.sh.ty?F.sh.prop:F.sh;return U.propertyIndex=D.ix,W.or.setGroupProperty(PropertyInterface("Outer Radius",G)),W.os.setGroupProperty(PropertyInterface("Outer Roundness",G)),W.pt.setGroupProperty(PropertyInterface("Points",G)),W.p.setGroupProperty(PropertyInterface("Position",G)),W.r.setGroupProperty(PropertyInterface("Rotation",G)),D.ir&&(W.ir.setGroupProperty(PropertyInterface("Inner Radius",G)),W.is.setGroupProperty(PropertyInterface("Inner Roundness",G))),Object.defineProperties(U,{position:{get:ExpressionPropertyInterface(W.p)},rotation:{get:ExpressionPropertyInterface(W.r)},points:{get:ExpressionPropertyInterface(W.pt)},outerRadius:{get:ExpressionPropertyInterface(W.or)},outerRoundness:{get:ExpressionPropertyInterface(W.os)},innerRadius:{get:ExpressionPropertyInterface(W.ir)},innerRoundness:{get:ExpressionPropertyInterface(W.is)},_name:{value:D.nm}}),U.mn=D.mn,U}function Q(D,F,O){function U(F){return D.p.ix===F?U.position:D.r.ix===F?U.roundness:D.s.ix===F||"Size"===F||"ADBE Vector Rect Size"===F?U.size:null}var G=propertyGroupFactory(U,O),W="tm"===F.sh.ty?F.sh.prop:F.sh;return U.propertyIndex=D.ix,W.p.setGroupProperty(PropertyInterface("Position",G)),W.s.setGroupProperty(PropertyInterface("Size",G)),W.r.setGroupProperty(PropertyInterface("Rotation",G)),Object.defineProperties(U,{position:{get:ExpressionPropertyInterface(W.p)},roundness:{get:ExpressionPropertyInterface(W.r)},size:{get:ExpressionPropertyInterface(W.s)},_name:{value:D.nm}}),U.mn=D.mn,U}function tt(D,F,O){function U(F){return D.r.ix===F||"Round Corners 1"===F?U.radius:null}var G=propertyGroupFactory(U,O),W=F;return U.propertyIndex=D.ix,W.rd.setGroupProperty(PropertyInterface("Radius",G)),Object.defineProperties(U,{radius:{get:ExpressionPropertyInterface(W.rd)},_name:{value:D.nm}}),U.mn=D.mn,U}function te(D,F,O){function U(F){return D.c.ix===F||"Copies"===F?U.copies:D.o.ix===F||"Offset"===F?U.offset:null}var G=propertyGroupFactory(U,O),W=F;return U.propertyIndex=D.ix,W.c.setGroupProperty(PropertyInterface("Copies",G)),W.o.setGroupProperty(PropertyInterface("Offset",G)),Object.defineProperties(U,{copies:{get:ExpressionPropertyInterface(W.c)},offset:{get:ExpressionPropertyInterface(W.o)},_name:{value:D.nm}}),U.mn=D.mn,U}return function(F,O,U){var G;function W(D){if("number"==typeof D)return 0===(D=void 0===D?1:D)?U:G[D-1];for(var F=0,O=G.length;F<O;){if(G[F]._name===D)return G[F];F+=1}return null}function Y(){return U}return W.propertyGroup=propertyGroupFactory(W,Y),G=D(F,O,W.propertyGroup),W.numProperties=G.length,W._name="Contents",W}}(),TextExpressionInterface=function(){return function(D){var F;function O(D){return"ADBE Text Document"===D?O.sourceText:null}return Object.defineProperty(O,"sourceText",{get:function(){D.textProperty.getValue();var O=D.textProperty.currentData.t;return F&&O===F.value||((F=new String(O)).value=O||new String(O),Object.defineProperty(F,"style",{get:function(){return{fillColor:D.textProperty.currentData.fc}}})),F}}),O}}();function _typeof(D){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(D){return typeof D}:function(D){return D&&"function"==typeof Symbol&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D})(D)}var FootageInterface=function(){var D=function(D){var F="",O=D.getFootageData();function U(D){if(O[D])return(F=D,"object"===_typeof(O=O[D]))?U:O;var G=D.indexOf(F);return -1!==G?"object"===_typeof(O=O[parseInt(D.substr(G+F.length),10)])?U:O:""}return function(){return F="",O=D.getFootageData(),U}},F=function(F){function O(D){return"Outline"===D?O.outlineInterface():null}return O._name="Outline",O.outlineInterface=D(F),O};return function(D){function O(D){return"Data"===D?O.dataInterface:null}return O._name="Data",O.dataInterface=F(D),O}}(),interfaces={layer:LayerExpressionInterface,effects:EffectsExpressionInterface,comp:CompExpressionInterface,shape:ShapeExpressionInterface,text:TextExpressionInterface,footage:FootageInterface};function getInterface(D){return interfaces[D]||null}var expressionHelpers=function(){return{searchExpressions:function(D,F,O){F.x&&(O.k=!0,O.x=!0,O.initiateExpression=ExpressionManager.initiateExpression,O.effectsSequence.push(O.initiateExpression(D,F,O).bind(O)))},getSpeedAtTime:function(D){var F,O=-.01,U=this.getValueAtTime(D),G=this.getValueAtTime(D+O),W=0;if(U.length){for(F=0;F<U.length;F+=1)W+=Math.pow(G[F]-U[F],2);W=100*Math.sqrt(W)}else W=0;return W},getVelocityAtTime:function(D){if(void 0!==this.vel)return this.vel;var F,O,U=-.001,G=this.getValueAtTime(D),W=this.getValueAtTime(D+U);if(G.length)for(O=0,F=createTypedArray("float32",G.length);O<G.length;O+=1)F[O]=(W[O]-G[O])/U;else F=(W-G)/U;return F},getValueAtTime:function(D){return D*=this.elem.globalData.frameRate,(D-=this.offsetTime)!==this._cachingAtTime.lastFrame&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastFrame<D?this._cachingAtTime.lastIndex:0,this._cachingAtTime.value=this.interpolateValue(D,this._cachingAtTime),this._cachingAtTime.lastFrame=D),this._cachingAtTime.value},getStaticValueAtTime:function(){return this.pv},setGroupProperty:function(D){this.propertyGroup=D}}}();function addPropertyDecorator(){function D(D,F,O){if(!this.k||!this.keyframes)return this.pv;D=D?D.toLowerCase():"";var U,G,W,Y,X,J=this.comp.renderedFrame,K=this.keyframes,Z=K[K.length-1].t;if(J<=Z)return this.pv;if(O?(U=F?Math.abs(Z-this.elem.comp.globalData.frameRate*F):Math.max(0,Z-this.elem.data.ip),G=Z-U):((!F||F>K.length-1)&&(F=K.length-1),U=Z-(G=K[K.length-1-F].t)),"pingpong"===D){if(Math.floor((J-G)/U)%2!=0)return this.getValueAtTime((U-(J-G)%U+G)/this.comp.globalData.frameRate,0)}else if("offset"===D){var Q=this.getValueAtTime(G/this.comp.globalData.frameRate,0),tt=this.getValueAtTime(Z/this.comp.globalData.frameRate,0),te=this.getValueAtTime(((J-G)%U+G)/this.comp.globalData.frameRate,0),tr=Math.floor((J-G)/U);if(this.pv.length){for(W=0,Y=(X=Array(Q.length)).length;W<Y;W+=1)X[W]=(tt[W]-Q[W])*tr+te[W];return X}return(tt-Q)*tr+te}else if("continue"===D){var ts=this.getValueAtTime(Z/this.comp.globalData.frameRate,0),tn=this.getValueAtTime((Z-.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(W=0,Y=(X=Array(ts.length)).length;W<Y;W+=1)X[W]=ts[W]+(ts[W]-tn[W])*((J-Z)/this.comp.globalData.frameRate)/5e-4;return X}return ts+(ts-tn)*((J-Z)/.001)}return this.getValueAtTime(((J-G)%U+G)/this.comp.globalData.frameRate,0)}function F(D,F,O){if(!this.k)return this.pv;D=D?D.toLowerCase():"";var U,G,W,Y,X,J=this.comp.renderedFrame,K=this.keyframes,Z=K[0].t;if(J>=Z)return this.pv;if(O?(U=F?Math.abs(this.elem.comp.globalData.frameRate*F):Math.max(0,this.elem.data.op-Z),G=Z+U):((!F||F>K.length-1)&&(F=K.length-1),U=(G=K[F].t)-Z),"pingpong"===D){if(Math.floor((Z-J)/U)%2==0)return this.getValueAtTime(((Z-J)%U+Z)/this.comp.globalData.frameRate,0)}else if("offset"===D){var Q=this.getValueAtTime(Z/this.comp.globalData.frameRate,0),tt=this.getValueAtTime(G/this.comp.globalData.frameRate,0),te=this.getValueAtTime((U-(Z-J)%U+Z)/this.comp.globalData.frameRate,0),tr=Math.floor((Z-J)/U)+1;if(this.pv.length){for(W=0,Y=(X=Array(Q.length)).length;W<Y;W+=1)X[W]=te[W]-(tt[W]-Q[W])*tr;return X}return te-(tt-Q)*tr}else if("continue"===D){var ts=this.getValueAtTime(Z/this.comp.globalData.frameRate,0),tn=this.getValueAtTime((Z+.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(W=0,Y=(X=Array(ts.length)).length;W<Y;W+=1)X[W]=ts[W]+(ts[W]-tn[W])*(Z-J)/.001;return X}return ts+(ts-tn)*(Z-J)/.001}return this.getValueAtTime((U-((Z-J)%U+Z))/this.comp.globalData.frameRate,0)}function O(D,F){if(!this.k||(D=.5*(D||.4),(F=Math.floor(F||5))<=1))return this.pv;var O,U,G=this.comp.renderedFrame/this.comp.globalData.frameRate,W=G-D,Y=G+D,X=F>1?(Y-W)/(F-1):1,J=0,K=0;for(O=this.pv.length?createTypedArray("float32",this.pv.length):0;J<F;){if(U=this.getValueAtTime(W+J*X),this.pv.length)for(K=0;K<this.pv.length;K+=1)O[K]+=U[K];else O+=U;J+=1}if(this.pv.length)for(K=0;K<this.pv.length;K+=1)O[K]/=F;else O/=F;return O}function U(D){this._transformCachingAtTime||(this._transformCachingAtTime={v:new Matrix});var F=this._transformCachingAtTime.v;if(F.cloneFromProps(this.pre.props),this.appliedTransformations<1){var O=this.a.getValueAtTime(D);F.translate(-O[0]*this.a.mult,-O[1]*this.a.mult,O[2]*this.a.mult)}if(this.appliedTransformations<2){var U=this.s.getValueAtTime(D);F.scale(U[0]*this.s.mult,U[1]*this.s.mult,U[2]*this.s.mult)}if(this.sk&&this.appliedTransformations<3){var G=this.sk.getValueAtTime(D),W=this.sa.getValueAtTime(D);F.skewFromAxis(-G*this.sk.mult,W*this.sa.mult)}if(this.r&&this.appliedTransformations<4){var Y=this.r.getValueAtTime(D);F.rotate(-Y*this.r.mult)}else if(!this.r&&this.appliedTransformations<4){var X=this.rz.getValueAtTime(D),J=this.ry.getValueAtTime(D),K=this.rx.getValueAtTime(D),Z=this.or.getValueAtTime(D);F.rotateZ(-X*this.rz.mult).rotateY(J*this.ry.mult).rotateX(K*this.rx.mult).rotateZ(-Z[2]*this.or.mult).rotateY(Z[1]*this.or.mult).rotateX(Z[0]*this.or.mult)}if(this.data.p&&this.data.p.s){var Q=this.px.getValueAtTime(D),tt=this.py.getValueAtTime(D);if(this.data.p.z){var te=this.pz.getValueAtTime(D);F.translate(Q*this.px.mult,tt*this.py.mult,-te*this.pz.mult)}else F.translate(Q*this.px.mult,tt*this.py.mult,0)}else{var tr=this.p.getValueAtTime(D);F.translate(tr[0]*this.p.mult,tr[1]*this.p.mult,-tr[2]*this.p.mult)}return F}function G(){return this.v.clone(new Matrix)}var W=TransformPropertyFactory.getTransformProperty;TransformPropertyFactory.getTransformProperty=function(D,F,O){var Y=W(D,F,O);return Y.dynamicProperties.length?Y.getValueAtTime=U.bind(Y):Y.getValueAtTime=G.bind(Y),Y.setGroupProperty=expressionHelpers.setGroupProperty,Y};var Y=PropertyFactory.getProp;function X(D){return this._cachingAtTime||(this._cachingAtTime={shapeValue:shapePool.clone(this.pv),lastIndex:0,lastTime:initialDefaultFrame}),D*=this.elem.globalData.frameRate,(D-=this.offsetTime)!==this._cachingAtTime.lastTime&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastTime<D?this._caching.lastIndex:0,this._cachingAtTime.lastTime=D,this.interpolateShape(D,this._cachingAtTime.shapeValue,this._cachingAtTime)),this._cachingAtTime.shapeValue}PropertyFactory.getProp=function(U,G,W,X,J){var K=Y(U,G,W,X,J);K.kf?K.getValueAtTime=expressionHelpers.getValueAtTime.bind(K):K.getValueAtTime=expressionHelpers.getStaticValueAtTime.bind(K),K.setGroupProperty=expressionHelpers.setGroupProperty,K.loopOut=D,K.loopIn=F,K.smooth=O,K.getVelocityAtTime=expressionHelpers.getVelocityAtTime.bind(K),K.getSpeedAtTime=expressionHelpers.getSpeedAtTime.bind(K),K.numKeys=1===G.a?G.k.length:0,K.propertyIndex=G.ix;var Z=0;return 0!==W&&(Z=createTypedArray("float32",1===G.a?G.k[0].s.length:G.k.length)),K._cachingAtTime={lastFrame:initialDefaultFrame,lastIndex:0,value:Z},expressionHelpers.searchExpressions(U,G,K),K.k&&J.addDynamicProperty(K),K};var J=ShapePropertyFactory.getConstructorFunction(),K=ShapePropertyFactory.getKeyframedConstructorFunction();function Z(){}Z.prototype={vertices:function(D,F){this.k&&this.getValue();var O,U=this.v;void 0!==F&&(U=this.getValueAtTime(F,0));var G=U._length,W=U[D],Y=U.v,X=createSizedArray(G);for(O=0;O<G;O+=1)"i"===D||"o"===D?X[O]=[W[O][0]-Y[O][0],W[O][1]-Y[O][1]]:X[O]=[W[O][0],W[O][1]];return X},points:function(D){return this.vertices("v",D)},inTangents:function(D){return this.vertices("i",D)},outTangents:function(D){return this.vertices("o",D)},isClosed:function(){return this.v.c},pointOnPath:function(D,F){var O,U=this.v;void 0!==F&&(U=this.getValueAtTime(F,0)),this._segmentsLength||(this._segmentsLength=bez.getSegmentsLength(U));for(var G=this._segmentsLength,W=G.lengths,Y=G.totalLength*D,X=0,J=W.length,K=0;X<J;){if(K+W[X].addedLength>Y){var Z=X,Q=U.c&&X===J-1?0:X+1,tt=(Y-K)/W[X].addedLength;O=bez.getPointInSegment(U.v[Z],U.v[Q],U.o[Z],U.i[Q],tt,W[X]);break}K+=W[X].addedLength,X+=1}return O||(O=U.c?[U.v[0][0],U.v[0][1]]:[U.v[U._length-1][0],U.v[U._length-1][1]]),O},vectorOnPath:function(D,F,O){1==D?D=this.v.c:0==D&&(D=.999);var U=this.pointOnPath(D,F),G=this.pointOnPath(D+.001,F),W=G[0]-U[0],Y=G[1]-U[1],X=Math.sqrt(Math.pow(W,2)+Math.pow(Y,2));return 0===X?[0,0]:"tangent"===O?[W/X,Y/X]:[-Y/X,W/X]},tangentOnPath:function(D,F){return this.vectorOnPath(D,F,"tangent")},normalOnPath:function(D,F){return this.vectorOnPath(D,F,"normal")},setGroupProperty:expressionHelpers.setGroupProperty,getValueAtTime:expressionHelpers.getStaticValueAtTime},extendPrototype([Z],J),extendPrototype([Z],K),K.prototype.getValueAtTime=X,K.prototype.initiateExpression=ExpressionManager.initiateExpression;var Q=ShapePropertyFactory.getShapeProp;ShapePropertyFactory.getShapeProp=function(D,F,O,U,G){var W=Q(D,F,O,U,G);return W.propertyIndex=F.ix,W.lock=!1,3===O?expressionHelpers.searchExpressions(D,F.pt,W):4===O&&expressionHelpers.searchExpressions(D,F.ks,W),W.k&&D.addDynamicProperty(W),W}}function initialize$1(){addPropertyDecorator()}function addDecorator(){function D(){return this.data.d.x?(this.calculateExpression=ExpressionManager.initiateExpression.bind(this)(this.elem,this.data.d,this),this.addEffect(this.getExpressionValue.bind(this)),!0):null}TextProperty.prototype.getExpressionValue=function(D,F){var O=this.calculateExpression(F);if(D.t!==O){var U={};return this.copyData(U,D),U.t=O.toString(),U.__complete=!1,U}return D},TextProperty.prototype.searchProperty=function(){var D=this.searchKeyframes(),F=this.searchExpressions();return this.kf=D||F,this.kf},TextProperty.prototype.searchExpressions=D}function initialize(){addDecorator()}function SVGComposableEffect(){}SVGComposableEffect.prototype={createMergeNode:function(D,F){var O,U,G=createNS("feMerge");for(G.setAttribute("result",D),U=0;U<F.length;U+=1)(O=createNS("feMergeNode")).setAttribute("in",F[U]),G.appendChild(O),G.appendChild(O);return G}};var linearFilterValue="0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0";function SVGTintFilter(D,F,O,U,G){this.filterManager=F;var W=createNS("feColorMatrix");W.setAttribute("type","matrix"),W.setAttribute("color-interpolation-filters","linearRGB"),W.setAttribute("values",linearFilterValue+" 1 0"),this.linearFilter=W,W.setAttribute("result",U+"_tint_1"),D.appendChild(W),(W=createNS("feColorMatrix")).setAttribute("type","matrix"),W.setAttribute("color-interpolation-filters","sRGB"),W.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),W.setAttribute("result",U+"_tint_2"),D.appendChild(W),this.matrixFilter=W;var Y=this.createMergeNode(U,[G,U+"_tint_1",U+"_tint_2"]);D.appendChild(Y)}function SVGFillFilter(D,F,O,U){this.filterManager=F;var G=createNS("feColorMatrix");G.setAttribute("type","matrix"),G.setAttribute("color-interpolation-filters","sRGB"),G.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),G.setAttribute("result",U),D.appendChild(G),this.matrixFilter=G}function SVGStrokeEffect(D,F,O){this.initialized=!1,this.filterManager=F,this.elem=O,this.paths=[]}function SVGTritoneFilter(D,F,O,U){this.filterManager=F;var G=createNS("feColorMatrix");G.setAttribute("type","matrix"),G.setAttribute("color-interpolation-filters","linearRGB"),G.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),D.appendChild(G);var W=createNS("feComponentTransfer");W.setAttribute("color-interpolation-filters","sRGB"),W.setAttribute("result",U),this.matrixFilter=W;var Y=createNS("feFuncR");Y.setAttribute("type","table"),W.appendChild(Y),this.feFuncR=Y;var X=createNS("feFuncG");X.setAttribute("type","table"),W.appendChild(X),this.feFuncG=X;var J=createNS("feFuncB");J.setAttribute("type","table"),W.appendChild(J),this.feFuncB=J,D.appendChild(W)}function SVGProLevelsFilter(D,F,O,U){this.filterManager=F;var G=this.filterManager.effectElements,W=createNS("feComponentTransfer");(G[10].p.k||0!==G[10].p.v||G[11].p.k||1!==G[11].p.v||G[12].p.k||1!==G[12].p.v||G[13].p.k||0!==G[13].p.v||G[14].p.k||1!==G[14].p.v)&&(this.feFuncR=this.createFeFunc("feFuncR",W)),(G[17].p.k||0!==G[17].p.v||G[18].p.k||1!==G[18].p.v||G[19].p.k||1!==G[19].p.v||G[20].p.k||0!==G[20].p.v||G[21].p.k||1!==G[21].p.v)&&(this.feFuncG=this.createFeFunc("feFuncG",W)),(G[24].p.k||0!==G[24].p.v||G[25].p.k||1!==G[25].p.v||G[26].p.k||1!==G[26].p.v||G[27].p.k||0!==G[27].p.v||G[28].p.k||1!==G[28].p.v)&&(this.feFuncB=this.createFeFunc("feFuncB",W)),(G[31].p.k||0!==G[31].p.v||G[32].p.k||1!==G[32].p.v||G[33].p.k||1!==G[33].p.v||G[34].p.k||0!==G[34].p.v||G[35].p.k||1!==G[35].p.v)&&(this.feFuncA=this.createFeFunc("feFuncA",W)),(this.feFuncR||this.feFuncG||this.feFuncB||this.feFuncA)&&(W.setAttribute("color-interpolation-filters","sRGB"),D.appendChild(W)),(G[3].p.k||0!==G[3].p.v||G[4].p.k||1!==G[4].p.v||G[5].p.k||1!==G[5].p.v||G[6].p.k||0!==G[6].p.v||G[7].p.k||1!==G[7].p.v)&&((W=createNS("feComponentTransfer")).setAttribute("color-interpolation-filters","sRGB"),W.setAttribute("result",U),D.appendChild(W),this.feFuncRComposed=this.createFeFunc("feFuncR",W),this.feFuncGComposed=this.createFeFunc("feFuncG",W),this.feFuncBComposed=this.createFeFunc("feFuncB",W))}function SVGDropShadowEffect(D,F,O,U,G){var W=F.container.globalData.renderConfig.filterSize,Y=F.data.fs||W;D.setAttribute("x",Y.x||W.x),D.setAttribute("y",Y.y||W.y),D.setAttribute("width",Y.width||W.width),D.setAttribute("height",Y.height||W.height),this.filterManager=F;var X=createNS("feGaussianBlur");X.setAttribute("in","SourceAlpha"),X.setAttribute("result",U+"_drop_shadow_1"),X.setAttribute("stdDeviation","0"),this.feGaussianBlur=X,D.appendChild(X);var J=createNS("feOffset");J.setAttribute("dx","25"),J.setAttribute("dy","0"),J.setAttribute("in",U+"_drop_shadow_1"),J.setAttribute("result",U+"_drop_shadow_2"),this.feOffset=J,D.appendChild(J);var K=createNS("feFlood");K.setAttribute("flood-color","#00ff00"),K.setAttribute("flood-opacity","1"),K.setAttribute("result",U+"_drop_shadow_3"),this.feFlood=K,D.appendChild(K);var Z=createNS("feComposite");Z.setAttribute("in",U+"_drop_shadow_3"),Z.setAttribute("in2",U+"_drop_shadow_2"),Z.setAttribute("operator","in"),Z.setAttribute("result",U+"_drop_shadow_4"),D.appendChild(Z);var Q=this.createMergeNode(U,[U+"_drop_shadow_4",G]);D.appendChild(Q)}extendPrototype([SVGComposableEffect],SVGTintFilter),SVGTintFilter.prototype.renderFrame=function(D){if(D||this.filterManager._mdf){var F=this.filterManager.effectElements[0].p.v,O=this.filterManager.effectElements[1].p.v,U=this.filterManager.effectElements[2].p.v/100;this.linearFilter.setAttribute("values",linearFilterValue+" "+U+" 0"),this.matrixFilter.setAttribute("values",O[0]-F[0]+" 0 0 0 "+F[0]+" "+(O[1]-F[1])+" 0 0 0 "+F[1]+" "+(O[2]-F[2])+" 0 0 0 "+F[2]+" 0 0 0 1 0")}},SVGFillFilter.prototype.renderFrame=function(D){if(D||this.filterManager._mdf){var F=this.filterManager.effectElements[2].p.v,O=this.filterManager.effectElements[6].p.v;this.matrixFilter.setAttribute("values","0 0 0 0 "+F[0]+" 0 0 0 0 "+F[1]+" 0 0 0 0 "+F[2]+" 0 0 0 "+O+" 0")}},SVGStrokeEffect.prototype.initialize=function(){var D,F,O,U,G=this.elem.layerElement.children||this.elem.layerElement.childNodes;for(1===this.filterManager.effectElements[1].p.v?(U=this.elem.maskManager.masksProperties.length,O=0):U=(O=this.filterManager.effectElements[0].p.v-1)+1,(F=createNS("g")).setAttribute("fill","none"),F.setAttribute("stroke-linecap","round"),F.setAttribute("stroke-dashoffset",1);O<U;O+=1)D=createNS("path"),F.appendChild(D),this.paths.push({p:D,m:O});if(3===this.filterManager.effectElements[10].p.v){var W=createNS("mask"),Y=createElementID();W.setAttribute("id",Y),W.setAttribute("mask-type","alpha"),W.appendChild(F),this.elem.globalData.defs.appendChild(W);var X=createNS("g");for(X.setAttribute("mask","url("+getLocationHref()+"#"+Y+")");G[0];)X.appendChild(G[0]);this.elem.layerElement.appendChild(X),this.masker=W,F.setAttribute("stroke","#fff")}else if(1===this.filterManager.effectElements[10].p.v||2===this.filterManager.effectElements[10].p.v){if(2===this.filterManager.effectElements[10].p.v)for(G=this.elem.layerElement.children||this.elem.layerElement.childNodes;G.length;)this.elem.layerElement.removeChild(G[0]);this.elem.layerElement.appendChild(F),this.elem.layerElement.removeAttribute("mask"),F.setAttribute("stroke","#fff")}this.initialized=!0,this.pathMasker=F},SVGStrokeEffect.prototype.renderFrame=function(D){this.initialized||this.initialize();var F=this.paths.length;for(O=0;O<F;O+=1)if(-1!==this.paths[O].m&&(U=this.elem.maskManager.viewData[this.paths[O].m],G=this.paths[O].p,(D||this.filterManager._mdf||U.prop._mdf)&&G.setAttribute("d",U.lastPath),D||this.filterManager.effectElements[9].p._mdf||this.filterManager.effectElements[4].p._mdf||this.filterManager.effectElements[7].p._mdf||this.filterManager.effectElements[8].p._mdf||U.prop._mdf)){if(0!==this.filterManager.effectElements[7].p.v||100!==this.filterManager.effectElements[8].p.v){var O,U,G,W,Y,X=.01*Math.min(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),J=.01*Math.max(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),K=G.getTotalLength();W="0 0 0 "+K*X+" ";var Z=Math.floor(K*(J-X)/(1+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01));for(Y=0;Y<Z;Y+=1)W+="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01+" ";W+="0 "+10*K+" 0 0"}else W="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01;G.setAttribute("stroke-dasharray",W)}if((D||this.filterManager.effectElements[4].p._mdf)&&this.pathMasker.setAttribute("stroke-width",2*this.filterManager.effectElements[4].p.v),(D||this.filterManager.effectElements[6].p._mdf)&&this.pathMasker.setAttribute("opacity",this.filterManager.effectElements[6].p.v),(1===this.filterManager.effectElements[10].p.v||2===this.filterManager.effectElements[10].p.v)&&(D||this.filterManager.effectElements[3].p._mdf)){var Q=this.filterManager.effectElements[3].p.v;this.pathMasker.setAttribute("stroke","rgb("+bmFloor(255*Q[0])+","+bmFloor(255*Q[1])+","+bmFloor(255*Q[2])+")")}},SVGTritoneFilter.prototype.renderFrame=function(D){if(D||this.filterManager._mdf){var F=this.filterManager.effectElements[0].p.v,O=this.filterManager.effectElements[1].p.v,U=this.filterManager.effectElements[2].p.v,G=U[0]+" "+O[0]+" "+F[0],W=U[1]+" "+O[1]+" "+F[1],Y=U[2]+" "+O[2]+" "+F[2];this.feFuncR.setAttribute("tableValues",G),this.feFuncG.setAttribute("tableValues",W),this.feFuncB.setAttribute("tableValues",Y)}},SVGProLevelsFilter.prototype.createFeFunc=function(D,F){var O=createNS(D);return O.setAttribute("type","table"),F.appendChild(O),O},SVGProLevelsFilter.prototype.getTableValue=function(D,F,O,U,G){for(var W,Y,X=0,J=256,K=Math.min(D,F),Z=Math.max(D,F),Q=Array.call(null,{length:256}),tt=0,te=G-U,tr=F-D;X<=256;)Y=(W=X/256)<=K?tr<0?G:U:W>=Z?tr<0?U:G:U+te*Math.pow((W-D)/tr,1/O),Q[tt]=Y,tt+=1,X+=256/(J-1);return Q.join(" ")},SVGProLevelsFilter.prototype.renderFrame=function(D){if(D||this.filterManager._mdf){var F,O=this.filterManager.effectElements;this.feFuncRComposed&&(D||O[3].p._mdf||O[4].p._mdf||O[5].p._mdf||O[6].p._mdf||O[7].p._mdf)&&(F=this.getTableValue(O[3].p.v,O[4].p.v,O[5].p.v,O[6].p.v,O[7].p.v),this.feFuncRComposed.setAttribute("tableValues",F),this.feFuncGComposed.setAttribute("tableValues",F),this.feFuncBComposed.setAttribute("tableValues",F)),this.feFuncR&&(D||O[10].p._mdf||O[11].p._mdf||O[12].p._mdf||O[13].p._mdf||O[14].p._mdf)&&(F=this.getTableValue(O[10].p.v,O[11].p.v,O[12].p.v,O[13].p.v,O[14].p.v),this.feFuncR.setAttribute("tableValues",F)),this.feFuncG&&(D||O[17].p._mdf||O[18].p._mdf||O[19].p._mdf||O[20].p._mdf||O[21].p._mdf)&&(F=this.getTableValue(O[17].p.v,O[18].p.v,O[19].p.v,O[20].p.v,O[21].p.v),this.feFuncG.setAttribute("tableValues",F)),this.feFuncB&&(D||O[24].p._mdf||O[25].p._mdf||O[26].p._mdf||O[27].p._mdf||O[28].p._mdf)&&(F=this.getTableValue(O[24].p.v,O[25].p.v,O[26].p.v,O[27].p.v,O[28].p.v),this.feFuncB.setAttribute("tableValues",F)),this.feFuncA&&(D||O[31].p._mdf||O[32].p._mdf||O[33].p._mdf||O[34].p._mdf||O[35].p._mdf)&&(F=this.getTableValue(O[31].p.v,O[32].p.v,O[33].p.v,O[34].p.v,O[35].p.v),this.feFuncA.setAttribute("tableValues",F))}},extendPrototype([SVGComposableEffect],SVGDropShadowEffect),SVGDropShadowEffect.prototype.renderFrame=function(D){if(D||this.filterManager._mdf){if((D||this.filterManager.effectElements[4].p._mdf)&&this.feGaussianBlur.setAttribute("stdDeviation",this.filterManager.effectElements[4].p.v/4),D||this.filterManager.effectElements[0].p._mdf){var F=this.filterManager.effectElements[0].p.v;this.feFlood.setAttribute("flood-color",rgbToHex(Math.round(255*F[0]),Math.round(255*F[1]),Math.round(255*F[2])))}if((D||this.filterManager.effectElements[1].p._mdf)&&this.feFlood.setAttribute("flood-opacity",this.filterManager.effectElements[1].p.v/255),D||this.filterManager.effectElements[2].p._mdf||this.filterManager.effectElements[3].p._mdf){var O=this.filterManager.effectElements[3].p.v,U=(this.filterManager.effectElements[2].p.v-90)*degToRads,G=O*Math.cos(U),W=O*Math.sin(U);this.feOffset.setAttribute("dx",G),this.feOffset.setAttribute("dy",W)}}};var _svgMatteSymbols=[];function SVGMatte3Effect(D,F,O){this.initialized=!1,this.filterManager=F,this.filterElem=D,this.elem=O,O.matteElement=createNS("g"),O.matteElement.appendChild(O.layerElement),O.matteElement.appendChild(O.transformedElement),O.baseElement=O.matteElement}function SVGGaussianBlurEffect(D,F,O,U){D.setAttribute("x","-100%"),D.setAttribute("y","-100%"),D.setAttribute("width","300%"),D.setAttribute("height","300%"),this.filterManager=F;var G=createNS("feGaussianBlur");G.setAttribute("result",U),D.appendChild(G),this.feGaussianBlur=G}function TransformEffect(){}function SVGTransformEffect(D,F){this.init(F)}function CVTransformEffect(D){this.init(D)}return SVGMatte3Effect.prototype.findSymbol=function(D){for(var F=0,O=_svgMatteSymbols.length;F<O;){if(_svgMatteSymbols[F]===D)return _svgMatteSymbols[F];F+=1}return null},SVGMatte3Effect.prototype.replaceInParent=function(D,F){var O,U=D.layerElement.parentNode;if(U){for(var G=U.children,W=0,Y=G.length;W<Y&&G[W]!==D.layerElement;)W+=1;W<=Y-2&&(O=G[W+1]);var X=createNS("use");X.setAttribute("href","#"+F),O?U.insertBefore(X,O):U.appendChild(X)}},SVGMatte3Effect.prototype.setElementAsMask=function(D,F){if(!this.findSymbol(F)){var O=createElementID(),U=createNS("mask");U.setAttribute("id",F.layerId),U.setAttribute("mask-type","alpha"),_svgMatteSymbols.push(F);var G=D.globalData.defs;G.appendChild(U);var W=createNS("symbol");W.setAttribute("id",O),this.replaceInParent(F,O),W.appendChild(F.layerElement),G.appendChild(W);var Y=createNS("use");Y.setAttribute("href","#"+O),U.appendChild(Y),F.data.hd=!1,F.show()}D.setMatte(F.layerId)},SVGMatte3Effect.prototype.initialize=function(){for(var D=this.filterManager.effectElements[0].p.v,F=this.elem.comp.elements,O=0,U=F.length;O<U;)F[O]&&F[O].data.ind===D&&this.setElementAsMask(this.elem,F[O]),O+=1;this.initialized=!0},SVGMatte3Effect.prototype.renderFrame=function(){this.initialized||this.initialize()},SVGGaussianBlurEffect.prototype.renderFrame=function(D){if(D||this.filterManager._mdf){var F=.3,O=this.filterManager.effectElements[0].p.v*F,U=this.filterManager.effectElements[1].p.v,G=3==U?0:O,W=2==U?0:O;this.feGaussianBlur.setAttribute("stdDeviation",G+" "+W);var Y=1==this.filterManager.effectElements[2].p.v?"wrap":"duplicate";this.feGaussianBlur.setAttribute("edgeMode",Y)}},TransformEffect.prototype.init=function(D){this.effectsManager=D,this.type=effectTypes.TRANSFORM_EFFECT,this.matrix=new Matrix,this.opacity=-1,this._mdf=!1,this._opMdf=!1},TransformEffect.prototype.renderFrame=function(D){if(this._opMdf=!1,this._mdf=!1,D||this.effectsManager._mdf){var F=this.effectsManager.effectElements,O=F[0].p.v,U=F[1].p.v,G=1===F[2].p.v,W=F[3].p.v,Y=G?W:F[4].p.v,X=F[5].p.v,J=F[6].p.v,K=F[7].p.v;this.matrix.reset(),this.matrix.translate(-O[0],-O[1],O[2]),this.matrix.scale(.01*Y,.01*W,1),this.matrix.rotate(-K*degToRads),this.matrix.skewFromAxis(-X*degToRads,(J+90)*degToRads),this.matrix.translate(U[0],U[1],0),this._mdf=!0,this.opacity!==F[8].p.v&&(this.opacity=F[8].p.v,this._opMdf=!0)}},extendPrototype([TransformEffect],SVGTransformEffect),extendPrototype([TransformEffect],CVTransformEffect),registerRenderer("canvas",CanvasRenderer),registerRenderer("html",HybridRenderer),registerRenderer("svg",SVGRenderer),ShapeModifiers.registerModifier("tm",TrimModifier),ShapeModifiers.registerModifier("pb",PuckerAndBloatModifier),ShapeModifiers.registerModifier("rp",RepeaterModifier),ShapeModifiers.registerModifier("rd",RoundCornersModifier),ShapeModifiers.registerModifier("zz",ZigZagModifier),ShapeModifiers.registerModifier("op",OffsetPathModifier),setExpressionsPlugin(Expressions),setExpressionInterfaces(getInterface),initialize$1(),initialize(),registerEffect$1(20,SVGTintFilter,!0),registerEffect$1(21,SVGFillFilter,!0),registerEffect$1(22,SVGStrokeEffect,!1),registerEffect$1(23,SVGTritoneFilter,!0),registerEffect$1(24,SVGProLevelsFilter,!0),registerEffect$1(25,SVGDropShadowEffect,!0),registerEffect$1(28,SVGMatte3Effect,!1),registerEffect$1(29,SVGGaussianBlurEffect,!0),registerEffect$1(35,SVGTransformEffect,!1),registerEffect(35,CVTransformEffect),lottie})}(lottie,lottie.exports);var lottieExports=lottie.exports,Lottie=getDefaultExportFromCjs(lottieExports);exports.PlayerState=void 0,function(D){D.Completed="completed",D.Destroyed="destroyed",D.Error="error",D.Frozen="frozen",D.Loading="loading",D.Paused="paused",D.Playing="playing",D.Stopped="stopped"}(exports.PlayerState||(exports.PlayerState={})),exports.PlayMode=void 0,function(D){D.Bounce="bounce",D.Normal="normal"}(exports.PlayMode||(exports.PlayMode={})),exports.PlayerEvents=void 0,function(D){D.Complete="complete",D.Destroyed="destroyed",D.Error="error",D.Frame="frame",D.Freeze="freeze",D.Load="load",D.Loop="loop",D.Pause="pause",D.Play="play",D.Ready="ready",D.Rendered="rendered",D.Stop="stop"}(exports.PlayerEvents||(exports.PlayerEvents={}));var ch2={},wk=function(D,F,O,U,G){var W=new Worker(ch2[F]||(ch2[F]=URL.createObjectURL(new Blob([D+';addEventListener("error",function(e){e=e.error;postMessage({$e$:[e.message,e.code,e.stack]})})'],{type:"text/javascript"}))));return W.onmessage=function(D){var F=D.data,O=F.$e$;if(O){var U=Error(O[0]);U.code=O[1],U.stack=O[2],G(U,null)}else G(null,F)},W.postMessage(O,U),W},u8=Uint8Array,u16=Uint16Array,u32=Uint32Array,fleb=new u8([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),fdeb=new u8([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),clim=new u8([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),freb=function(D,F){for(var O=new u16(31),U=0;U<31;++U)O[U]=F+=1<<D[U-1];for(var G=new u32(O[30]),U=1;U<30;++U)for(var W=O[U];W<O[U+1];++W)G[W]=W-O[U]<<5|U;return[O,G]},_a=freb(fleb,2),fl=_a[0],revfl=_a[1];fl[28]=258,revfl[258]=28;for(var _b=freb(fdeb,0),fd=_b[0],rev=new u16(32768),i$1=0;i$1<32768;++i$1){var x=(43690&i$1)>>>1|(21845&i$1)<<1;x=(61680&(x=(52428&x)>>>2|(13107&x)<<2))>>>4|(3855&x)<<4,rev[i$1]=((65280&x)>>>8|(255&x)<<8)>>>1}for(var hMap=function(D,F,O){for(var U,G=D.length,W=0,Y=new u16(F);W<G;++W)D[W]&&++Y[D[W]-1];var X=new u16(F);for(W=0;W<F;++W)X[W]=X[W-1]+Y[W-1]<<1;if(O){U=new u16(1<<F);var J=15-F;for(W=0;W<G;++W)if(D[W])for(var K=W<<4|D[W],Z=F-D[W],Q=X[D[W]-1]++<<Z,tt=Q|(1<<Z)-1;Q<=tt;++Q)U[rev[Q]>>>J]=K}else for(W=0,U=new u16(G);W<G;++W)D[W]&&(U[W]=rev[X[D[W]-1]++]>>>15-D[W]);return U},flt=new u8(288),i$1=0;i$1<144;++i$1)flt[i$1]=8;for(var i$1=144;i$1<256;++i$1)flt[i$1]=9;for(var i$1=256;i$1<280;++i$1)flt[i$1]=7;for(var i$1=280;i$1<288;++i$1)flt[i$1]=8;for(var fdt=new u8(32),i$1=0;i$1<32;++i$1)fdt[i$1]=5;var flrm=hMap(flt,9,1),fdrm=hMap(fdt,5,1),max=function(D){for(var F=D[0],O=1;O<D.length;++O)D[O]>F&&(F=D[O]);return F},bits=function(D,F,O){var U=F/8|0;return(D[U]|D[U+1]<<8)>>(7&F)&O},bits16=function(D,F){var O=F/8|0;return(D[O]|D[O+1]<<8|D[O+2]<<16)>>(7&F)},shft=function(D){return(D+7)/8|0},slc=function(D,F,O){(null==F||F<0)&&(F=0),(null==O||O>D.length)&&(O=D.length);var U=new(2==D.BYTES_PER_ELEMENT?u16:4==D.BYTES_PER_ELEMENT?u32:u8)(O-F);return U.set(D.subarray(F,O)),U},ec=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],err=function(D,F,O){var U=Error(F||ec[D]);if(U.code=D,Error.captureStackTrace&&Error.captureStackTrace(U,err),!O)throw U;return U},inflt=function(D,F,O){var U=D.length;if(!U||O&&O.f&&!O.l)return F||new u8(0);var G=!F||O,W=!O||O.i;O||(O={}),F||(F=new u8(3*U));var Y=function(D){var O=F.length;if(D>O){var U=new u8(Math.max(2*O,D));U.set(F),F=U}},X=O.f||0,J=O.p||0,K=O.b||0,Z=O.l,Q=O.d,tt=O.m,te=O.n,tr=8*U;do{if(!Z){X=bits(D,J,1);var ts=bits(D,J+1,3);if(J+=3,ts){if(1==ts)Z=flrm,Q=fdrm,tt=9,te=5;else if(2==ts){var tn=bits(D,J,31)+257,ta=bits(D,J+10,15)+4,th=tn+bits(D,J+5,31)+1;J+=14;for(var tp=new u8(th),tf=new u8(19),tu=0;tu<ta;++tu)tf[clim[tu]]=bits(D,J+3*tu,7);J+=3*ta;for(var tc=max(tf),tm=(1<<tc)-1,tg=hMap(tf,tc,1),tu=0;tu<th;){var tv=tg[bits(D,J,tm)];J+=15&tv;var tb=tv>>>4;if(tb<16)tp[tu++]=tb;else{var t_=0,tk=0;for(16==tb?(tk=3+bits(D,J,3),J+=2,t_=tp[tu-1]):17==tb?(tk=3+bits(D,J,7),J+=3):18==tb&&(tk=11+bits(D,J,127),J+=7);tk--;)tp[tu++]=t_}}var tw=tp.subarray(0,tn),tA=tp.subarray(tn);tt=max(tw),te=max(tA),Z=hMap(tw,tt,1),Q=hMap(tA,te,1)}else err(1)}else{var tb=shft(J)+4,tP=D[tb-4]|D[tb-3]<<8,tC=tb+tP;if(tC>U){W&&err(0);break}G&&Y(K+tP),F.set(D.subarray(tb,tC),K),O.b=K+=tP,O.p=J=8*tC,O.f=X;continue}if(J>tr){W&&err(0);break}}G&&Y(K+131072);for(var tS=(1<<tt)-1,tE=(1<<te)-1,tT=J;;tT=J){var t_=Z[bits16(D,J)&tS],tD=t_>>>4;if((J+=15&t_)>tr){W&&err(0);break}if(t_||err(2),tD<256)F[K++]=tD;else if(256==tD){tT=J,Z=null;break}else{var tM=tD-254;if(tD>264){var tu=tD-257,tF=fleb[tu];tM=bits(D,J,(1<<tF)-1)+fl[tu],J+=tF}var tI=Q[bits16(D,J)&tE],tL=tI>>>4;tI||err(3),J+=15&tI;var tA=fd[tL];if(tL>3){var tF=fdeb[tL];tA+=bits16(D,J)&(1<<tF)-1,J+=tF}if(J>tr){W&&err(0);break}G&&Y(K+131072);for(var tB=K+tM;K<tB;K+=4)F[K]=F[K-tA],F[K+1]=F[K+1-tA],F[K+2]=F[K+2-tA],F[K+3]=F[K+3-tA];K=tB}}O.l=Z,O.p=tT,O.b=K,O.f=X,Z&&(X=1,O.m=tt,O.d=Q,O.n=te)}while(!X);return K==F.length?F:slc(F,0,K)},et=new u8(0),mrg=function(D,F){var O={};for(var U in D)O[U]=D[U];for(var U in F)O[U]=F[U];return O},wcln=function(D,F,O){for(var U=D(),G=D.toString(),W=G.slice(G.indexOf("[")+1,G.lastIndexOf("]")).replace(/\s+/g,"").split(","),Y=0;Y<U.length;++Y){var X=U[Y],J=W[Y];if("function"==typeof X){F+=";"+J+"=";var K=X.toString();if(X.prototype){if(-1!=K.indexOf("[native code]")){var Z=K.indexOf(" ",8)+1;F+=K.slice(Z,K.indexOf("(",Z))}else for(var Q in F+=K,X.prototype)F+=";"+J+".prototype."+Q+"="+X.prototype[Q].toString()}else F+=K}else O[J]=X}return[F,O]},ch=[],cbfs=function(D){var F=[];for(var O in D)D[O].buffer&&F.push((D[O]=new D[O].constructor(D[O])).buffer);return F},wrkr=function(D,F,O,U){if(!ch[O]){for(var G,W="",Y={},X=D.length-1,J=0;J<X;++J)W=(G=wcln(D[J],W,Y))[0],Y=G[1];ch[O]=wcln(D[X],W,Y)}var K=mrg({},ch[O][1]);return wk(ch[O][0]+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+F.toString()+"}",O,K,cbfs(K),U)},bInflt=function(){return[u8,u16,u32,fleb,fdeb,clim,fl,fd,flrm,fdrm,rev,ec,hMap,max,bits,bits16,shft,slc,err,inflt,inflateSync,pbf,gu8]},pbf=function(D){return postMessage(D,[D.buffer])},gu8=function(D){return D&&D.size&&new u8(D.size)},cbify=function(D,F,O,U,G,W){var Y=wrkr(O,U,G,function(D,F){Y.terminate(),W(D,F)});return Y.postMessage([D,F],F.consume?[D.buffer]:[]),function(){Y.terminate()}},b2=function(D,F){return D[F]|D[F+1]<<8},b4=function(D,F){return(D[F]|D[F+1]<<8|D[F+2]<<16|D[F+3]<<24)>>>0},b8=function(D,F){return b4(D,F)+4294967296*b4(D,F+4)};function inflate(D,F,O){return O||(O=F,F={}),"function"!=typeof O&&err(7),cbify(D,F,[bInflt],function(D){return pbf(inflateSync(D.data[0],gu8(D.data[1])))},1,O)}function inflateSync(D,F){return inflt(D,F)}var td="undefined"!=typeof TextDecoder&&new TextDecoder,tds=0;try{td.decode(et,{stream:!0}),tds=1}catch(e){}var dutf8=function(D){for(var F="",O=0;;){var U=D[O++],G=(U>127)+(U>223)+(U>239);if(O+G>D.length)return[F,slc(D,O-1)];G?3==G?F+=String.fromCharCode(55296|(U=((15&U)<<18|(63&D[O++])<<12|(63&D[O++])<<6|63&D[O++])-65536)>>10,56320|1023&U):1&G?F+=String.fromCharCode((31&U)<<6|63&D[O++]):F+=String.fromCharCode((15&U)<<12|(63&D[O++])<<6|63&D[O++]):F+=String.fromCharCode(U)}};function strFromU8(D,F){if(F){for(var O="",U=0;U<D.length;U+=16384)O+=String.fromCharCode.apply(null,D.subarray(U,U+16384));return O}if(td)return td.decode(D);var G=dutf8(D),W=G[0];return G[1].length&&err(8),W}var slzh=function(D,F){return F+30+b2(D,F+26)+b2(D,F+28)},zh=function(D,F,O){var U=b2(D,F+28),G=strFromU8(D.subarray(F+46,F+46+U),!(2048&b2(D,F+8))),W=F+46+U,Y=b4(D,F+20),X=O&&4294967295==Y?z64e(D,W):[Y,b4(D,F+24),b4(D,F+42)],J=X[0],K=X[1],Z=X[2];return[b2(D,F+10),J,K,G,W+b2(D,F+30)+b2(D,F+32),Z]},z64e=function(D,F){for(;1!=b2(D,F);F+=4+b2(D,F+2));return[b8(D,F+12),b8(D,F+4),b8(D,F+20)]},mt="function"==typeof queueMicrotask?queueMicrotask:"function"==typeof setTimeout?setTimeout:function(D){D()};function unzip(D,F,O){O||(O=F,F={}),"function"!=typeof O&&err(7);var U=[],G=function(){for(var D=0;D<U.length;++D)U[D]()},W={},Y=function(D,F){mt(function(){O(D,F)})};mt(function(){Y=O});for(var X=D.length-22;101010256!=b4(D,X);--X)if(!X||D.length-X>65558)return Y(err(13,0,1),null),G;var J=b2(D,X+8);if(J){var K=J,Z=b4(D,X+16),Q=4294967295==Z||65535==K;if(Q){var tt=b4(D,X-12);(Q=101075792==b4(D,tt))&&(K=J=b4(D,tt+32),Z=b4(D,tt+48))}for(var te=F&&F.filter,tr=function(F){var O=zh(D,Z,Q),X=O[0],K=O[1],tt=O[2],tr=O[3],ts=O[4],tn=O[5],ta=slzh(D,tn);Z=ts;var th=function(D,F){D?(G(),Y(D,null)):(F&&(W[tr]=F),--J||Y(null,W))};if(!te||te({name:tr,size:K,originalSize:tt,compression:X})){if(X){if(8==X){var tp=D.subarray(ta,ta+K);if(K<32e4)try{th(null,inflateSync(tp,new u8(tt)))}catch(D){th(D,null)}else U.push(inflate(tp,{size:tt},th))}else th(err(14,"unknown compression type "+X,1),null)}else th(null,slc(D,ta,ta+K))}else th(null,null)},ts=0;ts<K;++ts)tr(ts)}else Y(null,{});return G}var buffer={},base64Js={};base64Js.byteLength=byteLength,base64Js.toByteArray=toByteArray,base64Js.fromByteArray=fromByteArray;for(var lookup=[],revLookup=[],Arr="undefined"!=typeof Uint8Array?Uint8Array:Array,code="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i=0,len=code.length;i<len;++i)lookup[i]=code[i],revLookup[code.charCodeAt(i)]=i;function getLens(D){var F=D.length;if(F%4>0)throw Error("Invalid string. Length must be a multiple of 4");var O=D.indexOf("=");-1===O&&(O=F);var U=O===F?0:4-O%4;return[O,U]}function byteLength(D){var F=getLens(D),O=F[0],U=F[1];return(O+U)*3/4-U}function _byteLength(D,F,O){return(F+O)*3/4-O}function toByteArray(D){var F,O,U=getLens(D),G=U[0],W=U[1],Y=new Arr(_byteLength(D,G,W)),X=0,J=W>0?G-4:G;for(O=0;O<J;O+=4)F=revLookup[D.charCodeAt(O)]<<18|revLookup[D.charCodeAt(O+1)]<<12|revLookup[D.charCodeAt(O+2)]<<6|revLookup[D.charCodeAt(O+3)],Y[X++]=F>>16&255,Y[X++]=F>>8&255,Y[X++]=255&F;return 2===W&&(F=revLookup[D.charCodeAt(O)]<<2|revLookup[D.charCodeAt(O+1)]>>4,Y[X++]=255&F),1===W&&(F=revLookup[D.charCodeAt(O)]<<10|revLookup[D.charCodeAt(O+1)]<<4|revLookup[D.charCodeAt(O+2)]>>2,Y[X++]=F>>8&255,Y[X++]=255&F),Y}function tripletToBase64(D){return lookup[D>>18&63]+lookup[D>>12&63]+lookup[D>>6&63]+lookup[63&D]}function encodeChunk(D,F,O){for(var U=[],G=F;G<O;G+=3)U.push(tripletToBase64((D[G]<<16&16711680)+(D[G+1]<<8&65280)+(255&D[G+2])));return U.join("")}function fromByteArray(D){for(var F,O=D.length,U=O%3,G=[],W=16383,Y=0,X=O-U;Y<X;Y+=W)G.push(encodeChunk(D,Y,Y+W>X?X:Y+W));return 1===U?G.push(lookup[(F=D[O-1])>>2]+lookup[F<<4&63]+"=="):2===U&&G.push(lookup[(F=(D[O-2]<<8)+D[O-1])>>10]+lookup[F>>4&63]+lookup[F<<2&63]+"="),G.join("")}revLookup["-".charCodeAt(0)]=62,revLookup["_".charCodeAt(0)]=63;var ieee754={};function asyncGeneratorStep$1(D,F,O,U,G,W,Y){try{var X=D[W](Y),J=X.value}catch(D){O(D);return}X.done?F(J):Promise.resolve(J).then(U,G)}function _async_to_generator$1(D){return function(){var F=this,O=arguments;return new Promise(function(U,G){var W=D.apply(F,O);function Y(D){asyncGeneratorStep$1(W,U,G,Y,X,"next",D)}function X(D){asyncGeneratorStep$1(W,U,G,Y,X,"throw",D)}Y(void 0)})}}function _ts_generator$1(D,F){var O,U,G,W,Y={label:0,sent:function(){if(1&G[0])throw G[1];return G[1]},trys:[],ops:[]};return W={next:X(0),throw:X(1),return:X(2)},"function"==typeof Symbol&&(W[Symbol.iterator]=function(){return this}),W;function X(D){return function(F){return J([D,F])}}function J(W){if(O)throw TypeError("Generator is already executing.");for(;Y;)try{if(O=1,U&&(G=2&W[0]?U.return:W[0]?U.throw||((G=U.return)&&G.call(U),0):U.next)&&!(G=G.call(U,W[1])).done)return G;switch(U=0,G&&(W=[2&W[0],G.value]),W[0]){case 0:case 1:G=W;break;case 4:return Y.label++,{value:W[1],done:!1};case 5:Y.label++,U=W[1],W=[0];continue;case 7:W=Y.ops.pop(),Y.trys.pop();continue;default:if(!(G=(G=Y.trys).length>0&&G[G.length-1])&&(6===W[0]||2===W[0])){Y=0;continue}if(3===W[0]&&(!G||W[1]>G[0]&&W[1]<G[3])){Y.label=W[1];break}if(6===W[0]&&Y.label<G[1]){Y.label=G[1],G=W;break}if(G&&Y.label<G[2]){Y.label=G[2],Y.ops.push(W);break}G[2]&&Y.ops.pop(),Y.trys.pop();continue}W=F.call(D,Y)}catch(D){W=[6,D],U=0}finally{O=G=0}if(5&W[0])throw W[1];return{value:W[0]?W[1]:void 0,done:!0}}}/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */ieee754.read=function(D,F,O,U,G){var W,Y,X=8*G-U-1,J=(1<<X)-1,K=J>>1,Z=-7,Q=O?G-1:0,tt=O?-1:1,te=D[F+Q];for(Q+=tt,W=te&(1<<-Z)-1,te>>=-Z,Z+=X;Z>0;W=256*W+D[F+Q],Q+=tt,Z-=8);for(Y=W&(1<<-Z)-1,W>>=-Z,Z+=U;Z>0;Y=256*Y+D[F+Q],Q+=tt,Z-=8);if(0===W)W=1-K;else{if(W===J)return Y?NaN:(te?-1:1)*(1/0);Y+=Math.pow(2,U),W-=K}return(te?-1:1)*Y*Math.pow(2,W-U)},ieee754.write=function(D,F,O,U,G,W){var Y,X,J,K=8*W-G-1,Z=(1<<K)-1,Q=Z>>1,tt=23===G?5960464477539062e-23:0,te=U?0:W-1,tr=U?1:-1,ts=F<0||0===F&&1/F<0?1:0;for(isNaN(F=Math.abs(F))||F===1/0?(X=isNaN(F)?1:0,Y=Z):(Y=Math.floor(Math.log(F)/Math.LN2),F*(J=Math.pow(2,-Y))<1&&(Y--,J*=2),Y+Q>=1?F+=tt/J:F+=tt*Math.pow(2,1-Q),F*J>=2&&(Y++,J/=2),Y+Q>=Z?(X=0,Y=Z):Y+Q>=1?(X=(F*J-1)*Math.pow(2,G),Y+=Q):(X=F*Math.pow(2,Q-1)*Math.pow(2,G),Y=0));G>=8;D[O+te]=255&X,te+=tr,X/=256,G-=8);for(Y=Y<<G|X,K+=G;K>0;D[O+te]=255&Y,te+=tr,Y/=256,K-=8);D[O+te-tr]|=128*ts},function(D){let F=base64Js,O=ieee754,U="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;D.Buffer=X,D.SlowBuffer=th,D.INSPECT_MAX_BYTES=50;let G=2147483647;function W(){try{let D=new Uint8Array(1),F={foo:function(){return 42}};return Object.setPrototypeOf(F,Uint8Array.prototype),Object.setPrototypeOf(D,F),42===D.foo()}catch(D){return!1}}function Y(D){if(D>G)throw RangeError('The value "'+D+'" is invalid for option "size"');let F=new Uint8Array(D);return Object.setPrototypeOf(F,X.prototype),F}function X(D,F,O){if("number"==typeof D){if("string"==typeof F)throw TypeError('The "string" argument must be of type string. Received type number');return Q(D)}return J(D,F,O)}function J(D,F,O){if("string"==typeof D)return tt(D,F);if(ArrayBuffer.isView(D))return tr(D);if(null==D)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof D);if(tQ(D,ArrayBuffer)||D&&tQ(D.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(tQ(D,SharedArrayBuffer)||D&&tQ(D.buffer,SharedArrayBuffer)))return ts(D,F,O);if("number"==typeof D)throw TypeError('The "value" argument must not be of type number. Received type number');let U=D.valueOf&&D.valueOf();if(null!=U&&U!==D)return X.from(U,F,O);let G=tn(D);if(G)return G;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof D[Symbol.toPrimitive])return X.from(D[Symbol.toPrimitive]("string"),F,O);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof D)}function K(D){if("number"!=typeof D)throw TypeError('"size" argument must be of type number');if(D<0)throw RangeError('The value "'+D+'" is invalid for option "size"')}function Z(D,F,O){return(K(D),D<=0)?Y(D):void 0!==F?"string"==typeof O?Y(D).fill(F,O):Y(D).fill(F):Y(D)}function Q(D){return K(D),Y(D<0?0:0|ta(D))}function tt(D,F){if(("string"!=typeof F||""===F)&&(F="utf8"),!X.isEncoding(F))throw TypeError("Unknown encoding: "+F);let O=0|tp(D,F),U=Y(O),G=U.write(D,F);return G!==O&&(U=U.slice(0,G)),U}function te(D){let F=D.length<0?0:0|ta(D.length),O=Y(F);for(let U=0;U<F;U+=1)O[U]=255&D[U];return O}function tr(D){if(tQ(D,Uint8Array)){let F=new Uint8Array(D);return ts(F.buffer,F.byteOffset,F.byteLength)}return te(D)}function ts(D,F,O){let U;if(F<0||D.byteLength<F)throw RangeError('"offset" is outside of buffer bounds');if(D.byteLength<F+(O||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(U=void 0===F&&void 0===O?new Uint8Array(D):void 0===O?new Uint8Array(D,F):new Uint8Array(D,F,O),X.prototype),U}function tn(D){if(X.isBuffer(D)){let F=0|ta(D.length),O=Y(F);return 0===O.length||D.copy(O,0,0,F),O}return void 0!==D.length?"number"!=typeof D.length||t3(D.length)?Y(0):te(D):"Buffer"===D.type&&Array.isArray(D.data)?te(D.data):void 0}function ta(D){if(D>=G)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+G.toString(16)+" bytes");return 0|D}function th(D){return+D!=D&&(D=0),X.alloc(+D)}function tp(D,F){if(X.isBuffer(D))return D.length;if(ArrayBuffer.isView(D)||tQ(D,ArrayBuffer))return D.byteLength;if("string"!=typeof D)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof D);let O=D.length,U=arguments.length>2&&!0===arguments[2];if(!U&&0===O)return 0;let G=!1;for(;;)switch(F){case"ascii":case"latin1":case"binary":return O;case"utf8":case"utf-8":return tY(D).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*O;case"hex":return O>>>1;case"base64":return tK(D).length;default:if(G)return U?-1:tY(D).length;F=(""+F).toLowerCase(),G=!0}}function tf(D,F,O){let U=!1;if((void 0===F||F<0)&&(F=0),F>this.length||((void 0===O||O>this.length)&&(O=this.length),O<=0||(O>>>=0)<=(F>>>=0)))return"";for(D||(D="utf8");;)switch(D){case"hex":return tT(this,F,O);case"utf8":case"utf-8":return tA(this,F,O);case"ascii":return tS(this,F,O);case"latin1":case"binary":return tE(this,F,O);case"base64":return tw(this,F,O);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return tD(this,F,O);default:if(U)throw TypeError("Unknown encoding: "+D);D=(D+"").toLowerCase(),U=!0}}function tu(D,F,O){let U=D[F];D[F]=D[O],D[O]=U}function tc(D,F,O,U,G){if(0===D.length)return -1;if("string"==typeof O?(U=O,O=0):O>2147483647?O=2147483647:O<-2147483648&&(O=-2147483648),t3(O=+O)&&(O=G?0:D.length-1),O<0&&(O=D.length+O),O>=D.length){if(G)return -1;O=D.length-1}else if(O<0){if(!G)return -1;O=0}if("string"==typeof F&&(F=X.from(F,U)),X.isBuffer(F))return 0===F.length?-1:tm(D,F,O,U,G);if("number"==typeof F)return(F&=255,"function"==typeof Uint8Array.prototype.indexOf)?G?Uint8Array.prototype.indexOf.call(D,F,O):Uint8Array.prototype.lastIndexOf.call(D,F,O):tm(D,[F],O,U,G);throw TypeError("val must be string, number or Buffer")}function tm(D,F,O,U,G){let W,Y=1,X=D.length,J=F.length;if(void 0!==U&&("ucs2"===(U=String(U).toLowerCase())||"ucs-2"===U||"utf16le"===U||"utf-16le"===U)){if(D.length<2||F.length<2)return -1;Y=2,X/=2,J/=2,O/=2}function K(D,F){return 1===Y?D[F]:D.readUInt16BE(F*Y)}if(G){let U=-1;for(W=O;W<X;W++)if(K(D,W)===K(F,-1===U?0:W-U)){if(-1===U&&(U=W),W-U+1===J)return U*Y}else -1!==U&&(W-=W-U),U=-1}else for(O+J>X&&(O=X-J),W=O;W>=0;W--){let O=!0;for(let U=0;U<J;U++)if(K(D,W+U)!==K(F,U)){O=!1;break}if(O)return W}return -1}function tg(D,F,O,U){let G;O=Number(O)||0;let W=D.length-O;U?(U=Number(U))>W&&(U=W):U=W;let Y=F.length;for(U>Y/2&&(U=Y/2),G=0;G<U;++G){let U=parseInt(F.substr(2*G,2),16);if(t3(U))break;D[O+G]=U}return G}function tv(D,F,O,U){return tZ(tY(F,D.length-O),D,O,U)}function tb(D,F,O,U){return tZ(tX(F),D,O,U)}function t_(D,F,O,U){return tZ(tK(F),D,O,U)}function tk(D,F,O,U){return tZ(tJ(F,D.length-O),D,O,U)}function tw(D,O,U){return 0===O&&U===D.length?F.fromByteArray(D):F.fromByteArray(D.slice(O,U))}function tA(D,F,O){O=Math.min(D.length,O);let U=[],G=F;for(;G<O;){let F=D[G],W=null,Y=F>239?4:F>223?3:F>191?2:1;if(G+Y<=O){let O,U,X,J;switch(Y){case 1:F<128&&(W=F);break;case 2:(192&(O=D[G+1]))==128&&(J=(31&F)<<6|63&O)>127&&(W=J);break;case 3:O=D[G+1],U=D[G+2],(192&O)==128&&(192&U)==128&&(J=(15&F)<<12|(63&O)<<6|63&U)>2047&&(J<55296||J>57343)&&(W=J);break;case 4:O=D[G+1],U=D[G+2],X=D[G+3],(192&O)==128&&(192&U)==128&&(192&X)==128&&(J=(15&F)<<18|(63&O)<<12|(63&U)<<6|63&X)>65535&&J<1114112&&(W=J)}}null===W?(W=65533,Y=1):W>65535&&(W-=65536,U.push(W>>>10&1023|55296),W=56320|1023&W),U.push(W),G+=Y}return tC(U)}D.kMaxLength=G,X.TYPED_ARRAY_SUPPORT=W(),X.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(X.prototype,"parent",{enumerable:!0,get:function(){if(X.isBuffer(this))return this.buffer}}),Object.defineProperty(X.prototype,"offset",{enumerable:!0,get:function(){if(X.isBuffer(this))return this.byteOffset}}),X.poolSize=8192,X.from=function(D,F,O){return J(D,F,O)},Object.setPrototypeOf(X.prototype,Uint8Array.prototype),Object.setPrototypeOf(X,Uint8Array),X.alloc=function(D,F,O){return Z(D,F,O)},X.allocUnsafe=function(D){return Q(D)},X.allocUnsafeSlow=function(D){return Q(D)},X.isBuffer=function(D){return null!=D&&!0===D._isBuffer&&D!==X.prototype},X.compare=function(D,F){if(tQ(D,Uint8Array)&&(D=X.from(D,D.offset,D.byteLength)),tQ(F,Uint8Array)&&(F=X.from(F,F.offset,F.byteLength)),!X.isBuffer(D)||!X.isBuffer(F))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(D===F)return 0;let O=D.length,U=F.length;for(let G=0,W=Math.min(O,U);G<W;++G)if(D[G]!==F[G]){O=D[G],U=F[G];break}return O<U?-1:U<O?1:0},X.isEncoding=function(D){switch(String(D).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},X.concat=function(D,F){let O;if(!Array.isArray(D))throw TypeError('"list" argument must be an Array of Buffers');if(0===D.length)return X.alloc(0);if(void 0===F)for(O=0,F=0;O<D.length;++O)F+=D[O].length;let U=X.allocUnsafe(F),G=0;for(O=0;O<D.length;++O){let F=D[O];if(tQ(F,Uint8Array))G+F.length>U.length?(X.isBuffer(F)||(F=X.from(F)),F.copy(U,G)):Uint8Array.prototype.set.call(U,F,G);else if(X.isBuffer(F))F.copy(U,G);else throw TypeError('"list" argument must be an Array of Buffers');G+=F.length}return U},X.byteLength=tp,X.prototype._isBuffer=!0,X.prototype.swap16=function(){let D=this.length;if(D%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(let F=0;F<D;F+=2)tu(this,F,F+1);return this},X.prototype.swap32=function(){let D=this.length;if(D%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(let F=0;F<D;F+=4)tu(this,F,F+3),tu(this,F+1,F+2);return this},X.prototype.swap64=function(){let D=this.length;if(D%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(let F=0;F<D;F+=8)tu(this,F,F+7),tu(this,F+1,F+6),tu(this,F+2,F+5),tu(this,F+3,F+4);return this},X.prototype.toString=function(){let D=this.length;return 0===D?"":0==arguments.length?tA(this,0,D):tf.apply(this,arguments)},X.prototype.toLocaleString=X.prototype.toString,X.prototype.equals=function(D){if(!X.isBuffer(D))throw TypeError("Argument must be a Buffer");return this===D||0===X.compare(this,D)},X.prototype.inspect=function(){let F="",O=D.INSPECT_MAX_BYTES;return F=this.toString("hex",0,O).replace(/(.{2})/g,"$1 ").trim(),this.length>O&&(F+=" ... "),"<Buffer "+F+">"},U&&(X.prototype[U]=X.prototype.inspect),X.prototype.compare=function(D,F,O,U,G){if(tQ(D,Uint8Array)&&(D=X.from(D,D.offset,D.byteLength)),!X.isBuffer(D))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof D);if(void 0===F&&(F=0),void 0===O&&(O=D?D.length:0),void 0===U&&(U=0),void 0===G&&(G=this.length),F<0||O>D.length||U<0||G>this.length)throw RangeError("out of range index");if(U>=G&&F>=O)return 0;if(U>=G)return -1;if(F>=O)return 1;if(F>>>=0,O>>>=0,U>>>=0,G>>>=0,this===D)return 0;let W=G-U,Y=O-F,J=Math.min(W,Y),K=this.slice(U,G),Z=D.slice(F,O);for(let D=0;D<J;++D)if(K[D]!==Z[D]){W=K[D],Y=Z[D];break}return W<Y?-1:Y<W?1:0},X.prototype.includes=function(D,F,O){return -1!==this.indexOf(D,F,O)},X.prototype.indexOf=function(D,F,O){return tc(this,D,F,O,!0)},X.prototype.lastIndexOf=function(D,F,O){return tc(this,D,F,O,!1)},X.prototype.write=function(D,F,O,U){if(void 0===F)U="utf8",O=this.length,F=0;else if(void 0===O&&"string"==typeof F)U=F,O=this.length,F=0;else if(isFinite(F))F>>>=0,isFinite(O)?(O>>>=0,void 0===U&&(U="utf8")):(U=O,O=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let G=this.length-F;if((void 0===O||O>G)&&(O=G),D.length>0&&(O<0||F<0)||F>this.length)throw RangeError("Attempt to write outside buffer bounds");U||(U="utf8");let W=!1;for(;;)switch(U){case"hex":return tg(this,D,F,O);case"utf8":case"utf-8":return tv(this,D,F,O);case"ascii":case"latin1":case"binary":return tb(this,D,F,O);case"base64":return t_(this,D,F,O);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return tk(this,D,F,O);default:if(W)throw TypeError("Unknown encoding: "+U);U=(""+U).toLowerCase(),W=!0}},X.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};let tP=4096;function tC(D){let F=D.length;if(F<=tP)return String.fromCharCode.apply(String,D);let O="",U=0;for(;U<F;)O+=String.fromCharCode.apply(String,D.slice(U,U+=tP));return O}function tS(D,F,O){let U="";O=Math.min(D.length,O);for(let G=F;G<O;++G)U+=String.fromCharCode(127&D[G]);return U}function tE(D,F,O){let U="";O=Math.min(D.length,O);for(let G=F;G<O;++G)U+=String.fromCharCode(D[G]);return U}function tT(D,F,O){let U=D.length;(!F||F<0)&&(F=0),(!O||O<0||O>U)&&(O=U);let G="";for(let U=F;U<O;++U)G+=t5[D[U]];return G}function tD(D,F,O){let U=D.slice(F,O),G="";for(let D=0;D<U.length-1;D+=2)G+=String.fromCharCode(U[D]+256*U[D+1]);return G}function tM(D,F,O){if(D%1!=0||D<0)throw RangeError("offset is not uint");if(D+F>O)throw RangeError("Trying to access beyond buffer length")}function tF(D,F,O,U,G,W){if(!X.isBuffer(D))throw TypeError('"buffer" argument must be a Buffer instance');if(F>G||F<W)throw RangeError('"value" argument is out of bounds');if(O+U>D.length)throw RangeError("Index out of range")}function tI(D,F,O,U,G){tU(F,U,G,D,O,7);let W=Number(F&BigInt(4294967295));D[O++]=W,W>>=8,D[O++]=W,W>>=8,D[O++]=W,W>>=8,D[O++]=W;let Y=Number(F>>BigInt(32)&BigInt(4294967295));return D[O++]=Y,Y>>=8,D[O++]=Y,Y>>=8,D[O++]=Y,Y>>=8,D[O++]=Y,O}function tL(D,F,O,U,G){tU(F,U,G,D,O,7);let W=Number(F&BigInt(4294967295));D[O+7]=W,W>>=8,D[O+6]=W,W>>=8,D[O+5]=W,W>>=8,D[O+4]=W;let Y=Number(F>>BigInt(32)&BigInt(4294967295));return D[O+3]=Y,Y>>=8,D[O+2]=Y,Y>>=8,D[O+1]=Y,Y>>=8,D[O]=Y,O+8}function tB(D,F,O,U,G,W){if(O+U>D.length||O<0)throw RangeError("Index out of range")}function tO(D,F,U,G,W){return F=+F,U>>>=0,W||tB(D,F,U,4),O.write(D,F,U,G,23,4),U+4}function tR(D,F,U,G,W){return F=+F,U>>>=0,W||tB(D,F,U,8),O.write(D,F,U,G,52,8),U+8}X.prototype.slice=function(D,F){let O=this.length;D=~~D,F=void 0===F?O:~~F,D<0?(D+=O)<0&&(D=0):D>O&&(D=O),F<0?(F+=O)<0&&(F=0):F>O&&(F=O),F<D&&(F=D);let U=this.subarray(D,F);return Object.setPrototypeOf(U,X.prototype),U},X.prototype.readUintLE=X.prototype.readUIntLE=function(D,F,O){D>>>=0,F>>>=0,O||tM(D,F,this.length);let U=this[D],G=1,W=0;for(;++W<F&&(G*=256);)U+=this[D+W]*G;return U},X.prototype.readUintBE=X.prototype.readUIntBE=function(D,F,O){D>>>=0,F>>>=0,O||tM(D,F,this.length);let U=this[D+--F],G=1;for(;F>0&&(G*=256);)U+=this[D+--F]*G;return U},X.prototype.readUint8=X.prototype.readUInt8=function(D,F){return D>>>=0,F||tM(D,1,this.length),this[D]},X.prototype.readUint16LE=X.prototype.readUInt16LE=function(D,F){return D>>>=0,F||tM(D,2,this.length),this[D]|this[D+1]<<8},X.prototype.readUint16BE=X.prototype.readUInt16BE=function(D,F){return D>>>=0,F||tM(D,2,this.length),this[D]<<8|this[D+1]},X.prototype.readUint32LE=X.prototype.readUInt32LE=function(D,F){return D>>>=0,F||tM(D,4,this.length),(this[D]|this[D+1]<<8|this[D+2]<<16)+16777216*this[D+3]},X.prototype.readUint32BE=X.prototype.readUInt32BE=function(D,F){return D>>>=0,F||tM(D,4,this.length),16777216*this[D]+(this[D+1]<<16|this[D+2]<<8|this[D+3])},X.prototype.readBigUInt64LE=t4(function(D){tG(D>>>=0,"offset");let F=this[D],O=this[D+7];(void 0===F||void 0===O)&&tq(D,this.length-8);let U=F+256*this[++D]+65536*this[++D]+16777216*this[++D],G=this[++D]+256*this[++D]+65536*this[++D]+16777216*O;return BigInt(U)+(BigInt(G)<<BigInt(32))}),X.prototype.readBigUInt64BE=t4(function(D){tG(D>>>=0,"offset");let F=this[D],O=this[D+7];(void 0===F||void 0===O)&&tq(D,this.length-8);let U=16777216*F+65536*this[++D]+256*this[++D]+this[++D],G=16777216*this[++D]+65536*this[++D]+256*this[++D]+O;return(BigInt(U)<<BigInt(32))+BigInt(G)}),X.prototype.readIntLE=function(D,F,O){D>>>=0,F>>>=0,O||tM(D,F,this.length);let U=this[D],G=1,W=0;for(;++W<F&&(G*=256);)U+=this[D+W]*G;return U>=(G*=128)&&(U-=Math.pow(2,8*F)),U},X.prototype.readIntBE=function(D,F,O){D>>>=0,F>>>=0,O||tM(D,F,this.length);let U=F,G=1,W=this[D+--U];for(;U>0&&(G*=256);)W+=this[D+--U]*G;return W>=(G*=128)&&(W-=Math.pow(2,8*F)),W},X.prototype.readInt8=function(D,F){return(D>>>=0,F||tM(D,1,this.length),128&this[D])?-((255-this[D]+1)*1):this[D]},X.prototype.readInt16LE=function(D,F){D>>>=0,F||tM(D,2,this.length);let O=this[D]|this[D+1]<<8;return 32768&O?4294901760|O:O},X.prototype.readInt16BE=function(D,F){D>>>=0,F||tM(D,2,this.length);let O=this[D+1]|this[D]<<8;return 32768&O?4294901760|O:O},X.prototype.readInt32LE=function(D,F){return D>>>=0,F||tM(D,4,this.length),this[D]|this[D+1]<<8|this[D+2]<<16|this[D+3]<<24},X.prototype.readInt32BE=function(D,F){return D>>>=0,F||tM(D,4,this.length),this[D]<<24|this[D+1]<<16|this[D+2]<<8|this[D+3]},X.prototype.readBigInt64LE=t4(function(D){tG(D>>>=0,"offset");let F=this[D],O=this[D+7];(void 0===F||void 0===O)&&tq(D,this.length-8);let U=this[D+4]+256*this[D+5]+65536*this[D+6]+(O<<24);return(BigInt(U)<<BigInt(32))+BigInt(F+256*this[++D]+65536*this[++D]+16777216*this[++D])}),X.prototype.readBigInt64BE=t4(function(D){tG(D>>>=0,"offset");let F=this[D],O=this[D+7];(void 0===F||void 0===O)&&tq(D,this.length-8);let U=(F<<24)+65536*this[++D]+256*this[++D]+this[++D];return(BigInt(U)<<BigInt(32))+BigInt(16777216*this[++D]+65536*this[++D]+256*this[++D]+O)}),X.prototype.readFloatLE=function(D,F){return D>>>=0,F||tM(D,4,this.length),O.read(this,D,!0,23,4)},X.prototype.readFloatBE=function(D,F){return D>>>=0,F||tM(D,4,this.length),O.read(this,D,!1,23,4)},X.prototype.readDoubleLE=function(D,F){return D>>>=0,F||tM(D,8,this.length),O.read(this,D,!0,52,8)},X.prototype.readDoubleBE=function(D,F){return D>>>=0,F||tM(D,8,this.length),O.read(this,D,!1,52,8)},X.prototype.writeUintLE=X.prototype.writeUIntLE=function(D,F,O,U){if(D=+D,F>>>=0,O>>>=0,!U){let U=Math.pow(2,8*O)-1;tF(this,D,F,O,U,0)}let G=1,W=0;for(this[F]=255&D;++W<O&&(G*=256);)this[F+W]=D/G&255;return F+O},X.prototype.writeUintBE=X.prototype.writeUIntBE=function(D,F,O,U){if(D=+D,F>>>=0,O>>>=0,!U){let U=Math.pow(2,8*O)-1;tF(this,D,F,O,U,0)}let G=O-1,W=1;for(this[F+G]=255&D;--G>=0&&(W*=256);)this[F+G]=D/W&255;return F+O},X.prototype.writeUint8=X.prototype.writeUInt8=function(D,F,O){return D=+D,F>>>=0,O||tF(this,D,F,1,255,0),this[F]=255&D,F+1},X.prototype.writeUint16LE=X.prototype.writeUInt16LE=function(D,F,O){return D=+D,F>>>=0,O||tF(this,D,F,2,65535,0),this[F]=255&D,this[F+1]=D>>>8,F+2},X.prototype.writeUint16BE=X.prototype.writeUInt16BE=function(D,F,O){return D=+D,F>>>=0,O||tF(this,D,F,2,65535,0),this[F]=D>>>8,this[F+1]=255&D,F+2},X.prototype.writeUint32LE=X.prototype.writeUInt32LE=function(D,F,O){return D=+D,F>>>=0,O||tF(this,D,F,4,4294967295,0),this[F+3]=D>>>24,this[F+2]=D>>>16,this[F+1]=D>>>8,this[F]=255&D,F+4},X.prototype.writeUint32BE=X.prototype.writeUInt32BE=function(D,F,O){return D=+D,F>>>=0,O||tF(this,D,F,4,4294967295,0),this[F]=D>>>24,this[F+1]=D>>>16,this[F+2]=D>>>8,this[F+3]=255&D,F+4},X.prototype.writeBigUInt64LE=t4(function(D,F=0){return tI(this,D,F,BigInt(0),BigInt("0xffffffffffffffff"))}),X.prototype.writeBigUInt64BE=t4(function(D,F=0){return tL(this,D,F,BigInt(0),BigInt("0xffffffffffffffff"))}),X.prototype.writeIntLE=function(D,F,O,U){if(D=+D,F>>>=0,!U){let U=Math.pow(2,8*O-1);tF(this,D,F,O,U-1,-U)}let G=0,W=1,Y=0;for(this[F]=255&D;++G<O&&(W*=256);)D<0&&0===Y&&0!==this[F+G-1]&&(Y=1),this[F+G]=(D/W>>0)-Y&255;return F+O},X.prototype.writeIntBE=function(D,F,O,U){if(D=+D,F>>>=0,!U){let U=Math.pow(2,8*O-1);tF(this,D,F,O,U-1,-U)}let G=O-1,W=1,Y=0;for(this[F+G]=255&D;--G>=0&&(W*=256);)D<0&&0===Y&&0!==this[F+G+1]&&(Y=1),this[F+G]=(D/W>>0)-Y&255;return F+O},X.prototype.writeInt8=function(D,F,O){return D=+D,F>>>=0,O||tF(this,D,F,1,127,-128),D<0&&(D=255+D+1),this[F]=255&D,F+1},X.prototype.writeInt16LE=function(D,F,O){return D=+D,F>>>=0,O||tF(this,D,F,2,32767,-32768),this[F]=255&D,this[F+1]=D>>>8,F+2},X.prototype.writeInt16BE=function(D,F,O){return D=+D,F>>>=0,O||tF(this,D,F,2,32767,-32768),this[F]=D>>>8,this[F+1]=255&D,F+2},X.prototype.writeInt32LE=function(D,F,O){return D=+D,F>>>=0,O||tF(this,D,F,4,2147483647,-2147483648),this[F]=255&D,this[F+1]=D>>>8,this[F+2]=D>>>16,this[F+3]=D>>>24,F+4},X.prototype.writeInt32BE=function(D,F,O){return D=+D,F>>>=0,O||tF(this,D,F,4,2147483647,-2147483648),D<0&&(D=4294967295+D+1),this[F]=D>>>24,this[F+1]=D>>>16,this[F+2]=D>>>8,this[F+3]=255&D,F+4},X.prototype.writeBigInt64LE=t4(function(D,F=0){return tI(this,D,F,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),X.prototype.writeBigInt64BE=t4(function(D,F=0){return tL(this,D,F,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),X.prototype.writeFloatLE=function(D,F,O){return tO(this,D,F,!0,O)},X.prototype.writeFloatBE=function(D,F,O){return tO(this,D,F,!1,O)},X.prototype.writeDoubleLE=function(D,F,O){return tR(this,D,F,!0,O)},X.prototype.writeDoubleBE=function(D,F,O){return tR(this,D,F,!1,O)},X.prototype.copy=function(D,F,O,U){if(!X.isBuffer(D))throw TypeError("argument should be a Buffer");if(O||(O=0),U||0===U||(U=this.length),F>=D.length&&(F=D.length),F||(F=0),U>0&&U<O&&(U=O),U===O||0===D.length||0===this.length)return 0;if(F<0)throw RangeError("targetStart out of bounds");if(O<0||O>=this.length)throw RangeError("Index out of range");if(U<0)throw RangeError("sourceEnd out of bounds");U>this.length&&(U=this.length),D.length-F<U-O&&(U=D.length-F+O);let G=U-O;return this===D&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(F,O,U):Uint8Array.prototype.set.call(D,this.subarray(O,U),F),G},X.prototype.fill=function(D,F,O,U){let G;if("string"==typeof D){if("string"==typeof F?(U=F,F=0,O=this.length):"string"==typeof O&&(U=O,O=this.length),void 0!==U&&"string"!=typeof U)throw TypeError("encoding must be a string");if("string"==typeof U&&!X.isEncoding(U))throw TypeError("Unknown encoding: "+U);if(1===D.length){let F=D.charCodeAt(0);("utf8"===U&&F<128||"latin1"===U)&&(D=F)}}else"number"==typeof D?D&=255:"boolean"==typeof D&&(D=Number(D));if(F<0||this.length<F||this.length<O)throw RangeError("Out of range index");if(O<=F)return this;if(F>>>=0,O=void 0===O?this.length:O>>>0,D||(D=0),"number"==typeof D)for(G=F;G<O;++G)this[G]=D;else{let W=X.isBuffer(D)?D:X.from(D,U),Y=W.length;if(0===Y)throw TypeError('The value "'+D+'" is invalid for argument "value"');for(G=0;G<O-F;++G)this[G+F]=W[G%Y]}return this};let tV={};function tN(D,F,O){tV[D]=class extends O{constructor(){super(),Object.defineProperty(this,"message",{value:F.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${D}]`,this.stack,delete this.name}get code(){return D}set code(D){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:D,writable:!0})}toString(){return`${this.name} [${D}]: ${this.message}`}}}function t$(D){let F="",O=D.length,U="-"===D[0]?1:0;for(;O>=U+4;O-=3)F=`_${D.slice(O-3,O)}${F}`;return`${D.slice(0,O)}${F}`}function tj(D,F,O){tG(F,"offset"),(void 0===D[F]||void 0===D[F+O])&&tq(F,D.length-(O+1))}function tU(D,F,O,U,G,W){if(D>O||D<F){let U;let G="bigint"==typeof F?"n":"";throw U=W>3?0===F||F===BigInt(0)?`>= 0${G} and < 2${G} ** ${(W+1)*8}${G}`:`>= -(2${G} ** ${(W+1)*8-1}${G}) and < 2 ** ${(W+1)*8-1}${G}`:`>= ${F}${G} and <= ${O}${G}`,new tV.ERR_OUT_OF_RANGE("value",U,D)}tj(U,G,W)}function tG(D,F){if("number"!=typeof D)throw new tV.ERR_INVALID_ARG_TYPE(F,"number",D)}function tq(D,F,O){if(Math.floor(D)!==D)throw tG(D,O),new tV.ERR_OUT_OF_RANGE(O||"offset","an integer",D);if(F<0)throw new tV.ERR_BUFFER_OUT_OF_BOUNDS;throw new tV.ERR_OUT_OF_RANGE(O||"offset",`>= ${O?1:0} and <= ${F}`,D)}tN("ERR_BUFFER_OUT_OF_BOUNDS",function(D){return D?`${D} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),tN("ERR_INVALID_ARG_TYPE",function(D,F){return`The "${D}" argument must be of type number. Received type ${typeof F}`},TypeError),tN("ERR_OUT_OF_RANGE",function(D,F,O){let U=`The value of "${D}" is out of range.`,G=O;return Number.isInteger(O)&&Math.abs(O)>4294967296?G=t$(String(O)):"bigint"==typeof O&&(G=String(O),(O>BigInt(2)**BigInt(32)||O<-(BigInt(2)**BigInt(32)))&&(G=t$(G)),G+="n"),U+=` It must be ${F}. Received ${G}`},RangeError);let tW=/[^+/0-9A-Za-z-_]/g;function tH(D){if((D=(D=D.split("=")[0]).trim().replace(tW,"")).length<2)return"";for(;D.length%4!=0;)D+="=";return D}function tY(D,F){let O;F=F||1/0;let U=D.length,G=null,W=[];for(let Y=0;Y<U;++Y){if((O=D.charCodeAt(Y))>55295&&O<57344){if(!G){if(O>56319||Y+1===U){(F-=3)>-1&&W.push(239,191,189);continue}G=O;continue}if(O<56320){(F-=3)>-1&&W.push(239,191,189),G=O;continue}O=(G-55296<<10|O-56320)+65536}else G&&(F-=3)>-1&&W.push(239,191,189);if(G=null,O<128){if((F-=1)<0)break;W.push(O)}else if(O<2048){if((F-=2)<0)break;W.push(O>>6|192,63&O|128)}else if(O<65536){if((F-=3)<0)break;W.push(O>>12|224,O>>6&63|128,63&O|128)}else if(O<1114112){if((F-=4)<0)break;W.push(O>>18|240,O>>12&63|128,O>>6&63|128,63&O|128)}else throw Error("Invalid code point")}return W}function tX(D){let F=[];for(let O=0;O<D.length;++O)F.push(255&D.charCodeAt(O));return F}function tJ(D,F){let O,U;let G=[];for(let W=0;W<D.length&&!((F-=2)<0);++W)U=(O=D.charCodeAt(W))>>8,G.push(O%256),G.push(U);return G}function tK(D){return F.toByteArray(tH(D))}function tZ(D,F,O,U){let G;for(G=0;G<U&&!(G+O>=F.length)&&!(G>=D.length);++G)F[G+O]=D[G];return G}function tQ(D,F){return D instanceof F||null!=D&&null!=D.constructor&&null!=D.constructor.name&&D.constructor.name===F.name}function t3(D){return D!=D}let t5=function(){let D="0123456789abcdef",F=Array(256);for(let O=0;O<16;++O){let U=16*O;for(let G=0;G<16;++G)F[U+G]=D[O]+D[G]}return F}();function t4(D){return"undefined"==typeof BigInt?t6:D}function t6(){throw Error("BigInt not supported")}}(buffer);var aspectRatio=function(D){switch(D){case"contain":case"scale-down":default:return"xMidYMid meet";case"cover":return"xMidYMid slice";case"fill":return"none";case"none":return"xMinYMin slice"}},fetchPath=function(){var D=_async_to_generator$1(function(D){var F,O,U,G,W,Y,X,J,K,Z;return _ts_generator$1(this,function(Q){switch(Q.label){case 0:O=null===(F=D.split(".").pop())||void 0===F?void 0:F.toLowerCase(),Q.label=1;case 1:return Q.trys.push([1,8,,9]),[4,fetch(D)];case 2:if(G=Q.sent(),"json"!==O)return[3,4];return[4,G.json()];case 3:return[2,Q.sent()];case 4:return J=Uint8Array.bind,[4,G.arrayBuffer()];case 5:return W=new(J.apply(Uint8Array,[void 0,Q.sent()])),[4,new Promise(function(D,F){unzip(W,function(O,U){O&&F(O),D(U)})})];case 6:if(!("animations"in(X=JSON.parse(strFromU8((Y=Q.sent())["manifest.json"])))))throw Error("Manifest not found");if(!X.animations.length)throw Error("No animations listed in the manifest");return K=X.animations[0].id,[4,JSON.parse(strFromU8(null===(U=Y)||void 0===U?void 0:U["animations/".concat(K,".json")]))];case 7:return"assets"in(Z=Q.sent())&&Promise.all(Z.assets.map(function(D){var F,O=D.p;if(O&&(null===(F=Y)||void 0===F?void 0:F["images/".concat(O)]))return new Promise(function(F){var U,G=O.split(".").pop(),W=null===(U=buffer.Buffer.from(Y?.["images/".concat(O)]))||void 0===U?void 0:U.toString("base64");switch(G){case"svg":case"svg+xml":D.p="data:image/svg+xml;base64,".concat(W);break;case"png":case"jpg":case"jpeg":case"gif":case"webp":D.p="data:image/".concat(G,";base64,").concat(W);break;default:D.p="data:;base64,".concat(W)}D.e=1,F()})})),[2,Z];case 8:throw Q.sent(),Error("Error loading Lottie file.");case 9:return[2]}})});return function(F){return D.apply(this,arguments)}}();function _tagged_template_literal$1(D,F){return F||(F=D.slice(0)),Object.freeze(Object.defineProperties(D,{raw:{value:Object.freeze(F)}}))}function _templateObject$1(){var D=_tagged_template_literal$1(["*{box-sizing:border-box}:host{--lottie-player-toolbar-height:35px;--lottie-player-toolbar-background-color:#FFF;--lottie-player-toolbar-icon-color:#000;--lottie-player-toolbar-icon-hover-color:#000;--lottie-player-toolbar-icon-active-color:#4285f4;--lottie-player-seeker-track-color:rgba(0, 0, 0, 0.2);--lottie-player-seeker-thumb-color:#4285f4;--lottie-player-seeker-display:block;display:block;width:100%;height:100%}@media (prefers-color-scheme:dark){:host{--lottie-player-toolbar-background-color:#000;--lottie-player-toolbar-icon-color:#FFF;--lottie-player-toolbar-icon-hover-color:#FFF;--lottie-player-seeker-track-color:rgba(255, 255, 255, 0.6)}}.main{display:flex;flex-direction:column;height:100%;width:100%}.animation{width:100%;height:100%;display:flex}.animation.controls{height:calc(100% - 35px)}.toolbar{display:flex;align-items:center;justify-items:center;background:var(--lottie-player-toolbar-background-color);margin:0 5px;height:35px;padding:5px;border-radius:5px}.toolbar button{cursor:pointer;fill:var(--lottie-player-toolbar-icon-color);display:flex;background:0 0;border:0;padding:0;outline:0;height:100%}.toolbar button.active{fill:var(--lottie-player-toolbar-icon-active-color)}.toolbar button:focus{outline:0}.toolbar button svg>*{fill:inherit}.toolbar button.disabled svg{display:none}.seeker,.seeker::-webkit-slider-runnable-track,.seeker::-webkit-slider-thumb{-webkit-appearance:none;appearance:none;outline:0}.seeker{-webkit-appearance:none;appearance:none;width:95%;background-color:var(--lottie-player-toolbar-background-color);display:var(--lottie-player-seeker-display);color:var(--lottie-player-seeker-thumb-color)}.seeker::-webkit-slider-runnable-track{width:100%;height:5px;cursor:pointer;background-color:var(--lottie-player-seeker-track-color);border-radius:3px}.seeker::-webkit-progress-value{background-color:var(--lottie-player-seeker-thumb-color)}.seeker::-webkit-slider-thumb{height:15px;width:15px;border-radius:50%;background-color:var(--lottie-player-seeker-thumb-color);cursor:pointer;-webkit-appearance:none;appearance:none;margin-top:-5px}.seeker:focus::-webkit-slider-runnable-track{background-color:var(--lottie-player-seeker-track-color)}.seeker::-moz-range-track{width:100%;height:5px;cursor:pointer;background-color:var(--lottie-player-seeker-track-color);border-radius:3px;border:0}.seeker::-moz-range-progress{background-color:var(--lottie-player-seeker-thumb-color);height:5px;border-radius:3px}.seeker::-moz-range-thumb{height:15px;width:15px;border-radius:50%;background-color:var(--lottie-player-seeker-thumb-color);border:0;cursor:pointer}.seeker::-ms-track{width:100%;height:5px;cursor:pointer;background:0 0;border-color:transparent;color:transparent}.seeker::-ms-fill-upper{background:var(--lottie-player-seeker-track-color);border-radius:3px}.seeker::-ms-fill-lower{background-color:var(--lottie-player-seeker-thumb-color);border-radius:3px}.seeker::-ms-thumb{border:0;height:15px;width:15px;border-radius:50%;background:var(--lottie-player-seeker-thumb-color);cursor:pointer}.seeker:focus::-ms-fill-lower{background:var(--lottie-player-seeker-track-color)}.seeker:focus::-ms-fill-upper{background:var(--lottie-player-seeker-track-color)}.error{display:flex;margin:auto;justify-content:center;height:100%;align-items:center}"]);return _templateObject$1=function(){return D},D}var styles=i$5(_templateObject$1());function _assert_this_initialized(D){if(void 0===D)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return D}function asyncGeneratorStep(D,F,O,U,G,W,Y){try{var X=D[W](Y),J=X.value}catch(D){O(D);return}X.done?F(J):Promise.resolve(J).then(U,G)}function _async_to_generator(D){return function(){var F=this,O=arguments;return new Promise(function(U,G){var W=D.apply(F,O);function Y(D){asyncGeneratorStep(W,U,G,Y,X,"next",D)}function X(D){asyncGeneratorStep(W,U,G,Y,X,"throw",D)}Y(void 0)})}}function _class_call_check(D,F){if(!(D instanceof F))throw TypeError("Cannot call a class as a function")}function _defineProperties(D,F){for(var O=0;O<F.length;O++){var U=F[O];U.enumerable=U.enumerable||!1,U.configurable=!0,"value"in U&&(U.writable=!0),Object.defineProperty(D,U.key,U)}}function _create_class(D,F,O){return F&&_defineProperties(D.prototype,F),O&&_defineProperties(D,O),D}function _define_property(D,F,O){return F in D?Object.defineProperty(D,F,{value:O,enumerable:!0,configurable:!0,writable:!0}):D[F]=O,D}function _get(D,F,O){return(_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(D,F,O){var U=_super_prop_base(D,F);if(U){var G=Object.getOwnPropertyDescriptor(U,F);return G.get?G.get.call(O||D):G.value}})(D,F,O||D)}function _get_prototype_of(D){return(_get_prototype_of=Object.setPrototypeOf?Object.getPrototypeOf:function(D){return D.__proto__||Object.getPrototypeOf(D)})(D)}function _inherits(D,F){if("function"!=typeof F&&null!==F)throw TypeError("Super expression must either be null or a function");D.prototype=Object.create(F&&F.prototype,{constructor:{value:D,writable:!0,configurable:!0}}),F&&_set_prototype_of(D,F)}function _object_spread(D){for(var F=1;F<arguments.length;F++){var O=null!=arguments[F]?arguments[F]:{},U=Object.keys(O);"function"==typeof Object.getOwnPropertySymbols&&(U=U.concat(Object.getOwnPropertySymbols(O).filter(function(D){return Object.getOwnPropertyDescriptor(O,D).enumerable}))),U.forEach(function(F){_define_property(D,F,O[F])})}return D}function ownKeys(D,F){var O=Object.keys(D);if(Object.getOwnPropertySymbols){var U=Object.getOwnPropertySymbols(D);F&&(U=U.filter(function(F){return Object.getOwnPropertyDescriptor(D,F).enumerable})),O.push.apply(O,U)}return O}function _object_spread_props(D,F){return F=null!=F?F:{},Object.getOwnPropertyDescriptors?Object.defineProperties(D,Object.getOwnPropertyDescriptors(F)):ownKeys(Object(F)).forEach(function(O){Object.defineProperty(D,O,Object.getOwnPropertyDescriptor(F,O))}),D}function _possible_constructor_return(D,F){return F&&("object"===_type_of(F)||"function"==typeof F)?F:_assert_this_initialized(D)}function _set_prototype_of(D,F){return(_set_prototype_of=Object.setPrototypeOf||function(D,F){return D.__proto__=F,D})(D,F)}function _super_prop_base(D,F){for(;!Object.prototype.hasOwnProperty.call(D,F)&&null!==(D=_get_prototype_of(D)););return D}function _tagged_template_literal(D,F){return F||(F=D.slice(0)),Object.freeze(Object.defineProperties(D,{raw:{value:Object.freeze(F)}}))}function _type_of(D){return D&&"undefined"!=typeof Symbol&&D.constructor===Symbol?"symbol":typeof D}function _is_native_reflect_construct(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(D){return!1}}function _create_super(D){var F=_is_native_reflect_construct();return function(){var O,U=_get_prototype_of(D);if(F){var G=_get_prototype_of(this).constructor;O=Reflect.construct(U,arguments,G)}else O=U.apply(this,arguments);return _possible_constructor_return(this,O)}}function _ts_decorate(D,F,O,U){for(var G,W=arguments.length,Y=W<3?F:null===U?U=Object.getOwnPropertyDescriptor(F,O):U,X=D.length-1;X>=0;X--)(G=D[X])&&(Y=(W<3?G(Y):W>3?G(F,O,Y):G(F,O))||Y);return W>3&&Y&&Object.defineProperty(F,O,Y),Y}function _ts_generator(D,F){var O,U,G,W,Y={label:0,sent:function(){if(1&G[0])throw G[1];return G[1]},trys:[],ops:[]};return W={next:X(0),throw:X(1),return:X(2)},"function"==typeof Symbol&&(W[Symbol.iterator]=function(){return this}),W;function X(D){return function(F){return J([D,F])}}function J(W){if(O)throw TypeError("Generator is already executing.");for(;Y;)try{if(O=1,U&&(G=2&W[0]?U.return:W[0]?U.throw||((G=U.return)&&G.call(U),0):U.next)&&!(G=G.call(U,W[1])).done)return G;switch(U=0,G&&(W=[2&W[0],G.value]),W[0]){case 0:case 1:G=W;break;case 4:return Y.label++,{value:W[1],done:!1};case 5:Y.label++,U=W[1],W=[0];continue;case 7:W=Y.ops.pop(),Y.trys.pop();continue;default:if(!(G=(G=Y.trys).length>0&&G[G.length-1])&&(6===W[0]||2===W[0])){Y=0;continue}if(3===W[0]&&(!G||W[1]>G[0]&&W[1]<G[3])){Y.label=W[1];break}if(6===W[0]&&Y.label<G[1]){Y.label=G[1],G=W;break}if(G&&Y.label<G[2]){Y.label=G[2],Y.ops.push(W);break}G[2]&&Y.ops.pop(),Y.trys.pop();continue}W=F.call(D,Y)}catch(D){W=[6,D],U=0}finally{O=G=0}if(5&W[0])throw W[1];return{value:W[0]?W[1]:void 0,done:!0}}}function _ts_metadata(D,F){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(D,F)}function _templateObject(){var D=_tagged_template_literal(['<svg width="24" height="24" aria-hidden="true" focusable="false"><path d="M14.016 5.016H18v13.969h-3.984V5.016zM6 18.984V5.015h3.984v13.969H6z"/></svg>']);return _templateObject=function(){return D},D}function _templateObject1(){var D=_tagged_template_literal(['<svg width="24" height="24" aria-hidden="true" focusable="false"><path d="M8.016 5.016L18.985 12 8.016 18.984V5.015z"/></svg>']);return _templateObject1=function(){return D},D}function _templateObject2(){var D=_tagged_template_literal(['<div class="lottie-controls toolbar" aria-label="Lottie Animation Controls" class="toolbar"><button @click="','" class="','" style="align-items:center" tabindex="0" aria-label="Toggle Play/Pause">','</button> <button @click="','" class="','" style="align-items:center" tabindex="0" aria-label="Stop"><svg width="24" height="24" aria-hidden="true" focusable="false"><path d="M6 6h12v12H6V6z"/></svg></button> <input class="seeker" type="range" min="0" max="100" value="','" @input="','" @mousedown="','" @mouseup="','" aria-valuemin="0" aria-valuemax="100" role="slider" aria-valuenow="','" tabindex="0" aria-label="Slider for search"> <button @click="','" class="','" style="align-items:center" tabindex="0" aria-label="Toggle looping"><svg width="24" height="24" aria-hidden="true" focusable="false"><path d="M17.016 17.016v-4.031h1.969v6h-12v3l-3.984-3.984 3.984-3.984v3h10.031zM6.984 6.984v4.031H5.015v-6h12v-3l3.984 3.984-3.984 3.984v-3H6.984z"/></svg></button> <button @click="','" class="','" aria-label="Toggle boomerang" style="align-items:center" tabindex="0"><svg width="24" height="24" aria-hidden="true" focusable="false"><path d="m11.8 13.2-.3.3c-.5.5-1.1 1.1-1.7 1.5-.5.4-1 .6-1.5.8-.5.2-1.1.3-1.6.3s-1-.1-1.5-.3c-.6-.2-1-.5-1.4-1-.5-.6-.8-1.2-.9-1.9-.2-.9-.1-1.8.3-2.6.3-.7.8-1.2 1.3-1.6.3-.2.6-.4 1-.5.2-.2.5-.2.8-.3.3 0 .7-.1 1 0 .3 0 .6.1.9.2.9.3 1.7.9 2.4 1.5.4.4.8.7 1.1 1.1l.1.1.4-.4c.6-.6 1.2-1.2 1.9-1.6.5-.3 1-.6 1.5-.7.4-.1.7-.2 1-.2h.9c1 .1 1.9.5 2.6 1.4.4.5.7 1.1.8 1.8.2.9.1 1.7-.2 2.5-.4.9-1 1.5-1.8 2-.4.2-.7.4-1.1.4-.4.1-.8.1-1.2.1-.5 0-.9-.1-1.3-.3-.8-.3-1.5-.9-2.1-1.5-.4-.4-.8-.7-1.1-1.1h-.3zm-1.1-1.1c-.1-.1-.1-.1 0 0-.3-.3-.6-.6-.8-.9-.5-.5-1-.9-1.6-1.2-.4-.3-.8-.4-1.3-.4-.4 0-.8 0-1.1.2-.5.2-.9.6-1.1 1-.2.3-.3.7-.3 1.1 0 .3 0 .6.1.9.1.5.4.9.8 1.2.5.4 1.1.5 1.7.5.5 0 1-.2 1.5-.5.6-.4 1.1-.8 1.6-1.3.1-.3.3-.5.5-.6zM13 12c.5.5 1 1 1.5 1.4.5.5 1.1.9 1.9 1 .4.1.8 0 1.2-.1.3-.1.6-.3.9-.5.4-.4.7-.9.8-1.4.1-.5 0-.9-.1-1.4-.3-.8-.8-1.2-1.7-1.4-.4-.1-.8-.1-1.2 0-.5.1-1 .4-1.4.7-.5.4-1 .8-1.4 1.2-.2.2-.4.3-.5.5z"/></svg></button></div>']);return _templateObject2=function(){return D},D}function _templateObject3(){var D=_tagged_template_literal(['<div class="error">⚠️</div>']);return _templateObject3=function(){return D},D}function _templateObject4(){var D=_tagged_template_literal(['<div class="','" lang="','" role="img" aria-label="','"><div class="','" style="background:','">',"</div>","</div>"]);return _templateObject4=function(){return D},D}exports.DotLottiePlayer=function(D){_inherits(O,D);var F=_create_super(O);function O(){var D;return _class_call_check(this,O),D=F.apply(this,arguments),D.background="transparent",D.controls=!1,D.currentState=exports.PlayerState.Loading,D.direction=1,D.hover=!1,D.intermission=0,D.loop=!1,D.mode=exports.PlayMode.Normal,D.objectfit="contain",D.renderer="svg",D.speed=1,D.subframe=!1,D._lottie=null,D._counter=0,D}return _create_class(O,[{key:"load",value:function(D){var F=this;return _async_to_generator(function(){var O,U,G,W,Y,X;return _ts_generator(this,function(J){switch(J.label){case 0:if(!F.shadowRoot)return[2];switch(U=null!==(O=F.preserveAspectRatio)&&void 0!==O?O:F.objectfit&&aspectRatio(F.objectfit),G={container:F.container,loop:!!F.loop,autoplay:!!F.autoplay,renderer:F.renderer,initialSegment:F.segment,rendererSettings:{imagePreserveAspectRatio:U}},F.renderer){case"svg":G.rendererSettings=_object_spread_props(_object_spread({},G.rendererSettings),{hideOnTransparent:!0,preserveAspectRatio:U,progressiveLoad:!0});break;case"canvas":G.rendererSettings=_object_spread_props(_object_spread({},G.rendererSettings),{clearCanvas:!0,preserveAspectRatio:U,progressiveLoad:!0});break;case"html":G.rendererSettings=_object_spread_props(_object_spread({},G.rendererSettings),{hideOnTransparent:!0})}J.label=1;case 1:if(J.trys.push([1,5,,6]),"string"!=typeof D&&"object"!=typeof D)throw Error("No Lottie animation to load, or the file is corrupted.");if("string"!=typeof D)return[3,3];return[4,fetchPath(D)];case 2:return Y=J.sent(),[3,4];case 3:Y=D,J.label=4;case 4:if(W=Y,!F.isLottie(W))throw Error("dotLottie: Load method failed. Object is not a valid Lottie.");return F._lottie&&F._lottie.destroy(),F._lottie=Lottie.loadAnimation(_object_spread_props(_object_spread({},G),{animationData:W})),[3,6];case 5:return console.error(J.sent()),F.currentState=exports.PlayerState.Error,F.dispatchEvent(new CustomEvent(exports.PlayerEvents.Error)),[2];case 6:return F._lottie&&(F._lottie.addEventListener("enterFrame",function(){var D=F._lottie,O=D.currentFrame,U=D.totalFrames;F.seeker=O/U*100,F.dispatchEvent(new CustomEvent(exports.PlayerEvents.Frame,{detail:{frame:O,seeker:F.seeker}}))}),F._lottie.addEventListener("complete",function(){F.currentState=exports.PlayerState.Completed,F.dispatchEvent(new CustomEvent(exports.PlayerEvents.Complete))}),X=function(){var D,O,U,G=F._lottie,W=G.firstFrame,Y=G.totalFrames,X=G.playDirection;if(F.count&&(F.mode===exports.PlayMode.Bounce?F._counter+=1:F._counter+=.5,F._counter>=F.count)){F.setLooping(!1),F.currentState=exports.PlayerState.Completed,F.dispatchEvent(new CustomEvent(exports.PlayerEvents.Complete));return}return(F.dispatchEvent(new CustomEvent(exports.PlayerEvents.Loop)),F.mode===exports.PlayMode.Bounce)?(null===(O=F._lottie)||void 0===O||O.goToAndStop(-1===X?W:.99*Y,!0),null===(U=F._lottie)||void 0===U||U.setDirection(-1*X),setTimeout(function(){var D;null===(D=F._lottie)||void 0===D||D.play()},F.intermission)):(null===(D=F._lottie)||void 0===D||D.goToAndStop(-1===X?.99*Y:W,!0),setTimeout(function(){var D;null===(D=F._lottie)||void 0===D||D.play()},F.intermission))},F._lottie.addEventListener("loopComplete",X),F._lottie.addEventListener("DOMLoaded",function(){F.dispatchEvent(new CustomEvent(exports.PlayerEvents.Ready))}),F._lottie.addEventListener("data_ready",function(){F.dispatchEvent(new CustomEvent(exports.PlayerEvents.Load))}),F._lottie.addEventListener("data_failed",function(){F.currentState=exports.PlayerState.Error,F.dispatchEvent(new CustomEvent(exports.PlayerEvents.Error))}),F.container&&(F.container.addEventListener("mouseenter",function(){F.hover&&F.currentState!==exports.PlayerState.Playing&&F.play()}),F.container.addEventListener("mouseleave",function(){F.hover&&F.currentState===exports.PlayerState.Playing&&F.stop()})),F.setSpeed(F.speed),F.setDirection(F.direction),F.setSubframe(!!F.subframe),F.autoplay&&(-1===F.direction&&F.seek("99%"),F.play())),[2]}})})()}},{key:"_onVisibilityChange",value:function(){document.hidden&&this.currentState===exports.PlayerState.Playing?this.freeze():this.currentState===exports.PlayerState.Frozen&&this.play()}},{key:"_handleSeekChange",value:function(D){if(!(!D.target||!this._lottie||isNaN(Number(D.target.value)))){var F=Number(D.target.value)/100*this._lottie.totalFrames;this.seek(F)}}},{key:"isLottie",value:function(D){return["v","ip","op","layers","fr","w","h"].every(function(F){return Object.prototype.hasOwnProperty.call(D,F)})}},{key:"getLottie",value:function(){return this._lottie}},{key:"play",value:function(){this._lottie&&(this.currentState=exports.PlayerState.Playing,this._lottie.play(),this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Play)))}},{key:"pause",value:function(){this._lottie&&(this.currentState=exports.PlayerState.Paused,this._lottie.pause(),this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Pause)))}},{key:"stop",value:function(){this._lottie&&(this.currentState=exports.PlayerState.Stopped,this._counter=0,this._lottie.stop(),this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Stop)))}},{key:"destroy",value:function(){this._lottie&&(this.currentState=exports.PlayerState.Destroyed,this._lottie.destroy(),this._lottie=null,this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Destroyed)),this.remove())}},{key:"seek",value:function(D){if(this._lottie){var F=D.toString().match(/^([0-9]+)(%?)$/);if(F){var O="%"===F[2]?this._lottie.totalFrames*Number(F[1])/100:F[1];this.seeker=Number(O),this.currentState===exports.PlayerState.Playing?this._lottie.goToAndPlay(O,!0):(this._lottie.goToAndStop(O,!0),this._lottie.pause())}}}},{key:"snapshot",value:function(){var D=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.shadowRoot){var F=this.shadowRoot.querySelector(".animation svg"),O=F instanceof Node?new XMLSerializer().serializeToString(F):null;if(O){if(D){var U=document.createElement("a");U.href="data:image/svg+xmlcharset=utf-8,"+encodeURIComponent(O),U.download="download_"+this.seeker+".svg",document.body.appendChild(U),U.click(),document.body.removeChild(U)}return O}}}},{key:"setSubframe",value:function(D){this._lottie&&(this.subframe=D,this._lottie.setSubframe(D))}},{key:"freeze",value:function(){this._lottie&&(this.currentState=exports.PlayerState.Frozen,this._lottie.pause(),this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Freeze)))}},{key:"reload",value:function(){var D=this;return _async_to_generator(function(){return _ts_generator(this,function(F){switch(F.label){case 0:if(!D._lottie)return[2];if(D._lottie.destroy(),!D.src)return[3,2];return[4,D.load(D.src)];case 1:F.sent(),F.label=2;case 2:return[2]}})})()}},{key:"setSpeed",value:function(){var D=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this._lottie&&(this.speed=D,this._lottie.setSpeed(D))}},{key:"setDirection",value:function(D){this._lottie&&(this.direction=D,this._lottie.setDirection(D))}},{key:"setLooping",value:function(D){this._lottie&&(this.loop=D,this._lottie.setLoop(D))}},{key:"togglePlay",value:function(){if(this._lottie){var D=this._lottie,F=D.currentFrame,O=D.playDirection,U=D.totalFrames;return this.currentState===exports.PlayerState.Playing?this.pause():this.currentState===exports.PlayerState.Completed?(this.currentState=exports.PlayerState.Playing,this.mode===exports.PlayMode.Bounce)?(this.setDirection(-1*O),this._lottie.goToAndPlay(F,!0)):-1===O?this._lottie.goToAndPlay(U,!0):this._lottie.goToAndPlay(0,!0):this.play()}}},{key:"toggleLooping",value:function(){this.setLooping(!this.loop)}},{key:"toggleBoomerang",value:function(){this.mode===exports.PlayMode.Normal?this.mode=exports.PlayMode.Bounce:this.mode=exports.PlayMode.Normal}},{key:"connectedCallback",value:function(){_get(_get_prototype_of(O.prototype),"connectedCallback",this).call(this),void 0!==document.hidden&&document.addEventListener("visibilitychange",this._onVisibilityChange)}},{key:"firstUpdated",value:function(){var D=this;return _async_to_generator(function(){return _ts_generator(this,function(F){switch(F.label){case 0:if("IntersectionObserver"in window&&(D._io=new IntersectionObserver(function(F){F[0].isIntersecting?document.hidden||D.currentState!==exports.PlayerState.Frozen||D.play():D.currentState===exports.PlayerState.Playing&&D.freeze()}),D._io.observe(D.container)),!D.src)return[3,2];return[4,D.load(D.src)];case 1:F.sent(),F.label=2;case 2:return D.dispatchEvent(new CustomEvent(exports.PlayerEvents.Rendered)),[2]}})})()}},{key:"disconnectedCallback",value:function(){_get(_get_prototype_of(O.prototype),"disconnectedCallback",this).call(this),this._io&&(this._io.disconnect(),this._io=void 0),this._lottie&&this._lottie.destroy(),document.removeEventListener("visibilitychange",this._onVisibilityChange)}},{key:"renderControls",value:function(){var D,F,O=this,U=this.currentState===exports.PlayerState.Playing,G=this.currentState===exports.PlayerState.Paused,W=this.currentState===exports.PlayerState.Stopped;return x$1(_templateObject2(),this.togglePlay,U||G?"active":"",U?x$1(_templateObject()):x$1(_templateObject1()),this.stop,W?"active":"",null!==(D=this.seeker)&&void 0!==D?D:0,this._handleSeekChange,function(){O._prevState=O.currentState,O.freeze()},function(){O._prevState===exports.PlayerState.Playing&&O.play()},null!==(F=this.seeker)&&void 0!==F?F:0,this.toggleLooping,this.loop?"active":"",this.toggleBoomerang,this.mode===exports.PlayMode.Bounce?"active":"")}},{key:"render",value:function(){var D,F,O,U=this.controls?"main controls":"main",G=this.controls?"animation controls":"animation";return x$1(_templateObject4(),"animation-container ".concat(U),this.description?null===(F=document)||void 0===F?void 0:null===(D=F.documentElement)||void 0===D?void 0:D.lang:"en",null!==(O=this.description)&&void 0!==O?O:"Lottie animation",G,this.background,this.currentState===exports.PlayerState.Error?x$1(_templateObject3()):A,this.controls?this.renderControls():A)}}],[{key:"styles",get:function(){return styles}}]),O}(s),_ts_decorate([n$1({type:Boolean,reflect:!0}),_ts_metadata("design:type","undefined"==typeof Autoplay?Object:Autoplay)],exports.DotLottiePlayer.prototype,"autoplay",void 0),_ts_decorate([n$1({type:String}),_ts_metadata("design:type",String)],exports.DotLottiePlayer.prototype,"background",void 0),_ts_decorate([n$1({type:Boolean,reflect:!0}),_ts_metadata("design:type","undefined"==typeof Controls?Object:Controls)],exports.DotLottiePlayer.prototype,"controls",void 0),_ts_decorate([n$1({type:Number}),_ts_metadata("design:type",Number)],exports.DotLottiePlayer.prototype,"count",void 0),_ts_decorate([n$1({type:String}),_ts_metadata("design:type",void 0===exports.PlayerState?Object:exports.PlayerState)],exports.DotLottiePlayer.prototype,"currentState",void 0),_ts_decorate([n$1({type:String}),_ts_metadata("design:type",String)],exports.DotLottiePlayer.prototype,"description",void 0),_ts_decorate([n$1({type:Number}),_ts_metadata("design:type","undefined"==typeof AnimationDirection?Object:AnimationDirection)],exports.DotLottiePlayer.prototype,"direction",void 0),_ts_decorate([n$1({type:Boolean})],exports.DotLottiePlayer.prototype,"hover",void 0),_ts_decorate([n$1({type:Number})],exports.DotLottiePlayer.prototype,"intermission",void 0),_ts_decorate([n$1({type:Boolean,reflect:!0}),_ts_metadata("design:type","undefined"==typeof Loop?Object:Loop)],exports.DotLottiePlayer.prototype,"loop",void 0),_ts_decorate([n$1({type:String}),_ts_metadata("design:type",void 0===exports.PlayMode?Object:exports.PlayMode)],exports.DotLottiePlayer.prototype,"mode",void 0),_ts_decorate([n$1({type:String}),_ts_metadata("design:type","undefined"==typeof ObjectFit?Object:ObjectFit)],exports.DotLottiePlayer.prototype,"objectfit",void 0),_ts_decorate([n$1({type:String}),_ts_metadata("design:type","undefined"==typeof PreserveAspectRatio?Object:PreserveAspectRatio)],exports.DotLottiePlayer.prototype,"preserveAspectRatio",void 0),_ts_decorate([n$1({type:String}),_ts_metadata("design:type","undefined"==typeof RendererType?Object:RendererType)],exports.DotLottiePlayer.prototype,"renderer",void 0),_ts_decorate([n$1({type:Array}),_ts_metadata("design:type",Object)],exports.DotLottiePlayer.prototype,"segment",void 0),_ts_decorate([n$1({type:Number}),_ts_metadata("design:type",Number)],exports.DotLottiePlayer.prototype,"seeker",void 0),_ts_decorate([n$1({type:Number}),_ts_metadata("design:type",Number)],exports.DotLottiePlayer.prototype,"speed",void 0),_ts_decorate([n$1({type:String}),_ts_metadata("design:type",String)],exports.DotLottiePlayer.prototype,"src",void 0),_ts_decorate([n$1({type:Boolean}),_ts_metadata("design:type","undefined"==typeof Subframe?Object:Subframe)],exports.DotLottiePlayer.prototype,"subframe",void 0),_ts_decorate([i$2(".animation"),_ts_metadata("design:type","undefined"==typeof HTMLElement?Object:HTMLElement)],exports.DotLottiePlayer.prototype,"container",void 0),exports.DotLottiePlayer=_ts_decorate([e$1("dotlottie-player")],exports.DotLottiePlayer)}(this["@johanaarstein/dotlottie-player"]=this["@johanaarstein/dotlottie-player"]||{});

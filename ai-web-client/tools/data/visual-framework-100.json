[{"name": "Disrupt the flow", "id": "2023-001", "description": "A dam, an obstruction, a boulder in the middle of the road: These are all things that disrupt the normal flow of things.  Whether that’s a good disruption or a bad disruption depends partly on your point of view and partly on the result.  Sometimes disruption can be helpful. For example, a dam may block the natural flow of a river, but it also creates a lake and can provide a source of energy.  Sometimes disruption can be a problem. For example, a traffic accident that shuts down a main highway, causing backups and delays.  Are there flows and routines in your life that no longer serve a useful purpose, that need to be disrupted? Is something blocking your progress? Is there a way around the block? Can the obstacle be removed? What new alternatives and possibilities does the block create?", "see also": ["Bottleneck", "Flowchart", "Pipes and tanks"], "tags": ["Abstract", "Process", "Space", "Spacetime", "Streams and flows", "Time", "What can we do?", "What is the problem?", "What’s going on?"], "url": "https://visualframeworks.com/disrupt-the-flow/", "summary": "Use this representation when examining disruptive elements within a system, evaluating their negative or positive impacts, seeking alternatives or solutions to blockages, or wishing to propose a constructive interruption within current processes or routines."}, {"name": "Minefield", "id": "2023-002", "description": "In wartime, minefields are sown to make terrain difficult for the enemy to navigate. If you’re caught in a minefield, you no longer know where to take your next step. At the least, a minefield will slow you down. It will expose you, making you vulnerable to enemy fire. And at the worst, it will get you blown up. Even long after the war is over, minefields may remain, creating hidden hazards for anyone who might happen by.  Some situations are like minefields, fraught with hidden dangers. In such cases it’s important to proceed carefully, because things could easily go wrong, very fast.   Three things will increase your chances of surviving a minefield. First, pay close attention to your surroundings; remain vigilant and alert, highly focused on the present moment. Second, do your best to detect the mines. Close observation can help you see them before they go off. Third, keep calm. Move slowly and deliberately until you have reached a safe zone. Be sure to mark mines as you find them and flag the area as a warning for others, or for yourself if you happen to cross the same path in the future.  Are you caught in a minefield? How can you bring yourself into the moment, to be as attentive as possible? Observe the situation closely. Can you learn how to detect the mines before they explode? What can help you keep your cool? How can you clear a path, and create markers to make the space safer in the future, for yourself and others?", "see also": ["Iceberg", "Subduction", "Layer cake", "Conflict"], "tags": ["Heart", "Landscape", "<PERSON><PERSON><PERSON>", "Space", "Structure", "Terrain", "What are the opportunities?", "What are the options?", "What are the risks?", "What can we do?", "What is it?", "What is the problem?", "What’s going on?"], "url": "https://visualframeworks.com/minefield/", "summary": "Use the 'Minefield' representation when navigating through complex and potentially dangerous situations with hidden risks, where close observation, calmness, and strategic planning are critical for safely marking hazards and creating a secure path forward."}, {"name": "Lost", "id": "2023-003", "description": "''In the middle of our walk of life, I found myself within a forest dark, for the straightforward pathway had been lost''.  <PERSON>, The Divine Comedy  Sometimes in life, you may find yourself lost, alone, in a dark wood. You have lost your way. You might feel disoriented, confused. You don’t know which way to turn.   If you find yourself lost, the first thing to do is pause. Stop moving. Take a few deep breaths. Look around. Look back at the way you have come. Do you see any clues that might help you find your way?   Being lost can generate anxiety. You don’t know where you are or where you are going. But you can also see this as an opportunity to reconnect with yourself, an adventure, a problem to solve. If you’re with a group, you can find your way in the dark by walking forward together, feeling your way, and calling out what you find as you go.  Are you feeling lost? How can you turn that feeling into an opportunity? Can you feel your way forward? Can you call out to others? Could this be a good time to reflect, find yourself, forge a new path, create a new life?", "see also": ["Doom loop", "Hole", "Fog", "<PERSON><PERSON>", "Pain map", "Tipping point"], "tags": ["Heart", "lost", "<PERSON><PERSON><PERSON>", "What are the options?", "What’s going on?", "What’s next?", "Where are we going?", "Where are we?"], "url": "https://visualframeworks.com/lost/", "summary": "This representation should be used when needing a metaphorical tool for self-orientation, introspection, problem-solving and negotiation of complex situations or emotions in moments of confusion or feeling lost."}, {"name": "Journey of a thousand miles", "id": "2023-004", "description": "''The journey of a thousand li begins with a single step.''  Lao Tsu  Your goal may seem far away and unattainable. You may think it’s too far away. But these kinds of thoughts won’t get you there.   True change will only begin when you take the first step. You may not be able to see all the steps between you and your vision. They may be covered in mists and fog.  A long journey will be full of unknowns. There is no way you will know everything you need to know in advance. If you’re not careful you can spend your life preparing and planning for a journey that you will never take.  Better to take the first steps on the path. Have faith in yourself! When problems come up you will deal with them. If you need help, you will find it along the way. The adventure begins when you start doing things.  What is your journey of a thousand miles? What is your first step? If you’re already on your journey, what is the next best step you can take?  This framework was inspired by <PERSON>.", "see also": ["Journey", "Mountain climbing", "Deep dive", "Milestones", "Stepping stones", "Path", "Highs and lows"], "tags": ["Heart", "How to decide?", "How to think about this?", "How will we get there?", "Journey", "<PERSON><PERSON><PERSON>", "What are the options?", "What to do?", "Where are we going?", "Where are we?"], "url": "https://visualframeworks.com/journey-of-a-thousand-miles/", "summary": "Use this representation when you need to encourage bold first steps towards a sizeable goal, inspire faith in the process of journeying through unknown territory, or reignite momentum during a daunting long-term project."}, {"name": "Target", "id": "2023-005", "description": "A target is a goal. Setting goals helps you think about what you are aiming for, and why it’s important. Imagine your goals are displayed on a dart board. If you are just learning to play darts, you might stand closer to the target. An expert might stand further away to make it more challenging. A goal that challenges you is called a stretch goal. When you first set a stretch goal, it may not be clear how you’re going to achieve it. That’s the power of a big ambitious goal: you commit to it before you know how you will make it happen. Stretch goals are important because they help you grow. They motivate you to think differently, because you can’t get there by doing things the same way you’ve always done them. Set your target too close and it’s too easy. You’re not challenging yourself. Set it too far away and you will have no hope of reaching it. A goal that you feel is impossible will sap your energy and deprive you of motivation. A stretch goal is just right: Far enough that you’re not sure you can do it, and close enough to feel achievable. Here’s a good litmus test: If you meet every goal you set, you probably aren’t setting them high enough. A good stretch goal should be a little bit scary.  ''The best moments in our lives are not the passive, receptive, relaxing times . . . The best moments usually occur if a person’s body or mind is stretched to its limits in a voluntary effort to accomplish something difficult and worthwhile'' by <PERSON><PERSON><PERSON>. The right goal will generate a state of flow, striking that perfect balance that is both challenging and achievable, so that it requires your full attention and engagement. Working toward a big ambitious goal is like throwing darts at a board. It’s unlikely that everything you try will work on the first go. When you first start throwing darts, you might have trouble hitting anything. You don’t know where your actions will land or whether they will stick. You might not even hit the board. But with practice you improve. You are learning as you go, building new skills, a little bit at a time. You might have to try a lot of things before you find something that works. Do you need to set a goal for yourself or your team? What is the right distance to create between you and your target? Can you set the challenge at the right level of difficulty to fully engage your energy, focus, and attention? If you’re facing a difficult challenge, what are some experiments you can try, or actions you can take, that might move you closer to your goals?", "see also": ["Altitude", "Pushing the envelope", "Bridge", "Leap of faith", "Stepping stones", "Countdown", "Crossroads", "Gauntlet", "Growth", "Mountain Climbing", "Milestones", "Tightrope"], "tags": ["Heart", "How can we get there?", "<PERSON><PERSON><PERSON>", "What are we aiming for?", "What can we do?", "What is the goal?"], "url": "https://visualframeworks.com/target/", "summary": "Use this representation when setting goals, especially challenging ones, to aid in understanding the importance of aiming for a target, finding the right balance of difficulty, and continuously striving for growth through trial and error."}, {"name": "Alignment", "id": "2023-006", "description": "Soldiers marching in rank and file. A symphony, playing to the same beat, from the same sheet of music. <PERSON><PERSON> flying south for the winter. An organized pantry with everything in its place and aligned on the shelves. When things are aligned we find them easier to see, comprehend, or count, more aesthetically pleasing, more harmonious. Things that are aligned work better together.  When the wheels of your car are out of alignment, it is harder to steer and brake. The car consumes more fuel. Tires wear out faster. When the wheels are aligned, the car is easier to handle and works more smoothly and efficiently. Likewise, when a group of people are aligned, it is a sign that they are in agreement about their goals and methods. They will work together more smoothly and get more accomplished, more effectively. To be aligned is to be working together, in harmony. Each person or element gives up some freedom and autonomy to synchronize with the larger whole. Alignment is an agreement to move in the same direction. It creates flow. This is true even within your own mind. When your thoughts are aligned, your theories are in agreement, without conflict, you feel at peace. When one belief contradicts another, it can be difficult. The mind seeks harmony and alignment, even though such contradictions can be useful. ''The test of a first-rate intelligence is the ability to hold two opposing ideas in mind at the same time and still retain the ability to function. One should, for example, be able to see that things are hopeless yet be determined to make them otherwise.'' <PERSON><PERSON>. Alignment is not always a good thing. Too much emphasis on alignment can stifle creativity and diversity. Just because people are working together smoothly and efficiently does not mean they are doing the right things. Overfocusing on alignment can create a culture where people are afraid to question things or speak out. A group that is synchronized to a work rhythm can also find it difficult to change when the situation demands it. It’s hard to let go of a smoothly functioning routine.  When you look around you, are you in alignment with the people and groups you are a part of?  Are you finding yourself in a situation that feels out of alignment? Is that a good or a bad thing? Does the group need more alignment, or less?  Does the group need to realign to deal with a changing reality?  Do you need to find another group where you feel more aligned?", "see also": ["Disrupt the flow", "DNA", "Braiding", "Virtuous cycle", "Gears", "Momentum", "Snowball effect"], "tags": ["Abstract", "Head", "How can we connect?", "How can we grow?", "How did we get here? Where are we going?", "How does it work?", "How to think about this?", "What can we do?"], "url": "https://visualframeworks.com/alignment/", "summary": "Use this representation of 'Alignment' when needing to communicate the importance and effects of coordination, harmony, and agreement within a group or system, including drawbacks such as stifling creativity, and triggering a discourse on the need for alignment or realignment in different contexts."}, {"name": "Shadow", "id": "2023-007", "shadow": "A shadow is intangible. It is not made of anything, it is an absence, a negative effect, something that is not there, and yet shadows are real. They are nothing, and yet they are something. A shadow is a negative fact. Plato told a story about a cave (actually it’s a story about <PERSON><PERSON> telling a story about a cave).  A group of people are chained to a wall for their entire lives, facing a blank wall, with a fire behind them. All they can see is the shadows projected on the wall. They give names to the shadows and use words to describe what’s going on, but they are still only seeing shadows, not reality. This is a metaphor for the limitations of our physical senses. <PERSON>’s point is that philosophers can escape the cave by reasoning, using tools like mathematics and logic, to escape the cave and perceive reality more clearly.  In Jungian psychology, the shadow is the unconscious part of your psyche that does not correspond to your ideal self: the things you don’t like about yourself, or would prefer not to admit. The shadow is your emotional blind spot, the parts of yourself you are embarrassed about, ashamed of, or even despise. Your shadow self is the self you tend to hide or avoid. Yet you can’t escape your shadow. It’s always with you. Ignoring or denying your shadow can lead to problems, most especially a distorted view of yourself, which leads to a distorted view of everything else, which in turn leads to bad decisions and negative outcomes. This self-denial manifests in things like low self-esteem, lying to yourself and to others, anxiety, self-sabotage, an inflated ego, overreacting to criticism and taking things too personally. So if you can’t ignore it and you can’t get rid of it, what can you do about your shadow?  You can recognize that it’s a natural part of you. Accept your shadow. Make peace with it. You can learn to listen to your unconscious and accept your shadow self. Becoming aware of what’s hidden and nurture the childish, immature, or traumatized parts of yourself, so they can heal and grow. Your shadow is a projection. Light hitting you from one direction will project your shadow in the opposite direction.  Similarly, when what you see as the negative aspects of your personality are exposed to the light, you will tend to project them onto others. You lash out at others for things you don’t like about yourself. As Plato wrote, shadows may appear to be real, but they are projections on a cave wall. They should not be confused with reality.  When people say or do things that hurt you, lean in with curiosity.  Is it possible that it;s not about you? Could someone be projecting their own shadow onto you?  Do you know and accept your own shadow, or do you deny it? Is it possible you might be projecting your own shadow onto others?", "see also": ["Lost", "Black hole", "Hole", "Fog", "Conflict", "Deep dive", "Iceberg", "<PERSON><PERSON>", "Doom loop", "You"], "tags": ["Growth", "Heart", "How do we grow?", "How to think about this?", "<PERSON><PERSON><PERSON>", "What are the options?", "What can we do?", "What’s going on?", "What’s stopping us?"], "url": "https://visualframeworks.com/shadow/", "summary": "Use this representation when exploring concepts of self-reflection, personal growth, and understanding of the unconscious self, inspired by philosophical and psychological theories such as <PERSON>'s allegory of the cave and <PERSON>'s shadow psychology."}, {"name": "Leap of faith", "id": "2023-008", "description": "There are times in life where you cannot take the next step without leaving the past behind completely. For example, Imagine you are offered a new job, which is a major step up in your career, but will require you to move to a new city. You can’t keep your current job and take the new one at the same time. You need to choose. Staying where you are would be comfortable, because it’s the world of the known. You know the ins and outs of your job, you have your circle of friends, you have worked out your work and life routines, and everything is working well.  The new job is full of unknowns. Will you like your new job and your new boss? Will you be able to make friends in a new and unfamiliar city? The only way to answer these questions is to take a leap of faith. Faith in a fact can help create the fact. William <PERSON> naturally tend to prefer the safer option. It’s called loss avoidance, and cognitively speaking, it has a stronger pull than reward. But avoiding losses will only maintain the status quo. To make significant progress, you will often need to take a leap of faith. You can’t embark on an adventure without leaving the safety and comfort of home behind. Here is a rule of thumb that will help you keep your loss avoidance tendency in check: When you are torn between two alternatives and finding it hard to make a decision, the very difficulty of the choice is a clue. Your doubt about taking the plunge is due to that strong natural pull toward your comfort zone. When in doubt, go towards the fear. Are you struggling to make a difficult decision? Are you torn between a safer and a riskier option? Are you in doubt? Is it time for a leap of faith?", "see also": ["Gap", "Bridge", "Tightrope", "Stepping stones", "Journey", "Pros and cons", "Switch", "Tipping point", "Transformation"], "tags": ["Heart", "How can we choose?", "How can we sift this?", "How can we sort this?", "How to decide?", "How to think about this?", "<PERSON><PERSON><PERSON>", "What are the options?", "What can we do?", "What’s going on?"], "url": "https://visualframeworks.com/leap-of-faith/", "summary": "This representation or concept, 'Leap of Faith', is used when one is at a crossroad making difficult decisions, often involving significant life changes attached with unknowns and risks, and it encourages embracing fear and stepping out of the comfort zone to seize the potential of greater rewards."}, {"name": "Filters", "id": "2023-009", "description": "Filters remove junk, impurities, and unwanted material. A screen door or mosquito net keeps the bugs out. An air filter in your car keeps gunk out of the engine. A coffee filter keeps the grounds out of your coffee cup. Sales funnels filter out unqualified opportunities.  Filters are typically used to keep bad stuff out, but they can also be used to keep good stuff in. For example, you can use a sieve to wash your vegetables in the sink. The water passes through and the vegetables stay in the sieve. A person who says what they mean without thinking first is said to have no filter. The implication is that you should think before you speak, to filter out things that might be extraneous, unwanted, or offensive.  Your mind has a filter between perception and long-term memory that is called working memory. It is limited in capacity and can only hold 5 to 7 things at a time. New ideas must pass through this filter before they can be understood and remembered. Your conscious mind filters out most of what you sense to help you focus on what’s important and relevant.  Filters don’t always work perfectly. For example, sometimes important emails don’t make it past your spam filter. Too much information at once, unfiltered, can cause information anxiety and map shock. When you communicate, do you have trouble filtering what you say? Are you overloading people with too much information? Alternatively, are you overwhelmed by too much information coming at you everywhere, all at once? Can you create filters to separate the signal from the noise?", "see also": ["Bottleneck", "Disrupt the flow", "Flowchart", "Funnel", "Decision tree", "Lens", "Prism", "Juggling", "Fog", "Mess"], "tags": ["Abstract", "Heart", "How can we sift this?", "How can we sort this?", "<PERSON><PERSON><PERSON>", "Space", "theoretical", "Time", "What are the options?", "What can we do?"], "url": "https://visualframeworks.com/filters/", "summary": "Use this representation when needing to understand or explain the process of selecting, sorting, or refining elements in a system, context, or statement, where unwanted or irrelevant components are eliminated and desirable or important parts are preserved."}, {"name": "Campfire", "id": "2023-010", "description": "A campfire is a circle of warmth and light that keeps the darkness at bay.  When people gather around the flickering crackle of a warm fire they feel a sense of community and belonging, common ground. The hearth is the emotional center, the heart, of a home, and a campfire creates a feeling of home, even in the wilderness.  Telling stories around the campfire is one of the most ancient traditions we have. Stories entertain us, they educate us, they connect us, and they create a common culture. A campfire is also a beacon. When people are lost or alone in the dark, a campfire in the distance gives them hope and something to aim for.  Are you feeling lost and alone? Look around. Are there any beacons of light out there in the darkness?   How might you create a campfire, a magnet for people to gather around, share, and connect?", "see also": ["Solar system", "Braiding", "DNA", "The universe"], "tags": ["Heart", "How can we connect?", "How did we get here? Where are we going?", "How to think about this?", "<PERSON><PERSON><PERSON>", "What are the options?", "What can we do?", "Where are we going?", "Where are we?", "Who are we?"], "url": "https://visualframeworks.com/campfire/", "summary": "This abstract representation should be used when discussing themes of belonging, connection, guidance, and shared experiences, drawing on the metaphor of a campfire as a beacon of hope and a communal gathering spot for storytelling and shared wisdom."}, {"name": "2×2 matrix", "id": "CORE-001", "description": "Also known as the magic quadrant, the 2×2 framework supports prioritization and decision-making by plotting options along two axes. Each axis represents a decision criteria, for example, cost and effort.  Are you facing an important decision? Can you define your two most important criteria and plot your options in a 2×2 matrix?", "see also": ["Grid", "Coordinates"], "tags": ["Abstract", "Decisions", "Head", "How should we choose?", "How to decide?", "How to think about this?", "Priorities", "What are the options?", "What’s important?", "Why does it matter?"], "url": "https://visualframeworks.com/2x2-matrix/", "summary": "Use the 2×2 matrix representation when faced with an important decision and you can define your two most vital criteria, as it aids in plotting options for deciding priorities and making choices."}, {"name": "Airplane", "id": "CORE-002", "description": "An airplane is a vehicle, a machine that takes us on a journey from here to there. A trip on an airplane requires a flight plan, an origin, a course, and a destination. A pilot to navigate through or around storms and turbulent weather patterns. Passengers, their baggage, and a crew to serve them. Wings to stabilize the plane and keep it aloft. Engines to drive it forward, tail fins and rudders to keep you on course. Fuel to provide energy.  An airplane can be compared to business or a project. Are you on the runway, just taking off, mid-flight, hitting a patch of turbulence, about to land?  What is your destination? Your flight plan? What provides energy? What keeps you on course?", "see also": ["Journey", "Altitude", "Sailboat"], "tags": ["Heart", "How will we get there?", "Machine", "<PERSON><PERSON><PERSON>", "Space", "Spacetime", "Time", "Vehicle", "What can we do?", "What is the plan?", "Where are we going?", "Why does it matter?", "Why should I care?"], "url": "https://visualframeworks.com/airplane/", "summary": "This representation should be used when you need to visualize complex ideas or concepts related to planning, navigation, momentum, energy, and method of operation, especially when you want to illuminate them through the metaphor of an airplane journey."}, {"name": "Altitude", "id": "CORE-003", "description": "Altitude is the height of something in relation to ground level.  When you are on the ground, you can see your situation in great detail. If you can get up above the ground in an airplane, you can see the bigger picture; the shape and flow of the terrain and how things fit together. Going still higher you can gain a satellite view of the situation.  Determining the right altitude can be key to how you think about your situation.  What’s the right altitude for the things you need to consider right now? Ground level, airplane, or satellite view?", "see also": ["Airplane", "Layer cake", "Pyramid"], "tags": ["Heart", "How to do it?", "How to think about this?", "<PERSON><PERSON><PERSON>", "Space"], "url": "https://visualframeworks.com/altitude/", "summary": "This representation is used to explore different perspectives and levels of understanding, from detailed ground level to the bigger picture, in order to gain insight and make informed decisions."}, {"name": "Architecture", "id": "CORE-004", "description": "Architecture is the design of buildings, bridges and other physical structures. Architects must translate a concept or idea into a practical structure that meets a purpose. They create blueprints, sets of instructions that engineers and construction crews can use to turn those ideas into reality.  Can you approach your situation like an architect? What is your concept or big idea? How might you make a blueprint or plan to translate that idea into reality?", "see also": ["Balancing tradeoffs", "Recipe", "Org chart", "Pyramid", "Bridge"], "tags": ["Abstract", "Head", "Heart", "How does it work?", "<PERSON><PERSON><PERSON>", "Space", "Structure", "What can we do?"], "url": "https://visualframeworks.com/architecture/", "summary": "Use the 'Architecture' representation when you need to transition a concept or big idea into a practical, functional reality, requiring a strategic plan or blueprint similar to the work of an architect designing structures."}, {"name": "Balancing tradeoffs", "id": "CORE-005", "description": "A tradeoff is a compromise between two desirable things that are incompatible. For example, if you love country life and work in the city, you may have to put up with a long commute. If you want to connect with friends on Facebook, you have to give up some privacy and accept the advertising.  Do you have goals that seem incompatible? What tradeoffs do you need to balance? How will you do that?", "see also": ["Pros and cons", "Measurement", "Leverage"], "tags": ["Control", "Decisions", "Heart", "How to decide?", "How to think about this?", "<PERSON><PERSON><PERSON>", "Space", "What are the options?", "What can we do?"], "url": "https://visualframeworks.com/balancing-tradeoffs/", "summary": "This representation is useful when there is a need to understand and visualize the concept of balancing tradeoffs, particularly in situations where two desirable things are incompatible and compromises need to be made in order to achieve certain goals."}, {"name": "Bar chart", "id": "CORE-006", "description": "When trying to make a decision, you can determine the value of something by comparing it to other things. When these things are measurable, you can compare them more easily. A bar chart is akin to stacking coins into piles in order to count them. The ''stacks'' make it easier to compare information across different groups or categories. Bar charts are good for comparing quantities.  How will you decide? What are the most important criteria? What do you need to quantify or measure? Compared to what?", "see also": ["Coordinates", "Line chart", "Grid"], "tags": ["Abstract", "Categories", "Decisions", "Head", "How to decide?", "How to think about this?", "Time", "What matters?"], "url": "https://visualframeworks.com/bar-chart/", "summary": "A bar chart, as an idea representation, is best used when needing to easily compare and quantify measurable parameters across varying categories or groups, aiding in decision-making processes."}, {"name": "Black hole", "id": "CORE-007", "description": "A black hole has a gravitational pull so strong that nothing, not even light, can escape its pull, once it has passed its event horizon. Since a black hole cannot be seen, it appears to outside observers as a void, an area of nothingness, although in fact it is the opposite — an almost infinite mass, compressed down to a tiny point called a singularity.  Once you have passed the event horizon of a black hole it’s too late. The only way to escape the gravity of a black hole is to pull away before it’s too late.  Do you feel a pull toward an inescapable destructive force? How can you pull away before it sucks you in and destroys you?", "see also": ["Universe", "Funnel", "Doom loop", "Solar system", "Hole"], "tags": ["Heart", "<PERSON><PERSON><PERSON>", "Space", "Structure", "What are the options?", "What can we do?", "What’s going on?"], "url": "https://visualframeworks.com/black-hole/", "summary": "This abstract representation can be used to understand or communicate concepts of inescapable destructive forces, choices, and consequences via the metaphor of a black hole, with an emphasis on gravitating towards danger and making timely decisions to avoid disaster."}, {"name": "Braiding", "id": "CORE-009", "description": "A braid is a complex structure created by interlacing three or more strands of flexible material into one. Braids are used for making rope, wire textiles, and for decoration. The opposite of braiding is unraveling.  A braided rope is stronger than any strand would be on its own, but remains flexible. Braiding is a kind of weaving, combining and interweaving elements to make a stronger whole.  If you’re not careful, a braid can also become a tangled knot!  Is there a way to weave together strands in your life or work, to make a stronger, flexible whole? How can you weave a smooth clean braid without creating a tangled knot? Are you intertwined in a situation you need to unravel?", "see also": ["Bottleneck", "Flowchart", "Decision tree", "Crossroads", "Switch", "DNA"], "tags": ["Heart", "How does it work?", "How to think about this?", "<PERSON><PERSON><PERSON>", "What are the options?", "What can we do?", "What’s going on?"], "url": "https://visualframeworks.com/braiding/", "summary": "Use this representation when trying to understand or communicate about integrating and harmonizing multiple elements, in your personal or work life, into a robust, flexible system while avoiding complexities and disarray."}, {"name": "Bridge", "id": "CORE-010", "description": "A bridge is a structure that allows passage over a river or other obstacle. It connects two things that would otherwise be separated, enabling a transition from one space or state to another. For example, bridging a gap between two cultures; let’s cross that bridge when we come to it; don’t burn your bridges.  Do you see a gap in your situation? Can you create a bridge?", "see also": ["Gap", "Tightrope", "Stepping stones", "Journey"], "tags": ["Heart", "How can we connect?", "How to do it?", "<PERSON><PERSON><PERSON>", "Space", "Spacetime", "Structure", "Time", "What can we do?", "What needs to change?"], "url": "https://visualframeworks.com/bridge/", "summary": "Use this 'Bridge' representation when there is a need to connect separated entities, traverse obstacles or gaps, make transitions, or when contemplating changes or connections in both conceptual and physical spaces."}, {"name": "Calendar", "id": "CORE-011", "description": "Your time and attention is a limited resource. There are only so many hours in the day. An hour spent doing one thing precludes you from doing anything else during that time.  A calendar is a grid you can use to organize your time and attention. You can use it to plan your future and to review how you’ve spent your time in the past. It is a tool you can use to spend your time wisely.  Do you rule your calendar or does your calendar rule you? How can you use your calendar to help you make the best use of your time?", "see also": ["Clock", "Grid", "Timeline", "Cycle"], "tags": ["Abstract", "Decisions", "Hands", "Head", "Practical", "Priorities", "Time", "What is the plan?", "What’s important?", "Who will do what? By when?"], "url": "https://visualframeworks.com/calendar/", "summary": "Use this representation when you need to manage your limited time and attention efficiently, plan future activities, review past time utilization, and make informed decisions about how best to allocate your resources."}, {"name": "Causal loops", "id": "CORE-012", "description": "Causal loops are cyclical chains of events in a system which interconnect and interact with each other. One event impacts another, which impacts another, and so on. Their interconnectedness and feedback loops between cycles can make it difficult to comprehend or control the system.  Some feedback loops amplify an effect and tend to cause instability in the system, like the gain on a microphone, while others dampen an effect and tend to help the system maintain equilibrium, like the controls on a thermostat.  A causal loop diagram can help you make sense of how the cycles in a system interact to create complex effects.  What are the amplifying, destabilizing loops in your life? What are the dampening, stabilizing loops? How do they interact and interconnect? Can you diagram the causal loops?", "see also": ["Flywheel", "Virtuous cycle", "Doom loop", "Cycle", "Pen<PERSON><PERSON>", "Snowball effect"], "tags": ["Abstract", "Cause and effect", "Cycles", "Head", "How does it work?", "Time", "What’s going on?"], "url": "https://visualframeworks.com/causal-loops/", "summary": "Causal loops are used to understand, visualize, and control interconnected cyclical chain of events within a system, which may either amplify effects leading to system instability or dampen effects to maintain equilibrium, and they are particularly beneficial when dealing with complex cause-and-effect relationships."}, {"name": "Clock", "id": "CORE-014", "description": "A clock is an instrument that measures and displays time. It sets the beat and rhythm that maintain the pace of daily life.  Your time is all you have. A limited resource. Clock in. On the clock. The clock is ticking. Clockwise, counterclockwise. Keep your eye on the clock. Your biological clock. Reset the clock. Wind the clock.  See also: Gears, Pendulum, Cycle, Calendar.", "see also": ["Gears", "Pen<PERSON><PERSON>", "Cycle", "Calendar"], "tags": ["Abstract", "Hands", "Head", "Heart", "<PERSON><PERSON><PERSON>", "Practical", "Rhythm", "Synchronization", "Time", "When?", "Who will do what? By when?"], "url": "https://visualframeworks.com/clock/", "summary": "Use this clock representation for depicting concepts related to time, such as making schedules, understanding sequences, marking the rhythm of events, and whenever there's a need to illustrate abstract notions associated with time, resource management, or synchronization."}, {"name": "Concept map", "id": "CORE-015", "description": "A concept map allows you to visually map out your knowledge and understanding about a complex issue. It can also be a useful tool for thinking about complex relationships such as you might find in a system analysis, a legal argument, or a software design. A concept map shows the relationships between concepts. Ideas and concepts are the ''nouns,'' represented by boxes and circles. The relationships between them are ''verbs,'' represented by lines and arrows. The lines can be labeled with relationship categories, like ''causes,'' ''contributes to,'' ''is related to,'' and so on. Concept maps are useful for clarifying your own thinking or the thinking within your team. They are not as useful for communicating ideas to others, because they can be complicated and hard to read. Do you have a complex issue you want to unravel and think about? Which parts are related to which, and how do they all connect to make a whole? Can you make a concept map to clarify your thoughts?", "see also": ["Mind map", "Causal loops"], "tags": ["Abstract", "Analysis", "Head", "Map", "Space"], "url": "https://visualframeworks.com/concept-map/", "summary": "Concept maps are best used for visually exploring, analyzing and understanding complex relationships and systems by representing ideas and their interconnections; they are especially useful for individual or team thought-clarification rather than external communication due to their potentially intricate nature."}, {"name": "Conflict", "id": "CORE-016", "description": "Sometimes two (or more) people, groups, or ideas are simply incompatible. They are at odds, or in opposition to each other. For whatever reason, compromise, reconciliation, and agreement seem unattainable. A conflict arises out of situations or belief systems which are incompatible and cannot coexist peacefully. Situations involving conflict can be resolved by: Competition. In nature that means survival of the fittest, in human conflict this could be politics or war. \nCollaboration: The parties find a third option that creates a win for each.\nCompromise. Each side gives in somewhat for the sake of peace\nAccommodation: One side gives in and the other side gets their way.\nAvoiding: The conflict is avoided or delayed.\nHas conflict reared its ugly head in your life or work? How can you best approach the situation? Should you compete, collaborate, compromise, accommodate, or avoid?", "see also": ["Mess", "Gap", "Fog", "Balancing tradeoffs", "<PERSON>/Gain", "Gauntlet"], "tags": ["Abstract", "Conflict", "Head", "Heart", "<PERSON><PERSON><PERSON>", "What can we do?", "What is the problem?", "What’s going on?"], "url": "https://visualframeworks.com/conflict/", "summary": "Use this representation when an incompatibility issue arises between people, groups, or ideas, and the need to understand, visualize, and approach the situation for potential resolution, such as through competition, collaboration, compromise, accommodation, or avoidance."}, {"name": "Coordinates", "id": "CORE-017", "description": "You can use a coordinate system to compare any set of variables that can be quantified along two axes. For example you could use a coordinate system to track product quality over time, or to compare product quality with customer satisfaction, or to track how your weight changes over time.  A coordinate system allows you to compare any two things that can be measured or quantified.  What are two numbers that matter to you right now? Does it make sense to compare them in one graph? Can you use a coordinate system to visually compare them?", "see also": ["2×2 matrix", "Bar chart", "Line chart", "Grid"], "tags": ["Abstract", "Control", "Decisions", "Head", "How many or how much?", "Measurement", "Priorities", "Space", "Time", "What are the options?", "What matters?", "What’s going on?"], "url": "https://visualframeworks.com/coordinates/", "summary": "Use a coordinate system for visual comparison of any set of quantifiable variables across two axes to assist in abstract thinking, control, decision making, prioritizing, or measuring trends over time or space."}, {"name": "Countdown", "id": "CORE-018", "description": "A countdown counts time backward from a starting point down to zero. Countdowns can be used for many things: A rocket launch, the start of a race, a pie in the oven. Some sports have countdown clocks, like the ''shot clock'' in basketball to ensure the game proceeds at a lively pace. A ''sell-by'' date on food packaging is a kind of countdown  The pressure to complete something within a timeframe can create anxiety, but it can also improve productivity. Sometimes work tends to expand to fill the time allotted for it. Setting a deadline or countdown can help you prioritize work, improve focus, and help you deliver on time  Without a deadline, it’s possible to continue working and iterating on a project, tweaking and re-tweaking it so much that you never launch it at all  Do you have a project or team that is foundering? Something that’s stalled and needs to be re-energized? Could a deadline or countdown help you get back on track?", "see also": ["Clock", "Calendar", "Cycle", "Project dependencies"], "tags": ["Hands", "Practical", "Priorities", "Time", "What do we measure?", "When?", "Who will do what? By when?"], "url": "https://visualframeworks.com/countdown/", "summary": "Use this representation when managing a project or team that needs a deadline to increase focus, improve productivity, prioritize tasks, and stimulate progress, particularly when there is a propensity for endless tweaks and iterations."}, {"name": "Crossroads", "id": "CORE-019", "description": "A crossroads is a critical juncture, a decision point. A situation where an important choice must be made. A product is nearing the end of its lifecycle. An important technology is becoming obsolete and must be replaced. You get a job offer in a new city.  It’s time to choose a direction. Which way to go? What are the important criteria? What does your gut tell you? How will you choose?", "see also": ["Balancing tradeoffs", "Switch", "Journey", "Path", "Pros and cons"], "tags": ["Decisions", "Heart", "How to decide?", "<PERSON><PERSON><PERSON>", "What are the options?", "What can we do?", "What matters?", "What to do?"], "url": "https://visualframeworks.com/crossroads/", "summary": "This representation should be used when encountering crucial turning points, decisions, or junctures in processes like product life cycles, technology updates, or life changes such as job prospects."}, {"name": "Cutaway view", "id": "CORE-020", "description": "A cutaway drawing peels away the outer layer of something to reveal what’s beneath. Like a magic window, it gives you X-ray vision and allows you to see the inside and outside of something at the same time.  What is beneath the surface? What would you see if you peeled away the outer layers? If you had a magic window or X-ray vision, what might you see?", "see also": ["Exploded view", "Org chart"], "tags": ["How does it work?", "Look inside", "Space", "Structure", "What’s inside?"], "url": "https://visualframeworks.com/cutaway-view/", "summary": "Use the 'Cutaway View' representation when you want to understand and visualize the interior structure or workings of an object, system, or concept by virtually peeling away its outer layers."}, {"name": "Cycle", "id": "CORE-021", "description": "A cycle is a process that turns back on itself and repeats regularly, in the same order, over time. The seasons of the year, runners running laps on a race track, and the phases of the moon are all types of cycles. Cycles are essential to life. Cycles affect your life and work in many ways. Most companies track and measure progress according to daily, weekly, monthly, quarterly, and yearly cycles.  Tracking information in this way allows you to compare how you are doing compared to previous cycles. For example, a company like Amazon typically does very well in the months leading up to Christmas. So comparing 4th quarter results this year vs. the last few years can provide insights about performance. Because cycles are regularly repeating activities, you can reflect on what you learned from the last cycle and apply those learnings to the next cycle. What are the most important cycles in your life and work? How do you track your progress over time? Can you find a way to compare cycles over time to see how you are doing?", "see also": ["Doom loop", "Virtuous cycle", "Flywheel", "Clock", "Causal loops", "Escape velocity", "Highs and lows", "Snowball effect"], "tags": ["Abstract", "Cycles", "Head", "How are we doing?", "How do we grow?", "How do we track progress?", "How does it work?", "Time", "What do we measure?"], "url": "https://visualframeworks.com/cycle/", "summary": "This representation should be used when trying to understand the concept of cycles, their importance in life and work, and how to track progress over time."}, {"name": "Dashboard", "id": "CORE-022", "description": "A dashboard is a real-time information display that you can quickly scan to get feedback on how you are doing. A dashboard gives you the up-to-date information you need to make better, faster decisions. It can also help you identify potential problems early, before they become major issues.  If you had a dashboard to monitor your life, or the performance of your team or company, what would be on it? What would you need to measure? How often? How would you want to see it?", "see also": ["Countdown", "Coordinates", "Bar chart", "Line chart", "2×2 matrix", "Regulation", "Clock", "Project dependencies"], "tags": ["Decisions", "<PERSON><PERSON><PERSON>", "Heart", "How are we doing?", "<PERSON><PERSON><PERSON>", "Priorities", "Time", "What do we measure?", "What’s going on?"], "url": "https://visualframeworks.com/dashboard/", "summary": "Use this representation when you need real-time data for performance monitoring, early problem detection, and decision-making processes for individual, team, or company-wide projects."}, {"name": "Decision tree", "id": "CORE-023", "description": "Choices lead to more choices, which lead to more choices. A decision tree can be helpful when decisions along the tree are simple and predictable, like yes or no. They are a way to think ahead, and lay out the choices you are likely to face, based on decisions you make today. How to do it: Start with a question and then add potential decisions and outcomes. What are the choices you must make first? And for each choice, think ahead to the next decision. What will your choices be at that point? Keep going and define the paths and decisions until you reach an end point or a point beyond which there are too many uncertainties. If you decide to go back to school, for example, then you will need to pick your major and courses, and you may need to quit your job. If you quit your job, you may have to live more frugally, and so on. Are you facing a major decision in your life or work? Can you make a decision tree to think ahead and predict the choices and paths available to you, and where they might lead? ", "see also": ["Balancing tradeoffs", "Crossroads", "Tree", "Mind map", "Org chart"], "tags": ["Abstract", "Decisions", "Head", "How to decide?", "How to think about this?", "Priorities", "What are the options?", "What can we do?", "What is the plan?", "What matters?", "What’s important?", "What’s next?"], "url": "https://visualframeworks.com/decision-tree/", "summary": "Use a decision tree representation when you are facing a major decision in your life or work and want to think ahead and predict the choices and paths available to you, based on simple and predictable decisions, in order to lay out potential outcomes and plan for the future."}, {"name": "Deep dive", "id": "CORE-024", "description": "Daily habits and routines make life comfortable and predictable, and most of the time they are sufficient for keeping things on track. But sometimes it’s necessary to look at what’s happening under the surface: a deep dive. Clear your calendar. Make time for thoughtful reflection. A deep dive is exploratory. You dive in with questions, not answers. You don’t know in advance what you will find. But it’s a chance to discover emotional and psychological currents and patterns, explore life’s mysteries, and maybe even find treasure. As you finish your dive, come up slowly. Take your time and process your insights. Return with treasure, insights, new ideas and perspectives. What matters most to you? Why have you arranged your life and work in the way that you have? What do you really want? What’s stopping you?", "see also": ["Mountain climbing", "Journey", "Peeling the onion", "Root causes"], "tags": ["Head", "Heart", "<PERSON><PERSON><PERSON>", "Time", "What are the options?", "What matters?", "What’s going on?", "What’s important?"], "url": "https://visualframeworks.com/deep-dive/", "summary": "Use this representation when you need to explore the emotional and psychological aspects, uncover patterns and insights, and discover what truly matters to you and what might be holding you back."}, {"name": "DNA", "id": "CORE-025", "description": "Your DNA is the genetic code that defines your uniqueness. It carries the instructions and messages necessary for you to develop, survive, and reproduce. Because DNA must be copied from cell to cell, sometimes it can degrade: errors in replication can cause damage which needs to be repaired. Sometimes damage leads to mutations. DNA can also be broken apart and recombined to create new variations.  Mutations and recombinations are the experiments inherent to evolution: they can create new opportunities, but they can also be dangerous, even life-threatening.  What’s in your DNA? What makes you unique? How does your DNA replicate, mutate, repair, and recombine to create new kinds of life?", "see also": ["Braiding", "Growth", "Solar system"], "tags": ["Heart", "<PERSON><PERSON><PERSON>", "Space", "What matters?", "What’s important?", "Where are we going?", "Who are we?", "Why are we here?"], "url": "https://visualframeworks.com/dna/", "summary": "This representation can be used when discussing or exploring topics related to genetics, evolution, individuality, life processes and philosophical existential questions, as it uses the concept of DNA and its functions to stimulate deep conceptual understanding."}, {"name": "Doom loop", "id": "CORE-026", "description": "You’re doing the same thing you’ve always done, but for some reason it’s longer working for you the way it once did. You feel like you’re digging yourself deeper and deeper into a hole, and you can’t see your way out. Whatever you do, it seems that the hole keeps pulling you back down. You’re stuck in a doom loop.   When you’re stuck in a doom loop, it’s important to realize that continuing to do the same things, the same way, will only perpetuate the loop. To escape the doom loop, you need to do something different. Even if you don’t know what to do, anything you can do to disrupt your routines can help.   What routines do you need to disrupt? What can you do differently?", "see also": ["Cycle", "Pain/gain", "Flywheel", "Virtuous cycle", "Causal loops", "Snowball effect", "Infinite loop", "Pen<PERSON><PERSON>", "Momentum"], "tags": ["Cycles", "Heart", "<PERSON><PERSON><PERSON>", "Pain/gain", "Time", "What are the options?", "What can we do?", "What needs to change?", "What’s going on?"], "url": "https://visualframeworks.com/doom-loop/", "summary": "This representation can be used when someone is trapped in a negative cycle and needs to find a way to break free from it by disrupting their routines and trying something different."}, {"name": "Escape velocity", "id": "CORE-027", "description": "Escape velocity: In celestial mechanics, escape velocity is the minimum speed required to escape the influence of a gravitational body.  In everyday life, things tend to be held in place by the gravitational pull of things like home, work, habits and routines. Occasionally something triggers a rapid acceleration that completely disrupts the status quo. You or your project hit escape velocity.  You have left orbit and you find yourself in unknown territory. There are no longer established rules, habits, places and practices. You’re on your own.  What opportunities does this moment of disequilibrium provide? What are the risks? What are the most important questions to be thinking about right now?", "see also": ["Pen<PERSON><PERSON>", "Solar system", "Snowball effect"], "tags": ["Heart", "<PERSON><PERSON><PERSON>", "Space", "Time", "What are the opportunities?", "What are the options?", "What are the risks?", "What can we do?", "What’s going on?"], "url": "https://visualframeworks.com/escape-velocity/", "summary": "Use this representation when you or your project experiences a significant shift or change, dislodging you from routine practices and norms, triggering uncertainty but also new opportunities and challenges, essentially, a moment that metaphorically resembles hitting the 'Escape Velocity' in celestial mechanics."}, {"name": "Exploded view", "id": "CORE-028", "description": "An explosion is a rapid expansion of energy which can blow things apart. An exploded view shows the pieces and parts of something suspended in space and separated from each other, so you can see how they all fit together.  Because exploded views often mirror the assembly process, they make useful instructions for how to put something together or take it apart. If you have put together furniture from IKEA, you have probably encountered an exploded view diagram.  Exploded views are often used in manuals or patent drawings, to explain how a design or idea is structured or put together.  Are you looking at a situation that’s hard to unravel? What are the parts? How do they fit together? How can it be taken apart, or put back together? Can you make an exploded view drawing to explore the possibilities?", "see also": ["Cutaway view", "Org chart"], "tags": ["How does it work?", "Literal", "Space", "Structure", "What is it?", "What’s inside?"], "url": "https://visualframeworks.com/exploded-view/", "summary": "Use the 'Exploded view' representation when you need to visually explain how various individual parts of a design or idea fit together or can be disassembled, which is especially helpful in creating instructional material and understanding complex structures."}, {"name": "Flowchart", "id": "CORE-029", "description": "A flowchart is a diagram that depicts a workflow or process. Flowcharts employ a specific visual language, using different types of boxes and arrows to show beginning and end points, steps or stages, and decision points.  Flowcharts can be useful for designing workflows and processes, or re-thinking them to be more robust or efficient. They can also be useful for identifying problem areas.  What are the inputs and outputs? How does it work? Why is it breaking down? Where are the problem areas? How can we make it better? Can you make a flowchart to help answer these questions?", "see also": ["Bottleneck", "Pipes and tanks", "Path", "Project dependencies"], "tags": ["How does it work?", "How to think about this?", "What are the options?", "What can we do?", "What’s going on?"], "url": "https://visualframeworks.com/flowchart/", "summary": "Use a Flowchart as a visual tool to design, analyze, or improve a process or workflow, identifying inputs, outputs, and problem areas by depicting steps, stages, and decision points with boxes and arrows."}, {"name": "Flywheel", "id": "CORE-030", "description": "A flywheel is a simple machine that stores angular momentum as energy. The first flywheels were probably potter’s wheels. One property of a flywheel is the tremendous amount of momentum it can reach over time. When a large, heavy flywheel is stationary, it takes a lot of energy to get it to budge, but as it starts rotating faster, it gains more power and its own momentum keeps it going. The flywheel gained business popularity when <PERSON> coined the term ''flywheel effect,'' which he described as follows: ''There is no single action that creates momentum. Instead, as you consistently push in a single direction over time, you’ll begin to benefit from the power of compounding results. And it’s this momentum that causes what eventually appears, from the outside, to be an overnight success.'' Amazon’s flywheel is a core element of its business strategy: a whole set of different activities create small ''nudges'' to the flywheel, but these small actions build great momentum over time. Customers who join Prime get more benefits which makes them more loyal, so they recommend the company and they also but more. A lower cost structure allows Amazon to charge lower prices, which drives more purchases. As more people sell on the platform, it becomes more attractive to buyers. Can you find a way to create a flywheel effect in your life or work? What kinds of small things can you do to create momentum over time? What can you do that has a ''multiplying effect'' on your other activities?", "see also": ["Cycle", "Virtuous cycle", "Doom loop", "Escape velocity", "Pen<PERSON><PERSON>", "Momentum", "Snowball effect"], "tags": ["Heart", "How does it work?", "<PERSON><PERSON><PERSON>", "What are the options?", "What can we do?"], "url": "https://visualframeworks.com/flywheel/", "summary": "This representation should be used when explaining or exploring the concept of a flywheel, its benefits, and how it can be applied in life or work to create momentum and compounding results."}, {"name": "Fog", "id": "CORE-031", "description": "Sometimes you find you have become lost in a fog of ambiguity, uncertainty, and confusion. You’re in unknown territory. You haven’t been here before. It’s perplexing. It can be scary. It’s hard to make decisions because you don’t really know what’s going on or how to make the best choice.  Military strategist <PERSON> described the ''fog of war'' as the uncertainty experienced in the frenzy of battle. Military commanders don’t have the luxury of time; they must act decisively without perfect information, when the situation is fuzzy, opaque, unclear.  It’s hard to navigate in the fog because you can’t see clearly. Traffic slows down and sometimes even stops.   Complexity, confusion, and misunderstanding can create a fog that obscures the situation. It can be unclear what’s really going on, how people feel, or whether they mean what they say. Nevertheless, sometimes you don’t have the time to wait for the fog to clear. There are occasions when you must make decisions and move forward anyway.  When you need to navigate in the fog, you have a few choices:  You can stay put and wait for the fog to clear.  You can stick to the trail you’re on.  You can find other ways to navigate, like using a GPS device or a compass, listening for clues, or feeling your way forward. You can even lock arms with friends or colleagues and move forward together, feeling your way and communicating what you find.  Sometimes you can find a guide who knows the territory and can help you navigate it.  Are you lost in a fog of confusion? How can you clarify the situation? Can you find a guide to help? What needs to happen next? How will you find your way?", "see also": ["Mess", "<PERSON><PERSON>", "Gap"], "tags": ["Heart", "<PERSON><PERSON><PERSON>", "Space", "What is it?", "What is the problem?", "What’s going on?"], "url": "https://visualframeworks.com/fog/", "summary": "Use this representation when needing to navigate through situations riddled with ambiguity, confusion, and complexity; where decision-making must occur despite the lack of clear information, mimicking navigating through a 'fog'."}, {"name": "Force fields", "id": "CORE-032", "description": "In physics, a force field is a region where forces are present. For example, the force of gravity, electromagnetism, and the nuclear forces that hold atoms together. In the age of sail, forces like ocean currents and trade winds could make or break a journey. In a force field analysis, you compare the forces for and against any change you want to make — the forces that propel you forward and the ones that hold you back. You can use the size or length of the arrows to show the magnitude of the force. Is it time to make a change? What are the forces that will help the change? What are the forces against it? Can you make a force field diagram to help you think about the change?", "see also": ["Escape velocity", "Impact", "Momentum", "Pros and cons", "Balancing tradeoffs"], "tags": ["Compared to what?", "Decisions", "How to decide?", "What are the options?"], "url": "https://visualframeworks.com/force-fields/", "summary": "Use this representation when you need to analyze the possible impact of change, evaluating the forces or factors that can drive or resist it, in order to support decision-making."}, {"name": "Funnel", "id": "CORE-034", "description": "A funnel is a cone-shaped tube that is wide at one end and narrow at the other, used to guide liquid or powder into a small opening.  You could see a funnel as a sort of intentional bottleneck, slowing a flow so it can fit into a small opening. For example, you might create merge lanes to funnel highway traffic from four lanes down to two.  The funnel is a useful metaphor for any process or workflow that takes a large number of inputs and narrows them down into a small number of outputs.  Sheep farmers use a fence shaped like a funnel to move a herd of sheep from a pen into a single-file chute, so they can be sorted, vaccinated, marked and so on.  In business, sales and marketing teams use funnels to narrow down general traffic, like web visitors, to the people who are most likely to buy or become loyal customers. A sales or marketing funnel typically uses meaningful criteria to move people from one stage to another along the funnel. This funneling process helps teams focus their energy and efforts where they are most likely to drive meaningful results.  Do you have a large quantity of people or things that need to be narrowed down, so you can focus your attention on what matters? Could a funnel help you qualify and sort them into meaningful categories?", "see also": ["Bottleneck", "Black hole"], "tags": ["Abstract", "Decisions", "Head", "Heart", "How do we track progress?", "How does it work?", "How should we choose?", "How to decide?", "<PERSON><PERSON><PERSON>", "Process", "Space", "Time"], "url": "https://visualframeworks.com/funnel/", "summary": "Use the funnel representation when there's a need to systematically narrow down a large number of inputs into a manageable amount of outputs, helpful in focusing attention where needed and streamlining processes such as marketing funnels, decision-making, and progress tracking."}, {"name": "Fractal", "id": "CORE-033", "description": "A fractal is a pattern that is defined by self-similarity: it has the same shape at all scales. Clouds, mountains, and coastlines all follow a fractal pattern: no matter how far you zoom out or how close you zoom in, you will see a similar pattern. Fractal patterns are growth patterns; they are created by iteration, like the growth of a snail shell. They approximate nature more closely than traditional geometry. There are no perfect circles and squares in nature, but fractal patterns abound. Can you recognize a fractal pattern in your situation? Is it a natural, living system, or does it resemble one? Does it repeat at small and large scales? Do small parts resemble the whole?", "see also": ["Org chart", "Puzzle", "Peeling the onion"], "tags": ["Compared to what?", "Head", "How do we grow?", "How to think about this?", "Space", "Structure", "What is it?"], "url": "https://visualframeworks.com/fractal/", "summary": "Use this fractal representation when analyzing systems or situations that exhibit self-similar growth patterns across different scales, typically reflecting characteristics of natural phenomena."}, {"name": "Gain map", "id": "CORE-035", "description": "A gain map is the counterpoint to the pain map. Showing that you empathize and understand your customer’s pain is helpful, but it’s not enough by itself. You must also show what they stand to gain from your product or service. The gain map shows them the potential benefits of working with you. You can also make your own personal gain map to visualize the rewards you will get from a difficult project. Say you want to lose weight. Your gain map might include things like feeling healthier, being more active, getting to shop for new clothes, feeling more attractive, and so on. Pain and gain maps should be mirror images of each other as much as possible.", "see also": ["<PERSON>/Gain", "Pain map", "Gauntlet", "Doom loop", "Virtuous cycle", "Map"], "tags": ["Heart", "<PERSON><PERSON><PERSON>", "Pain/gain", "Why should I care?"], "url": "https://visualframeworks.com/gain-map/", "summary": "This representation, called the gain map, is used to visually depict the potential benefits and rewards that customers or individuals can experience from a product/service or a personal project."}, {"name": "Gap", "id": "CORE-036", "description": "A gap is a break or space between two things. Two people who have trouble communicating have an understanding gap. A customer need that remains infilled is a service gap. Some common sayings: mind the gap, bridge the gap, close the gap.  What are the gaps in your situation, or your understanding of it? How might you close the gap?", "see also": ["Architecture", "Bridge", "Pain/gain", "Pain map", "Conflict", "Mess", "<PERSON><PERSON>", "Fog"], "tags": [], "url": "https://visualframeworks.com/gap/", "summary": "Use the concept of a 'gap' to identify and address breaks or spaces between desires and reality, such as understanding issues or unfulfilled needs, and consider ways to 'bridge' or 'close' these gaps for a clearer road ahead."}, {"name": "Gauntlet", "id": "CORE-037", "description": "There are times in life where the road ahead is fraught with trials and tribulations. Your life feels like an obstacle course or a minefield. What’s next? You’re running a gauntlet. In ancient times, running the gauntlet was a trial or form of punishment where a person was forced to run between two lines of people and suffer a pummeling with sticks or stones. The faster you could get through it, the less it hurt. Sometimes dealing with a large company, or the government, requires a long, difficult, and bureaucratic process that feels like a gauntlet. Sometimes there’s no way around it, the only way out is to go through it as quickly as you can? Are you facing a gauntlet? Are you running a gauntlet right now? Do you absolutely need to do it? If the answer is yes, how can you get through it as quickly as possible?", "see also": ["Journey", "<PERSON>/Gain", "Pain map", "Gain map"], "tags": ["Heart", "Journey", "<PERSON><PERSON><PERSON>", "Pain/gain", "Process", "Spacetime", "Time", "What’s next?"], "url": "https://visualframeworks.com/gauntlet/", "summary": "This representation should be used when facing a long, difficult, and bureaucratic process that feels like a gauntlet, where the goal is to find the quickest way through it."}, {"name": "Gears", "id": "CORE-038", "description": "Gears are rotating machine parts with cut teeth, designed to connect to other toothed parts to create torque and speed. A gear is like a circular lever, working with other gears to gain control of forces and direct them in order to achieve goals.  Because gears are circular, they work in repeating cycles to get things done. This is akin to how small actions, like processes, habits and routines, can be repeated over time to get a long-term gain.  Gears don’t work on their own; they also have to be connected to other gears and mechanisms in the right way, in order to generate results. Gears are not always a good thing. People sometimes say they feel like a cog in a machine, lost and insignificant in a vast bureaucracy. Which repeating cycles (processes, habits and routines) have the potential to deliver long-term gains? Can they be connected and synchronized in such a way that they can deliver meaningful results?", "see also": ["Flywheel", "Virtuous cycle", "Doom loop", "Causal loops", "Cycle", "Leverage"], "tags": ["Heart", "How can we connect?", "How does it work?", "How to do it?", "How will we get there?", "<PERSON><PERSON><PERSON>"], "url": "https://visualframeworks.com/gears/", "summary": "Use this representation when looking to understand how seemingly small, repeated actions (like habits and routines) when interconnected correctly, can create significant long-term results, much like how gears in a machine work together to produce an outcome."}, {"name": "Grid", "id": "CORE-039", "description": "A grid is a way to organize a space into columns and rows by using vertical and horizontal lines to create squares or rectangles, like you might see in a chessboard or spreadsheet.  Grids are useful for organizing information according to two criteria. For example, the basic layout of a spreadsheet uses numbers to designate rows and letters to designate columns. So the items in the first column will be A1, A2, A3, and so on, while the numbers in the first row will be A1, B1, C1, and so on.  Grids make a space more legible, so it’s easier to find things and make comparisons. For example, many city streets are laid out in a grid, making it easier to find your way. A grid can be used to compare products by features, benefits, cost, and so on.  Do you have a space that’s hard to navigate, or a set of things that you want to compare? Can you make a grid to make it easier to navigate a space or make comparisons?", "see also": ["Coordinates", "Calendar"], "tags": ["Abstract", "Analysis", "Compared to what?", "Decisions", "Head", "How to decide?", "Space", "What are the options?", "What matters?", "What’s important?"], "url": "https://visualframeworks.com/grid/", "summary": "Use this representation when you need to organize a space or information with two criteria and make comparisons easily, whether it is for navigating a data-rich spreadsheet or identifying relationships between different pieces of information."}, {"name": "Highs and lows", "id": "CORE-041", "description": "Life fluctuates. We learn to appreciate life by comparing and contrasting highs and lows. We wouldn’t know good without bad, happiness without sadness, lightness without weight, or clear without clutter.<PERSON> claimed that every story could be graphed as a series of highs and lows over time. What is your story? What are the highs and lows of your situation or experience? Can you graph them as highs and lows over time?", "see also": ["Pen<PERSON><PERSON>", "Infinite loop", "Cycle", "Line chart", "Coordinates", "Measurement"], "tags": ["Abstract", "Head", "Heart", "Time", "What happened?", "What is the problem?", "What matters?", "What needs to change?", "What’s going on?", "What’s the story?", "What’s working?"], "url": "https://visualframeworks.com/highs-and-lows/", "summary": "Use this representation when you need to visualize and compare the high and low points of a situation, experience, or story over a period of time to gain a better understanding and appreciation of it."}, {"name": "Hole", "id": "CORE-042", "description": "There are times when you find yourself in a hole, with no easy way to get out. A dark night of the soul.  The first law of holes: If you find yourself in a hole, stop digging. You may not feel like you’ve been digging, but chances are there might be habits and routines that are contributing to your situation.  The second law of holes: If you stop digging, you’re still in a hole. If you’re in a hole, you can’t talk yourself out of it. You have to work yourself out.  The most important step in getting out of a hole is admitting that you’re in one. The second most important thing is to do something different. Experiment with new ways of doing things. The first thing you try may not work. But if you’re persistent and keep trying, eventually you will find something that works.  Do you feel like you’re in a hole? Can you stop digging? What can you do differently?", "see also": ["Doom loop", "Mess", "<PERSON><PERSON>", "Gap", "Pain map", "Pain/gain", "Tipping point"], "tags": ["Heart", "How did we get here? Where are we going?", "<PERSON><PERSON><PERSON>", "Space", "What can we do?", "What’s going on?", "Where are we?"], "url": "https://visualframeworks.com/hole/", "summary": "Use this 'Hole' concept when you're stuck in a difficult situation, realize you're contributing to it through routines or habits, and need to devise innovative solutions and actions to improve your circumstances, making it fitting for problem resolution and strategy development contexts."}, {"name": "Hot potato", "id": "CORE-043", "description": "Hot potato is a parlor game where people pass something, like a bean bag, from person to person. The loser is the one caught with the hot potato when the music stops.  A hot potato can be anything difficult, controversial, or troublesome, that people want to get rid of as soon as possible.  Like passing the buck, passing on the hot potato is a way to avoid responsibility, taking a problem that’s on your plate and handing it off, eliminating the problem for you by simply making it someone else’s problem.  The hot potato game is not productive. While it may solve the immediate, local challenge, it doesn’t solve the problem or improve the overall situation. Instead, it just moves the problem around, making it harder to identify and harder to solve.", "see also": ["Infinite loop", "Doom loop"], "tags": ["Heart", "How does it work?", "<PERSON><PERSON><PERSON>", "Time", "What’s going on?", "Who are we?", "Who is affected?"], "url": "https://visualframeworks.com/hot-potato/", "summary": "This representation should be used to understand the concept of 'Hot Potato' in the context of avoiding responsibility and passing along troublesome issues or problems, which merely shifts the issue rather than addressing it effectively."}, {"name": "Hub and spokes", "id": "CORE-044", "description": "The hub and spokes model can be used to describe any system that resembles a bicycle wheel, where paths (spokes) radiate outward from a central core (the hub).  Many airline networks, for example, are arranged in a hub-and-spokes manner, where passengers and planes are routed to their destinations via centralized hubs. Distribution networks often have a central “hub” model, where inventory is stored in a central distribution center before being distributed to multiple fulfillment centers.  The nervous system and circulation system in your body can be seen as hub-and-spoke networks. The brain and the heart serve as hubs, while the nerves and blood vessels serve as spokes, moving blood and nerve impulses throughout the body.  Usually, some things, like data, reporting, finances, maintenance, and coordination activities, work better when they are centralized, while other things, like sales and customer service, work better when they have the freedom and flexibility to make fast, local decisions.  A hub-and-spoke network tends to be more efficient, but reliance on a central hub can also make it less resilient. A hub can easily become a bottleneck or a single point of failure, that is, the entire network can be crippled because of problems at the hub.  Could a hub-and-spoke model be helpful in thinking about your situation? In what areas are centralization and standardization important? What areas need more flexibility? Can you improve your system, or design a better one?", "see also": ["Org chart", "Pipes and tanks", "Flowchart", "Causal loops", "Cycle"], "tags": ["Abstract", "Head", "How do we grow?", "How does it work?", "How to think about this?", "Space", "Structure", "What can we do?", "What is it?", "Who will do what? By when?"], "url": "https://visualframeworks.com/hub-and-spokes/", "summary": "The 'Hu<PERSON> and Spokes' representation is utilized when describing any system that operates in a similar pattern of radial paths or decisions emanating from a centralized core, such as airline networks or distribution centers, acknowledging the efficiencies and potential vulnerabilities these models possess."}, {"name": "Iceberg", "id": "CORE-045", "description": "An iceberg is a free-floating mountain of ice. Icebergs can be very hazardous to shipping, because the majority of an iceberg is invisible from the surface, below the water, unseen. The tip of an iceberg is a small part of a much larger situation. The tip may be more visible and easily seen, but there’s much more under the surface. An iceberg metaphor is helpful for exploring things where most of the issues are unseen, invisible, and obscure, like root causes, emotional undercurrents, and unconscious thoughts, feelings or memories.  For example, psychologist and business theorist <PERSON> used an iceberg metaphor as a model to describe organizational culture. Artifacts are things that are visible above the surface, such as office jokes, dress code, and how the workplace is laid out and arranged. Espoused values are the values the organization claims to have, promotes, and codifies in its public statements and rules for behavior. Assumptions are deeply ingrained and taken for granted. They are unstated and often unconscious, and may or may not be congruent with the espoused values. Assumptions are the essence of the culture. Unseen and unacknowledged, and difficult for outsiders to see, they are the center of gravity that drives organizational priorities and behavior. Do you suspect that you’re only seeing the tip of the iceberg? What might be underneath? What unseen and invisible forces might be creating the effect you see on the surface?", "see also": ["Pyramid", "Prism", "Layer cake", "Root causes", "Org chart"], "tags": ["Heart", "How does it work?", "<PERSON><PERSON><PERSON>", "Structure", "What’s going on?"], "url": "https://visualframeworks.com/iceberg/", "summary": "This representation is used to visually explore hidden and unseen aspects of a situation or concept, such as root causes, emotional undercurrents, and unconscious thoughts, using the iceberg metaphor as a model."}, {"name": "Impact", "id": "CORE-046", "description": "Billiards is a game of applied physics. To win, you must apply force strategically and with precision, transferring momentum from the cue ball to others, and using the balls and boundaries of the table to achieve your goals. When and where you apply force can make the difference between success and failure. In The Art of War, <PERSON> wrote ''When torrential water tosses boulders, it is because of momentum. When the strike of a hawk breaks the body of its prey, it is because of timing.'' What resources, powers and forces are available to you? When should you conserve them and when should they be deployed? How can you determine when and where they can be applied most effectively?", "see also": ["Momentum", "Leverage", "Tipping point", "Pen<PERSON><PERSON>"], "tags": ["Heart", "How does it work?", "How to do it?", "<PERSON><PERSON><PERSON>", "Time", "What are the options?", "What can we do?", "What happened?", "What to do?", "What’s going on?", "What’s the story?"], "url": "https://visualframeworks.com/impact/", "summary": "Use this representation when you need to understand the importance and strategic application of resources and forces in achieving specific goals, reflecting on the concept of impact through the metaphor of a billiard game and <PERSON>'s teachings."}, {"name": "Infinite loop", "id": "CORE-047", "description": "Life repeats itself in patterns and cycles. Sometimes you find yourself in a pattern that oscillates between two different states.  For example, when things are crazy and chaotic, you crave order, so you impose more structure on your activities. But over time the structure becomes stifling and you feel trapped, so you loosen the constraints to create more freedom. Over time, too much freedom leads to chaos, and you find yourself looping back to order again.  Another example from nature: Wolves feast on the deer population until there are too many wolves and not enough deer. Many of the wolves starve to death. With fewer wolves around, the deer population surges. With more deer to feed on, the wolf population multiplies, until once again there are too many wolves and not enough deer, and the cycle starts again.  Are you stuck in an infinite loop, oscillating between two extremes, when what you really want is the happy medium? Can you improve your understanding of the oscillating pattern by diagramming it out? Can you design a more measured, managed approach that helps you escape the infinite loop?", "see also": ["Cycle", "Pen<PERSON><PERSON>", "Hole", "Doom loop"], "tags": ["Abstract", "Head", "How does it work?", "Time", "What is it?", "What needs to change?", "What’s going on?"], "url": "https://visualframeworks.com/infinite-loop/", "summary": "Use this representation to understand, identify, and mitigate cyclical patterns of extremes that lack an optimal middle ground, often leading to repeated and undesirable outcomes in personal life, natural ecosystems, or other systems."}, {"name": "Journey", "id": "CORE-048", "description": "Everyone is the hero of their own story. And everyone is on a journey of some kind. Right now you’re either preparing for a journey, starting one, in the middle of one, or finishing one. Probably you are on several journeys. For example, a startup, a marriage, a career, an education, a journey of self-discovery — every path has its obstacles, hard choices, steep inclines and giddying heights. What journey are you on right now? How did you get here? Where are you going? How far have you come? How far do you still have to go? What does it look like when you have reached your destination?", "see also": ["Deep dive", "Mountain climbing", "Tightrope", "Milestones", "Bridge", "Airplane", "Sailboat"], "tags": ["Heart", "How did we get here?", "Where are we going?", "<PERSON><PERSON><PERSON>", "Where are we?", "Why are we here?"], "url": "https://visualframeworks.com/journey/", "summary": "This representation can be used to visually illustrate and explore the concept of life's journey, highlighting the idea that everyone is the protagonist of their own story, facing challenges and making choices while striving towards personal destinations and growth."}, {"name": "Juggling", "id": "CORE-049", "description": "Juggling is the art of keeping as many balls in the air as possible without dropping any.  Cybernetics pioneer <PERSON><PERSON> <PERSON> proposed a law that is now known as <PERSON><PERSON><PERSON>s law: A system must be at least as complex as the environment it wants to control.  With only two hands to work with, there will always be an upper limit to the number of balls you can juggle at one time. Juggling can be a beautiful thing, but it also consumes a majority of your attention, and you can’t juggle all of the time.  Are you juggling too many priorities? Are you having trouble keeping all of them on track? How many balls can you keep in the air at one time without dropping any? Can you figure out your capacity and find a way to limit the number of things that command your attention?", "see also": ["Conflict", "Mess", "Fog", "Decision tree", "Coordinates", "2×2 matrix"], "tags": ["Heart", "How to decide?", "<PERSON><PERSON><PERSON>", "Time", "What can we do?", "What matters?", "What needs to change?", "What’s going on?", "What’s important?"], "url": "https://visualframeworks.com/juggling/", "summary": "This representation can be used when you are struggling with prioritization, task overload, or time management, aiming to better understand your capacity and determine ways to balance your attention among various tasks and responsibilities."}, {"name": "Layer cake", "id": "CORE-050", "description": "Layering allows you to stack things higher than you otherwise would be able to. It also gives you a way to keep elements separated from each other.  In a building, for example, you might designate the ground floor for retail and keep the upper floors for residents. Or in an office building you might reserve the ground floor for lobby and reception, the second floor for food service, and the upper floors for office space. Dedicating a specific activity to each layer makes it easier to get the best use out of the space.  Layering can also be useful in virtual and conceptual spaces. In software design, for example, you might want to separate the interface layer, where users interact with the software, from other layers where you manage data and hardware.  Can layering help you simplify and make sense of your situation? Can you use it as a design approach, to make a space easier to understand and navigate? Can you use layering to optimize a physical or virtual space?", "see also": ["Iceberg", "Pyramid", "Altitude", "<PERSON><PERSON><PERSON>", "Subduction"], "tags": ["How to think about this?", "Structure", "What is it?", "What is the problem?", "What’s inside?"], "url": "https://visualframeworks.com/layer-cake/", "summary": "This representation should be used when there is a need to understand and optimize the use of space, whether physical or virtual, through layering and separating different elements or activities to simplify and make sense of the situation."}, {"name": "Lens", "id": "CORE-051", "description": "A lens is an optical device that uses refraction to focus or disperse a beam of light. Lenses are used in telescopes, binoculars, microscopes, cameras, and eyeglasses. Different types of lenses can serve different kinds of purposes. For example, a zoom lens, a wide-angle lens, and a telephoto lens each light differently to achieve different effects.  Can you try on different lenses to view the situation differently? Do you need to focus more deeply on a topic? See more detail farther away? Sharpen the image? Do you need a wider angle? How can a lens help you clarify the situation?", "see also": ["Prism", "Altitude"], "tags": ["How to think about this?", "What are the opportunities?", "What are the risks?", "What is it?", "What is the problem?", "What matters?", "What’s going on?", "What’s important?"], "url": "https://visualframeworks.com/lens/", "summary": "Use this representation when you want to think about different perspectives, gain clarity, and understand the nuances of a situation by visualizing it as looking through different lenses."}, {"name": "Leverage", "id": "CORE-052", "description": "A lever is a simple machine that allows you to gain a mechanical advantage as you transfer force. A crowbar is a simple lever that allows you to crack things open or lift heavy objects.  Leverage is any activity that gives you a force-multiplier advantage. Anything that gets you a maximum outcome for minimal effort. In other words, when you get more out of something than you put into it, you have leverage.  Is there an area in the situation where you can create leverage? Where can you apply minimal effort to get an outsize return? How might you do that?", "see also": ["Gears", "Momentum", "Balancing tradeoffs", "Impact"], "tags": ["How does it work?", "How to do it?", "What are the options?", "What can we do?", "What is the plan?", "What is the problem?", "What needs to change?"], "url": "https://visualframeworks.com/leverage/", "summary": "The concept of leverage can be used in situations where we aim to achieve maximum output with minimal input by exploiting a specific area or mechanism, much like using a lever to lift heavy objects with lesser effort."}, {"name": "Line chart", "id": "CORE-053", "description": "A line chart plots a numerical value against another variable, usually time. Line charts are a great way to track progress. Two maxims can be helpful here Progress, not perfection. It’s easy to punish yourself when you don’t meet your plans and targets. But what’s really important is progress. Whatever you’re measuring, is it better than yesterday? If you’re making consistent progress, that matters a lot more than how fast you’re going. It’s easy to go too fast and burn yourself out, but slow and steady wins the race. The trend is your friend. You will never be perfect, and you probably won’t hit your goal every day. Life gets in the way. But a line chart gives you a way to see the longer-term trends. If the line is generally moving up and to the right, you’re on course. What are the areas you most want to make progress on? Can you make a line chart and track your progress over time?", "see also": ["Coordinates", "Bar chart", "Timeline", "Highs and lows"], "tags": ["Abstract", "How are we doing?", "How do we track progress?", "How many or how much?", "Measurement", "Time", "What do we measure?", "What matters?", "What’s important?"], "url": "https://visualframeworks.com/line-chart/", "summary": "A line chart is used to plot numerical values against another variable like time, and is an effective tool for tracking progress, understanding trends, and measuring improvements over time, often applied in situations where progress or change is more significant than perfection or speed."}, {"name": "Map", "id": "CORE-054", "description": "A map is one of the most fundamental methods for representing information in visual space. A map can be a literal depiction of physical space, with actual correspondence to real places, like rooms, hallways, rivers and roads. A map can be conceptual, laying out regions, landmarks and paths of a thinking space, like a mind map. A map can also delineate an emotional or metaphorical space, like a map of your heart, showing your loves, passions, pains and scars.  How would you characterize the situation you’re in right now? Is it a physical space, a conceptual space, or an emotional space? Can you make a map to help you make sense of the space and navigate more effectively?", "see also": ["Coordinates", "Grid", "Pain map", "Gain map", "<PERSON><PERSON>", "Mind map", "Tube map", "Hub and spokes"], "tags": ["Hands", "How can we connect?", "How did we get here? Where are we going?", "How to think about this?", "How will we get there?", "Literal", "What can we do?", "Where are we going?", "Where are we?"], "url": "https://visualframeworks.com/map/", "summary": "Use this representation when you need to visualize and make sense of either physical, conceptual, or emotional information, or when you need to plan or evaluate pathways within a specific situation, be it real or abstract."}, {"name": "<PERSON><PERSON>", "id": "CORE-055", "description": "A maze is a puzzle, a problem, a confusing, intricate, complicated network of passages, twists and turns, and dead ends. If you can make it through the maze, you might reach your goal, but it’s easy to become lost and bewildered.  When the Greek hero <PERSON><PERSON> entered the maze in search of the <PERSON><PERSON>ur, he brought a ball of thread with him and unwound it,as he went, so he could follow the thread to find his way out. When <PERSON><PERSON> and <PERSON><PERSON><PERSON> went into the woods, they left a breadcrumb trail for the same reason.  If you’re lost in a maze, you can find your way out by keeping your hand on one wall and keeping it there until you get out. It may be a very long path, but it will get you out of the maze.  You can also navigate a maze if you can get above it somehow. A maze is confusing from the inside, but with a birds-eye view it becomes easier to solve?  Are you facing a lot of complex, contradictory, or puzzling choices? Are you lost in a maze of complexity and confusion? How might you find your way out? Is there a thread you can follow? A breadcrumb trail? A way to get above it or view it from a different angle?", "see also": ["Mess", "Fog", "Puzzle", "Gap"], "tags": ["Heart", "How did we get here? Where are we going?", "<PERSON><PERSON><PERSON>", "What are the options?", "What are the risks?", "What can we do?", "What is the problem?", "What to do?", "What’s going on?", "What’s next?", "What’s the story?", "Where are we going?", "Where are we?"], "url": "https://visualframeworks.com/maze/", "summary": "Use idea visualization and abstract thinking to navigate and make sense of intricate problems, complex choices and scenarios, much like finding your way through a maze; these tools can help simplify the complex, discover new perspectives, and guide strategies towards your goal."}, {"name": "Measurement", "id": "CORE-056", "description": "<PERSON> famously said ''Not everything that counts can be counted, and not everything that can be counted counts.'' Numbers aren’t everything, but numbers and math are critically important in business, science and life, especially if you want to track progress against others, or over time. Some things, like money, time, temperature, and distance, have clear quantitative values, making them easier to count. Other things, like the quality of a product or service, happiness, satisfaction, depression, pain, are much harder to measure. Nevertheless you can use numbers to measure such intangibles by asking people to rate them on a scale. For example, the number of stars you might give when rating a restaurant or product. By finding a way to measure the things that matter to you, you can gain more insight into how you are doing relative to others or over time. What matters most to you right now? How might you measure it? Can you track progress over time?", "see also": ["Line chart", "Bar chart", "Coordinates", "Grid", "Regulation", "Countdown", "2×2 matrix"], "tags": ["Abstract", "How do we measure?", "How many or how much?", "Measurement", "Quantify", "Space", "Time", "What do we measure?", "What matters?", "What’s important?"], "url": "https://visualframeworks.com/measurement/", "summary": "Use this representation when you want to understand the importance of numbers and measurements in various aspects of life, and how they can help track progress and gain insights."}, {"name": "<PERSON><PERSON>", "id": "CORE-057", "description": "A menu is a curated list of items offered in a restaurant. More generally, a menu is any list of options that has been thoughtfully designed and displayed, like the menu on a website.  A menu is a kind of interface between the diner and the kitchen. It sets the tone for the interaction, describes what choices are available, and ensures that customers order something that the kitchen has the stocks and skills to deliver. A good menu offers a rich variety of options without becoming so cluttered it becomes overwhelming.  Can you create a menu that improves your “customer interface,” whatever that might be? How can you set the right tone for interaction? How might you create the right mix of variety and simplicity to deliver a great experience?", "see also": ["Crossroads", "Coordinates", "Grid", "Pros and cons", "Balancing tradeoffs"], "tags": ["Decisions", "How many or how much?", "How should we choose?", "How to decide?", "What are the options?", "What can we do?"], "url": "https://visualframeworks.com/menu/", "summary": "Use this representation when you need to design an organized selection of choices, options or steps, in a structured layout that assists decision-making, balances variety with simplicity, and facilitates effective interactions or experiences, like a menu in a restaurant or website."}, {"name": "Mess", "id": "CORE-058", "description": "Sometimes your situation is simply a mess; a tangled hairball of complexity and confusion.  Tame problems are solvable. It may not be easy or simple, but a tame problem will ''lie still'' while you work on it. For example, a problem in a mechanical device, like a car. You can solve it by breaking it down into its constituent parts and analyzing the system.  Wicked problems are complex, fluid, and adaptive. The problem is slippery and hard to define because it’s in constant flux, and attempts to solve it can shift the problem, change it, or even make it worse. For example, a problem involving war, refugees, multiple cultures and political borders. A wicked problem can’t be solved, but it can be improved by making small, adaptive moves and learning as you go.  A mess is a convoluted cluster of interconnected and interwoven problems, a problem system made up of problem systems. A mess is like a complex math equation. Before you can attempt to address it, you must make it more legible, by finding ways to unravel it and resolve its complexities.  Are you in the middle of a mess? A tangled hairball of complexity? How might you begin to untangle it and make it legible enough to think about a next step?", "see also": ["Mind map", "Causal loops", "Concept map", "Fog", "<PERSON><PERSON>"], "tags": ["Abstract", "Head", "Heart", "How does it work?", "<PERSON><PERSON><PERSON>", "Space", "What are the risks?", "What is it?", "What is the problem?", "What to do?", "What’s going on?", "What’s inside?"], "url": "https://visualframeworks.com/mess/", "summary": "Use idea visualization and abstract thinking when dealing with a 'mess', a complex, interconnected, and interwoven system of problems that require clarifying and disentangling to formulate an actionable next step."}, {"name": "Milestones", "id": "CORE-059", "description": "Literally, milestones are stones set beside a road to show the distance to or from a particular point. A milestone can be any important point in a development process. When you start a project you might set up some project milestones to mark the points where significant phases of the project are completed and a new stage begins.  Milestones give a project a sense of purpose and direction. They can help keep you on track. They make it easier to note and report progress. They give you a feeling that you’re moving forward, and keep people motivated, even when the road seems long. Reaching an important milestone is a moment for celebration.   Do you have a big project that you’re having trouble getting traction on? Could you break it up into phases, and set up milestones along the way, to keep you on track and motivated?", "see also": ["Path", "Journey", "Project dependencies", "Measurement"], "tags": ["How are we doing?", "How did we get here? Where are we going?", "How do we track progress?", "How does it work?", "How will we get there?", "Measurement", "What do we measure?", "When?", "Who will do what? By when", "Who will do what? By when?"], "url": "https://visualframeworks.com/milestones/", "summary": "Use idea visualization and abstract thinking to represent the concept of 'Milestones' when needing to measure and manage the progress of a significant task or project, and to motivate and guide those involved in its completion."}, {"name": "Mind map", "id": "CORE-060", "description": "A mind map is a way to take knowledge and ideas in your head and make them explicit in a diagram. Typically it takes the form of a tree, as if viewed from above. Start by drawing a ''root'' node, where you write the concept or question you want to think about and draw a circle around it. From there, draw ''branches'' to connect the root to related concepts and ideas. As the diagram radiates outward, you can label branches and create more circles to represent ideas and connections.  When a question or problem is so big that you don’t know where to start, a mind map is a great way to begin your exploration. It will help you see discover links and connections, and break the issue down into more manageable parts.  Are you at the very beginning of a complex project? Are you trying to make sense of a mess? Can you make a mind map to help you make sense of it?", "see also": ["Org chart", "Mess", "Fog", "Stepping stones", "Decision tree"], "tags": ["How does it work?", "How to think about this?", "What are the options?", "What can we do?", "What do we know?", "What is it?", "What is the plan?", "What is the problem?", "What’s going on?"], "url": "https://visualframeworks.com/mind-map/", "summary": "Use a mind map to explore a complex project, make sense of a messy situation, discover connections, break down problems, and generate ideas when you don't know where to start."}, {"name": "Momentum", "id": "CORE-061", "description": "Momentum is a word for ''body in motion.'' Momentum is a product of mass and speed, that is, how much stuff and how fast it is moving. A large object moving slowly, like a truck, can have the same momentum as a small object moving fast, like a bullet.  An object’s momentum is equal to the force needed to stop it.  Anything that’s moving has momentum. A company, a project, a process, a relationship. The bigger it is, and the longer it’s been in motion, the harder it is to stop it or change direction.  A 20-year relationship, with all its embedded habits and routines, is harder to stop or shift to a new direction than a brand new one. A large company with thousands of employees can have the momentum of an oil tanker, while a startup with 20 people might be as agile as a sailboat.  When starting something new, momentum is low. The smaller it is, the easier it will be to get it going. The bigger it is, the harder it is to move, at first. Likewise, when something big is moving fast, it’s equally hard to stop it.  Do you need to start something, stop something, or change its direction? How big is it? How long has it been moving? How much, how fast? How much force will it require to make the change?", "see also": ["Flywheel", "Pen<PERSON><PERSON>", "Escape velocity", "Solar system", "Impact", "Tipping point", "Snowball effect", "Uphill climb"], "tags": ["Heart", "How did we get here? Where are we going?", "How do we track progress?", "How to do it?", "How to think about this?", "How will we get there?", "<PERSON><PERSON><PERSON>", "Space", "Time", "What are the opportunities?", "What can we do?", "What’s going on?", "Where are we going?"], "url": "https://visualframeworks.com/momentum/", "summary": "Use this representation when analyzing the impact of size, speed, and duration on the ease or difficulty of initiating, changing, or stopping a task, project or relationship."}, {"name": "Mountain climbing", "id": "CORE-062", "description": "When asked ''why climb a mountain?'' Mountaineer <PERSON> famously answered ''because it is there.'' A mountain stands as a challenge, a call to adventure. It promises just about everything that makes life worth living: physical and mental challenges, danger, thrills, lofty peaks, placid meadows, dazzling views.A mountain-climbing expedition requires planning, equipment, maps and gear, and a team with a shared goal. A business, development process, or project can be like a mountain-climbing expedition. What is your big goal? How will you know when you get there? Who is on the team? What equipment, maps and gear do you need? What is your plan? Have you considered worst-case scenarios? What is your contingency plan if things go wrong?", "see also": ["Journey", "Deep dive", "Milestones"], "tags": ["Heart", "How do we grow?", "How do we track progress?", "How will we get there?", "<PERSON><PERSON><PERSON>", "Time", "What can we do?", "What matters?", "What’s important?", "Who will do what? By when?"], "url": "https://visualframeworks.com/mountain-climbing/", "summary": "This representation should be used when trying to visualize and abstractly think about achieving a big goal, planning, teamwork, challenges, and contingency plans in a business, development process, or project by using mountain climbing as a metaphor."}, {"name": "Org chart", "id": "CORE-063", "description": "An org chart is a diagram that shows how an organization is structured internally. An organization doesn’t have to be a company, it can be anything at all, when you want to think about how it is organized.  The branching tree structure is a great tool for analyzing or mapping any system or structure: it can be used to break down any system into its constituent parts, showing the relationships between the parts and the whole.  Do you need to think about how the parts of something relate to the whole? Can you diagram it using an org chart?", "see also": ["Peeling the onion", "Tree", "Mind map", "Root causes"], "tags": ["Abstract", "Head", "How does it work?", "How to think about this?", "Space", "Structure", "What is it?", "What is the problem?"], "url": "https://visualframeworks.com/org-chart/", "summary": "Use an org chart representation when you want to analyze or map the internal structure of an organization or any other system, to understand the relationships between its constituent parts and the whole."}, {"name": "<PERSON>/Gain", "id": "CORE-064", "description": "Two approaches are common to all forms of life: seek reward, avoid threat. Any species that fails to do both will not survive. At a biological level, we seek food, warmth, shelter, the security of a group. We also seek higher-level rewards like self-actualization, status among our peers, love and belonging, and we avoid threats to our emotional well-being, like shame, or being left out.  What rewards are you seeking right now? What threats are you avoiding?", "see also": ["Pain map", "Gain map", "Gauntlet", "Doom loop", "Virtuous cycle"], "tags": ["Compared to what?", "Feeling", "Heart", "Pain/gain", "Why should I care?"], "url": "https://visualframeworks.com/pain-gain/", "summary": "Use this representation when you want to understand the fundamental motivations and desires of all living beings, including seeking rewards and avoiding threats, both at a biological and higher-level psychological level."}, {"name": "Pain map", "id": "CORE-065", "description": "If you’re creating a product or service, the better you understand the problems you are solving, the better your offering will be. Mapping out customer pains is a good way to develop that deep understanding and it can also be a powerful way to start a conversation with customers. You can also map out the pain points in your own life to consider the things that can motivate you to change. What’s on your pain map? How well do you understand your customer’s pain map? How would you know?", "see also": ["<PERSON>/Gain", "Gain map", "Gauntlet", "Doom loop", "Virtuous cycle", "Conflict", "Map"], "tags": ["Heart", "<PERSON><PERSON><PERSON>", "Pain/gain", "Space"], "url": "https://visualframeworks.com/pain-map/", "summary": "Use the Pain Map representation when you want to better understand the problems your product or service solves, start a conversation with customers, consider motivators for change, and gain a deep understanding of customer pain points."}, {"name": "Path", "id": "CORE-066", "description": "Literally, a path is a trail that is created when people or animals follow the same course over time. A path can be any route or set of steps that gets you from here to there.  In fairy tales, the path is usually the safest course. <PERSON> <PERSON> was warned to stay on the path. In The Wizard of Oz, the yellow brick road was the path to follow if you wanted to meet the wizard. Straying off the path leads to uncertainty and potential danger.  What path are you on today? Where does it lead? Is it the right path for you? Do you know where you want to go? Can you find the right path?", "see also": ["Journey", "Milestones", "Project dependencies", "Highs and lows", "Routine", "Timeline"], "tags": ["Heart", "How did we get here? Where are we going?", "How does it work?", "How to do it?", "<PERSON><PERSON><PERSON>", "Process", "Time", "When?", "Where are we going?", "Where are we?", "Who will do what? By when", "Who will do what? By when?"], "url": "https://visualframeworks.com/path/", "summary": "Use this representation when discussing sequences, progressions, plans or workflows, when you need to illustrate a journey or process over time, or when using metaphors for strategic guidance and planning."}, {"name": "Peeling the onion", "id": "CORE-067", "description": "Some situations are layered. You can only delve in one layer at a time. You peel the first layer, only to find a second layer. To peel the onion is to approach a problem slowly, peeling away one layer at a time, to gain a thorough understanding of what you’re dealing with.  Peeling the onion is a process of asking questions until you reveal the truth at the core. You can peel your own onion, delving into your own motivations and emotions. You can peel the onion when analyzing a problem, conducting a job interview, or when getting to know someone in an intimate relationship.  What’s going on? Is the situation more than it appears on the surface? How can you peel the onion?", "see also": ["Root causes", "Org chart", "Deep dive", "Decision tree", "Layer cake", "Puzzle", "Pyramid", "Altitude"], "tags": ["Cause and effect", "Heart", "<PERSON><PERSON><PERSON>", "What do we know?", "What is it?", "What is the problem?", "What’s going on?"], "url": "https://visualframeworks.com/peeling-the-onion/", "summary": "This representation is used to symbolize the process of thoroughly understanding a situation or problem by gradually uncovering its layers, whether in personal introspection, analyzing a problem, conducting a job interview, or building an intimate relationship."}, {"name": "Pen<PERSON><PERSON>", "id": "CORE-068", "description": "A pendulum is a simple mechanical device made of a suspended weight that oscillates around a central point of equilibrium. The motion of a pendulum is one of the simplest types of oscillations. Pendulums are used in clocks, as well as instruments that measure gravity and seismographic activity.  Pendulums are used in clocks because they keep a regular rhythm. A metronome, used to set a pace or tempo when practicing music, is a type of pendulum. A swing on a playground is another kind of pendulum.  Think of the swing on a playground. In order to get started you need to push off to start the oscillation. Once you are swinging, you need to pump your legs to keep the swing going, or eventually inertia will make it stop. When you want to stop, you might scrape your feet on the ground. This is called dampening.  Mood swings are another kind of oscillation that are harder to control than the motion of a swing. Sometimes a minor setback can be enough to swing you from an even-keel flow into doom-and-gloom, or a simple moment of praise or appreciation can make your entire day.  Life can swing you in many different ways. Oscillation, like you feel on a swing, can be enjoyable when you feel like you are in control. When oscillation is not under your control it can be frightening.  Are you experiencing oscillation in your life or work? Is it a good thing or a bad thing? If it’s good, can you keep it going, or even increase your momentum? If it’s bad, can you slow or stop the oscillation by dampening it somehow? If you can’t change it, can you find a way to endure it, or even enjoy it?", "see also": ["Momentum", "Flywheel", "Solar system", "Infinite loop", "Highs and lows", "Causal loops", "Cycle"], "tags": ["Heart", "How did we get here? Where are we going?", "How does it work?", "How to think about this?", "<PERSON><PERSON><PERSON>", "What is the problem?", "What’s going on?", "Where are we going?", "Where are we?"], "url": "https://visualframeworks.com/pendulum/", "summary": "Use this representation to visualize or explain the concept of oscillation in mechanical, emotional, or situational contexts and to discuss its control, implications and relation to other cyclic, rhythmic, or loop concepts."}, {"name": "Pipes and tanks", "id": "CORE-069", "description": "Pipes and tanks are good for moving fluid around and storing it for later use. You can see them in plumbing, oil refineries, chemical manufacturing, and water systems.  Pipes and tanks can be useful for thinking about anything that flows and can be stored or “banked” for later use. Many fields, like economics, business, and accounting distinguish between stocks (like water held in tanks) and flows (like water flowing through pipes).   A stock is a quantity measured at a specific point in time. A flow is an exchange where money flows from one party to another.  For example: your kitchen pantry is stocked with food. When you take it out to cook or make a snack, you’re creating a flow. In a relationship you build up a stock of trust and goodwill over time. When you ask for a favor, you’re making a withdrawal from that account.  Stocks can refer to anything that’s stored. It can refer to money in a bank, an investment portfolio, inventory in a warehouse, or information stored in a database. Flows refer to any kind of deposits and withdrawals, like earned interest, dividends, items shipped and received from a warehouse, or data being input or accessed and used by customers.  Can you look at your situation in terms of stocks and flows? How are the stocks stored, and how do flows move through the system? What stocks do you have available to you? Can you make more deposits? What can you withdraw?", "see also": ["Flowchart", "Causal loops", "Tube map"], "tags": ["Heart", "How does it work?", "How to think about this?", "<PERSON><PERSON><PERSON>", "Space", "Streams and flows", "Time", "What do we know?", "What do we measure?", "What’s going on?"], "url": "https://visualframeworks.com/pipes-and-tanks/", "summary": "Use the Pipes and Tanks representation when understanding or explaining situations wherein elements (stocks) are stored and then moved or used (flows) over time, as seen in many fields including economics, business, and accounting."}, {"name": "Prism", "id": "CORE-070", "description": "A prism refracts light, dispersing a single beam into a rainbow of color, revealing the colors of the spectrum. Thus, a prism distorts, it separates, and it also reveals things that are hidden.  To look at something through a prism is to find a viewpoint that helps you see it and understand it in a fundamentally different way.  For example, when you look at events in the past through the prism of memory, you may see things in a fundamentally different way. Perhaps some things are distorted while others are clarified.   When you adopt a new philosophy or religion, the prism by which you view the world, and understand your place in it, can change dramatically.   Can you find a prism or perspective that might help you see your situation in a fundamentally new or different way? Can the situation be separated or dispersed to reveal things that are hidden?", "see also": ["Lens", "Iceberg", "Subduction"], "tags": ["How does it work?", "What are the options?", "What is it?", "What is the problem?", "What’s going on?", "Who are we?"], "url": "https://visualframeworks.com/prism/", "summary": "This representation should be used when looking for a different viewpoint or perspective that can help in understanding something in a fundamentally new or different way."}, {"name": "Project dependencies", "id": "CORE-071", "description": "Any project involving two or more workstreams creates dependencies — relationships between tasks based on how they are coordinated or sequenced. For example, you can’t start construction until you have a plot of land to build on, permits, and plans. You can’t bill a customer until you have collected their information. You can’t write a book report until you have read the book, and so on. The more complex the project, the more dependencies you are likely to have. Dependencies are critical links that you depend on to get the project completed. Handoffs, where important information and materials are transferred from one person or place to another, creates a possibility for things to go wrong. Good communication is important. If everyone understands what needs to be done and how they fit, they can help you keep everything on track. Here are some things that can help you gain perspective on a big project: Find the dependencies. Reduce the number of dependencies, if you can. Improve communication. The more people are communicating, the fewer things will fall between the cracks. On a complex project it can help to visualize the workstreams and dependencies, not just for yourself, but so everyone can be on the same page about who needs to do what, by when.  Can you visualize the work streams and dependencies? Can you organize work streams into swim lanes? Can you identify high-risk points, like handoffs? How can you plan in advance to mitigate these risks?", "see also": ["Milestones", "Journey", "Flowchart", "Path", "Routine"], "tags": ["Abstract", "Head", "Space", "Spacetime", "Time", "When?", "Who will do what? By when", "Who will do what? By when?"], "url": "https://visualframeworks.com/project-dependencies/", "summary": "Use this representation for managing complex projects involving multiple workstreams where visualizing dependencies, workstreams, and potential risks is essential for effective communication, coordination, planning, and reducing the likelihood of complications."}, {"name": "Pros and cons", "id": "CORE-072", "description": "In a 1772 letter to a friend facing a difficult dilemma, <PERSON> was the first to articulate the now-famous pro-and-con list as an aid for decision-making. He described it like a balance sheet or scale, assigning relative weights to the thoughts on either side of the page.  Are you facing a difficult decision? Can you list out the pros and cons, and assign relative weights to them to make your choice less perplexing?", "see also": ["Measurement", "Conflict", "Pain/gain", "Balancing tradeoffs"], "tags": ["Abstract", "Decisions", "Head", "How should we choose?", "How to decide?", "How to think about this?", "Thinking", "What are the options?"], "url": "https://visualframeworks.com/pros-and-cons/", "summary": "Use this representation when facing a difficult decision, as it aids in visualizing and weighing the beneficial and unfavorable aspects of each option to simplify decision-making."}, {"name": "Pushing the envelope", "id": "CORE-073", "description": "In aeronautics, ''the envelope'' refers to performance limits that cannot be safely exceeded. To push the envelope is to approach or extend the boundaries of what’s possible. Pushing the envelope is inevitably perceived as dangerous or risky because it means going into unknown territory, ''where no one has gone before.'' It’s important to test limits and boundaries once in a while. For example, running a mile in four minutes was once considered impossible. Then, in 1954, <PERSON> ran a mile in 3 minutes 59 seconds. Since then, the four-minute mile barrier has been broken more than a thousand times.  Many things that used to be considered impossible are now commonplace. Flight. Space flight. Photos without film. Self-driving cars.  What was impossible yesterday may be possible today. The only way to know for sure is to test the boundaries and see. Are people telling you that something is impossible? Can you design an experiment to test that belief?", "see also": ["Growth", "Mountain climbing", "Airplane", "Altitude", "Deep dive", "Impact", "Escape velocity"], "tags": ["Heart", "How did we get here? Where are we going?", "How to do it?", "How to think about this?", "<PERSON><PERSON><PERSON>", "Space", "What are the opportunities?", "What are the risks?", "What can we do?", "What’s important?"], "url": "https://visualframeworks.com/pushing-the-envelope/", "summary": "Use this representation in the context of exploring and challenging limits or boundaries, going beyond what is typically deemed as possible, and innovating in a way that may involve risks, but with potential opportunities for groundbreaking discoveries or advancements."}, {"name": "Puzzle", "id": "CORE-074", "description": "A puzzle is a question or problem that tests your ingenuity. A conundrum. There are different kinds of puzzles: jigsaw puzzles, crossword puzzles, word searches, sudoku and more.  Life itself can be a puzzle sometimes. When you’re working on a puzzle, you look for clues and try out different combinations. Most of the time you will make some wrong turns before you get it right. Sometimes you need to take a few steps back before you can go forward. For example, you might have a word in a crossword puzzle that feels right and fits the clue but happens to be wrong.  Some puzzles you will never solve, and some puzzles are even unsolvable.  Are you trying to solve a puzzle right now? What are the pieces? How do they fit? Can you try out different combinations?", "see also": ["<PERSON><PERSON>", "Org chart", "Exploded view", "Fog", "Mess", "Peeling the onion"], "tags": ["How can we connect?", "How does it work?", "How to think about this?", "What is it?", "What is the problem?", "What’s going on?"], "url": "https://visualframeworks.com/puzzle/", "summary": "Use the puzzle metaphor to represent problems or situations that require experimentation with various combinations, error-prone paths and occasional regressive steps to reach a solution, embodying the essence of challenge, complexity and intrigue."}, {"name": "Pyramid", "id": "CORE-075", "description": "A pyramid is a human-made mountain: a monumental structure with a triangular or square base and sides that slope to a point at the top. The three pyramids at Giza, in Egypt, are some of the most ancient structures in the world. Pyramids are layered structures; they were constructed starting with a large square base and building one layer at a time until they reached the top. The large square base creates a very stable structure.  Like a mountain, a pyramid is often used as a metaphor for the stages in human development: As you rise in the pyramid, you come closer and closer to maturity and self-actualization.  A pyramid can also symbolize a work structure, like the upside-down tree you see in an org chart. The boss sits at the top of the pyramid and workers are arranged in a hierarchy below.  A pyramid scheme, also known as multi-level marketing, is a kind of pyramid constructed from the top down. The founders sit at the top of the pyramid, and recruit people to inhabit the layers below them. Each new recruit is required to generate sales by recruiting people to work below them in the pyramid.  Author and consultant <PERSON> coined the term ''pyramid principle'' in her book of the same name, to describe a writing method where a main topic sentence sits at the top of the pyramid, and the essay or story builds by progressively adding detail, layer by layer, until the right level of granularity is achieved.", "see also": ["Iceberg", "Layer cake", "Org chart"], "tags": ["How do we grow?", "How to think about this?", "Structure", "What is it?", "What is the problem?", "What matters?", "What’s going on?", "What’s important?"], "url": "https://visualframeworks.com/pyramid/", "summary": "Use this representation when you want to understand the concept of a pyramid as a physical structure, a metaphor for human development, a work structure, a pyramid scheme, or a writing method."}, {"name": "Recipe", "id": "CORE-076", "description": "A recipe is a list of ingredients and a set of instructions that you can follow to achieve a particular outcome. Often a recipe will include a picture of what a ''good'' outcome looks like. Recipes are not just for food: you can make a recipe for almost anything. For example, Gamestorming is a book of recipes for having for engaging, productive meetings.  The advantage of a recipe is that it can help you ''turn your magic into science.'' That is, it allows you to take something you do that seems magical, intuitive and mysterious to others, and create a path that others can follow. A recipe makes your magic shareable and scalable.  Recipes are a great scaffolding for learning new things. If you want to learn how to cook, for example, you might start by following other people’s recipes. Later, as you learn new skills and begin to master your crafts, you might begin to deviate from other people’s recipes and start to create your own.  Can you create recipes to turn your magic into science? Have others created recipes that you can follow to learn a new skill?", "see also": ["Flowchart", "Routine", "Path", "<PERSON><PERSON>", "Layer cake"], "tags": ["Hands", "Heart", "How does it work?", "How to do it?", "Literal", "<PERSON><PERSON><PERSON>", "Practical"], "url": "https://visualframeworks.com/recipe/", "summary": "This representation is useful when you want to turn a magical, intuitive process into a step-by-step guide that others can follow and learn from, making it shareable, scalable, and a great scaffold for learning new things."}, {"name": "Regulation", "id": "CORE-077", "description": "A thermostat helps regulate the temperature of your environment. When it gets too hot, it will turn on the air conditioning and start the cooling process. If it gets too cold, it will start the heater. It senses the environment and reacts by switching devices on or off to keep the temperature at the ''just right'' level. Many things require regulation to maintain equilibrium. An amplifying feedback loop can easily spiral exponentially out of control if nothing is there to balance it.  One example of a natural amplifying loop is soil erosion. Soil begins to erode when there are no plants and roots to keep it in place. As the soil is washed away, ruts and channels form that make it easier for water flow, the more soil is washed away, the easier and faster water flows. When a feedback loop like this gains momentum, it can be harder and harder to stop.      In the German fairy tale ''The Sorcerer’s Apprentice'' (Played by <PERSON> in Fantasia), a wizard’s apprentice, tired of fetching water with a pail, enchants a broom to do the work for him. The floor soon floods with water, but the apprentice doesn’t know how to stop the enchanted brooms because he doesn’t know the spell. He tries chopping the broom into pieces with an axe, but each piece becomes a broom and they only move faster. He has created an out-of-control amplifying loop. Balancing loops help keep amplifying feedback in check. For example, activities like planting vegetation, creating terraces or dams can create balancing loops for erosion. The wizard’s return ended the apprentice’s problem, and he learned a valuable lesson about magic.         Even good things can rapidly spin out of control. For example, a product or service that goes viral can quickly outstrip your ability to deliver, resulting in unhappy customers and negative reviews. In such cases you might introduce a balancing loop like a first-come, first-serve waiting list, or by raising prices until demand slows to a level you can reliably meet. Does your situation feel scary and out of control? Is the problem getting worse? Are things growing so fast that you can’t keep up? Can you create a balancing loop to bring things back to equilibrium?", "see also": ["Infinite loop", "Pen<PERSON><PERSON>", "Flywheel", "Causal loops", "Doom loop", "Virtuous cycle", "Cycles", "Escape velocity"], "tags": ["Heart", "How do we measure?", "How do we track progress?", "How does it work?", "How to think about this?", "<PERSON><PERSON><PERSON>", "What can we do?", "What do we know?", "What do we measure?"], "url": "https://visualframeworks.com/regulation/", "summary": "Use the 'Regulation' representation when you're experiencing situations that seem out-of-control, rapid growth you can't keep up with, or worsening problems, and want to understand how to create balancing mechanisms to restore equilibrium, much like a thermostat regulates temperature or a dam controls erosion."}, {"name": "Root causes", "id": "CORE-078", "description": "The fishbone diagram, also known as the <PERSON><PERSON><PERSON> diagram, was designed by Dr. <PERSON><PERSON><PERSON>, a management theorist and engineering professor who introduced the idea of quality circles to organizations in Japan.  In a fishbone diagram, the issue or problem to be explored is shown as the fish’s head, with possible causes spread out to the left in a way that resembles fishbones.  A complementary concept, called ''five whys'' was developed by <PERSON><PERSON><PERSON> of Toyota Motors, during the evolution of the famous Toyota Production System, which revolutionized the auto industry between 1948 and 1975. The idea is that you need to ask ''why?'' at least five times before you can uncover the true root cause of a problem.  Are you seeing a recurring problem? Are you struggling to understand what is causing it? Can you draw a fishbone diagram and use the  ''five whys'' method to discover the root causes of the problem?", "see also": ["Org chart", "Decision tree", "Peeling the onion"], "tags": ["Cause and effect", "How did we get here? Where are we going?", "How does it work?", "How to think about this?", "What can we do?", "What do we measure?", "What is the problem?", "What needs to change?", "What’s going on?", "Why does it matter?"], "url": "https://visualframeworks.com/root-causes/", "summary": "This representation, known as a fishbone diagram or <PERSON><PERSON><PERSON> diagram, is used when there is a recurring problem and a need to understand and identify the root causes through the application of the 'five whys' method."}, {"name": "Routine", "id": "CORE-079", "description": "A routine is a series of things you do at a regular time. A habit. The order in which you do things. A routine can also be a series of instructions in a computer program. Once you start a routine, it usually proceeds in an unthinking way until the routine is completed. Have you ever started to drive somewhere, got lost in a train of thought, and ended up somewhere you didn’t consciously intend to go? You probably unintentionally slipped into a routine. Routines are the rhythms of daily life: they give your life and work structure, regularity, and predictability. They give you a sense of control, and reduce stress and anxiety. A recurring task takes up less cognitive energy. Once you have established a habit, you can do it automatically, freeing your mind up to think about other things. Healthy and productive routines not only make life feel more regular and consistent, but also create a virtuous cycle of improvement. A small change or adjustment to a daily routine, like a daily walk or run, can have a great positive impact over the long term. A routine can help you get in the groove, but spend too much time in a groove and it becomes a rut. If you are too inflexible about your routines, they can become a prison, making it difficult to make spontaneous plans, socialize with others, and have a good time. What kind of life do you want? What kind of workplace do you want? Can you establish consistent routines that will make you happy, healthy, and productive?", "see also": ["Path", "Journey", "Recipe", "Flowchart", "Milestones", "Project dependencies", "Highs and lows", "Timeline"], "tags": ["Heart", "How did we get here? Where are we going?", "How does it work?", "How to do it?", "<PERSON><PERSON><PERSON>", "Process", "Time", "When?", "Where are we going?", "Where are we?", "Who will do what? By when", "Who will do what? By when?"], "url": "https://visualframeworks.com/routine/", "summary": "This representation is used when visualizing and understanding the concept of routines, their importance in daily life and work, their benefits and potential drawbacks, and their role in creating structure, predictability, and productivity."}, {"name": "Sailboat", "id": "CORE-080", "description": "Sailboats, like airplanes and other vehicles, are simple machines designed to get us from here to there. A sailboat uses the natural power of wind and water to make it go.   A sailboat can be a wonderful metaphor for thinking about a business venture or project. When sailing, you rely on wind and weather, and your skills in using them to your advantage. The sun and stars, compass, and nautical charts guide you on your way. A rudder and keel helps you stay upright and on course. The weather can change without warning. Dangers can lurk under the water.  Where is your sailboat going? What is your North Star that guides your decision-making? What is the wind that drives you forward? What are the weather patterns you should be prepared for? What should you be looking for as you scan the horizon? What might lurk under the surface?", "see also": ["Airplane", "Iceberg", "Journey", "Deep dive"], "tags": ["Heart", "How did we get here? Where are we going?", "How do we grow?", "How does it work?", "How to think about this?", "<PERSON><PERSON><PERSON>", "What are the options?", "What is the problem?", "What’s going on?", "Where are we going?", "Where are we?", "Who are we?", "Who will do what? By when?"], "url": "https://visualframeworks.com/sailboat/", "summary": "This representation is used to encourage abstract thinking about any venture or project, using the metaphor of a sailboat to help visualize the factors impacting the endeavor, guide decision-making, assess potential risks, and chart the course forward."}, {"name": "Seasons", "id": "CORE-081", "description": "The celestial orbits give rise to the rhythms of earthly nature: day and night, the phases of the moon, and the seasons. Each season has its own particular character. Spring is a time for life and new growth; summer is a time of abundance and easy living; fall is a time for harvesting and increasing hardship; winter is a time for death, seclusion and hibernation. Life lies dormant until spring starts the cycle again. In the spring we plant seeds and nourish their growth. In the abundance of summer we enjoy life, pull weeds and water the plants. In the fall we reap our harvest, and in the winter we make time to reflect and think about what comes next. Life, work, projects, products and services all have their seasons. They are born, grow, enjoy times of abundance and hardship, die, and are reborn again in new and varied forms. Some industries, like fashion and book publishing, have rhythms that sync with the seasons. Others have their own cycles of abundance and hardship. The economy cycles in this way as well, from springlike boom times of economic growth, to the autumns and winters of recession/ What are the seasonal cycles in your life and work? What seeds do you plant in the spring? How do you nurture growth in the summer? What harvest do you reap in the fall? How do you reflect and plan during the cold winter months?", "see also": ["Cycle", "Water cycle", "Solar system", "Timeline", "Clock", "Calendar", "Highs and Lows"], "tags": ["Heart", "How did we get here? Where are we going?", "How do we grow?", "How does it work?", "<PERSON><PERSON><PERSON>", "Time", "What’s going on?", "When?", "Where are we going?", "Who will do what? By when", "Who will do what? By when?"], "url": "https://visualframeworks.com/seasons/", "summary": "Use this representation when interpreting cyclic processes or seasons in various aspects of life, work, or industries, and when planning or reflecting based on the characteristics of each season."}, {"name": "Snowball effect", "id": "CORE-082", "description": "Imagine a snowball rolling downhill. You might need a certain size, shape, and compactness in your snowball to get it rolling in the first place, but once you get it rolling, it gains momentum and gravity does a lot of the work. Depending on the height you start from and the slope of the hill, the snowball will get bigger and bigger, and harder to control, until it reaches the bottom. Just like an online video that goes viral, the snowball effect can be positive or negative. If it’s about something you want attention for, like a product or service, it’s great. If it’s something you’d prefer to downplay, like a customer complaint, it;s not so great. Good or bad, the bigger your snowball is, and the more it gains momentum, the harder it is to stop. Are you seeing a snowball effect? How does it work? How can you encourage ''good'' snowballs and avoid ''bad'' ones?", "see also": ["Flywheel", "Virtuous cycle", "Doom loop", "Cycle", "Momentum", "Escape velocity"], "tags": ["Compared to what?", "What are the opportunities?", "What are the options?", "What are the risks?", "What can we do?", "What is the problem?", "What needs to change?", "What’s going on?"], "url": "https://visualframeworks.com/snowball-effect/", "summary": "Use this representation when trying to understand and analyze the concept of a snowball effect, its implications, and how to foster positive outcomes and prevent negative ones."}, {"name": "Solar system", "id": "CORE-083", "description": "Our solar system includes our sun and everything that orbits around it. The force of gravity holds everything together in a regular rhythm, a symphony of movement, a celestial dance: Planets revolve around the sun, moons revolve around the planets.  Any group of people held together by shared interests or common purpose is like a solar system: A family, a team, a company, a club.  You could see the sun as the common ground or shared purpose that holds the group together. Planets might be teams or regional hubs. Moons and other satellites could represent projects or initiatives. Meteors might represent uncontrollable factors that might disrupt things.  What is the sun that holds your situation together? What are the planets that revolve around it? What are the moons and other satellites? What are the meteors?", "see also": ["Escape velocity", "Causal loops", "Cycle", "Seasons", "Pen<PERSON><PERSON>", "Momentum", "Black hole"], "tags": ["How can we connect?", "How do we grow?", "How does it work?", "How to think about this?", "What can we do?", "What is it?", "What’s going on?"], "url": "https://visualframeworks.com/solar-system/", "summary": "Use this solar system representation when conceptualizing relationships in a cohesive unit—such as a team, company, or organization—with a central focus or purpose, and when understanding the roles of its members, their projects, and potential disruptive factors."}, {"name": "Stepping stones", "id": "CORE-084", "description": "Stepping stones are rocks arranged in such a way that they can help you cross a river or stream safely, without getting wet.  Any action that moves you closer to a goal is a stepping stone. If you can’t achieve your goal in one big leap, it can be useful to break it down into a series of more manageable steps.", "see also": ["Path", "Journey", "Gap", "Bridge", "Tightrope", "Milestones", "Mind map", "Routine", "Recipe"], "tags": ["Heart", "How to think about this?", "How will we get there?", "<PERSON><PERSON><PERSON>", "What are the options?", "What can we do?", "What’s going on?"], "url": "https://visualframeworks.com/stepping-stones/", "summary": "Use this representation when breaking down a complex goal into manageable tasks or steps, symbolizing the journey to a goal where each individual action moves you closer to your objective."}, {"name": "Storyboard", "id": "CORE-085", "description": "A storyboard is a sequence of scenes that visually map out a story. Storyboards are used in filmmaking because they are a cheap, fast, and easy way to imagine how a scene might look, without incurring the time and cost of filming.  Panels in a comic book, slides in a presentation, even a quick sequence of scribbled diagrams on index cards are all ways that you can storyboard your idea.  You can use storyboards to quickly imagine anything that you’re planning or thinking about doing. Thinking it through and visualizing ''how it will look'' is a way of thinking it through before you act.  Are you thinking through various approaches and scenarios? Could you make a storyboard to make that future vision more tangible?", "see also": ["Path", "Journey", "Flowchart"], "tags": ["Hands", "How does it work?", "Literal", "Practical", "What can we do?", "What does it look like?", "Who is affected?", "Who will do what? By when", "Who will do what? By when?", "WHODO"], "url": "https://visualframeworks.com/storyboard/", "summary": "Use a storyboard representation for visually mapping out a sequence of scenes in a story, planning scenarios, and making future visions more tangible, which is especially helpful in filmmaking, presentations, and brainstorming sessions."}, {"name": "Subduction", "id": "CORE-086", "description": "Subduction is a process that happens when the tectonic plates that make up the Earth’s crust collide: one tectonic plate slides under the other.  Subduction is a mostly invisible, slow-moving but powerful process that alters the shape of continents, creates mountains, ocean trenches and volcanoes, and generates earthquakes and tsunamis.  Slow-moving, powerful processes like subduction are hard to see, but they alter the very shape of the terrain, occasionally causing major disruptive crises.   For example, the first digital camera was invented in 1975, but sales of digital cameras didn’t surpass film cameras until 2003. Emotional conflicts on a team might remain simmering below the surface, until one day they erupt.  What slow but powerful processes are creating and changing the landscape you’re navigating? Are there deep rifts or conflicts lurking beneath the surface? What kinds of earthquakes or tsunamis might they cause?", "see also": ["Impact", "Tipping point", "Momentum", "Conflict", "Layer cake", "Iceberg"], "tags": ["Forces", "How does it work?", "How to think about this?", "What is the problem?", "What’s going on?"], "url": "https://visualframeworks.com/subduction/", "summary": "Use this representation to understand and visualize slow, powerful underlying processes or conflicts that, like tectonic subduction, can reshape landscapes, organizations or relationships, and potentially cause significant disruptions or transformations."}, {"name": "Switch", "id": "CORE-087", "description": "There are many kinds of switches. What they have in common is that they tend to flip quickly from one state into another, different state.  A railroad switch, like the one shown above, can change the direction of the tracks. A light switch turns on the light or turns it off.  Your body also has switches. There are times when you make decisions, and times when your body makes them for you. Some people call them triggers — when you are triggered, your brain flips a switch, delivers a quick dose of cortisone, and you flip downward, into fight-freeze-or-flight mode. There are also triggers that flip a positive switch, delivering a dopamine rush that flips you upward into elation. The positive flip can also become an addiction.  A switch has the potential to quickly and dramatically shift a situation or system from one state to another.  What triggers you? What are you addicted to? What needs to flip or shift in your life right now? Is there a switch? Can you find or make one?", "see also": ["Crossroads", "Tipping point", "Impact", "Snowball effect", "Doom loop", "Virtuous cycle"], "tags": ["Control", "Decisions", "Heart", "How to decide?", "<PERSON><PERSON><PERSON>", "What needs to change?"], "url": "https://visualframeworks.com/switch/", "summary": "Use this representation when exploring concepts of change, decision-making, and control, particularly when wanting to visualize instances of abrupt or dramatic shifts in situations or systems."}, {"name": "<PERSON><PERSON><PERSON>", "id": "CORE-088", "description": "Tetris is a simple and absorbing video game where players sort falling blocks of different shapes as they fall toward the bottom of the screen. The goal of the game is to make the blocks fit neatly together so they are as tightly packed as possible.  Tetris is fun and engaging, so much that it can be addictive, leading to what has been called the Tetris effect: when someone spends so much time on an activity that it begins to shape their thought patterns, dreams and ideas.  Tetris can be a useful construct for thinking about the decisions you make as you make your way through life. It seems as if there’s always something that needs to be sorted, folded, arranged, packed, configured or reconfigured.  Like the falling blocks in Tetris, time is always moving forward, and we need to make the best decisions we can make in the time we have. Mistakes can accumulate and eventually overwhelm us.  What blocks are falling in front of you right now? What needs to be sorted, arranged, configured? How much time do you have to decide?", "see also": ["Puzzle", "Routine", "Countdown", "Balancing tradeoffs"], "tags": ["Heart", "How did we get here? Where are we going?", "How does it work?", "How to decide?", "How to think about this?", "<PERSON><PERSON><PERSON>", "What are the options?", "What can we do?", "What’s going on?"], "url": "https://visualframeworks.com/tetris/", "summary": "Use this representation when you need a metaphor to illustrate the concept of time management, decision-making, and dealing with life’s complexities, likening them to the dynamic and often pressuring task of ordering falling blocks in the game of Tetris."}, {"name": "Tightrope", "id": "CORE-089", "description": "Walking a tightrope is a straight, narrow, difficult, and dangerous path. When walking a tightrope you need to be extremely careful about everything you do. It’s about keeping your balance between two extremes. Stray too far in either direction and the results could be catastrophic.  What tightrope are you walking right now? How far do you have to go? How can you maintain your balance? ", "see also": ["Gap", "Bridge", "Path", "Stepping stones", "Balancing tradeoffs"], "tags": ["Heart", "How will we get there?", "<PERSON><PERSON><PERSON>", "What are the options?", "What can we do?", "What is the problem?", "What’s going on?", "What’s next?"], "url": "https://visualframeworks.com/tightrope/", "summary": "This representation should be used when needing a metaphor to describe the handling of a delicate situation or a process that requires balance between two extremes, due caution, and careful steps."}, {"name": "Timeline", "id": "CORE-090", "description": "A timeline is a chronological sequence of events arranged along a line. A timeline is useful for thinking about sequences of events. The events can be historical in nature, or you can use a timeline for planning.  What is the sequence of events? How can they be arranged in time? What came before? What comes after? ", "see also": ["Clock", "Calendar", "Path", "Routine", "Measurement"], "tags": ["Head", "How did we get here? Where are we going?", "How does it work?", "Time", "What’s going on?", "What’s next?", "When?", "Where are we going?", "Where are we?", "Who will do what? By when", "Who will do what? By when?"], "url": "https://visualframeworks.com/timeline/", "summary": "Use this representation when you need to visualize a sequence of events or processes, understand their chronological order, plan future steps, or analyze historical context and progression."}, {"name": "Tipping point", "id": "CORE-091", "description": "A tipping point is a critical threshold, like a ball precisely positioned at the top of a hill that could easily roll in either direction. The boiling and freezing points of water are both tipping points. When a system is at a tipping point, very small changes can affect it in very significant ways. For example, at most temperatures, water remains liquid and ice remains ice — stable equilibrium is maintained in either case — but when the temperature hits the freezing point, small changes in temperature are enough to change the system dramatically from one state to another. A tipping point is a time and place of instability and opportunity, where small nudges can have outsized impact. Does your situation feel like you are nearing a tipping point? What are the signs? Can you find a time and place where a small nudge can make a big difference?", "see also": ["Snowball effect", "Escape velocity", "Momentum", "Pen<PERSON><PERSON>", "Switch", "Transformation"], "tags": ["How did we get here?", "Where are we going?", "How does it work?", "How to think about this?", "What are the options?", "What can we do?", "What’s going on?", "What’s next?", "Why does it matter?", "Why now?"], "url": "https://visualframeworks.com/tipping-point/", "summary": "Use this representation when you want to understand the concept of a tipping point, which is a critical threshold where small changes can have significant effects, and explore its implications and applications."}, {"name": "Transformation", "id": "CORE-092", "description": "A transformation is a metamorphosis: a dramatic change. Not the kind of change that happens incrementally, in small steps, but fundamentally, a change from one thing into something different, like a caterpillar turning into a butterfly. For example, when you bake a cake, it starts as liquid batter, and when you bake it in the oven, it becomes a light and fluffy cake. Disruptive technologies like the web and social media can transform markets dramatically. For example, the most profitable revenue stream for newspapers used to be the classified ads section. Now that activity — buying and selling items, making romantic connections, finding jobs or hiring employees — have all moved to apps and online platforms.  Technology is always advancing, and there is always a transformation happening or on the horizon. You may also be going through a personal transformation, or thinking about one. Or it might be time to transform your company or work life. What transformations do you see around you or feel inside you? How might they change things? How can you prepare for the transition?", "see also": ["Tipping point", "Subduction", "Water cycle", "Switch", "Crossroads"], "tags": ["How do we grow?", "How will we get there?", "What can we do?", "What’s going on?", "What’s next?", "Why does it matter?", "Why now?"], "url": "https://visualframeworks.com/transformation/", "summary": "Use this representation when you want to explain or discuss fundamental, dramatic changes in various aspects like technology, personal growth, or business practices, typically associated with a significant shift or transformation from one state to another."}, {"name": "Tree", "id": "CORE-093", "description": "A tree is a powerful metaphor for life: Nourished by the sun, rain, and soil, rooted in the ground but reaching for the sky. The tree of life is a basic archetype in philosophies, mythologies, and religions around the world.  Trees play a major role in real life as well as mythology. They are the yin to our yang, breathing in the carbon dioxide we exhale and breathing out fresh oxygen.  The roots of the tree can be origins, foundations, the past; the trunk of the tree is its center, its core; branches and leaves stand for movement, reaching out, new growth; the fruits of the tree represent outcomes, results, the fruits of your labor.  What is the central tree in your situation? What are its roots, trunk, branches, leaves and fruits? What does that mean to you?", "see also": ["Growth", "Org chart", "Decision tree", "Root causes"], "tags": ["How did we get here? Where are we going?", "How do we grow?", "How to think about this?", "Structure", "What are the options?", "What can we do?", "What is it?", "What is the plan?", "What’s going on?", "Where are we going?", "Where are we?", "Who are we?", "Why are we here?", "Why does it matter?"], "url": "https://visualframeworks.com/tree/", "summary": "This representation should be used when trying to understand the structure and components of a situation or problem, considering their origins, core, growth, outcomes, and implications, with the goal of gaining insight into the current state, future possibilities, and overall significance."}, {"name": "Tube map", "id": "CORE-094", "description": "The London Underground Tube Map, designed by <PERSON> and first published in 1933, was instantly popular and has now become a design classic. It is a way to show a point-to-point network schematically, focusing not on the exact geographic locations of the points, or stations, but their conceptual relationship.  The London Tube map has become a standard for all subway maps and has been used in many other contexts as well.  Tube maps can be used to simplify and clarify all kinds of processes, systems and interactions.  What kinds of systems, processes, and interactions do you need to think about? Can you make a Tube map to simplify and clarify them?", "see also": ["Hub and spokes", "Chutes and ladders", "Flowchart", "Concept map", "Causal loops", "Map"], "tags": ["Abstract", "Head", "Heart", "How can we connect?", "How does it work?", "How to think about this?", "<PERSON><PERSON><PERSON>", "Process", "Space", "Who will do what? By when", "Who will do what? By when?"], "url": "https://visualframeworks.com/tube-map/", "summary": "Use Tube Map representation when you need to simplify and illustrate the conceptual relationship of systems, processes, or interactions, focusing on their connection and sequence rather than their geographical or factual arrangement."}, {"name": "Uphill climb", "id": "CORE-096", "description": "<PERSON><PERSON><PERSON> was a Greek king who angered the gods. As punishment for his sins, he was condemned to push a boulder uphill for eternity. Every time he neared the top, the weight of the boulder sent it crashing down to the bottom again, and he had to start over.  In his essay ''The Myth of Sisyphus,'' philosopher <PERSON> compared <PERSON><PERSON><PERSON>’ endless punishment to the endless toil of working in modern factories and offices.  Work can sometimes feel futile. You may feel like <PERSON><PERSON><PERSON>, stuck in an endless loop with no escape. <PERSON><PERSON> wrote that we must find joy in this absudity; we must imagine <PERSON><PERSON><PERSON> happy.  But there are other ways to find fulfillment in work. Work can be meaningful and fulfilling when it has purpose and meaning. Unlike Sisyphus, we are not condemned to have every rock roll back down to the starting point. Even hard work is rewarding when you push that rock over the top and watch it gain momentum on the other side.  Do you feel like you;re pushing a big rock up a long hill? Why are you doing it? How far have you come already? How far do you have to go? How will it feel when you get to the top?", "see also": ["Infinite loop", "Doom loop", "Momentum", "Snowball effect"], "tags": ["How does it work?", "How will we get there?", "What is the problem?", "What’s going on?", "What’s next?", "Who will do what? By when?", "Why are we here?"], "url": "https://visualframeworks.com/uphill-climb/", "summary": "Use this representation for providing perspective on challenging tasks, encouraging reflection on purpose, progress and ultimate goals, and enhancing motivation by transforming the perspective on task repetition from being punitive to a source of joy and fulfillment."}, {"name": "Venn diagram", "id": "CORE-097", "description": "In 1880, philosopher <PERSON> invented the Venn diagram as a way to diagram logical propositions and reasoning.  In a Venn diagram, circles or other shapes represent sets of data. The shapes are overlapped with each other in such a way that the areas of overlap contain items the data sets have in common.  Venn diagrams are useful for exploring similarities and differences, and can be used to compare and contrast systems, situations, approaches, and things.  Do you need to compare and contrast two or more things? What do they have in common? How are they different? Can you make a Venn diagram to show the similarities and differences?", "see also": ["Coordinates", "2×2 matrix", "Line chart", "Bar chart", "Grid", "Balancing tradeoffs", "Puzzle"], "tags": ["Abstract", "Compared to what?", "Head", "How are we different?", "How is it different?", "How is it similar?", "How to think about this?", "Space", "Thinking", "What are the options?"], "url": "https://visualframeworks.com/venn-diagram/", "summary": "The <PERSON><PERSON><PERSON> diagram is ideally used whenever there's a need to compare, contrast, or visualize the similarities and differences among two or more data sets, systems, situations, or things."}, {"name": "Virtuous cycle", "id": "CORE-098", "description": "A virtuous cycle is the opposite of a doom loop. It’s a positive, productive habit that builds up major gains over time. A mentor of mine once told me, ''<PERSON><PERSON>, it’s not hard to be successful in business. You just find something that works and you do it a lot of times.'' There’s a lot to be said for that. Find something that works, even if it’s a small thing, if you repeat it enough time you will see significant gains. What are you doing that’s working? Can you do it a lot of times and create a virtuous cycle of increasing returns?", "see also": ["Cycle", "Flywheel", "Pain/gain", "Doom loop", "Escape velocity"], "tags": ["Cycles", "Heart", "<PERSON><PERSON><PERSON>", "Pain/gain", "Time", "What’s going on?", "What’s working?"], "url": "https://visualframeworks.com/virtuous-cycle/", "summary": "Use this representation when aiming to highlight positive, productive habits or activities that result in significant gains over time, particularly in business, when repeated consistently."}, {"name": "Water cycle", "id": "CORE-099", "description": "The water cycle is a complex system of cause, effect, and interconnections.  Water is suspended in the air as vapor. It comes down from the heavens as rain or snow. In extreme cold, it freezes into a solid. When warmed by the sun, it trickles down mountains. Trickles become streams, and streams become rivers, as water washes down into lakes and the oceans. Over time, water evaporates from a liquid back into vapor again, forms into clouds and mists, and comes back to the earth as rain. It is filtered and purified along the way. Water vapor in the air is all around us, but it’s intangible and can be hard to see. When water freezes into ice, it becomes visible and tangible, but inflexible. When water is in its liquid state, it flows freely and nourishes all life. Is your situation intangible and hard to see, like vapor? Tangible but rigid, like ice? Or free and flowing, like water? Is there a natural cycle at work here? What might be the next stage, and how can you prepare for it?", "see also": ["Cycle", "Seasons", "Transformation", "Tipping point"], "tags": ["Cycles", "Heart", "How do we grow?", "How does it work?", "How to think about this?", "<PERSON><PERSON><PERSON>", "Process", "Time", "What’s going on?", "What’s working?"], "url": "https://visualframeworks.com/water-cycle/", "summary": "Use this representation when needing to understand or explain complex systems, cycles, transformations, or when trying to visualize intangible situations, rigid conditions, or free-flowing scenarios and the evolution that follows."}, {"name": "Swiss Army Knife", "id": "NAPKIN-001", "description": "The Swiss Army Knife concept symbolizes a versatile, multi-functional tool. It serves as a single body holding numerous solutions for different problems, similar to the famous tool with a multitude of applications. This concept can be used when discussing multi-functional processes, versatile skills, or diverse solutions provided by an individual, system, or product. It helps in understanding the readiness and adaptability to deal with different scenarios. Are you or your solution ready for differing situations? Do you or your solution have various 'blades' to tackle diverse 'problems'?", "see also": [], "tags": [], "url": "", "summary": "Use the Swiss Army Knife representation when discussing the versatility and multi-functionality of a product, system, or individual's skills, highlighting their readiness and adaptability to tackle varying problems or scenarios."}]
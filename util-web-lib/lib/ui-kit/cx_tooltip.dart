import 'package:io_web_lib/logging.dart';
import 'package:vector_math/vector_math.dart';

import '../environment.dart';
import '../helper.dart' as helper;
import '../releaser.dart';
import 'cx_base.dart';
import 'cx_enums.dart' show CxPointyPlacement;

/// Configuration for tooltip positioning and sizing
class CxTooltipConfig {
  final double defaultWidth;
  final double defaultHeight;
  final double viewportMargin;
  final double targetOffset;
  final int zIndex;

  const CxTooltipConfig({
    this.defaultWidth = 200,
    this.defaultHeight = 40,
    this.viewportMargin = 10,
    this.targetOffset = 4,
    this.zIndex = 1000,
  });
}

final class CxTooltip extends CxBase<HTMLDivElement> {
  static final Logger _log = Logger('CxTooltip', browser: Logger.INFO);
  static const CxTooltipConfig _defaultConfig = CxTooltipConfig();

  final String? textContent;
  final Node? htmlContent;
  final List<String> _innerClasses;
  final List<String> _classes;

  // sizing and placement
  final CxPointyPlacement _placement;
  final Vector2? absolute;
  final String? nudgeX;
  final String? nudgeY;
  final double? maxWidth;
  final CxTooltipConfig _config;

  // Auto-hover functionality
  final HTMLElement? _targetElement;
  bool _autoHover = false;

  CxTooltip({
    required super.releaser,
    required CxPointyPlacement placement,
    this.textContent,
    this.htmlContent,
    this.maxWidth,
    this.absolute,
    this.nudgeX,
    this.nudgeY,
    List<String> innerClasses = const [],
    List<String> classes = const [],
    HTMLElement? targetElement,
    CxTooltipConfig config = _defaultConfig,
  }) : _placement = placement,
       _innerClasses = innerClasses,
       _classes = classes,
       _targetElement = targetElement,
       _config = config;

  /// Factory method to create a tooltip with automatic hover handling
  static CxTooltip withAutoHover({
    required Releaser releaser,
    required HTMLElement targetElement,
    required CxPointyPlacement placement,
    String? textContent,
    Node? htmlContent,
    double? maxWidth,
    String? nudgeX,
    String? nudgeY,
    List<String> innerClasses = const [],
    List<String> classes = const [],
    CxTooltipConfig config = _defaultConfig,
  }) {
    final tooltip = CxTooltip(
      releaser: releaser,
      placement: placement,
      textContent: textContent,
      htmlContent: htmlContent,
      maxWidth: maxWidth,
      nudgeX: nudgeX,
      nudgeY: nudgeY,
      innerClasses: innerClasses,
      classes: classes,
      targetElement: targetElement,
      config: config,
    );
    tooltip.enableAutoHover();
    return tooltip;
  }

  /// Enables automatic hover handling for the tooltip
  void enableAutoHover() {
    if (_targetElement == null || _autoHover) return;

    _autoHover = true;

    // Listen to mouse enter on target element
    autoUnsubscribe(_targetElement.onMouseEnter.listen((_) {
      // Cancel any pending hide operation
      helper.terminateDelayedCall(channel: 'hideTooltip', executeCallback: false);
      _showTooltip();
    }));

    // Listen to mouse leave on target element
    autoUnsubscribe(_targetElement.onMouseLeave.listen((_) {
      _hideTooltip();
    }));
  }

  void _showTooltip() {
    if (_targetElement == null) return;

    // Only show if not already visible
    if (cachedRoot?.parentNode != null) return;

    // Calculate position relative to target element
    final targetRect = _targetElement.getBoundingClientRect();
    final position = _calculatePosition(targetRect);

    // Ensure tooltip doesn't go off-screen
    final adjustedPosition = _adjustPositionForViewport(position);
    final tooltipElement = root;
    tooltipElement.style.zIndex = '${_config.zIndex}';
    Environment.document.body?.appendChild(tooltipElement);

    if (_autoHover) {
      autoUnsubscribe(tooltipElement.onMouseEnter.listen((_) {
        helper.terminateDelayedCall(channel: 'hideTooltip', executeCallback: false);
      }));

      autoUnsubscribe(tooltipElement.onMouseLeave.listen((_) {
        _hideTooltip();
      }));
    }

    updateAbsoluteLocation(adjustedPosition);
  }

  Vector2 _calculatePosition(DOMRect targetRect) {
    final centerX = (targetRect.left + targetRect.width / 2).toDouble();
    final centerY = (targetRect.top + targetRect.height / 2).toDouble();
    final offset = _config.targetOffset;

    switch (_placement) {
      // Top placements
      case CxPointyPlacement.topLeft:
        return Vector2(targetRect.left.toDouble(), targetRect.bottom.toDouble() + offset);
      case CxPointyPlacement.topCenter:
        return Vector2(centerX, targetRect.bottom.toDouble() + offset);
      case CxPointyPlacement.topRight:
        return Vector2(targetRect.right.toDouble(), targetRect.bottom.toDouble() + offset);

      // Bottom placements
      case CxPointyPlacement.bottomLeft:
        return Vector2(targetRect.left.toDouble(), targetRect.top.toDouble() - offset);
      case CxPointyPlacement.bottomCenter:
        return Vector2(centerX, targetRect.top.toDouble() - offset);
      case CxPointyPlacement.bottomRight:
        return Vector2(targetRect.right.toDouble(), targetRect.top.toDouble() - offset);

      // Left placements
      case CxPointyPlacement.leftTop:
        return Vector2(targetRect.right.toDouble() + offset, targetRect.top.toDouble());
      case CxPointyPlacement.leftCenter:
        return Vector2(targetRect.right.toDouble() + offset, centerY);
      case CxPointyPlacement.leftBottom:
        return Vector2(targetRect.right.toDouble() + offset, targetRect.bottom.toDouble());

      // Right placements
      case CxPointyPlacement.rightTop:
        return Vector2(targetRect.left.toDouble() - offset, targetRect.top.toDouble());
      case CxPointyPlacement.rightCenter:
        return Vector2(targetRect.left.toDouble() - offset, centerY);
      case CxPointyPlacement.rightBottom:
        return Vector2(targetRect.left.toDouble() - offset, targetRect.bottom.toDouble());

      case CxPointyPlacement.none:
        _log.warning(() => 'CxTooltip._calculatePosition: Unsupported placement: ${_placement.name}');
        return Vector2(centerX, targetRect.bottom.toDouble() + offset);
    }
  }

  Vector2 _adjustPositionForViewport(Vector2 position) {
    final viewport = Environment.window.innerWidth.toDouble();
    final viewportHeight = Environment.window.innerHeight.toDouble();

    // Get tooltip dimensions
    final tooltipWidth = maxWidth ?? _config.defaultWidth;
    final tooltipHeight = _config.defaultHeight;
    final margin = _config.viewportMargin;

    var adjustedX = position.x;
    var adjustedY = position.y;

    // Adjust horizontal position
    if (position.x + tooltipWidth / 2 > viewport) {
      adjustedX = viewport - tooltipWidth / 2 - margin;
    } else if (position.x - tooltipWidth / 2 < 0) {
      adjustedX = tooltipWidth / 2 + margin;
    }

    // Adjust vertical position
    if (position.y + tooltipHeight > viewportHeight) {
      adjustedY = position.y - tooltipHeight - (_config.targetOffset * 2);
    } else if (position.y < 0) {
      adjustedY = margin;
    }

    return Vector2(adjustedX, adjustedY);
  }

  void _hideTooltip() {
    // Use a small delay to prevent flickering when mouse moves quickly
    helper.delayedCall(0.05, () {
      hideAndRemoveRoot();
    }, channel: 'hideTooltip');
  }

  @override
  void releaseComponent() {
    helper.terminateDelayedCall(channel: 'copied', executeCallback: false);
    helper.terminateDelayedCall(channel: 'removeRoot', executeCallback: false);
    helper.terminateDelayedCall(channel: 'hideTooltip', executeCallback: false);
    helper.terminateDelayedCall(channel: 'updateAbsoluteLocation', executeCallback: false);
    helper.terminateDelayedCall(channel: 'updateNudgeLocation', executeCallback: false);
    removeRoot();
  }

  void hideAndRemoveRoot() {
    final div = cachedRoot;
    if (div == null) return;

    final rect = div.getBoundingClientRect().toAabb2();
    if (rect.min.isZero()) {
      removeRoot();
      return;
    }

    div.classList.remove('absolute');
    div.classList.add('fixed');
    div.style.setProperty('--viewport-left', '${rect.min.x}px');
    div.style.setProperty('--viewport-top', '${rect.min.y}px');
    helper.triggerCssAnimation(div, 'active', removeClass: true);
    Environment.document.body?.appendChild(div);
    helper.delayedCall(0.5, () => removeRoot(), channel: 'removeRoot');
  }

  @override
  HTMLDivElement buildComponent() {
    final content = HTMLDivElement()..classList.addAll(['content', ..._innerClasses]);
    if (textContent != null) {
      content.textContent = textContent;
    }
    if (htmlContent != null) {
      content.appendNotNull(htmlContent);
    }

    final div = HTMLDivElement()
      ..classList.addAll(['tooltip', 'pointy-${_placement.name}', ..._classes])
      ..appendChild(
        HTMLDivElement()
          ..classList.add('tooltip-border')
          ..appendChild(content),
      );

    if (absolute != null) {
      div.classList.add('absolute');
      div.style.setProperty('--absolute-x', '${absolute!.x}px');
      div.style.setProperty('--absolute-y', '${absolute!.y}px');
    }

    if (nudgeX != null) {
      div.style.setProperty('--nudge-x', nudgeX!);
    }

    if (nudgeY != null) {
      div.style.setProperty('--nudge-y', nudgeY!);
    }

    if (maxWidth != null) {
      div.style.setProperty('--max-width', '${maxWidth}px');
    }

    helper.triggerCssAnimation(div, 'active');

    return div;
  }

  void updateAbsoluteLocation(Vector2? location) {
    helper.delayedCall(0.01, () {
      if (location == null) return;

      final div = cachedRoot;
      if (div == null) return;

      div.classList.add('absolute');
      div.style.setProperty('--absolute-x', '${location.x}px');
      div.style.setProperty('--absolute-y', '${location.y}px');
    }, channel: 'updateAbsoluteLocation');
  }

  void updateNudgeLocation(String? nudgeX, String? nudgeY) {
    helper.delayedCall(0.01, () {
      final div = cachedRoot;
      if (div == null) return;

      if (nudgeX != null) {
        div.style.setProperty('--nudge-x', nudgeX);
      }

      if (nudgeY != null) {
        div.style.setProperty('--nudge-y', nudgeY);
      }
    }, channel: 'updateNudgeLocation');
  }

  void updateInnerText(String text) {
    helper.delayedCall(0.01, () {
      final div = cachedRoot;
      if (div == null) return;

      final content = div.querySelector('.content');
      if (content == null) return;

      content.textContent = text;
    }, channel: 'updateInnerText');
  }
}

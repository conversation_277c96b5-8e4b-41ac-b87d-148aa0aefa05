import 'package:test/test.dart';
import 'package:util_web_lib/releaser.dart';
import 'package:util_web_lib/ui-kit/cx_enums.dart' show CxPointyPlacement;
import 'package:util_web_lib/ui-kit/cx_tooltip.dart';
import 'package:vector_math/vector_math.dart';

class TestReleaser with Releaser {}

void main() {
  group('CxTooltip', () {
    late TestReleaser releaser;

    setUp(() {
      releaser = TestReleaser();
    });

    tearDown(() {
      releaser.releaseAll();
    });

    test('should create tooltip with basic configuration', () {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxPointyPlacement.topCenter,
        textContent: 'Test tooltip',
      );

      final root = tooltip.root;
      expect(root.classList.contains('cx-tooltip'), isTrue);
      expect(root.classList.contains('pointy-topCenter'), isTrue);
      expect(root.querySelector('.content')?.textContent, equals('Test tooltip'));
    });

    test('should support all placement positions', () {
      final placements = [
        CxPointyPlacement.topLeft,
        CxPointyPlacement.topCenter,
        CxPointyPlacement.topRight,
        CxPointyPlacement.bottomLeft,
        CxPointyPlacement.bottomCenter,
        CxPointyPlacement.bottomRight,
        CxPointyPlacement.leftTop,
        CxPointyPlacement.leftCenter,
        CxPointyPlacement.leftBottom,
        CxPointyPlacement.rightTop,
        CxPointyPlacement.rightCenter,
        CxPointyPlacement.rightBottom,
      ];

      for (final placement in placements) {
        final tooltip = CxTooltip(
          releaser: releaser,
          placement: placement,
          textContent: 'Test',
        );

        expect(tooltip.root.classList.contains('pointy-${placement.name}'), isTrue);
      }
    });

    test('should support absolute positioning', () {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxPointyPlacement.topCenter,
        textContent: 'Test',
        absolute: Vector2(100, 200),
      );

      final root = tooltip.root;
      expect(root.classList.contains('absolute'), isTrue);
      expect(root.style.getPropertyValue('--absolute-x'), equals('100.0px'));
      expect(root.style.getPropertyValue('--absolute-y'), equals('200.0px'));
    });

    test('should support nudge positioning', () {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxPointyPlacement.topCenter,
        textContent: 'Test',
        nudgeX: '10px',
        nudgeY: '5px',
      );

      final root = tooltip.root;
      expect(root.style.getPropertyValue('--nudge-x'), equals('10px'));
      expect(root.style.getPropertyValue('--nudge-y'), equals('5px'));
    });

    test('should support max width', () {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxPointyPlacement.topCenter,
        textContent: 'Test',
        maxWidth: 250,
      );

      final root = tooltip.root;
      expect(root.style.getPropertyValue('--max-width'), equals('250.0px'));
    });

    test('should support inner classes', () {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxPointyPlacement.topCenter,
        textContent: 'Test',
        innerClasses: ['custom-class', 'another-class'],
      );

      final content = tooltip.root.querySelector('.content');
      expect(content?.classList.contains('custom-class'), isTrue);
      expect(content?.classList.contains('another-class'), isTrue);
    });

    test('should update inner text', () async {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxPointyPlacement.topCenter,
        textContent: 'Original text',
      );
      final content = tooltip.root.querySelector('.content');
      expect(content?.textContent, equals('Original text'));
      tooltip.updateInnerText('Updated text');
      // Wait a bit for the delayed update
      await Future.delayed(const Duration(milliseconds: 50));
      expect(content?.textContent, equals('Updated text'));
    });
  });
}

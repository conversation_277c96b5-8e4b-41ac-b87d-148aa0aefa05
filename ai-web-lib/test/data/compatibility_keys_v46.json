{"items": [{"id": "tf_qu12mo19jpo93", "v": 46, "theme": {"theme": "colorful-gradient", "styles": {"fillScheme": [{"selectors": [{"classes": ["ThemeFrame"]}], "value": {"name": "soft"}, "type": "FillScheme"}, {"selectors": [{"classes": ["Part", "Indexed_0"]}, {"classes": ["Part", "Fill"]}], "value": {"name": "gradient"}, "type": "FillScheme"}], "fillColorScheme": [{"selectors": [{"classes": ["ThemeFrame"]}], "value": {"color": "white"}, "type": "ColorScheme"}, {"selectors": [{"classes": ["Part", "Indexed_0"]}], "value": {"color": "black"}, "type": "ColorScheme", "priority": "strong"}, {"selectors": [{"classes": ["Part", "Indexed", "Fill"]}], "value": {"color": "black"}, "type": "ColorScheme"}, {"selectors": [{"classes": ["Part", "Fill"]}], "value": {"color": "gray"}, "type": "ColorScheme"}], "fillTreatment": [{"selectors": [{"classes": ["ThemeFrame"]}], "value": {"name": "solid"}, "type": "FillTreatment"}], "strokeColorScheme": [{"selectors": [{"classes": ["ChartLine"]}], "value": {"color": "blue"}, "type": "ColorScheme"}, {"selectors": [{"classes": ["Label"], "exclusionsClasses": ["InsideFill"]}, {"classes": ["Label", "InsideFill"]}, {"classes": ["Label", "Indexed"]}], "value": {"color": "black"}, "type": "ColorScheme"}, {"selectors": [{"classes": ["Icon", "InsideFill"]}, {"classes": ["Icon"], "exclusionsClasses": ["InsideFill"]}], "value": {"color": "black"}, "type": "ColorScheme"}, {"selectors": [{"classes": ["Part", "Indexed", "Fill"]}, {"classes": ["Stroke", "Indexed"]}, {"classes": ["Part", "Stroke"], "exclusionsClasses": ["Indexed"]}, {"classes": ["Part", "Indexed_0"]}], "value": {"color": "black"}, "type": "ColorScheme"}], "strokeScheme": [{"selectors": [{"classes": ["ChartLine"]}], "value": {"name": "standard"}, "type": "StrokeScheme", "priority": "strong"}, {"selectors": [{"classes": ["Label", "InsideFill"]}, {"classes": ["Icon", "InsideFill"]}, {"classes": ["Part", "Indexed", "Fill"]}], "value": {"name": "black"}, "type": "StrokeScheme"}, {"selectors": [{"classes": ["Label"], "exclusionsClasses": ["InsideFill"]}], "value": {"name": "dim"}, "type": "StrokeScheme", "priority": "strong"}, {"selectors": [{"classes": ["Part", "Indexed_0"]}, {"classes": ["Stroke", "Indexed"]}, {"classes": ["Part", "Stroke"], "exclusionsClasses": ["Indexed"]}, {"classes": ["Label", "Indexed"]}, {"classes": ["Icon"], "exclusionsClasses": ["InsideFill"]}], "value": {"name": "black"}, "type": "StrokeScheme", "priority": "strong"}], "fontFamily": [{"selectors": [{"classes": ["All"]}], "value": {"type": "text", "name": "'Roboto'"}, "type": "Font"}]}, "darkModeStyles": {"fillColorScheme": [{"selectors": [{"classes": ["ThemeFrame"]}], "value": {"color": "gray", "force": true}, "type": "ColorScheme"}, {"selectors": [{"classes": ["Part", "Decor", "ColorFillBlack"]}], "value": {"color": "gray", "force": true}, "type": "ColorScheme", "priority": "strong"}], "strokeScheme": [{"selectors": [{"classes": ["Part", "Fill"]}], "value": {"name": "standard"}, "type": "StrokeScheme", "priority": "strong"}], "strokeColorScheme": [{"selectors": [{"classes": ["Part", "Fill"]}], "value": {"color": "white", "force": true}, "type": "ColorScheme", "priority": "strong"}]}}, "frame": {"children": ["gr_1hd78uo19jh6vb"]}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "themeFrame": {"id": "tf_qu12mo19jpo93", "frameOrderIndex": 0, "aspectRatio": null}, "around": {"inside": ["gr_1hd78uo19jh6vb", "-1.g-0b_fr_1cxdqq819jh7nk", "1.g-1_fr_142d7kg19jh62p", "4.g-4_fr_v6q7bk19jh6vj", "-1.g-0a_fr_zmjpg019jh6v5"], "core": [756, 640.828125]}, "frameBox": {"localBox": [0, 0, 756, 640.828125]}, "topLeft": {"topLeft": [174, 6834]}, "behavior": {"themeFrame": ["gr_1hd78uo19jh6vb", "-1.g-0b_fr_1cxdqq819jh7nk", "1.g-1_fr_142d7kg19jh62p", "4.g-4_fr_v6q7bk19jh6vj", "-1.g-0a_fr_zmjpg019jh6v5"]}, "svgSymbols": {"passes": [{"usage": "both", "outer": "<symbol><g><path d=\"M0 0h756 v640.83 h -756 Z\"></path></g></symbol>", "locaLBox": [0, 0, 756, 640.8300170898438]}]}, "brush": {}, "resizable": {"forceUniformScalingFromCorners": true, "defaultToUniformScaling": false, "showHull": true}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [174, 6834], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [162, 0]}}, "item": {}, "shape": {"localBox": [0, 0, 756, 640.828125], "shape": "rectangle", "padding": "large"}}, {"id": "gr_1hd78uo19jh6vb", "v": 46, "frame": {"children": ["-1.g-0b_fr_1cxdqq819jh7nk", "1.g-1_fr_142d7kg19jh62p", "4.g-4_fr_v6q7bk19jh6vj", "-1.g-0a_fr_zmjpg019jh6v5"]}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "layout": {"layoutTree": {"key": "root", "stack": {"direction": "column", "primaryAlignment": "min", "counterAlignment": "min", "padding": [0, 24, 0, 24], "gap": 12}, "constraints": {"x": {"c": "min"}, "y": {"c": "min"}}, "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "<PERSON><PERSON><PERSON><PERSON>"}, "position": {"box": [0, 0, 708, 573.489990234375]}, "children": [{"key": "-1.<PERSON><PERSON><PERSON>582", "stack": {"direction": "column", "primaryAlignment": "min", "counterAlignment": "center", "padding": [24, 0, 0, 0]}, "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "<PERSON><PERSON><PERSON><PERSON>"}, "position": {"box": [24, 0, 684, 72]}, "children": [{"key": "tx-xx-title", "position": {"box": [30, 24, 630, 72]}}]}, {"key": "-1.<PERSON><PERSON><PERSON><PERSON>", "stack": {"direction": "row", "primaryAlignment": "min", "counterAlignment": "min", "gap": 12}, "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "<PERSON><PERSON><PERSON><PERSON>"}, "position": {"box": [24, 84, 684, 573.489990234375]}, "children": [{"key": "-1.lines", "stack": {"direction": "column", "primaryAlignment": "center", "counterAlignment": "min", "gap": 10}, "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "<PERSON><PERSON><PERSON><PERSON>"}, "position": {"box": [0, 0, 288, 489.489990234375]}, "children": [{"key": "-1.lines_1", "position": {"box": [0, 0, 288, 489.489990234375]}, "children": [{"key": "-1.g-0b", "constraints": {"x": {"c": "min", "before": 76.9260025024414}, "y": {"c": "min", "before": 260.0039978027344}}, "position": {"box": [76.9260025024414, 260.0039978027344, 207.27499389648438, 489.489990234375]}}, {"key": "1.g-1", "constraints": {"x": {"c": "min", "before": 64.04299926757812}, "y": {"c": "min", "before": 247.10499572753906}}, "position": {"box": [64.04299926757812, 247.10499572753906, 140.2740020751953, 485.62799072265625]}}, {"key": "2.g-2", "constraints": {"x": {"c": "min", "before": 104.79900360107422}, "y": {"c": "min", "before": 250.25}}, "position": {"box": [104.79900360107422, 250.25, 217.85800170898438, 489.489990234375]}}, {"key": "-1.g-0a", "constraints": {"x": {"c": "min"}, "y": {"c": "min"}}, "position": {"box": [0, 0, 288, 287.9389953613281]}, "children": [{"key": "tx-xx-0-label", "constraints": {"x": {"c": "min", "before": 54.09600067138672}, "y": {"c": "min", "before": 165.9409942626953}}, "position": {"box": [54.09600067138672, 165.9409942626953, 234.09600830078125, 213.9409942626953]}}, {"key": "ic-xx-0", "constraints": {"x": {"c": "min", "before": 96.09600067138672}, "y": {"c": "min", "before": 57.941001892089844}}, "position": {"box": [96.09600067138672, 57.941001892089844, 192.09600830078125, 153.94100952148438]}}]}]}]}, {"key": "-1.<PERSON><PERSON><PERSON>609", "stack": {"direction": "column", "primaryAlignment": "center", "counterAlignment": "min", "padding": [36, 0, 36, 0], "gap": 48}, "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "<PERSON><PERSON><PERSON><PERSON>"}, "position": {"box": [300, 0, 660, 489.489990234375]}, "children": [{"key": "-1.<PERSON><PERSON><PERSON>604", "stack": {"direction": "column", "primaryAlignment": "min", "counterAlignment": "min", "gap": 36}, "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "<PERSON><PERSON><PERSON><PERSON>"}, "position": {"box": [0, 166.7449951171875, 360, 322.7449951171875]}, "children": [{"key": "-1.<PERSON><PERSON><PERSON>603", "stack": {"direction": "row", "primaryAlignment": "min", "counterAlignment": "min", "padding": [0, 0, 0, 60], "gap": 46}, "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "<PERSON><PERSON><PERSON><PERSON>"}, "position": {"box": [0, 0, 360, 60]}, "children": [{"key": "-1.text", "stack": {"direction": "column", "primaryAlignment": "min", "counterAlignment": "min", "gap": 12}, "resize": {"x": "fixed", "y": "<PERSON><PERSON><PERSON><PERSON>"}, "position": {"box": [60, 0, 360, 60]}, "children": [{"key": "tx-xx-1", "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "fixed"}, "position": {"box": [0, 0, 300, 24]}}, {"key": "tx-xx-1-desc", "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "fixed"}, "position": {"box": [0, 36, 300, 60]}}, {"key": "bt-xx-add-1", "constraints": {"x": {"c": "center"}, "y": {"c": "min", "before": -6}}, "position": {"box": [138, -6, 162, 18]}}]}, {"key": "1.g-1_1", "constraints": {"x": {"c": "min", "before": 12.22599983215332}, "y": {"c": "min", "before": -6}}, "position": {"box": [12.22599983215332, -6, 48.22599792480469, 30]}, "children": [{"key": "ic-xx-1", "constraints": {"x": {"c": "min"}, "y": {"c": "min"}}, "position": {"box": [0, 0, 36, 36]}}]}]}, {"key": "-1.<PERSON><PERSON>_604_1", "stack": {"direction": "row", "primaryAlignment": "min", "counterAlignment": "min", "padding": [0, 0, 0, 60], "gap": 46}, "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "<PERSON><PERSON><PERSON><PERSON>"}, "position": {"box": [0, 96, 360, 156]}, "children": [{"key": "-1.text_1", "stack": {"direction": "column", "primaryAlignment": "min", "counterAlignment": "min", "gap": 12}, "resize": {"x": "fixed", "y": "<PERSON><PERSON><PERSON><PERSON>"}, "position": {"box": [60, 0, 360, 60]}, "children": [{"key": "tx-xx-2", "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "fixed"}, "position": {"box": [0, 0, 300, 24]}}, {"key": "tx-xx-2-desc", "resize": {"x": "<PERSON><PERSON><PERSON><PERSON>", "y": "fixed"}, "position": {"box": [0, 36, 300, 60]}}, {"key": "bt-xx-add-2", "constraints": {"x": {"c": "center"}, "y": {"c": "min", "before": -6}}, "position": {"box": [138, -6, 162, 18]}}, {"key": "bt-xx-add-3", "constraints": {"x": {"c": "center"}, "y": {"c": "max", "after": -54.253997802734375}}, "position": {"box": [138, 90.25399780273438, 162, 114.25399780273438]}}]}, {"key": "2.g-2_1", "constraints": {"x": {"c": "min", "before": 12.22599983215332}, "y": {"c": "min", "before": -6}}, "position": {"box": [12.22599983215332, -6, 48.22599792480469, 30]}, "children": [{"key": "ic-xx-2", "constraints": {"x": {"c": "min"}, "y": {"c": "min"}}, "position": {"box": [0, 0, 36, 36]}}]}]}]}]}]}]}, "nodeIds": {"root": "gr_1hd78uo19jh6vb", "-1.g-0b": "-1.g-0b_fr_1cxdqq819jh7nk", "1.g-1": "1.g-1_fr_142d7kg19jh62p", "2.g-2": "4.g-4_fr_v6q7bk19jh6vj", "-1.g-0a": "-1.g-0a_fr_zmjpg019jh6v5", "tx-xx-title": "tx_venturec_qqwp7419jh7nv", "tx-xx-1": "tx_software_mb372o19jh8g4", "tx-xx-1-desc": "tx_represen_mb372o19jh8g5", "ic-xx-1": "sand_de78nk19jk1sa", "tx-xx-2": "tx_consumer_de78nk19jo936", "tx-xx-2-desc": "tx_showsthe_de78nk19jo937", "ic-xx-2": "cart_9095sg19jpma6"}}, "inBehavior": {"parents": {"themeFrame": "tf_qu12mo19jpo93"}}, "frameBox": {"localBox": [0, 0, 708, 549.48974609375]}, "group": {"children": ["-1.g-0b_fr_1cxdqq819jh7nk", "1.g-1_fr_142d7kg19jh62p", "4.g-4_fr_v6q7bk19jh6vj", "-1.g-0a_fr_zmjpg019jh6v5"]}, "topLeft": {"topLeft": [198, 6881.99951171875]}, "behavior": {"scene": ["tx_venturec_qqwp7419jh7nv", "tx_software_mb372o19jh8g4", "tx_represen_mb372o19jh8g5", "sand_de78nk19jk1sa", "tx_consumer_de78nk19jo936", "tx_showsthe_de78nk19jo937", "cart_9095sg19jpma6"], "data": {"scene": {}}}, "capture": {"captureId": "9af2e617-5992-4030-a08b-72ab1f2d093d", "layoutName": "families#pillars-keyset-v1--family--4"}, "scene": {"dbId": "families#pillars-keyset-v1--family--2", "initialBox": [0, 0, 708, 573.489990234375], "initialViewBox": [0, 0, 708, 573.489501953125], "elements": {"tx-xx-title": {"key": "tx-xx-title", "type": "label", "frameBox": [54, 24, 654, 72], "alignment": "cb", "defaultParameters": {"textWeight": "700", "fontFamily": "Roboto", "fontSize": "20", "color": "#484848"}, "additionClasses": ["Title"]}, "tx-xx-0-label": {"key": "tx-xx-0-label", "type": "label", "frameBox": [78.09600067138672, 249.9409942626953, 258.09600830078125, 297.94097900390625], "alignment": "ct", "defaultParameters": {"textWeight": "700", "fontFamily": "Roboto", "fontSize": "20", "color": "#484848"}, "additionClasses": ["NotInside"]}, "ic-xx-0": {"key": "ic-xx-0", "type": "icon", "frameBox": [120.09600067138672, 141.94100952148438, 216.09600830078125, 237.94100952148438], "alignment": "cc", "additionClasses": ["NotInside"]}, "tx-xx-1": {"key": "tx-xx-1", "type": "label", "frameBox": [384, 250.74476623535156, 684, 274.7447509765625], "alignment": "lt", "index": 1, "defaultParameters": {"textWeight": "700", "fontFamily": "Roboto", "fontSize": "20", "color": "#484848"}, "additionClasses": ["Desc<PERSON><PERSON><PERSON>"]}, "tx-xx-1-desc": {"key": "tx-xx-1-desc", "type": "label", "frameBox": [384, 286.7447509765625, 684, 310.7447509765625], "alignment": "lt", "defaultParameters": {"textWeight": "400", "fontFamily": "Roboto", "fontSize": "15", "color": "#484848"}, "additionClasses": ["Description"]}, "bt-xx-add-1": {"key": "bt-xx-add-1", "type": "buttonAdd", "frameBox": [522, 244.74476623535156, 546, 268.7447509765625], "alignment": "cc", "index": 1}, "ic-xx-1": {"key": "ic-xx-1", "type": "icon", "frameBox": [336.2255859375, 244.74476623535156, 372.2255859375, 280.7447509765625], "alignment": "cc", "index": 1, "defaultParameters": {"color": "#d1bd08"}}, "tx-xx-2": {"key": "tx-xx-2", "type": "label", "frameBox": [384, 346.7447509765625, 684, 370.7447509765625], "alignment": "lt", "index": 2, "defaultParameters": {"textWeight": "700", "fontFamily": "Roboto", "fontSize": "20", "color": "#484848"}, "additionClasses": ["Desc<PERSON><PERSON><PERSON>"]}, "tx-xx-2-desc": {"key": "tx-xx-2-desc", "type": "label", "frameBox": [384, 382.7447509765625, 684, 406.7447509765625], "alignment": "lt", "defaultParameters": {"textWeight": "400", "fontFamily": "Roboto", "fontSize": "15", "color": "#484848"}, "additionClasses": ["Description"]}, "bt-xx-add-2": {"key": "bt-xx-add-2", "type": "buttonAdd", "frameBox": [522, 340.7447509765625, 546, 364.7447509765625], "alignment": "cc", "index": 2}, "bt-xx-add-3": {"key": "bt-xx-add-3", "type": "buttonAdd", "frameBox": [522, 436.9987487792969, 546, 460.9987487792969], "alignment": "cc", "index": 3}, "ic-xx-2": {"key": "ic-xx-2", "type": "icon", "frameBox": [336.2255859375, 340.7447509765625, 372.2255859375, 376.7447509765625], "alignment": "cc", "index": 2, "defaultParameters": {"color": "#93c332"}}}, "parts": [["-1.g-0b", "-1.g-0a"], ["1.g-1"], ["2.g-2"]], "elementIds": {"tx-xx-title": "tx_venturec_qqwp7419jh7nv", "tx-xx-1": "tx_software_mb372o19jh8g4", "tx-xx-1-desc": "tx_represen_mb372o19jh8g5", "ic-xx-1": "sand_de78nk19jk1sa", "tx-xx-2": "tx_consumer_de78nk19jo936", "tx-xx-2-desc": "tx_showsthe_de78nk19jo937", "ic-xx-2": "cart_9095sg19jpma6"}, "partIds": {"-1.g-0b": "-1.g-0b_fr_1cxdqq819jh7nk", "1.g-1": "1.g-1_fr_142d7kg19jh62p", "2.g-2": "4.g-4_fr_v6q7bk19jh6vj", "-1.g-0a": "-1.g-0a_fr_zmjpg019jh6v5"}}, "anchors": {"anchor": {"semantic": "center", "frameId": "tf_qu12mo19jpo93", "type": "ratioOffset", "offset": [0, 0], "ratio": [0.5, 0.5]}}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [198, 6882], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [186, 48]}}, "item": {}}, {"id": "tx_venturec_qqwp7419jh7nv", "v": 46, "theme": {"styles": {"fontSize": [{"selectors": [{"classes": ["All"]}], "value": {"name": "medium"}, "type": "FontSize"}], "fontWeight": [{"selectors": [{"classes": ["All"]}], "value": {"name": "bold"}, "type": "FontWeight"}], "strokeOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "fillColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "black"}, "type": "ColorScheme"}], "strokeColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "black"}, "type": "ColorScheme"}]}}, "frame": {}, "text": {"text": "Venture Capital Activity", "font": {"type": "text", "name": "'Roboto'"}, "aiAlign": "center", "weight": "bold", "boardToText": [2.1015625, 0], "boardSize": [216, 24], "maxWidth": 600, "textBox": [2.1015625, 0, 213.8984375, 24], "lineBounds": [[2.1015625, 0, 213.8984375, 24]], "lineStarts": [0]}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "inLayout": {"rootId": "gr_1hd78uo19jh6vb"}, "inBehavior": {"parents": {"scene": "gr_1hd78uo19jh6vb"}}, "topLeft": {"topLeft": [444, 6905.99951171875]}, "behavior": {"label": []}, "svgSymbols": {"passes": [{"usage": "fill", "outer": "<symbol><g><text style=\"font: bold 20px <PERSON>o, sans-serif; white-space: pre;\"><tspan x=\"-103.8984375\" y=\"11.5\" dominant-baseline=\"ideographic\">Venture Capital Activity</tspan></text></g></symbol>", "locaLBox": [-103.8984375, -12, 107.8984375, 12]}], "svgTransform": [1, 0, 0, 1, 106, 12]}, "brush": {"type": "text"}, "resizable": {"defaultToUniformScaling": false}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [444, 6906], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [432, 72]}}, "item": {}}, {"id": "-1.g-0a_fr_zmjpg019jh6v5", "v": 46, "theme": {"styles": {"fillOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "strokeOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "strokeColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "gray"}, "type": "ColorScheme"}]}}, "frame": {}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "inLayout": {"rootId": "gr_1hd78uo19jh6vb"}, "inBehavior": {"parents": {"themeFrame": "tf_qu12mo19jpo93"}}, "scenePart": {"partId": "-1.g-0a", "sceneId": "gr_1hd78uo19jh6vb", "drawOrderOffset": 13}, "frameBox": {"localBox": [0, 0, 288, 287.9389953613281]}, "topLeft": {"topLeft": [222, 6941.99951171875]}, "svgSymbols": {"passes": [{"usage": "fill", "outer": "<symbol><g><path d=\"M89.013 277.129 C36.7635 255.524 0 204.055 0 144 C0 64.471 64.471 0 144 0 C223.529 0 288 64.471 288 144 C288 222.112 225.806 285.697 148.237 287.939 C147.074 286.672 146.3 285.01 146.136 283.144 C145.928 280.762 146.757 278.535 148.24 276.934 C219.731 274.695 277 216.035 277 144 C277 70.5461 217.454 11 144 11 C70.5461 11 11 70.5461 11 144 C11 201.52 47.5148 250.512 98.6288 269.06 L89.013 277.129 Z\"></path></g></symbol>", "locaLBox": [0, 0, 288, 287.9389953613281]}, {"usage": "stroke", "outer": "<symbol><g><path d=\"M 89.013 277.1289 C 36.7635 255.5242 0 204.0554 0 144 C 0 64.471 64.471 0 144 0 C 223.529 0 288 64.471 288 144 C 288 222.1117 225.8065 285.6973 148.2374 287.9388 C 147.0737 286.6721 146.2997 285.01 146.1364 283.1436 C 145.928 280.7617 146.7569 278.535 148.2395 276.9337 C 219.7314 274.695 277 216.0354 277 144 C 277 70.5461 217.4539 11 144 11 C 70.5461 11 11 70.5461 11 144 C 11 201.5205 47.5148 250.5124 98.6288 269.0603 L 89.013 277.1289 Z\"></path></g></symbol>", "locaLBox": [0, 0, 288, 287.9388122558594]}]}, "brush": {}, "compound": {"elements": [{"paths": [{"usage": "fill", "path": "M89.013 277.129 C36.7635 255.524 0 204.055 0 144 C0 64.471 64.471 0 144 0 C223.529 0 288 64.471 288 144 C288 222.112 225.806 285.697 148.237 287.939 C147.074 286.672 146.3 285.01 146.136 283.144 C145.928 280.762 146.757 278.535 148.24 276.934 C219.731 274.695 277 216.035 277 144 C277 70.5461 217.454 11 144 11 C70.5461 11 11 70.5461 11 144 C11 201.52 47.5148 250.512 98.6288 269.06 L89.013 277.129 Z"}, {"usage": "stroke", "path": "M 89.013 277.1289 C 36.7635 255.5242 0 204.0554 0 144 C 0 64.471 64.471 0 144 0 C 223.529 0 288 64.471 288 144 C 288 222.1117 225.8065 285.6973 148.2374 287.9388 C 147.0737 286.6721 146.2997 285.01 146.1364 283.1436 C 145.928 280.7617 146.7569 278.535 148.2395 276.9337 C 219.7314 274.695 277 216.0354 277 144 C 277 70.5461 217.4539 11 144 11 C 70.5461 11 11 70.5461 11 144 C 11 201.5205 47.5148 250.5124 98.6288 269.0603 L 89.013 277.1289 Z"}], "box": [0, 0, 288, 287.9389953613281], "x": {"c": "min"}, "y": {"c": "min"}}]}, "resizable": {"horizontalControls": false, "verticalControls": false, "cornerControls": false, "showHull": true}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [222, 6942], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [210, 108]}}, "item": {}, "inGroup": {"parent": "gr_1hd78uo19jh6vb"}}, {"id": "sand_de78nk19jk1sa", "v": 46, "theme": {"styles": {"fillColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "cyan"}, "type": "ColorScheme"}], "strokeColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "cyan"}, "type": "ColorScheme"}]}}, "frame": {}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "db": {"dbId": "24px#code-sandbox--logos--24x24", "board": [0, 0, 36, 36], "srcSize": [24, 24]}, "inLayout": {"rootId": "gr_1hd78uo19jh6vb"}, "inBehavior": {"parents": {"scene": "gr_1hd78uo19jh6vb"}}, "topLeft": {"topLeft": [534.2260131835938, 7090.74462890625]}, "svgSymbols": {"passes": [{"usage": "stroke", "outer": "<symbol><g><path d=\"M21.959999084472656 6.25L21.959999084472656 17.75L12 23.5L2.0399999618530273 17.75L2.0399999618530273 6.25L12 0.5L21.959999084472656 6.25ZM14.75,4.41,12,6,9.36,4.47,5.11,6.93,12,10.85c2.18-1.23,5-2.84,7-4ZM4.21,8.73V13.5L6.8,15v3.05L11,20.48v-7.9C7.84,10.8,5.69,9.59,4.21,8.73ZM17.2,15,20,13.38V8.61c-1.48.86-3.69,2.11-7,4v8l4.2-2.42Z\"></path></g></symbol>", "locaLBox": [2.0399999618530273, 0.5, 21.959999084472656, 23.5]}], "svgTransform": [1.5, 0, 0, 1.5, 0, 0]}, "brush": {}, "resizable": {"forceUniformScaling": true}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [534.22998046875, 7090.740234375], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [522.22998046875, 256.739990234375]}}, "item": {}}, {"id": "tx_software_mb372o19jh8g4", "v": 46, "theme": {"styles": {"fontSize": [{"selectors": [{"classes": ["All"]}], "value": {"name": "medium"}, "type": "FontSize"}], "fontWeight": [{"selectors": [{"classes": ["All"]}], "value": {"name": "bold"}, "type": "FontWeight"}], "strokeOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "fillColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "cyan"}, "type": "ColorScheme"}], "strokeColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "cyan"}, "type": "ColorScheme"}]}}, "frame": {}, "text": {"text": "Software Sector", "font": {"type": "text", "name": "'Roboto'"}, "weight": "bold", "boardSize": [300, 24], "maxWidth": 300, "textBox": [2, 0, 146.453125, 24], "lineBounds": [[2, 0, 146.453125, 24]], "lineStarts": [0]}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "inLayout": {"rootId": "gr_1hd78uo19jh6vb"}, "inBehavior": {"parents": {"scene": "gr_1hd78uo19jh6vb"}}, "topLeft": {"topLeft": [582, 7096.74462890625]}, "behavior": {"label": []}, "svgSymbols": {"passes": [{"usage": "fill", "outer": "<symbol><g><text style=\"font: bold 20px <PERSON>o, sans-serif; white-space: pre;\"><tspan x=\"2\" y=\"11.5\" dominant-baseline=\"ideographic\">Software Sector</tspan></text></g></symbol>", "locaLBox": [2, -12, 146.453125, 12]}], "svgTransform": [1, 0, 0, 1, 0, 12]}, "brush": {"type": "text"}, "resizable": {"defaultToUniformScaling": false}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [582, 7096.740234375], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [570, 262.739990234375]}}, "item": {}}, {"id": "tx_represen_mb372o19jh8g5", "v": 46, "theme": {"styles": {"fontSize": [{"selectors": [{"classes": ["All"]}], "value": {"name": "small"}, "type": "FontSize"}], "fontWeight": [{"selectors": [{"classes": ["All"]}], "value": {"name": "normal"}, "type": "FontWeight"}], "strokeOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "fillColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "black"}, "type": "ColorScheme"}], "strokeColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "black"}, "type": "ColorScheme"}]}}, "frame": {}, "text": {"text": "Represents the significant increase in software-related VC investments.", "font": {"type": "text", "name": "'Roboto'"}, "fontSize": "small", "fontHeight": 18, "boardSize": [300, 36], "maxWidth": 300, "textBox": [2, 0, 254.3671875, 36], "lineBounds": [[2, 0, 254.3671875, 18], [2, 18, 223.1015625, 36]], "lineStarts": [0, 39]}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "inLayout": {"rootId": "gr_1hd78uo19jh6vb"}, "inBehavior": {"parents": {"scene": "gr_1hd78uo19jh6vb"}}, "topLeft": {"topLeft": [582, 7132.74462890625]}, "behavior": {"label": []}, "svgSymbols": {"passes": [{"usage": "fill", "outer": "<symbol><g><text style=\"font: 15px <PERSON><PERSON>, sans-serif; white-space: pre;\"><tspan x=\"2\" y=\"-0.5\" dominant-baseline=\"ideographic\">Represents the significant increase in </tspan><tspan x=\"2\" y=\"17.5\" dominant-baseline=\"ideographic\">software-related VC investments.</tspan></text></g></symbol>", "locaLBox": [2, -18, 254.3671875, 18]}], "svgTransform": [1, 0, 0, 1, 0, 18]}, "brush": {"type": "text"}, "resizable": {"defaultToUniformScaling": false}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [582, 7132.740234375], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [570, 298.739990234375]}}, "item": {}}, {"id": "1.g-1_fr_142d7kg19jh62p", "v": 46, "theme": {"styles": {"fillOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "strokeOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "strokeColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "cyan"}, "type": "ColorScheme"}], "fillColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "cyan"}, "type": "ColorScheme"}]}}, "frame": {}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "inLayout": {"rootId": "gr_1hd78uo19jh6vb"}, "inBehavior": {"parents": {"themeFrame": "tf_qu12mo19jpo93"}}, "scenePart": {"brickIndex": 1, "partId": "1.g-1", "sceneId": "gr_1hd78uo19jh6vb", "drawOrderOffset": 9}, "frameBox": {"localBox": [0, 0, 76.23100280761719, 238.5229949951172]}, "topLeft": {"topLeft": [286.0429992675781, 7189.1044921875]}, "svgSymbols": {"passes": [{"usage": "fill", "outer": "<symbol><g><path d=\"M60.1379 92.1631 L42.626 90.631 C39.4555 90.3537 36.5286 88.8285 34.4828 86.3905 L14.7567 62.8818 C11.9167 59.4972 12.3582 54.4511 15.7428 51.6111 L59.1767 15.1657 C59.6516 14.7672 60.1592 14.4333 60.6889 14.1631 L60.7514 14.0266 C62.5483 10.1008 66.3162 7.4428 70.6173 7.0665 L76.2308 6.5754 L74.3105 4.2868 C70.0504 -0.7901 62.4814 -1.4523 57.4045 2.8077 L4.2868 47.3787 C-0.7901 51.6388 -1.4523 59.2078 2.8077 64.2847 L33.2731 100.592 L68.0498 103.634 L60.1379 92.1631 Z\"></path></g></symbol>", "locaLBox": [-3.1061765184858814e-05, -3.1061765184858814e-05, 76.23079681396484, 103.63400268554688]}, {"usage": "stroke", "outer": "<symbol><g><path d=\"M 60.1379 92.1631 L 42.626 90.631 C 39.4555 90.3537 36.5286 88.8285 34.4828 86.3905 L 14.7567 62.8818 C 11.9167 59.4972 12.3582 54.4511 15.7428 51.6111 L 59.1767 15.1657 C 59.6516 14.7672 60.1592 14.4333 60.6889 14.1631 L 60.7514 14.0266 C 62.5483 10.1008 66.3162 7.4428 70.6173 7.0665 L 76.2308 6.5754 L 74.3105 4.2868 C 70.0504 -0.7901 62.4814 -1.4523 57.4045 2.8077 L 4.2868 47.3787 C -0.7901 51.6388 -1.4523 59.2078 2.8077 64.2847 L 33.2731 100.5919 L 68.0498 103.6342 L 60.1379 92.1631 Z\"></path></g></symbol>", "locaLBox": [-3.1061765184858814e-05, -3.1061765184858814e-05, 76.23079681396484, 103.63420104980469]}]}, "brush": {}, "compound": {"elements": [{"paths": [{"usage": "fill", "path": "M60.1379 92.1631 L42.626 90.631 C39.4555 90.3537 36.5286 88.8285 34.4828 86.3905 L14.7567 62.8818 C11.9167 59.4972 12.3582 54.4511 15.7428 51.6111 L59.1767 15.1657 C59.6516 14.7672 60.1592 14.4333 60.6889 14.1631 L60.7514 14.0266 C62.5483 10.1008 66.3162 7.4428 70.6173 7.0665 L76.2308 6.5754 L74.3105 4.2868 C70.0504 -0.7901 62.4814 -1.4523 57.4045 2.8077 L4.2868 47.3787 C-0.7901 51.6388 -1.4523 59.2078 2.8077 64.2847 L33.2731 100.592 L68.0498 103.634 L60.1379 92.1631 Z"}, {"usage": "stroke", "path": "M 60.1379 92.1631 L 42.626 90.631 C 39.4555 90.3537 36.5286 88.8285 34.4828 86.3905 L 14.7567 62.8818 C 11.9167 59.4972 12.3582 54.4511 15.7428 51.6111 L 59.1767 15.1657 C 59.6516 14.7672 60.1592 14.4333 60.6889 14.1631 L 60.7514 14.0266 C 62.5483 10.1008 66.3162 7.4428 70.6173 7.0665 L 76.2308 6.5754 L 74.3105 4.2868 C 70.0504 -0.7901 62.4814 -1.4523 57.4045 2.8077 L 4.2868 47.3787 C -0.7901 51.6388 -1.4523 59.2078 2.8077 64.2847 L 33.2731 100.5919 L 68.0498 103.6342 L 60.1379 92.1631 Z"}], "box": [0, 0, 76.23100280761719, 103.63400268554688], "x": {"c": "min"}, "y": {"c": "min"}}]}, "resizable": {"horizontalControls": false, "verticalControls": false, "cornerControls": false, "showHull": true}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [286.0400085449219, 7189.10009765625], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [274.0400085449219, 355.1000061035156]}}, "item": {}, "inGroup": {"parent": "gr_1hd78uo19jh6vb"}}, {"id": "-1.g-0b_fr_1cxdqq819jh7nk", "v": 46, "theme": {"styles": {"fillOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "strokeOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "strokeColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "gray"}, "type": "ColorScheme"}]}}, "frame": {}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "inLayout": {"rootId": "gr_1hd78uo19jh6vb"}, "inBehavior": {"parents": {"themeFrame": "tf_qu12mo19jpo93"}}, "scenePart": {"partId": "-1.g-0b", "sceneId": "gr_1hd78uo19jh6vb", "drawOrderOffset": 6}, "frameBox": {"localBox": [0, 0, 130.3489990234375, 229.48599243164062]}, "topLeft": {"topLeft": [298.9259948730469, 7202.00341796875]}, "svgSymbols": {"passes": [{"usage": "fill", "outer": "<symbol><g><path d=\"M 55.164979 90.735581L 20.388979 87.693381C 17.079679 87.443881 14.179479 89.871181 13.892879 93.147581C 13.606079 96.425181 16.042779 99.320181 19.347079 99.647281L 10.673679 198.783781C 10.535079 200.368781 11.031779 201.944781 12.054679 203.163781L 17.728379 209.924781L 26.530979 109.310781C 26.819779 106.009681 29.729979 103.567681 33.031079 103.856581C 36.332179 104.145381 38.774079 107.055581 38.485279 110.356681L 28.636779 222.924781L 29.702879 224.195781C 31.122879 225.887781 33.645979 226.108781 35.338279 224.688781L 36.605879 223.624781L 46.715879 108.066181C 47.004679 104.765081 49.914879 102.323081 53.215979 102.611981L 55.209879 102.786481L 55.211279 102.785381L 60.237979 103.225181C 60.154679 102.906081 60.096979 102.575981 60.067279 102.236681C 59.858879 99.854481 61.089179 97.674381 63.045479 96.535481C 60.138179 95.884681 57.528479 94.162381 55.787079 91.637581L 55.164979 90.735581Z\"></path></g></symbol>", "locaLBox": [10.650856971740723, 87.67575073242188, 63.04547882080078, 225.62460327148438]}, {"usage": "stroke", "outer": "<symbol><g><path d=\"M 55.164979 90.735581L 20.388979 87.693381C 17.079679 87.443881 14.179479 89.871181 13.892879 93.147581C 13.606079 96.425181 16.042779 99.320181 19.347079 99.647281L 10.673679 198.783581C 10.535079 200.368881 11.031779 201.944281 12.054679 203.163281L 17.728379 209.924881L 26.530979 109.310781C 26.819779 106.009681 29.729979 103.567681 33.031079 103.856581C 36.332179 104.145381 38.774079 107.055581 38.485279 110.356681L 28.636779 222.925081L 29.702879 224.195581C 31.122879 225.887881 33.645979 226.108581 35.338279 224.688581L 36.605879 223.624781L 46.715879 108.066181C 47.004679 104.765081 49.914879 102.323081 53.215979 102.611981L 55.209879 102.786481L 55.211279 102.785381L 60.237979 103.225181C 60.154679 102.906081 60.096979 102.575981 60.067279 102.236681C 59.858879 99.854481 61.089179 97.674381 63.045479 96.535381C 60.138179 95.884681 57.528479 94.162381 55.787079 91.637581L 55.164979 90.735581ZM 17.728379 209.924881L 17.728179 209.926381M 28.636779 222.925081L 28.636679 222.926681M 53.215979 102.611981L 60.238279 103.226281\"></path></g></symbol>", "locaLBox": [10.650856971740723, 87.67575073242188, 63.04547882080078, 225.62451171875]}, {"usage": "fill", "outer": "<symbol><g><path d=\"M 33.031316 103.855231C 29.730216 103.566431 26.820016 106.008331 26.531216 109.309431L 17.728616 209.924031L 17.728516 209.925031L 28.637016 222.925031L 28.637116 222.924031L 38.485616 110.355331C 38.774416 107.054231 36.332416 104.144031 33.031316 103.855231Z\"></path></g></symbol>", "locaLBox": [17.728515625, 103.83203887939453, 38.50880813598633, 222.92503356933594]}, {"usage": "stroke", "outer": "<symbol><g><path d=\"M 33.031316 103.855231C 29.730216 103.566431 26.820016 106.008331 26.531216 109.309431L 17.728616 209.923631L 17.728516 209.925131L 28.637016 222.925331L 28.637116 222.923831L 38.485616 110.355331C 38.774416 107.054231 36.332416 104.144031 33.031316 103.855231ZM 17.728616 209.923631L 28.637116 222.923831\"></path></g></symbol>", "locaLBox": [17.728515625, 103.83203887939453, 38.50880813598633, 222.92532348632812]}, {"usage": "fill", "outer": "<symbol><g><path d=\"M 53.217134 102.613044C 49.916034 102.324244 47.005834 104.766144 46.717034 108.067244L 36.606934 223.625844L 53.305934 209.613844C 53.712234 209.272844 53.966534 208.784844 54.012734 208.255844L 54.565634 201.936944C 54.611834 201.408544 54.446234 200.883344 54.105334 200.477044L 47.365434 192.444844L 53.230934 187.523144C 53.637334 187.182144 53.891534 186.693744 53.937734 186.165344L 54.316334 181.838744C 54.362534 181.310344 54.196934 180.785144 53.856034 180.378844L 48.934234 174.513344L 53.902334 170.344744C 54.748434 169.634744 54.858834 168.373144 54.148834 167.527044L 49.980134 162.559044L 58.198234 155.663244C 59.044334 154.953244 59.154734 153.691744 58.444734 152.845544L 51.548934 144.627544L 56.516934 140.458844C 57.363134 139.748844 57.473534 138.487344 56.763434 137.641244L 52.594834 132.673144L 58.460334 127.751444C 58.866634 127.410544 59.120934 126.922044 59.167134 126.393644L 59.545634 122.067044C 59.591834 121.538644 59.426334 121.013544 59.085334 120.607144L 54.163634 114.741644L 60.029134 109.819944C 60.435434 109.479044 60.689734 108.990544 60.735934 108.462144L 61.186634 103.310244L 53.217134 102.613044Z\"></path></g></symbol>", "locaLBox": [36.60693359375, 102.58985137939453, 61.1866340637207, 223.62583923339844]}, {"usage": "stroke", "outer": "<symbol><g><path d=\"M 53.217134 102.613044C 49.916034 102.324244 47.005834 104.766144 46.717034 108.067244L 36.606934 223.625844L 53.305934 209.613744C 53.712234 209.272744 53.966534 208.784344 54.012734 208.255944L 54.565634 201.936944C 54.611834 201.408544 54.446234 200.883344 54.105334 200.477044L 47.365434 192.444844L 53.230934 187.523144C 53.637334 187.182144 53.891534 186.693744 53.937734 186.165344L 54.316334 181.838744C 54.362534 181.310344 54.196934 180.785144 53.856034 180.378844L 48.934234 174.513344L 53.902334 170.344744C 54.748434 169.634744 54.858834 168.373144 54.148834 167.527044L 49.980134 162.559044L 58.198234 155.663244C 59.044334 154.953244 59.154734 153.691744 58.444734 152.845544L 51.548934 144.627544L 56.516934 140.458844C 57.363134 139.748844 57.473434 138.487344 56.763434 137.641244L 52.594834 132.673144L 58.460334 127.751444C 58.866634 127.410544 59.120934 126.922044 59.167134 126.393644L 59.545634 122.067044C 59.591834 121.538644 59.426334 121.013544 59.085334 120.607144L 54.163634 114.741644L 60.029134 109.819944C 60.435434 109.479044 60.689734 108.990544 60.735934 108.462144L 61.186634 103.310244L 53.217134 102.613044ZM 55.212334 102.786444L 55.211034 102.787644L 53.217134 102.613044\"></path></g></symbol>", "locaLBox": [36.60693359375, 102.58985137939453, 61.1866340637207, 223.62583923339844]}, {"usage": "fill", "outer": "<symbol><g><path d=\"M 28.96 42.432125C 27.2009 46.275325 27.593 50.759925 29.9927 54.239225L 47.2536 79.265125L 29.7415 77.733025C 26.5711 77.455625 23.6437 75.930425 21.598 73.492325L 1.8718 49.983725C -0.9682 46.599125 -0.5267 41.553025 2.8579 38.713025L 46.2918 2.267625C 46.7664 1.869425 47.2736 1.535725 47.8028 1.265625L 28.96 42.432125Z\"></path></g></symbol>", "locaLBox": [-6.890656095492886e-06, 1.265625, 47.802799224853516, 79.26512145996094]}, {"usage": "stroke", "outer": "<symbol><g><path d=\"M 28.96 42.432125C 27.2009 46.275325 27.593 50.759925 29.9927 54.239225L 47.2536 79.265125L 29.7415 77.733025C 26.5711 77.455625 23.6437 75.930425 21.598 73.492325L 1.8718 49.983725C -0.9682 46.599125 -0.5267 41.553025 2.8579 38.713025L 46.2918 2.267625C 46.7664 1.869425 47.2736 1.535725 47.8028 1.265625L 28.96 42.432125Z\"></path></g></symbol>", "locaLBox": [-6.890656095492886e-06, 1.265625, 47.802799224853516, 79.26512145996094]}, {"usage": "fill", "outer": "<symbol><g><path d=\"M 100.437849 104.724544L 108.407449 104.027344L 109.904049 121.133544C 109.950249 121.661944 109.784649 122.187144 109.443749 122.593444L 104.522049 128.458944L 109.490049 132.627644C 110.336149 133.337644 110.446549 134.599144 109.736549 135.445244L 105.567849 140.413344L 111.433349 145.335044C 111.839649 145.675944 112.093949 146.164444 112.140149 146.692844L 112.518749 151.019444C 112.564949 151.547844 112.399349 152.072944 112.058449 152.479344L 107.136649 158.344844L 113.002149 163.266544C 113.408549 163.607444 113.662749 164.095944 113.708949 164.624344L 114.087549 168.950944C 114.133749 169.479344 113.968149 170.004444 113.627249 170.410844L 108.705449 176.276344L 116.737649 183.016144C 117.144049 183.357044 117.398249 183.845444 117.444449 184.373844L 117.997349 190.692944C 118.043549 191.221344 117.877949 191.746444 117.537049 192.152744L 110.797249 200.184944L 119.015249 207.080344C 119.861449 207.790344 119.971849 209.052344 119.261749 209.898344L 105.093749 226.783344L 94.983649 111.224744C 94.694849 107.923644 97.136749 105.013444 100.437849 104.724544Z\"></path></g></symbol>", "locaLBox": [94.96045684814453, 104.02734375, 119.72976684570312, 226.78334045410156]}, {"usage": "stroke", "outer": "<symbol><g><path d=\"M 100.437849 104.724544L 108.407449 104.027344L 109.904049 121.133544C 109.950249 121.661944 109.784649 122.187144 109.443749 122.593444L 104.522049 128.458944L 109.490049 132.627644C 110.336149 133.337644 110.446549 134.599144 109.736549 135.445244L 105.567849 140.413344L 111.433349 145.335044C 111.839649 145.675944 112.093949 146.164444 112.140149 146.692844L 112.518749 151.019444C 112.564949 151.547844 112.399349 152.072944 112.058449 152.479344L 107.136649 158.344844L 113.002149 163.266544C 113.408549 163.607444 113.662749 164.095944 113.708949 164.624344L 114.087549 168.950944C 114.133749 169.479344 113.968149 170.004444 113.627249 170.410844L 108.705449 176.276344L 116.737649 183.016144C 117.144049 183.357044 117.398249 183.845444 117.444449 184.373844L 117.997349 190.692944C 118.043549 191.221344 117.877949 191.746444 117.537049 192.152744L 110.797249 200.184944L 119.015249 207.080744C 119.861449 207.790744 119.971849 209.052244 119.261749 209.898444L 105.093749 226.783244L 94.983649 111.224644C 94.694849 107.923544 97.136749 105.013444 100.437849 104.724544Z\"></path></g></symbol>", "locaLBox": [94.96045684814453, 104.02734375, 119.72976684570312, 226.7832489013672]}, {"usage": "fill", "outer": "<symbol><g><path d=\"M 80.776716 109.456794C 77.475616 109.745594 75.033716 112.655794 75.322516 115.956894L 84.125216 216.572594L 97.125416 227.480594L 87.276816 114.910994C 86.988016 111.609894 84.077816 109.167994 80.776716 109.456794ZM 87.276816 114.910994L 97.125416 227.480594L 84.125216 216.572594L 75.322516 115.956894C 75.033716 112.655794 77.475616 109.745594 80.776716 109.456794C 84.077816 109.167994 86.988016 111.609894 87.276816 114.910994ZM 97.125416 227.480594L 87.276816 114.910994C 86.988016 111.609894 84.077816 109.167994 80.776716 109.456794C 77.475616 109.745594 75.033716 112.655794 75.322516 115.956894L 84.125216 216.572594L 97.125416 227.480594Z\"></path></g></symbol>", "locaLBox": [75.29932403564453, 109.43360137939453, 97.12541961669922, 227.4805908203125]}, {"usage": "stroke", "outer": "<symbol><g><path d=\"M 80.776716 109.456794C 77.475616 109.745594 75.033716 112.655794 75.322516 115.956894L 84.125216 216.572594L 97.125416 227.480994L 87.276816 114.910994C 86.988016 111.609894 84.077816 109.167994 80.776716 109.456794ZM 87.276816 114.910994L 97.125416 227.480994L 84.125216 216.572594L 75.322516 115.956894C 75.033716 112.655794 77.475616 109.745594 80.776716 109.456794C 84.077816 109.167994 86.988016 111.609894 87.276816 114.910994ZM 97.125416 227.480994L 87.276816 114.910994C 86.988016 111.609894 84.077816 109.167994 80.776716 109.456794C 77.475616 109.745594 75.033716 112.655794 75.322516 115.956894L 84.125216 216.572594L 97.125416 227.480994Z\"></path></g></symbol>", "locaLBox": [75.29932403564453, 109.43360137939453, 97.12541961669922, 227.48098754882812]}, {"usage": "fill", "outer": "<symbol><g><path d=\"M 115.703534 91.515625C 118.411634 91.916625 120.588734 94.105425 120.835134 96.921425C 121.123934 100.222525 118.650234 103.135525 115.309834 103.427825L 100.439334 104.728225C 97.138234 105.017025 94.696334 107.927225 94.985134 111.228325L 105.095234 226.786625L 104.028834 228.056625C 102.608834 229.748625 100.085834 229.969625 98.393534 228.549625L 97.126034 227.485625L 87.277434 114.915225C 86.988634 111.614125 84.078434 109.172225 80.777334 109.461025C 77.476234 109.749825 75.034334 112.660025 75.323134 115.961125L 84.125834 216.576625L 77.361134 210.901625C 76.142134 209.878625 75.379334 208.413625 75.240634 206.828625L 66.567334 107.691825C 63.256534 107.943525 60.354134 105.515625 60.067334 102.237925C 59.858834 99.854925 61.090034 97.674125 63.047534 96.535525L 115.703534 91.515625Z\"></path></g></symbol>", "locaLBox": [60.04440689086914, 91.515625, 120.8580551147461, 229.48545837402344]}, {"usage": "stroke", "outer": "<symbol><g><path d=\"M 115.703534 91.515625C 118.411634 91.916625 120.588734 94.105425 120.835134 96.921425C 121.123934 100.222525 118.650234 103.135525 115.309834 103.427825L 100.439334 104.728225C 97.138234 105.017025 94.696334 107.927225 94.985134 111.228325L 105.095234 226.786925L 104.028834 228.056725C 102.608834 229.749025 100.085834 229.969725 98.393534 228.549725L 97.126034 227.485225L 87.277434 114.915225C 86.988634 111.614125 84.078434 109.172225 80.777334 109.461025C 77.476234 109.749825 75.034334 112.660025 75.323134 115.961125L 84.125834 216.576825L 77.361134 210.901525C 76.142134 209.878625 75.379334 208.413425 75.240634 206.828225L 66.567334 107.691825C 63.256534 107.943525 60.354134 105.515625 60.067334 102.237925C 59.858834 99.854925 61.090034 97.674125 63.047534 96.535525L 115.703534 91.515625ZM 115.309834 103.427825L 102.430434 104.554625\"></path></g></symbol>", "locaLBox": [60.04440689086914, 91.515625, 120.8580551147461, 229.48565673828125]}, {"usage": "fill", "outer": "<symbol><g><path d=\"M 110.84774 9.1987L 106.24004 2.5741C 105.01214 0.8087 102.93354 -0.1645 100.79134 0.0229L 60.89714 3.5132C 58.75494 3.7006 56.87694 5.02 55.97424 6.9717L 39.04004 43.587C 38.14594 45.5204 38.34384 47.7829 39.56014 49.5316L 59.93674 78.8277C 61.04774 80.425 63.55654 79.5163 63.38694 77.578C 63.09814 74.2769 65.54004 71.3667 68.84114 71.0779L 104.70414 67.9403C 108.00524 67.6515 110.91544 70.0934 111.20424 73.3945C 111.37374 75.3311 113.99984 75.79 114.81584 74.0255L 129.79514 41.6371C 130.68934 39.7037 130.49144 37.4412 129.27514 35.6924L 117.34404 18.5388C 110.37984 21.1672 103.15824 23.2713 95.72754 24.8028C 94.55424 27.1706 92.25484 28.8779 89.48524 29.1202L 77.08814 30.2048C 74.62354 30.4204 72.32424 29.4289 70.74374 27.7084C 69.65274 26.5208 68.90424 24.9858 68.68044 23.2606C 68.66554 23.1456 68.65294 23.0297 68.64274 22.9131C 68.43434 20.5312 69.26324 18.3045 70.74584 16.7032C 72.00304 15.3455 73.73014 14.4374 75.69364 14.2657L 88.09074 13.181C 89.72654 13.0379 91.28954 13.4266 92.61794 14.2072C 98.86324 12.964 104.95094 11.2834 110.84774 9.1987Z\"></path></g></symbol>", "locaLBox": [38.48583221435547, 7.958065907587297e-06, 130.34942626953125, 79.64743041992188]}, {"usage": "stroke", "outer": "<symbol><g><path d=\"M 110.84774 9.1987L 106.24004 2.5741C 105.01214 0.8087 102.93354 -0.1645 100.79134 0.0229L 60.89714 3.5132C 58.75494 3.7006 56.87694 5.02 55.97424 6.9717L 39.04004 43.587C 38.14594 45.5204 38.34384 47.7829 39.56014 49.5316L 59.93674 78.8277C 61.04774 80.425 63.55654 79.5163 63.38694 77.578C 63.09814 74.2769 65.54004 71.3667 68.84114 71.0779L 104.70414 67.9403C 108.00524 67.6515 110.91544 70.0934 111.20424 73.3945C 111.37374 75.3311 113.99984 75.79 114.81584 74.0255L 129.79514 41.6371C 130.68934 39.7037 130.49144 37.4412 129.27514 35.6924L 117.34404 18.5388C 110.37984 21.1672 103.15824 23.2713 95.72754 24.8028C 94.55424 27.1706 92.25484 28.8779 89.48524 29.1202L 77.08814 30.2048C 74.62354 30.4204 72.32424 29.4289 70.74374 27.7084C 69.65274 26.5208 68.90424 24.9858 68.68044 23.2606C 68.66554 23.1456 68.65294 23.0297 68.64274 22.9131C 68.43434 20.5312 69.26324 18.3045 70.74584 16.7032C 72.00304 15.3455 73.73014 14.4374 75.69364 14.2657L 88.09074 13.181C 89.72654 13.0379 91.28954 13.4266 92.61794 14.2072C 98.86324 12.964 104.95094 11.2834 110.84774 9.1987Z\"></path></g></symbol>", "locaLBox": [38.48583221435547, 7.958065907587297e-06, 130.34942626953125, 79.64743041992188]}]}, "brush": {}, "compound": {"elements": [{"paths": [{"usage": "fill", "path": "M 55.164979 90.735581L 20.388979 87.693381C 17.079679 87.443881 14.179479 89.871181 13.892879 93.147581C 13.606079 96.425181 16.042779 99.320181 19.347079 99.647281L 10.673679 198.783781C 10.535079 200.368781 11.031779 201.944781 12.054679 203.163781L 17.728379 209.924781L 26.530979 109.310781C 26.819779 106.009681 29.729979 103.567681 33.031079 103.856581C 36.332179 104.145381 38.774079 107.055581 38.485279 110.356681L 28.636779 222.924781L 29.702879 224.195781C 31.122879 225.887781 33.645979 226.108781 35.338279 224.688781L 36.605879 223.624781L 46.715879 108.066181C 47.004679 104.765081 49.914879 102.323081 53.215979 102.611981L 55.209879 102.786481L 55.211279 102.785381L 60.237979 103.225181C 60.154679 102.906081 60.096979 102.575981 60.067279 102.236681C 59.858879 99.854481 61.089179 97.674381 63.045479 96.535481C 60.138179 95.884681 57.528479 94.162381 55.787079 91.637581L 55.164979 90.735581Z"}, {"usage": "stroke", "path": "M 55.164979 90.735581L 20.388979 87.693381C 17.079679 87.443881 14.179479 89.871181 13.892879 93.147581C 13.606079 96.425181 16.042779 99.320181 19.347079 99.647281L 10.673679 198.783581C 10.535079 200.368881 11.031779 201.944281 12.054679 203.163281L 17.728379 209.924881L 26.530979 109.310781C 26.819779 106.009681 29.729979 103.567681 33.031079 103.856581C 36.332179 104.145381 38.774079 107.055581 38.485279 110.356681L 28.636779 222.925081L 29.702879 224.195581C 31.122879 225.887881 33.645979 226.108581 35.338279 224.688581L 36.605879 223.624781L 46.715879 108.066181C 47.004679 104.765081 49.914879 102.323081 53.215979 102.611981L 55.209879 102.786481L 55.211279 102.785381L 60.237979 103.225181C 60.154679 102.906081 60.096979 102.575981 60.067279 102.236681C 59.858879 99.854481 61.089179 97.674381 63.045479 96.535381C 60.138179 95.884681 57.528479 94.162381 55.787079 91.637581L 55.164979 90.735581ZM 17.728379 209.924881L 17.728179 209.926381M 28.636779 222.925081L 28.636679 222.926681M 53.215979 102.611981L 60.238279 103.226281"}, {"usage": "fill", "path": "M 33.031316 103.855231C 29.730216 103.566431 26.820016 106.008331 26.531216 109.309431L 17.728616 209.924031L 17.728516 209.925031L 28.637016 222.925031L 28.637116 222.924031L 38.485616 110.355331C 38.774416 107.054231 36.332416 104.144031 33.031316 103.855231Z"}, {"usage": "stroke", "path": "M 33.031316 103.855231C 29.730216 103.566431 26.820016 106.008331 26.531216 109.309431L 17.728616 209.923631L 17.728516 209.925131L 28.637016 222.925331L 28.637116 222.923831L 38.485616 110.355331C 38.774416 107.054231 36.332416 104.144031 33.031316 103.855231ZM 17.728616 209.923631L 28.637116 222.923831"}, {"usage": "fill", "path": "M 53.217134 102.613044C 49.916034 102.324244 47.005834 104.766144 46.717034 108.067244L 36.606934 223.625844L 53.305934 209.613844C 53.712234 209.272844 53.966534 208.784844 54.012734 208.255844L 54.565634 201.936944C 54.611834 201.408544 54.446234 200.883344 54.105334 200.477044L 47.365434 192.444844L 53.230934 187.523144C 53.637334 187.182144 53.891534 186.693744 53.937734 186.165344L 54.316334 181.838744C 54.362534 181.310344 54.196934 180.785144 53.856034 180.378844L 48.934234 174.513344L 53.902334 170.344744C 54.748434 169.634744 54.858834 168.373144 54.148834 167.527044L 49.980134 162.559044L 58.198234 155.663244C 59.044334 154.953244 59.154734 153.691744 58.444734 152.845544L 51.548934 144.627544L 56.516934 140.458844C 57.363134 139.748844 57.473534 138.487344 56.763434 137.641244L 52.594834 132.673144L 58.460334 127.751444C 58.866634 127.410544 59.120934 126.922044 59.167134 126.393644L 59.545634 122.067044C 59.591834 121.538644 59.426334 121.013544 59.085334 120.607144L 54.163634 114.741644L 60.029134 109.819944C 60.435434 109.479044 60.689734 108.990544 60.735934 108.462144L 61.186634 103.310244L 53.217134 102.613044Z"}, {"usage": "stroke", "path": "M 53.217134 102.613044C 49.916034 102.324244 47.005834 104.766144 46.717034 108.067244L 36.606934 223.625844L 53.305934 209.613744C 53.712234 209.272744 53.966534 208.784344 54.012734 208.255944L 54.565634 201.936944C 54.611834 201.408544 54.446234 200.883344 54.105334 200.477044L 47.365434 192.444844L 53.230934 187.523144C 53.637334 187.182144 53.891534 186.693744 53.937734 186.165344L 54.316334 181.838744C 54.362534 181.310344 54.196934 180.785144 53.856034 180.378844L 48.934234 174.513344L 53.902334 170.344744C 54.748434 169.634744 54.858834 168.373144 54.148834 167.527044L 49.980134 162.559044L 58.198234 155.663244C 59.044334 154.953244 59.154734 153.691744 58.444734 152.845544L 51.548934 144.627544L 56.516934 140.458844C 57.363134 139.748844 57.473434 138.487344 56.763434 137.641244L 52.594834 132.673144L 58.460334 127.751444C 58.866634 127.410544 59.120934 126.922044 59.167134 126.393644L 59.545634 122.067044C 59.591834 121.538644 59.426334 121.013544 59.085334 120.607144L 54.163634 114.741644L 60.029134 109.819944C 60.435434 109.479044 60.689734 108.990544 60.735934 108.462144L 61.186634 103.310244L 53.217134 102.613044ZM 55.212334 102.786444L 55.211034 102.787644L 53.217134 102.613044"}, {"usage": "fill", "path": "M 28.96 42.432125C 27.2009 46.275325 27.593 50.759925 29.9927 54.239225L 47.2536 79.265125L 29.7415 77.733025C 26.5711 77.455625 23.6437 75.930425 21.598 73.492325L 1.8718 49.983725C -0.9682 46.599125 -0.5267 41.553025 2.8579 38.713025L 46.2918 2.267625C 46.7664 1.869425 47.2736 1.535725 47.8028 1.265625L 28.96 42.432125Z"}, {"usage": "stroke", "path": "M 28.96 42.432125C 27.2009 46.275325 27.593 50.759925 29.9927 54.239225L 47.2536 79.265125L 29.7415 77.733025C 26.5711 77.455625 23.6437 75.930425 21.598 73.492325L 1.8718 49.983725C -0.9682 46.599125 -0.5267 41.553025 2.8579 38.713025L 46.2918 2.267625C 46.7664 1.869425 47.2736 1.535725 47.8028 1.265625L 28.96 42.432125Z"}], "box": [0, 1.2660000324249268, 63.04600143432617, 225.625], "x": {"c": "min"}, "y": {"c": "min", "before": 1.2660000324249268}}, {"paths": [{"usage": "fill", "path": "M 100.437849 104.724544L 108.407449 104.027344L 109.904049 121.133544C 109.950249 121.661944 109.784649 122.187144 109.443749 122.593444L 104.522049 128.458944L 109.490049 132.627644C 110.336149 133.337644 110.446549 134.599144 109.736549 135.445244L 105.567849 140.413344L 111.433349 145.335044C 111.839649 145.675944 112.093949 146.164444 112.140149 146.692844L 112.518749 151.019444C 112.564949 151.547844 112.399349 152.072944 112.058449 152.479344L 107.136649 158.344844L 113.002149 163.266544C 113.408549 163.607444 113.662749 164.095944 113.708949 164.624344L 114.087549 168.950944C 114.133749 169.479344 113.968149 170.004444 113.627249 170.410844L 108.705449 176.276344L 116.737649 183.016144C 117.144049 183.357044 117.398249 183.845444 117.444449 184.373844L 117.997349 190.692944C 118.043549 191.221344 117.877949 191.746444 117.537049 192.152744L 110.797249 200.184944L 119.015249 207.080344C 119.861449 207.790344 119.971849 209.052344 119.261749 209.898344L 105.093749 226.783344L 94.983649 111.224744C 94.694849 107.923644 97.136749 105.013444 100.437849 104.724544Z"}, {"usage": "stroke", "path": "M 100.437849 104.724544L 108.407449 104.027344L 109.904049 121.133544C 109.950249 121.661944 109.784649 122.187144 109.443749 122.593444L 104.522049 128.458944L 109.490049 132.627644C 110.336149 133.337644 110.446549 134.599144 109.736549 135.445244L 105.567849 140.413344L 111.433349 145.335044C 111.839649 145.675944 112.093949 146.164444 112.140149 146.692844L 112.518749 151.019444C 112.564949 151.547844 112.399349 152.072944 112.058449 152.479344L 107.136649 158.344844L 113.002149 163.266544C 113.408549 163.607444 113.662749 164.095944 113.708949 164.624344L 114.087549 168.950944C 114.133749 169.479344 113.968149 170.004444 113.627249 170.410844L 108.705449 176.276344L 116.737649 183.016144C 117.144049 183.357044 117.398249 183.845444 117.444449 184.373844L 117.997349 190.692944C 118.043549 191.221344 117.877949 191.746444 117.537049 192.152744L 110.797249 200.184944L 119.015249 207.080744C 119.861449 207.790744 119.971849 209.052244 119.261749 209.898444L 105.093749 226.783244L 94.983649 111.224644C 94.694849 107.923544 97.136749 105.013444 100.437849 104.724544Z"}, {"usage": "fill", "path": "M 80.776716 109.456794C 77.475616 109.745594 75.033716 112.655794 75.322516 115.956894L 84.125216 216.572594L 97.125416 227.480594L 87.276816 114.910994C 86.988016 111.609894 84.077816 109.167994 80.776716 109.456794ZM 87.276816 114.910994L 97.125416 227.480594L 84.125216 216.572594L 75.322516 115.956894C 75.033716 112.655794 77.475616 109.745594 80.776716 109.456794C 84.077816 109.167994 86.988016 111.609894 87.276816 114.910994ZM 97.125416 227.480594L 87.276816 114.910994C 86.988016 111.609894 84.077816 109.167994 80.776716 109.456794C 77.475616 109.745594 75.033716 112.655794 75.322516 115.956894L 84.125216 216.572594L 97.125416 227.480594Z"}, {"usage": "stroke", "path": "M 80.776716 109.456794C 77.475616 109.745594 75.033716 112.655794 75.322516 115.956894L 84.125216 216.572594L 97.125416 227.480994L 87.276816 114.910994C 86.988016 111.609894 84.077816 109.167994 80.776716 109.456794ZM 87.276816 114.910994L 97.125416 227.480994L 84.125216 216.572594L 75.322516 115.956894C 75.033716 112.655794 77.475616 109.745594 80.776716 109.456794C 84.077816 109.167994 86.988016 111.609894 87.276816 114.910994ZM 97.125416 227.480994L 87.276816 114.910994C 86.988016 111.609894 84.077816 109.167994 80.776716 109.456794C 77.475616 109.745594 75.033716 112.655794 75.322516 115.956894L 84.125216 216.572594L 97.125416 227.480994Z"}, {"usage": "fill", "path": "M 115.703534 91.515625C 118.411634 91.916625 120.588734 94.105425 120.835134 96.921425C 121.123934 100.222525 118.650234 103.135525 115.309834 103.427825L 100.439334 104.728225C 97.138234 105.017025 94.696334 107.927225 94.985134 111.228325L 105.095234 226.786625L 104.028834 228.056625C 102.608834 229.748625 100.085834 229.969625 98.393534 228.549625L 97.126034 227.485625L 87.277434 114.915225C 86.988634 111.614125 84.078434 109.172225 80.777334 109.461025C 77.476234 109.749825 75.034334 112.660025 75.323134 115.961125L 84.125834 216.576625L 77.361134 210.901625C 76.142134 209.878625 75.379334 208.413625 75.240634 206.828625L 66.567334 107.691825C 63.256534 107.943525 60.354134 105.515625 60.067334 102.237925C 59.858834 99.854925 61.090034 97.674125 63.047534 96.535525L 115.703534 91.515625Z"}, {"usage": "stroke", "path": "M 115.703534 91.515625C 118.411634 91.916625 120.588734 94.105425 120.835134 96.921425C 121.123934 100.222525 118.650234 103.135525 115.309834 103.427825L 100.439334 104.728225C 97.138234 105.017025 94.696334 107.927225 94.985134 111.228325L 105.095234 226.786925L 104.028834 228.056725C 102.608834 229.749025 100.085834 229.969725 98.393534 228.549725L 97.126034 227.485225L 87.277434 114.915225C 86.988634 111.614125 84.078434 109.172225 80.777334 109.461025C 77.476234 109.749825 75.034334 112.660025 75.323134 115.961125L 84.125834 216.576825L 77.361134 210.901525C 76.142134 209.878625 75.379334 208.413425 75.240634 206.828225L 66.567334 107.691825C 63.256534 107.943525 60.354134 105.515625 60.067334 102.237925C 59.858834 99.854925 61.090034 97.674125 63.047534 96.535525L 115.703534 91.515625ZM 115.309834 103.427825L 102.430434 104.554625"}, {"usage": "fill", "path": "M 110.84774 9.1987L 106.24004 2.5741C 105.01214 0.8087 102.93354 -0.1645 100.79134 0.0229L 60.89714 3.5132C 58.75494 3.7006 56.87694 5.02 55.97424 6.9717L 39.04004 43.587C 38.14594 45.5204 38.34384 47.7829 39.56014 49.5316L 59.93674 78.8277C 61.04774 80.425 63.55654 79.5163 63.38694 77.578C 63.09814 74.2769 65.54004 71.3667 68.84114 71.0779L 104.70414 67.9403C 108.00524 67.6515 110.91544 70.0934 111.20424 73.3945C 111.37374 75.3311 113.99984 75.79 114.81584 74.0255L 129.79514 41.6371C 130.68934 39.7037 130.49144 37.4412 129.27514 35.6924L 117.34404 18.5388C 110.37984 21.1672 103.15824 23.2713 95.72754 24.8028C 94.55424 27.1706 92.25484 28.8779 89.48524 29.1202L 77.08814 30.2048C 74.62354 30.4204 72.32424 29.4289 70.74374 27.7084C 69.65274 26.5208 68.90424 24.9858 68.68044 23.2606C 68.66554 23.1456 68.65294 23.0297 68.64274 22.9131C 68.43434 20.5312 69.26324 18.3045 70.74584 16.7032C 72.00304 15.3455 73.73014 14.4374 75.69364 14.2657L 88.09074 13.181C 89.72654 13.0379 91.28954 13.4266 92.61794 14.2072C 98.86324 12.964 104.95094 11.2834 110.84774 9.1987Z"}, {"usage": "stroke", "path": "M 110.84774 9.1987L 106.24004 2.5741C 105.01214 0.8087 102.93354 -0.1645 100.79134 0.0229L 60.89714 3.5132C 58.75494 3.7006 56.87694 5.02 55.97424 6.9717L 39.04004 43.587C 38.14594 45.5204 38.34384 47.7829 39.56014 49.5316L 59.93674 78.8277C 61.04774 80.425 63.55654 79.5163 63.38694 77.578C 63.09814 74.2769 65.54004 71.3667 68.84114 71.0779L 104.70414 67.9403C 108.00524 67.6515 110.91544 70.0934 111.20424 73.3945C 111.37374 75.3311 113.99984 75.79 114.81584 74.0255L 129.79514 41.6371C 130.68934 39.7037 130.49144 37.4412 129.27514 35.6924L 117.34404 18.5388C 110.37984 21.1672 103.15824 23.2713 95.72754 24.8028C 94.55424 27.1706 92.25484 28.8779 89.48524 29.1202L 77.08814 30.2048C 74.62354 30.4204 72.32424 29.4289 70.74374 27.7084C 69.65274 26.5208 68.90424 24.9858 68.68044 23.2606C 68.66554 23.1456 68.65294 23.0297 68.64274 22.9131C 68.43434 20.5312 69.26324 18.3045 70.74584 16.7032C 72.00304 15.3455 73.73014 14.4374 75.69364 14.2657L 88.09074 13.181C 89.72654 13.0379 91.28954 13.4266 92.61794 14.2072C 98.86324 12.964 104.95094 11.2834 110.84774 9.1987Z"}], "box": [38.486000061035156, 0, 130.35000610351562, 229.48599243164062], "x": {"c": "min", "before": 38.486000061035156}, "y": {"c": "min"}}]}, "resizable": {"horizontalControls": false, "verticalControls": false, "cornerControls": false, "showHull": true}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [298.92999267578125, 7202], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [286.92999267578125, 368]}}, "item": {}, "inGroup": {"parent": "gr_1hd78uo19jh6vb"}}, {"id": "4.g-4_fr_v6q7bk19jh6vj", "v": 46, "theme": {"styles": {"fillOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "strokeOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "strokeColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "yellow"}, "type": "ColorScheme"}], "fillColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "yellow"}, "type": "ColorScheme"}]}}, "frame": {}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "inLayout": {"rootId": "gr_1hd78uo19jh6vb"}, "inBehavior": {"parents": {"themeFrame": "tf_qu12mo19jpo93"}}, "scenePart": {"brickIndex": 2, "partId": "2.g-2", "sceneId": "gr_1hd78uo19jh6vb", "drawOrderOffset": 11}, "frameBox": {"localBox": [0, 0, 113.05899810791016, 239.24000549316406]}, "topLeft": {"topLeft": [326.79901123046875, 7192.24951171875]}, "svgSymbols": {"passes": [{"usage": "fill", "outer": "<symbol><g><path d=\"M92.3907 15.6913 L85.1455 5.1869 C82.6941 1.6328 78.5225 -0.3305 74.2214 0.0458 L29.8606 3.9269 C25.5595 4.3031 21.7922 6.961 19.9952 10.8868 L1.0888 52.1918 C-0.6703 56.0349 -0.2779 60.5201 2.1218 63.9994 L27.9136 101.394 C29.6553 103.919 32.2656 105.641 35.1736 106.292 L87.8296 101.272 C90.0989 100.053 91.9559 98.1144 93.0639 95.6939 L111.97 54.3889 C113.729 50.5458 113.337 46.0606 110.937 42.5813 L98.7453 24.9047 C95.8914 26.2025 92.9877 27.4099 90.0379 28.5232 L101.968 45.6762 C103.185 47.425 103.383 49.6875 102.489 51.6209 L87.5092 84.0093 C86.6932 85.7738 84.0671 85.3149 83.8976 83.3783 C83.6088 80.0773 80.6986 77.6353 77.3975 77.9241 L41.5345 81.0617 C38.2334 81.3505 35.7915 84.2607 36.0803 87.5618 C36.2499 89.5001 33.7411 90.4088 32.6301 88.8115 L12.2535 59.5154 C11.0372 57.7667 10.8393 55.5042 11.7334 53.5708 L28.6676 16.9555 C29.5703 15.0038 31.4483 13.6844 33.5905 13.497 L73.4847 10.0067 C75.6269 9.8193 77.7055 10.7925 78.9334 12.5578 L83.5415 19.1831 C86.5421 18.1224 89.4933 16.957 92.3907 15.6913 Z\"></path></g></symbol>", "locaLBox": [3.911759631591849e-05, 9.914291695167776e-06, 113.05880737304688, 106.29199981689453]}, {"usage": "stroke", "outer": "<symbol><g><path d=\"M 92.3907 15.6913 L 85.1455 5.1869 C 82.6941 1.6328 78.5225 -0.3305 74.2214 0.0458 L 29.8606 3.9269 C 25.5595 4.3032 21.7922 6.961 19.9952 10.8868 L 1.0888 52.1918 C -0.6703 56.0349 -0.2779 60.5201 2.1218 63.9994 L 27.9136 101.3938 C 29.6553 103.9191 32.2656 105.6413 35.1736 106.2917 L 87.8296 101.2718 C 90.0989 100.0529 91.9559 98.1144 93.0639 95.6939 L 111.9703 54.3889 C 113.7294 50.5458 113.337 46.0606 110.9373 42.5813 L 98.7453 24.9047 C 95.8914 26.2025 92.9877 27.4099 90.0379 28.5232 L 101.9685 45.6762 C 103.1848 47.425 103.3827 49.6875 102.4885 51.6209 L 87.5092 84.0093 C 86.6932 85.7738 84.0671 85.3149 83.8976 83.3783 C 83.6088 80.0773 80.6986 77.6353 77.3975 77.9241 L 41.5345 81.0617 C 38.2334 81.3505 35.7915 84.2607 36.0803 87.5618 C 36.2499 89.5001 33.7411 90.4088 32.6301 88.8115 L 12.2535 59.5154 C 11.0372 57.7667 10.8393 55.5042 11.7334 53.5708 L 28.6676 16.9555 C 29.5703 15.0038 31.4483 13.6844 33.5905 13.497 L 73.4847 10.0067 C 75.6269 9.8193 77.7055 10.7925 78.9334 12.5578 L 83.5415 19.1831 C 86.5421 18.1224 89.4933 16.957 92.3907 15.6913 Z\"></path></g></symbol>", "locaLBox": [3.911759631591849e-05, 9.914291695167776e-06, 113.0590591430664, 106.29170227050781]}]}, "brush": {}, "compound": {"elements": [{"paths": [{"usage": "fill", "path": "M92.3907 15.6913 L85.1455 5.1869 C82.6941 1.6328 78.5225 -0.3305 74.2214 0.0458 L29.8606 3.9269 C25.5595 4.3031 21.7922 6.961 19.9952 10.8868 L1.0888 52.1918 C-0.6703 56.0349 -0.2779 60.5201 2.1218 63.9994 L27.9136 101.394 C29.6553 103.919 32.2656 105.641 35.1736 106.292 L87.8296 101.272 C90.0989 100.053 91.9559 98.1144 93.0639 95.6939 L111.97 54.3889 C113.729 50.5458 113.337 46.0606 110.937 42.5813 L98.7453 24.9047 C95.8914 26.2025 92.9877 27.4099 90.0379 28.5232 L101.968 45.6762 C103.185 47.425 103.383 49.6875 102.489 51.6209 L87.5092 84.0093 C86.6932 85.7738 84.0671 85.3149 83.8976 83.3783 C83.6088 80.0773 80.6986 77.6353 77.3975 77.9241 L41.5345 81.0617 C38.2334 81.3505 35.7915 84.2607 36.0803 87.5618 C36.2499 89.5001 33.7411 90.4088 32.6301 88.8115 L12.2535 59.5154 C11.0372 57.7667 10.8393 55.5042 11.7334 53.5708 L28.6676 16.9555 C29.5703 15.0038 31.4483 13.6844 33.5905 13.497 L73.4847 10.0067 C75.6269 9.8193 77.7055 10.7925 78.9334 12.5578 L83.5415 19.1831 C86.5421 18.1224 89.4933 16.957 92.3907 15.6913 Z"}, {"usage": "stroke", "path": "M 92.3907 15.6913 L 85.1455 5.1869 C 82.6941 1.6328 78.5225 -0.3305 74.2214 0.0458 L 29.8606 3.9269 C 25.5595 4.3032 21.7922 6.961 19.9952 10.8868 L 1.0888 52.1918 C -0.6703 56.0349 -0.2779 60.5201 2.1218 63.9994 L 27.9136 101.3938 C 29.6553 103.9191 32.2656 105.6413 35.1736 106.2917 L 87.8296 101.2718 C 90.0989 100.0529 91.9559 98.1144 93.0639 95.6939 L 111.9703 54.3889 C 113.7294 50.5458 113.337 46.0606 110.9373 42.5813 L 98.7453 24.9047 C 95.8914 26.2025 92.9877 27.4099 90.0379 28.5232 L 101.9685 45.6762 C 103.1848 47.425 103.3827 49.6875 102.4885 51.6209 L 87.5092 84.0093 C 86.6932 85.7738 84.0671 85.3149 83.8976 83.3783 C 83.6088 80.0773 80.6986 77.6353 77.3975 77.9241 L 41.5345 81.0617 C 38.2334 81.3505 35.7915 84.2607 36.0803 87.5618 C 36.2499 89.5001 33.7411 90.4088 32.6301 88.8115 L 12.2535 59.5154 C 11.0372 57.7667 10.8393 55.5042 11.7334 53.5708 L 28.6676 16.9555 C 29.5703 15.0038 31.4483 13.6844 33.5905 13.497 L 73.4847 10.0067 C 75.6269 9.8193 77.7055 10.7925 78.9334 12.5578 L 83.5415 19.1831 C 86.5421 18.1224 89.4933 16.957 92.3907 15.6913 Z"}], "box": [0, 0, 113.05899810791016, 106.29199981689453], "x": {"c": "min"}, "y": {"c": "min"}}]}, "resizable": {"horizontalControls": false, "verticalControls": false, "cornerControls": false, "showHull": true}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [326.79998779296875, 7192.25], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [314.79998779296875, 358.25]}}, "item": {}, "inGroup": {"parent": "gr_1hd78uo19jh6vb"}}, {"id": "cart_9095sg19jpma6", "v": 46, "theme": {"styles": {"fillColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "yellow"}, "type": "ColorScheme"}], "strokeColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "yellow"}, "type": "ColorScheme"}]}}, "frame": {}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "db": {"dbId": "24px#e-commerce-cart--shopping-ecommerce--24x24", "board": [0, 0, 36, 36], "srcSize": [24, 24]}, "inLayout": {"rootId": "gr_1hd78uo19jh6vb"}, "inBehavior": {"parents": {"scene": "gr_1hd78uo19jh6vb"}}, "topLeft": {"topLeft": [534.2260131835938, 7198.74462890625]}, "svgSymbols": {"passes": [{"usage": "stroke", "outer": "<symbol><g><path d=\"M23.5 23.5L19 19M17.047,21.4a.5.5,0,0,0,.922.006L19,19l2.406-1.031a.5.5,0,0,0-.006-.922l-6.29-2.59a.5.5,0,0,0-.653.653ZM10.000 13.500 A1.000 1.000 0 1 0 12.000 13.500 A1.000 1.000 0 1 0 10.000 13.500 ZM6.000 13.500 A1.000 1.000 0 1 0 8.000 13.500 A1.000 1.000 0 1 0 6.000 13.500 ZM14 4L11.5 11.5 6.5 11.5 5 6.5 13.167 6.5M12.771,17.887a8.994,8.994,0,1,1,4.958-4.737\"></path></g></symbol>", "locaLBox": [0.511804461479187, 0.5124581456184387, 23.5, 23.5]}], "svgTransform": [1.5, 0, 0, 1.5, 0, 0]}, "brush": {}, "resizable": {"forceUniformScaling": true}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [534.22998046875, 7198.740234375], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [522.22998046875, 364.739990234375]}}, "item": {}}, {"id": "tx_consumer_de78nk19jo936", "v": 46, "theme": {"styles": {"fontSize": [{"selectors": [{"classes": ["All"]}], "value": {"name": "medium"}, "type": "FontSize"}], "fontWeight": [{"selectors": [{"classes": ["All"]}], "value": {"name": "bold"}, "type": "FontWeight"}], "strokeOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "fillColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "yellow"}, "type": "ColorScheme"}], "strokeColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "yellow"}, "type": "ColorScheme"}]}}, "frame": {}, "text": {"text": "Consumer Goods & Services", "font": {"type": "text", "name": "'Roboto'"}, "weight": "bold", "boardSize": [300, 24], "maxWidth": 300, "textBox": [2, 0, 257.859375, 24], "lineBounds": [[2, 0, 257.859375, 24]], "lineStarts": [0]}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "inLayout": {"rootId": "gr_1hd78uo19jh6vb"}, "inBehavior": {"parents": {"scene": "gr_1hd78uo19jh6vb"}}, "topLeft": {"topLeft": [582, 7204.74462890625]}, "behavior": {"label": []}, "svgSymbols": {"passes": [{"usage": "fill", "outer": "<symbol><g><text style=\"font: bold 20px <PERSON>o, sans-serif; white-space: pre;\"><tspan x=\"2\" y=\"11.5\" dominant-baseline=\"ideographic\">Consumer Goods &amp; Services</tspan></text></g></symbol>", "locaLBox": [2, -12, 257.859375, 12]}], "svgTransform": [1, 0, 0, 1, 0, 12]}, "brush": {"type": "text"}, "resizable": {"defaultToUniformScaling": false}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [582, 7204.740234375], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [570, 370.739990234375]}}, "item": {}}, {"id": "tx_showsthe_de78nk19jo937", "v": 46, "theme": {"styles": {"fontSize": [{"selectors": [{"classes": ["All"]}], "value": {"name": "small"}, "type": "FontSize"}], "fontWeight": [{"selectors": [{"classes": ["All"]}], "value": {"name": "normal"}, "type": "FontWeight"}], "strokeOpacity": [{"selectors": [{"classes": ["All"]}], "value": 1, "type": "double"}], "fillColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "black"}, "type": "ColorScheme"}], "strokeColorScheme": [{"selectors": [{"classes": ["All"]}], "value": {"color": "black"}, "type": "ColorScheme"}]}}, "frame": {}, "text": {"text": "Shows the expansion in consumer-focused investments.", "font": {"type": "text", "name": "'Roboto'"}, "fontSize": "small", "fontHeight": 18, "boardSize": [300, 36], "maxWidth": 300, "textBox": [2, 0, 291.7734375, 36], "lineBounds": [[2, 0, 291.7734375, 18], [2, 18, 87.625, 36]], "lineStarts": [0, 40]}, "clock": {"clock": "2024-12-19T14:46:35.397Z-0000-j<PERSON><PERSON>@secondlayer.co#11f5c"}, "inLayout": {"rootId": "gr_1hd78uo19jh6vb"}, "inBehavior": {"parents": {"scene": "gr_1hd78uo19jh6vb"}}, "topLeft": {"topLeft": [582, 7240.74462890625]}, "behavior": {"label": []}, "svgSymbols": {"passes": [{"usage": "fill", "outer": "<symbol><g><text style=\"font: 15px <PERSON><PERSON>, sans-serif; white-space: pre;\"><tspan x=\"2\" y=\"-0.5\" dominant-baseline=\"ideographic\">Shows the expansion in consumer-focused </tspan><tspan x=\"2\" y=\"17.5\" dominant-baseline=\"ideographic\">investments.</tspan></text></g></symbol>", "locaLBox": [2, -18, 291.7734375, 18]}], "svgTransform": [1, 0, 0, 1, 0, 18]}, "brush": {"type": "text"}, "resizable": {"defaultToUniformScaling": false}, "blockPositioner": {"clusterId": "gr_1hd78uo19jh6vb", "positioner": {"type": "HookedItemPositioner", "topLeft": [582, 7240.740234375], "clustered": true, "dynamicTopLeft": {"static": [12, 6834], "hookId": "bh-VrLB2UlUvQ0zwY2gkna-j", "deltaHook": [-198, 22]}, "clusterToTopLeft": [570, 406.739990234375]}}, "item": {}}]}
<svg xmlns="http://www.w3.org/2000/svg" width="888" height="696">    <g id="list-postits-desc-icon-v1--family--12" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L888 0 L888 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:888;h:24"></path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:888;h:0">            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:24;primary:CENTER;counter:MIN" data-position="x:0;y:0;w:888;h:648">                <g id="column" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MAX;counter:CENTER" data-position="x:24;y:24;w:192;h:588" transform="translate(24, 24)">                    <g id="bubble-1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:192;h:180">                        <path id="ct_fill" transform="translate(0, -0.0000152587890625)" fill="#fefbdb" d="M192 48 L0 48 C5.7939e-7 21.4903 0 0 0 0 L192 4.5776e-5 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="cc_fill" transform="translate(0, 48)" fill="#fefbdb" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="cb_fill" transform="translate(0, 132)" fill="#fefbdb" d="M0 0 L0 48 L107.068 48 C132.719 48 160 48 185 42 C185 42 192 33 192 0 L0 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <path id="ct_stroke" transform="translate(0, -0.000013637953088618815)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0.0001 L 0.0001 0 L 0 47.9999"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="lc_stroke" transform="translate(192, 48)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke" transform="translate(0, 48)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="cb_stroke" transform="translate(0, 132)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 48 L 107.0683 48 C 132.7191 48 160 48 185 42 C 185 42 192 33 192 0"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <g id="ic-cc-1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_1" transform="translate(0, -5)" fill="none" stroke="#d1bd08" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-1" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-1-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_1"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-1"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g>
                    <g id="bubble-5" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:204;w:192;h:180" transform="translate(0, 204)">                        <path id="ct_fill_1" transform="translate(0, -0.0000152587890625)" fill="#edf4ff" d="M192 48 L0 48 C5.7939e-7 21.4903 0 0 0 0 L192 4.5776e-5 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="cc_fill_1" transform="translate(0, 48)" fill="#edf4ff" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="cb_fill_1" transform="translate(0, 132)" fill="#edf4ff" d="M0 0 L0 48 L137.5 48 C147.734 48 165.637 47.3677 173 42 L184.5 32.5 C188 29 191.215 24.9087 191.549 17.8146 C191.785 12.7946 192 7.0268 192 2.6307 L192 0 L0 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <path id="ct_stroke_1" transform="translate(0, -0.000013637953088618815)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0.0001 L 0.0001 0 L 0 47.9999"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="lc_stroke_1" transform="translate(192, 48)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke_1" transform="translate(0, 48)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="cb_stroke_1" transform="translate(0, 132)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 48 L 137.5 48 C 147.7341 48 165.6366 47.3677 173 42 L 184.5 32.5 C 188 29 191.2149 24.9087 191.549 17.8146 C 191.7854 12.7946 192 7.0268 192 2.6307 L 192 0"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <path id="cb_corner" transform="translate(155.5, 151)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 28 C 13 26 15.5 20.5 19 11.5 C 19 11.5 34.5 13 36 0"  data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:155.500;y:151;w:36;h:28"></path>
                        <g id="ic-cc-5"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon_2"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_3" transform="translate(0, -5)" fill="none" stroke="#4987ec" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-5" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label_2"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-5-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_3"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-5"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g>
                    <g id="bubble-9" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:408;w:192;h:180" transform="translate(0, 408)">                        <path id="ct_fill_2" transform="translate(0, -0.0000152587890625)" fill="#ffedeb" d="M192 48 L0 48 C5.7939e-7 21.4903 0 0 0 0 L192 4.5776e-5 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="cc_fill_2" transform="translate(0, 48)" fill="#ffedeb" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="cb_fill_2" transform="translate(0, 132)" fill="#ffedeb" d="M0 0 L0 48 L107.068 48 C131.597 48 156.033 44.9918 179.829 39.0428 L182.91 38.2724 C188.252 36.9369 192 32.1371 192 26.6307 L192 0 L0 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <path id="ct_stroke_2" transform="translate(0, -0.000013637953088618815)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0.0001 L 0.0001 0 L 0 47.9999"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="lc_stroke_2" transform="translate(192, 48)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke_2" transform="translate(0, 48)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="cb_stroke_2" transform="translate(0, 132)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 48 L 107.0683 48 C 131.5968 48 156.0328 44.9918 179.829 39.0428 L 182.9104 38.2724 C 188.2524 36.9369 192 32.1371 192 26.6307 L 192 0"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <path id="cb_corner_1" transform="translate(111, 163)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 16.9999 C 16.9753 14.4408 42.0556 12.0645 61.5 0 C 61.5 0 64.5 10 74 6.5"  data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:111;y:163;w:74;h:17.000"></path>
                        <g id="ic-cc-9"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon_4"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_5" transform="translate(0, -5)" fill="none" stroke="#df5e59" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-9" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label_4"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-9-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_5"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-9"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g></g>
                <g id="column_1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 0 0 0;gap:24;primary:MAX;counter:CENTER" data-position="x:240;y:24;w:192;h:600" transform="translate(240, 24)">                    <g id="bubble-2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:12;w:192;h:180" transform="translate(0, 12)">                        <path id="ct_fill_3" transform="translate(0, -0.0000152587890625)" fill="#f2fae1" d="M192 48 L0 48 C5.7939e-7 21.4903 0 0 0 0 L192 4.5776e-5 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="cc_fill_3" transform="translate(0, 48)" fill="#f2fae1" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="cb_fill_3" transform="translate(0, 132)" fill="#f2fae1" d="M192 0 L192 48 L54.5 48 C44.2659 48 26.3634 47.3677 19 42 L7.5 32.5 C4 29 0.7851 24.9087 0.451 17.8146 C0.2146 12.7946 0 7.0268 0 2.6307 L0 0 L192 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <path id="ct_stroke_3" transform="translate(0, -0.000013637953088618815)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0.0001 L 0.0001 0 L 0 47.9999"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="lc_stroke_3" transform="translate(192, 48)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke_3" transform="translate(0, 48)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="cb_stroke_3" transform="translate(0, 132)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 0 L 192 48 L 54.5 48 C 44.2659 48 26.3634 47.3677 19 42 L 7.5 32.5 C 4 29 0.7851 24.9087 0.451 17.8146 C 0.2146 12.7946 0 7.0268 0 2.6307 L 0 0"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <path id="cb_corner_2" transform="translate(0.5, 151)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="none" stroke-miterlimit="4"  d="M 36 28 C 23 26 20.5 20.5 17 11.5 C 17 11.5 1.5 13 0 0"  data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.500;y:151;w:36;h:28"></path>
                        <g id="ic-cc-2"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon_6"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_7" transform="translate(0, -5)" fill="none" stroke="#93c332" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text_3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-2" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label_6"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-2-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_7"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-2"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g>
                    <g id="bubble-6" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:216;w:192;h:180" transform="translate(0, 216)">                        <path id="ct_fill_4" transform="translate(0, -0.0000152587890625)" fill="#f3f0ff" d="M192 48 L0 48 C5.7939e-7 21.4903 0 0 0 0 L192 4.5776e-5 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="cc_fill_4" transform="translate(0, 48)" fill="#f3f0ff" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="cb_fill_4" transform="translate(0, 132)" fill="#f3f0ff" d="M192 0 L192 48 L84.9317 48 C60.4032 48 35.9672 44.9918 12.171 39.0428 L9.0896 38.2724 C3.7476 36.9369 0 32.1371 0 26.6307 L0 0 L192 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <path id="ct_stroke_4" transform="translate(0, -0.000013637953088618815)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0.0001 L 0.0001 0 L 0 47.9999"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="lc_stroke_4" transform="translate(192, 48)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke_4" transform="translate(0, 48)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="cb_stroke_4" transform="translate(0, 132)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 0 L 192 48 L 84.9317 48 C 60.4032 48 35.9672 44.9918 12.171 39.0428 L 9.0896 38.2724 C 3.7476 36.9369 0 32.1371 0 26.6307 L 0 0"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <path id="cb_corner_3" transform="translate(7, 163)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="none" stroke-miterlimit="4"  d="M 74 16.9999 C 57.0247 14.4408 31.9444 12.0645 12.5 0 C 12.5 0 9.5 10 0 6.5"  data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:7;y:163;w:74;h:17.000"></path>
                        <g id="ic-cc-6"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon_8"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_9" transform="translate(0, -5)" fill="none" stroke="#7e62ec" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text_4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-6" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label_8"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-6-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_9"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-6"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g>
                    <g id="bubble-10" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:420;w:192;h:180" transform="translate(0, 420)">                        <path id="ct_fill_5" transform="translate(0, -0.0000152587890625)" fill="#fef2e6" d="M192 48 L0 48 C5.7939e-7 21.4903 0 0 0 0 L192 4.5776e-5 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="cc_fill_5" transform="translate(0, 48)" fill="#fef2e6" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="cb_fill_5" transform="translate(0, 132)" fill="#fef2e6" d="M192 0 L192 48 L84.9317 48 C59.2809 48 32 48 7 42 C7 42 0 33 0 0 L192 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <path id="ct_stroke_5" transform="translate(0, -0.000013637953088618815)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0.0001 L 0.0001 0 L 0 47.9999"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="lc_stroke_5" transform="translate(192, 48)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke_5" transform="translate(0, 48)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="cb_stroke_5" transform="translate(0, 132)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 0 L 192 48 L 84.9317 48 C 59.2809 48 32 48 7 42 C 7 42 0 33 0 0"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:132;w:192;h:48"></path>
                        <g id="ic-cc-10"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon_10"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_11" transform="translate(0, -5)" fill="none" stroke="#db8333" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text_5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-10" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label_10"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-10-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_11"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-10"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g></g>
                <g id="column_2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MAX;counter:CENTER" data-position="x:456;y:24;w:192;h:588" transform="translate(456, 24)">                    <g id="bubble-3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:192;h:180">                        <path id="cb_fill_6" transform="translate(3.979039320256561e-12, 132)" fill="#e7fbf2" d="M4.1963e-6 0 L192 6.2562e-5 C192 26.5097 192 48 192 48 L0 48 L4.1963e-6 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="cc_fill_6" transform="translate(0, 48)" fill="#e7fbf2" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="ct_fill_6" transform="translate(0, 0.0000058985783653042745)" fill="#e7fbf2" d="M192 48 L192 9.3602e-6 L84.9317 1.3642e-12 C60.4032 -2.1444e-6 35.9672 3.0082 12.171 8.9572 L9.0896 9.7276 C3.7476 11.0631 2.8095e-6 15.8629 2.3281e-6 21.3693 L0 48 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="cb_stroke_6" transform="translate(4.433786671143025e-12, 132)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 48 L 192 48 L 192 0.0001"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="lc_stroke_6" transform="translate(192, 48)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke_6" transform="translate(0, 48)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="ct_stroke_6" transform="translate(0, 0.0000058985783653042745)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0 L 84.9317 1.1464e-12 C 60.4032 -0 35.9672 3.0082 12.171 8.9572 L 9.0896 9.7276 C 3.7476 11.0631 0 15.8629 0 21.3693 L 0 48"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="ct_corner" transform="translate(7.000000476837158, 0.0001163482666015625)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="none" stroke-miterlimit="4"  d="M 74 0 C 57.0247 2.5591 31.9444 4.9354 12.5 16.9999 C 12.5 16.9999 9.5 6.9999 0 10.4999"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:7.000;y:0.000;w:74;h:17.000"></path>
                        <g id="ic-cc-3"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon_12"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_13" transform="translate(0, -5)" fill="none" stroke="#3cc583" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text_6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-3" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label_12"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-3-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_13"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-3"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g>
                    <g id="bubble-7" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:204;w:192;h:180" transform="translate(0, 204)">                        <path id="cb_fill_7" transform="translate(3.979039320256561e-12, 132)" fill="#faf0ff" d="M4.1963e-6 0 L192 6.2562e-5 C192 26.5097 192 48 192 48 L0 48 L4.1963e-6 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="cc_fill_7" transform="translate(0, 48)" fill="#faf0ff" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="ct_fill_7" transform="translate(0, 0.0000032381567507400177)" fill="#faf0ff" d="M192 48 L192 1.2021e-5 L54.5 4.5475e-13 C44.2659 -8.947e-7 26.3634 0.6323 19 6 L7.5 15.5 C4 19 0.7851 23.0913 0.451 30.1854 C0.2146 35.2054 6.143e-7 40.9732 2.2998e-7 45.3693 L0 48 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="cb_stroke_7" transform="translate(4.433786671143025e-12, 132)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 48 L 192 48 L 192 0.0001"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="lc_stroke_7" transform="translate(192, 48)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke_7" transform="translate(0, 48)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="ct_stroke_7" transform="translate(0, 0.0000032381567507400177)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0 L 54.5 0 C 44.2659 0 26.3634 0.6323 19 6 L 7.5 15.5 C 4 19 0.7851 23.0913 0.451 30.1854 C 0.2146 35.2054 0 40.9732 0 45.3693 L 0 48"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="ct_corner_1" transform="translate(0.5, 1.0000171661376953)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="none" stroke-miterlimit="4"  d="M 36 0 C 23 2 20.5 7.5 17 16.5 C 17 16.5 1.5 15 0 28"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.500;y:1.000;w:36.000;h:28.000"></path>
                        <g id="ic-cc-7"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon_14"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_15" transform="translate(0, -5)" fill="none" stroke="#b960e2" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text_7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-7" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label_14"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-7-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_15"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-7"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g>
                    <g id="bubble-11" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:408;w:192;h:180" transform="translate(0, 408)">                        <path id="cb_fill_8" transform="translate(3.979039320256561e-12, 132)" fill="#fefbdb" d="M4.1963e-6 0 L192 6.2562e-5 C192 26.5097 192 48 192 48 L0 48 L4.1963e-6 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="cc_fill_8" transform="translate(0, 48)" fill="#fefbdb" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="ct_fill_8" transform="translate(0, 0.000005895837603020482)" fill="#fefbdb" d="M192 48 L192 9.363e-6 L84.9317 2.7421e-9 C59.2809 -2.2397e-6 32 -4.2915e-6 7 6 C7 6 2.885e-6 15 0 48 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="cb_stroke_8" transform="translate(4.433786671143025e-12, 132)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 48 L 192 48 L 192 0.0001"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="lc_stroke_8" transform="translate(192, 48)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke_8" transform="translate(0, 48)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="ct_stroke_8" transform="translate(0, 0.000005895837603020482)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0 L 84.9317 2.7366e-9 C 59.2809 -0 32 -0 7 6 C 7 6 0 15 0 48"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <g id="ic-cc-11"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon_16"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_17" transform="translate(0, -5)" fill="none" stroke="#d1bd08" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text_8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-11" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label_16"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-11-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_17"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-11"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g></g>
                <g id="column_3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 0 0 0;gap:24;primary:MAX;counter:CENTER" data-position="x:672;y:24;w:192;h:600" transform="translate(672, 24)">                    <g id="bubble-4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:12;w:192;h:180" transform="translate(0, 12)">                        <path id="cb_fill_9" transform="translate(3.979039320256561e-12, 132)" fill="#e8f9ff" d="M4.1963e-6 0 L192 6.2562e-5 C192 26.5097 192 48 192 48 L0 48 L4.1963e-6 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="cc_fill_9" transform="translate(0, 48)" fill="#e8f9ff" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="ct_fill_9" transform="translate(0, 0.000005895838057767833)" fill="#e8f9ff" d="M0 48 L0 9.363e-6 L107.068 2.7417e-9 C132.719 -2.2397e-6 160 -4.2915e-6 185 6 C185 6 192 15 192 48 L0 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="cb_stroke_9" transform="translate(4.433786671143025e-12, 132)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 48 L 192 48 L 192 0.0001"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="lc_stroke_9" transform="translate(192, 48)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke_9" transform="translate(0, 48)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="ct_stroke_9" transform="translate(0, 0.000005893101388210198)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 48 L 0 0 L 107.0683 2.7316e-9 C 132.7191 -0 160 -0 185 6 C 185 6 192 15 192 48"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <g id="ic-cc-4"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon_18"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_19" transform="translate(0, -5)" fill="none" stroke="#17aee1" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text_9" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-4" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label_18"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-4-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_19"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-4"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g>
                    <g id="bubble-8" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:216;w:192;h:180" transform="translate(0, 216)">                        <path id="cb_fill_10" transform="translate(3.979039320256561e-12, 132)" fill="#feecf7" d="M4.1963e-6 0 L192 6.2562e-5 C192 26.5097 192 48 192 48 L0 48 L4.1963e-6 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="cc_fill_10" transform="translate(0, 48)" fill="#feecf7" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="ct_fill_10" transform="translate(0, 0.0000058985783653042745)" fill="#feecf7" d="M0 48 L0 9.3602e-6 L107.068 1.3642e-12 C131.597 -2.1444e-6 156.033 3.0082 179.829 8.9572 L182.91 9.7276 C188.252 11.0631 192 15.8629 192 21.3693 L192 48 L0 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="cb_stroke_10" transform="translate(4.433786671143025e-12, 132)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 48 L 192 48 L 192 0.0001"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="lc_stroke_10" transform="translate(192, 48)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke_10" transform="translate(0, 48)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="ct_stroke_10" transform="translate(0, 0.000005898577001062222)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 48 L 0 0 L 107.0683 1.1464e-12 C 131.5968 -0 156.0328 3.0082 179.829 8.9572 L 182.9104 9.7276 C 188.2524 11.0631 192 15.8629 192 21.3693 L 192 48"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="ct_corner_2" transform="translate(111, 0.0001163482666015625)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 C 16.9753 2.5591 42.0556 4.9354 61.5 16.9999 C 61.5 16.9999 64.5 6.9999 74 10.4999"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:111;y:0.000;w:74;h:17.000"></path>
                        <g id="ic-cc-8"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon_20"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_21" transform="translate(0, -5)" fill="none" stroke="#d95da7" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text_10" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-8" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label_20"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-8-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_21"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-8"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g>
                    <g id="bubble-12" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:420;w:192;h:180" transform="translate(0, 420)">                        <path id="cb_fill_11" transform="translate(3.979039320256561e-12, 132)" fill="#f2fae1" d="M4.1963e-6 0 L192 6.2562e-5 C192 26.5097 192 48 192 48 L0 48 L4.1963e-6 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="cc_fill_11" transform="translate(0, 48)" fill="#f2fae1" d="M192 84 L192 0 L0 8.5675e-6 L3.5968e-6 84 L192 84 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:84"></path>
                        <path id="ct_fill_11" transform="translate(0, 0.000003238156295992667)" fill="#f2fae1" d="M0 48 L0 1.2021e-5 L137.5 9.095e-13 C147.734 -8.947e-7 165.637 0.6323 173 6 L184.5 15.5 C188 19 191.215 23.0913 191.549 30.1854 C191.785 35.2054 192 40.9732 192 45.3693 L192 48 L0 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="cb_stroke_11" transform="translate(4.433786671143025e-12, 132)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 48 L 192 48 L 192 0.0001"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:132;w:192;h:48.000"></path>
                        <path id="lc_stroke_11" transform="translate(192, 48)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:84"></path>
                        <path id="rc_stroke_11" transform="translate(0, 48)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 84 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:84"></path>
                        <path id="ct_stroke_11" transform="translate(0, 0.0000032381567507400177)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 48 L 0 0 L 137.5 0 C 147.7341 0 165.6366 0.6323 173 6 L 184.5 15.5 C 188 19 191.2149 23.0913 191.549 30.1854 C 191.7854 35.2054 192 40.9732 192 45.3693 L 192 48"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="ct_corner_3" transform="translate(155.5, 1.0000171661376953)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 C 13 2 15.5 7.5 19 16.5 C 19 16.5 34.5 15 36 28"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:155.500;y:1.000;w:36.000;h:28.000"></path>
                        <g id="ic-cc-12"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:66;y:24;w:60;h:60" fill="#33de7b1a" transform="translate(66, 24)">                            <g id="icon_22"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:12.500;y:12.500;w:35.625;h:35.625" transform="translate(12.5, 12.5)">                                <path id="icon_23" transform="translate(0, -5)" fill="none" stroke="#93c332" stroke-width="2.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0.0002 0 C 12.0981 7.0846 24.1668 14.2174 36.25 21.3261 C 29.2979 25.1794 18.0743 31.3294 9.9559 35.7703 C 4.1966 38.9208 0 41.2112 0 41.2112 L 0.0002 0 Z M 36.25 21.3261 C 28.8119 29.1545 21.5108 37.109 14.1416 45 C 12.7495 41.923 11.4065 38.8213 9.9559 35.7703"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-5;w:36.250;h:45"></path></g></g>
                        <g id="text_11" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:96;w:144;h:60" transform="translate(24, 96)">                            <g id="tx-ct-12" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                <text id="Label_22"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                            <g id="tx-ct-12-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                <text id="Label_23"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g>
                        <rect id="bt-cc-remove-12"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g></g></g></g></g></svg>
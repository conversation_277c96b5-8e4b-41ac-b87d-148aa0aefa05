<svg xmlns="http://www.w3.org/2000/svg" width="720" height="936">
    <g id="stairs-bookmarks-v1--family--9" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L720 0 L720 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:720;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:720;h:0">
            <g id="body" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 0 0 0;gap:0;primary:MIN;counter:MAX" data-position="x:0;y:0;w:720;h:888">
                <g id="g-9" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 192;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:24;w:720;h:96" transform="translate(0, 24)">
                    <g id="border-9" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:192;y:0;w:528;h:96" transform="translate(192, 0)">
                        <path id="top-left-fill" fill="#e7fbf2" d="M 8.6466e-27 47 C -4.8298e-13 21.0426 20.2333 0 45.1923 0 L 96 0 L 96 47 L 8.6466e-27 47 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:96;h:47"/>
                        <path id="center-fill" transform="translate(0, 47)" fill="#e7fbf2" d="M 0 0 L 96 0 L 96 2 L 0 2 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:47;w:96;h:2"/>
                        <path id="center-right-fill" transform="translate(95.99989318847656, 0)" fill="#e7fbf2" d="M 0.0004 0 L 432.0001 0 L 432.0001 96 L 0 96 L 0.0004 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:432;h:96"/>
                        <path id="bottom-left-fill" transform="translate(0, 49)" fill="#e7fbf2" d="M 0 0.01 L 96 0 L 96 47.0004 L 47 47.0104 C 21 47.0104 0 25.9676 0 0.01 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:96;h:47.010"/>
                        <path id="trop-left-stroke" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 47 C 0 21.0426 21.0426 0 47 0 L 196.5 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:196.500;h:47"/>
                        <path id="center-left-stroke" transform="translate(0, 46.98046875)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 2.0195 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:46.980;w:0;h:2.020"/>
                        <path id="bottom-left-stroke" transform="translate(0, 49)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 C 0 25.9576 21 47.0004 47 47.0004 L 76.5 47.0004 M 197 47.0004 L 173 47.0004" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:197;h:47.000"/>
                        <path id="Vector" transform="translate(196.6429443359375, 0)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 331.3573 0 L 331.3573 96 L 0.0002 96" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:196.643;y:0;w:331.357;h:96"/>
                        <g id="index" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:96;h:96">
                            <g id="tx-cc-9-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#ff00001a" transform="translate(24, 24)">
                                <text id="9" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#3cc583" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">9</text>
                            </g>
                        </g>
                        <g id="icon" data-resize="horizontal:HUG;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:96" transform="translate(96, 0)">
                            <path id="Rectangle_118" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 13.4164 C 0 8.4588 1.6889 3.7576 4.642 0 M 100.642 0 C 97.6889 3.7576 96 8.4587 96 13.4164" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:100.642;h:13.416"/>
                            <path id="Rectangle_120" transform="translate(0, 13)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 96 0 L 96 60 M 0 60 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:13;w:96;h:60"/>
                            <path id="Rectangle_122" transform="translate(-19.34765625, 72.87109375)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 115.3474 0 C 115.3474 7.4707 110.7018 14.2999 103.3474 17.6409 C 100.4109 18.9749 97.9063 20.865 95.9568 23.1301 M 19.3474 0 C 19.3474 7.4707 14.7018 14.2999 7.3474 17.6409 C 4.4326 18.9651 1.9432 20.8372 0 23.0801" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-19.348;y:72.871;w:115.347;h:23.130"/>
                            <rect id="bt-cc-remove-9" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 36)" width="24" height="24" rx="0" ry="0"/>
                            <g id="ic-cc-9" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#33de7b1a" transform="translate(24, 24)">
                                <g id="icon_1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_2" transform="translate(9, 5)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <g id="text-9" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:12;primary:MAX;counter:MIN" data-position="x:192;y:6;w:336;h:84" transform="translate(192, 6)">
                            <g id="tx-lt-9" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:12;w:288;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:288;h:24" fill="#3cc583" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-9-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:48;w:288;h:24" fill="#ff00001a" transform="translate(24, 48)">
                                <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:288;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                        </g>
                        <rect id="bt-cc-add-10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, -12)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="g-8" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 168;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:120;w:720;h:96" transform="translate(0, 120)">
                    <g id="border-8" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:168;y:0;w:552;h:96" transform="translate(168, 0)">
                        <path id="top-left-fill_1" fill="#e8f9ff" d="M 8.6466e-27 47 C -4.8298e-13 21.0426 20.2333 0 45.1923 0 L 96 0 L 96 47 L 8.6466e-27 47 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:96;h:47"/>
                        <path id="center-fill_1" transform="translate(0, 47)" fill="#e8f9ff" d="M 0 0 L 96 0 L 96 2 L 0 2 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:47;w:96;h:2"/>
                        <path id="center-right-fill_1" transform="translate(95.99989318847656, 0)" fill="#e8f9ff" d="M 0.0004 0 L 456.0001 0 L 456.0001 96 L 0 96 L 0.0004 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:456;h:96"/>
                        <path id="bottom-left-fill_1" transform="translate(0, 49)" fill="#e8f9ff" d="M 0 0.01 L 96 0 L 96 47.0004 L 47 47.0104 C 21 47.0104 0 25.9676 0 0.01 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:96;h:47.010"/>
                        <path id="trop-left-stroke_1" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 47 C 0 21.0426 21.0426 0 47 0 L 100.5 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:100.500;h:47"/>
                        <path id="center-left-stroke_1" transform="translate(0, 46.98046875)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 2.0195 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:46.980;w:0;h:2.020"/>
                        <path id="bottom-left-stroke_1" transform="translate(0, 49)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 C 0 25.9576 21 47.0004 47 47.0004 L 76.5 47.0004 M 197 47.0004 L 173 47.0004" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:197;h:47.000"/>
                        <path id="Vector_1" transform="translate(196.64295959472656, 0)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 355.3573 0 L 355.3573 96 L 0.0002 96" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:196.643;y:0;w:355.357;h:96"/>
                        <g id="index_1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:96;h:96">
                            <g id="tx-cc-8-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#ff00001a" transform="translate(24, 24)">
                                <text id="8" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#17aee1" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">8</text>
                            </g>
                        </g>
                        <g id="icon_3" data-resize="horizontal:HUG;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:96" transform="translate(96, 0)">
                            <path id="Rectangle_118_1" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 13.4164 C 0 8.4588 1.6889 3.7576 4.642 0 M 100.642 0 C 97.6889 3.7576 96 8.4587 96 13.4164" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:100.642;h:13.416"/>
                            <path id="Rectangle_120_1" transform="translate(0, 13)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 96 0 L 96 60 M 0 60 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:13;w:96;h:60"/>
                            <path id="Rectangle_122_1" transform="translate(-19.34765625, 72.87109375)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 115.3474 0 C 115.3474 7.4707 110.7018 14.2999 103.3474 17.6409 C 100.4109 18.9749 97.9063 20.865 95.9568 23.1301 M 19.3474 0 C 19.3474 7.4707 14.7018 14.2999 7.3474 17.6409 C 4.4326 18.9651 1.9432 20.8372 0 23.0801" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-19.348;y:72.871;w:115.347;h:23.130"/>
                            <rect id="bt-cc-remove-8" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 36)" width="24" height="24" rx="0" ry="0"/>
                            <g id="ic-cc-8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#33de7b1a" transform="translate(24, 24)">
                                <g id="icon_4" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_5" transform="translate(9, 5)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <g id="text-8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:12;primary:MAX;counter:MIN" data-position="x:192;y:6;w:360;h:84" transform="translate(192, 6)">
                            <g id="tx-lt-8" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:12;w:312;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-8-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:48;w:312;h:24" fill="#ff00001a" transform="translate(24, 48)">
                                <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                        </g>
                        <rect id="bt-cc-add-9" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, -12)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="g-7" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 144;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:216;w:720;h:96" transform="translate(0, 216)">
                    <g id="border-7" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:144;y:0;w:576;h:96" transform="translate(144, 0)">
                        <path id="top-left-fill_2" fill="#edf4ff" d="M 8.6466e-27 47 C -4.8298e-13 21.0426 20.2333 0 45.1923 0 L 96 0 L 96 47 L 8.6466e-27 47 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:96;h:47"/>
                        <path id="center-fill_2" transform="translate(0, 47)" fill="#edf4ff" d="M 0 0 L 96 0 L 96 2 L 0 2 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:47;w:96;h:2"/>
                        <path id="center-right-fill_2" transform="translate(95.99988555908203, 0)" fill="#edf4ff" d="M 0.0005 0 L 480.0001 0 L 480.0001 96 L 0 96 L 0.0005 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:480;h:96"/>
                        <path id="bottom-left-fill_2" transform="translate(0, 49)" fill="#edf4ff" d="M 0 0.01 L 96 0 L 96 47.0004 L 47 47.0104 C 21 47.0104 0 25.9676 0 0.01 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:96;h:47.010"/>
                        <path id="trop-left-stroke_2" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 47 C 0 21.0426 21.0426 0 47 0 L 100.5 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:100.500;h:47"/>
                        <path id="center-left-stroke_2" transform="translate(0, 46.98046875)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 2.0195 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:46.980;w:0;h:2.020"/>
                        <path id="bottom-left-stroke_2" transform="translate(0, 49)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 C 0 25.9576 21 47.0004 47 47.0004 L 76.5 47.0004 M 197 47.0004 L 173 47.0004" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:197;h:47.000"/>
                        <path id="Vector_2" transform="translate(196.64295959472656, 0)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 379.3573 0 L 379.3573 96 L 0.0002 96" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:196.643;y:0;w:379.357;h:96"/>
                        <g id="index_2" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:96;h:96">
                            <g id="tx-cc-7-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#ff00001a" transform="translate(24, 24)">
                                <text id="7" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#4987ec" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">7</text>
                            </g>
                        </g>
                        <g id="icon_6" data-resize="horizontal:HUG;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:96" transform="translate(96, 0)">
                            <path id="Rectangle_118_2" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 13.4164 C 0 8.4588 1.6889 3.7576 4.642 0 M 100.642 0 C 97.6889 3.7576 96 8.4587 96 13.4164" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:100.642;h:13.416"/>
                            <path id="Rectangle_120_2" transform="translate(0, 13)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 96 0 L 96 60 M 0 60 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:13;w:96;h:60"/>
                            <path id="Rectangle_122_2" transform="translate(-19.34765625, 72.87109375)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 115.3474 0 C 115.3474 7.4707 110.7018 14.2999 103.3474 17.6409 C 100.4109 18.9749 97.9063 20.865 95.9568 23.1301 M 19.3474 0 C 19.3474 7.4707 14.7018 14.2999 7.3474 17.6409 C 4.4326 18.9651 1.9432 20.8372 0 23.0801" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-19.348;y:72.871;w:115.347;h:23.130"/>
                            <rect id="bt-cc-remove-7" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 36)" width="24" height="24" rx="0" ry="0"/>
                            <g id="ic-cc-7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#33de7b1a" transform="translate(24, 24)">
                                <g id="icon_7" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_8" transform="translate(9, 5)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <g id="text-7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:12;primary:MAX;counter:MIN" data-position="x:192;y:6;w:384;h:84" transform="translate(192, 6)">
                            <g id="tx-lt-7" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:12;w:336;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:336;h:24" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-7-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:48;w:336;h:24" fill="#ff00001a" transform="translate(24, 48)">
                                <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:336;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                        </g>
                        <rect id="bt-cc-add-8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, -12)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="g-6" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 120;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:312;w:720;h:96" transform="translate(0, 312)">
                    <g id="border-6" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:120;y:0;w:600;h:96" transform="translate(120, 0)">
                        <path id="top-left-fill_3" fill="#f3f0ff" d="M 8.6466e-27 47 C -4.8298e-13 21.0426 20.2333 0 45.1923 0 L 96 0 L 96 47 L 8.6466e-27 47 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:96;h:47"/>
                        <path id="center-fill_3" transform="translate(0, 47)" fill="#f3f0ff" d="M 0 0 L 96 0 L 96 2 L 0 2 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:47;w:96;h:2"/>
                        <path id="center-right-fill_3" transform="translate(95.9998779296875, 0)" fill="#f3f0ff" d="M 0.0005 0 L 504.0001 0 L 504.0001 96 L 0 96 L 0.0005 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:504;h:96"/>
                        <path id="bottom-left-fill_3" transform="translate(0, 49)" fill="#f3f0ff" d="M 0 0.01 L 96 0 L 96 47.0004 L 47 47.0104 C 21 47.0104 0 25.9676 0 0.01 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:96;h:47.010"/>
                        <path id="trop-left-stroke_3" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 47 C 0 21.0426 21.0426 0 47 0 L 100.5 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:100.500;h:47"/>
                        <path id="center-left-stroke_3" transform="translate(0, 46.98046875)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 2.0195 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:46.980;w:0;h:2.020"/>
                        <path id="bottom-left-stroke_3" transform="translate(0, 49)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 C 0 25.9576 21 47.0004 47 47.0004 L 76.5 47.0004 M 197 47.0004 L 173 47.0004" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:197;h:47.000"/>
                        <path id="Vector_3" transform="translate(196.64297485351562, 0)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 403.3573 0 L 403.3573 96 L 0.0002 96" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:196.643;y:0;w:403.357;h:96"/>
                        <g id="index_3" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:96;h:96">
                            <g id="tx-cc-6-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#ff00001a" transform="translate(24, 24)">
                                <text id="6" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#7e62ec" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">6</text>
                            </g>
                        </g>
                        <g id="icon_9" data-resize="horizontal:HUG;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:96" transform="translate(96, 0)">
                            <path id="Rectangle_118_3" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 13.4164 C 0 8.4588 1.6889 3.7576 4.642 0 M 100.642 0 C 97.6889 3.7576 96 8.4587 96 13.4164" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:100.642;h:13.416"/>
                            <path id="Rectangle_120_3" transform="translate(0, 13)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 96 0 L 96 60 M 0 60 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:13;w:96;h:60"/>
                            <path id="Rectangle_122_3" transform="translate(-19.34765625, 72.87109375)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 115.3474 0 C 115.3474 7.4707 110.7018 14.2999 103.3474 17.6409 C 100.4109 18.9749 97.9063 20.865 95.9568 23.1301 M 19.3474 0 C 19.3474 7.4707 14.7018 14.2999 7.3474 17.6409 C 4.4326 18.9651 1.9432 20.8372 0 23.0801" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-19.348;y:72.871;w:115.347;h:23.130"/>
                            <rect id="bt-cc-remove-6" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 36)" width="24" height="24" rx="0" ry="0"/>
                            <g id="ic-cc-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#33de7b1a" transform="translate(24, 24)">
                                <g id="icon_10" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_11" transform="translate(9, 5)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <g id="text-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:12;primary:MAX;counter:MIN" data-position="x:192;y:6;w:408;h:84" transform="translate(192, 6)">
                            <g id="tx-lt-6" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:12;w:360;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:360;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-6-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:48;w:360;h:24" fill="#ff00001a" transform="translate(24, 48)">
                                <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:360;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                        </g>
                        <rect id="bt-cc-add-7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, -12)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="g-5" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 96;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:408;w:720;h:96" transform="translate(0, 408)">
                    <g id="border-5" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:624;h:96" transform="translate(96, 0)">
                        <path id="top-left-fill_4" fill="#faf0ff" d="M 8.6466e-27 47 C -4.8298e-13 21.0426 20.2333 0 45.1923 0 L 96 0 L 96 47 L 8.6466e-27 47 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:96;h:47"/>
                        <path id="center-fill_4" transform="translate(0, 47)" fill="#faf0ff" d="M 0 0 L 96 0 L 96 2 L 0 2 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:47;w:96;h:2"/>
                        <path id="center-right-fill_4" transform="translate(95.99987030029297, 0)" fill="#faf0ff" d="M 0.0005 0 L 528.0001 0 L 528.0001 96 L 0 96 L 0.0005 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:528;h:96"/>
                        <path id="bottom-left-fill_4" transform="translate(0, 49)" fill="#faf0ff" d="M 0 0.01 L 96 0 L 96 47.0004 L 47 47.0104 C 21 47.0104 0 25.9676 0 0.01 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:96;h:47.010"/>
                        <path id="trop-left-stroke_4" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 47 C 0 21.0426 21.0426 0 47 0 L 100.5 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:100.500;h:47"/>
                        <path id="center-left-stroke_4" transform="translate(0, 46.98046875)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 2.0195 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:46.980;w:0;h:2.020"/>
                        <path id="bottom-left-stroke_4" transform="translate(0, 49)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 C 0 25.9576 21 47.0004 47 47.0004 L 76.5 47.0004 M 197 47.0004 L 173 47.0004" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:197;h:47.000"/>
                        <path id="Vector_4" transform="translate(196.6429901123047, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 427.3573 0 L 427.3573 96 L 0.0002 96" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:196.643;y:0;w:427.357;h:96"/>
                        <g id="index_4" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:96;h:96">
                            <g id="tx-cc-5-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#ff00001a" transform="translate(24, 24)">
                                <text id="5" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#b960e2" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">5</text>
                            </g>
                        </g>
                        <g id="icon_12" data-resize="horizontal:HUG;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:96" transform="translate(96, 0)">
                            <path id="Rectangle_118_4" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 13.4164 C 0 8.4588 1.6889 3.7576 4.642 0 M 100.642 0 C 97.6889 3.7576 96 8.4587 96 13.4164" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:100.642;h:13.416"/>
                            <path id="Rectangle_120_4" transform="translate(0, 13)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 96 0 L 96 60 M 0 60 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:13;w:96;h:60"/>
                            <path id="Rectangle_122_4" transform="translate(-19.34765625, 72.87109375)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 115.3474 0 C 115.3474 7.4707 110.7018 14.2999 103.3474 17.6409 C 100.4109 18.9749 97.9063 20.865 95.9568 23.1301 M 19.3474 0 C 19.3474 7.4707 14.7018 14.2999 7.3474 17.6409 C 4.4326 18.9651 1.9432 20.8372 0 23.0801" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-19.348;y:72.871;w:115.347;h:23.130"/>
                            <rect id="bt-cc-remove-5" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 36)" width="24" height="24" rx="0" ry="0"/>
                            <g id="ic-cc-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#33de7b1a" transform="translate(24, 24)">
                                <g id="icon_13" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_14" transform="translate(9, 5)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:12;primary:MAX;counter:MIN" data-position="x:192;y:6;w:432;h:84" transform="translate(192, 6)">
                            <g id="tx-lt-5" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:12;w:384;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:384;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-5-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:48;w:384;h:24" fill="#ff00001a" transform="translate(24, 48)">
                                <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:384;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                        </g>
                        <rect id="bt-cc-add-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, -12)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="g-4" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 72;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:504;w:720;h:96" transform="translate(0, 504)">
                    <g id="border-4" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:72;y:0;w:648;h:96" transform="translate(72, 0)">
                        <path id="top-left-fill_5" fill="#feecf7" d="M 8.6466e-27 47 C -4.8298e-13 21.0426 20.2333 0 45.1923 0 L 96 0 L 96 47 L 8.6466e-27 47 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:96;h:47"/>
                        <path id="center-fill_5" transform="translate(0, 47)" fill="#feecf7" d="M 0 0 L 96 0 L 96 2 L 0 2 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:47;w:96;h:2"/>
                        <path id="center-right-fill_5" transform="translate(95.99986267089844, 0)" fill="#feecf7" d="M 0.0005 0 L 552.0001 0 L 552.0001 96 L 0 96 L 0.0005 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:552;h:96"/>
                        <path id="bottom-left-fill_5" transform="translate(0, 49)" fill="#feecf7" d="M 0 0.01 L 96 0 L 96 47.0004 L 47 47.0104 C 21 47.0104 0 25.9676 0 0.01 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:96;h:47.010"/>
                        <path id="trop-left-stroke_5" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 47 C 0 21.0426 21.0426 0 47 0 L 100.5 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:100.500;h:47"/>
                        <path id="center-left-stroke_5" transform="translate(0, 46.98046875)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 2.0195 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:46.980;w:0;h:2.020"/>
                        <path id="bottom-left-stroke_5" transform="translate(0, 49)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 C 0 25.9576 21 47.0004 47 47.0004 L 76.5 47.0004 M 197 47.0004 L 173 47.0004" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:197;h:47.000"/>
                        <path id="Vector_5" transform="translate(196.6429901123047, 0)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 451.3573 0 L 451.3573 96 L 0.0003 96" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:196.643;y:0;w:451.357;h:96"/>
                        <g id="index_5" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:96;h:96">
                            <g id="tx-cc-4-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#ff00001a" transform="translate(24, 24)">
                                <text id="4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#d95da7" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">4</text>
                            </g>
                        </g>
                        <g id="icon_15" data-resize="horizontal:HUG;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:96" transform="translate(96, 0)">
                            <path id="Rectangle_118_5" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 13.4164 C 0 8.4588 1.6889 3.7576 4.642 0 M 100.642 0 C 97.6889 3.7576 96 8.4587 96 13.4164" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:100.642;h:13.416"/>
                            <path id="Rectangle_120_5" transform="translate(0, 13)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 96 0 L 96 60 M 0 60 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:13;w:96;h:60"/>
                            <path id="Rectangle_122_5" transform="translate(-19.34765625, 72.87109375)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 115.3474 0 C 115.3474 7.4707 110.7018 14.2999 103.3474 17.6409 C 100.4109 18.9749 97.9063 20.865 95.9568 23.1301 M 19.3474 0 C 19.3474 7.4707 14.7018 14.2999 7.3474 17.6409 C 4.4326 18.9651 1.9432 20.8372 0 23.0801" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-19.348;y:72.871;w:115.347;h:23.130"/>
                            <rect id="bt-cc-remove-4" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 36)" width="24" height="24" rx="0" ry="0"/>
                            <g id="ic-cc-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#33de7b1a" transform="translate(24, 24)">
                                <g id="icon_16" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_17" transform="translate(9, 5)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:12;primary:MAX;counter:MIN" data-position="x:192;y:6;w:456;h:84" transform="translate(192, 6)">
                            <g id="tx-lt-4" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:12;w:408;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:408;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:48;w:408;h:24" fill="#ff00001a" transform="translate(24, 48)">
                                <text id="Label_11" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:408;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                        </g>
                        <rect id="bt-cc-add-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, -12)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="g-3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 48;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:600;w:720;h:96" transform="translate(0, 600)">
                    <g id="border-3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:48;y:0;w:672;h:96" transform="translate(48, 0)">
                        <path id="top-left-fill_6" fill="#ffedeb" d="M 8.6466e-27 47 C -4.8298e-13 21.0426 20.2333 0 45.1923 0 L 96 0 L 96 47 L 8.6466e-27 47 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:96;h:47"/>
                        <path id="center-fill_6" transform="translate(0, 47)" fill="#ffedeb" d="M 0 0 L 96 0 L 96 2 L 0 2 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:47;w:96;h:2"/>
                        <path id="center-right-fill_6" transform="translate(95.99986267089844, 0)" fill="#ffedeb" d="M 0.0006 0 L 576.0001 0 L 576.0001 96 L 0 96 L 0.0006 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:576;h:96"/>
                        <path id="bottom-left-fill_6" transform="translate(0, 49)" fill="#ffedeb" d="M 0 0.01 L 96 0 L 96 47.0004 L 47 47.0104 C 21 47.0104 0 25.9676 0 0.01 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:96;h:47.010"/>
                        <path id="trop-left-stroke_6" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 47 C 0 21.0426 21.0426 0 47 0 L 100.5 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:100.500;h:47"/>
                        <path id="center-left-stroke_6" transform="translate(0, 46.98046875)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 2.0195 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:46.980;w:0;h:2.020"/>
                        <path id="bottom-left-stroke_6" transform="translate(0, 49)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 C 0 25.9576 21 47.0004 47 47.0004 L 76.5 47.0004 M 197 47.0004 L 173 47.0004" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:197;h:47.000"/>
                        <path id="Vector_6" transform="translate(196.64300537109375, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 475.3573 0 L 475.3573 96 L 0.0003 96" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:196.643;y:0;w:475.357;h:96"/>
                        <g id="index_6" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:96;h:96">
                            <g id="tx-cc-3-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#ff00001a" transform="translate(24, 24)">
                                <text id="3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#df5e59" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">3</text>
                            </g>
                        </g>
                        <g id="icon_18" data-resize="horizontal:HUG;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:96" transform="translate(96, 0)">
                            <path id="Rectangle_118_6" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 13.4164 C 0 8.4588 1.6889 3.7576 4.642 0 M 100.642 0 C 97.6889 3.7576 96 8.4587 96 13.4164" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:100.642;h:13.416"/>
                            <path id="Rectangle_120_6" transform="translate(0, 13)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 96 0 L 96 60 M 0 60 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:13;w:96;h:60"/>
                            <path id="Rectangle_122_6" transform="translate(-19.34765625, 72.87109375)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 115.3474 0 C 115.3474 7.4707 110.7018 14.2999 103.3474 17.6409 C 100.4109 18.9749 97.9063 20.865 95.9568 23.1301 M 19.3474 0 C 19.3474 7.4707 14.7018 14.2999 7.3474 17.6409 C 4.4326 18.9651 1.9432 20.8372 0 23.0801" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-19.348;y:72.871;w:115.347;h:23.130"/>
                            <rect id="bt-cc-remove-3" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 36)" width="24" height="24" rx="0" ry="0"/>
                            <g id="ic-cc-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#33de7b1a" transform="translate(24, 24)">
                                <g id="icon_19" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_20" transform="translate(9, 5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:12;primary:MAX;counter:MIN" data-position="x:192;y:6;w:480;h:84" transform="translate(192, 6)">
                            <g id="tx-lt-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:12;w:432;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_12" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:432;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:48;w:432;h:24" fill="#ff00001a" transform="translate(24, 48)">
                                <text id="Label_13" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:432;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                        </g>
                        <rect id="bt-cc-add-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, -12)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="g-2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 24;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:696;w:720;h:96" transform="translate(0, 696)">
                    <g id="border-2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:24;y:0;w:696;h:96" transform="translate(24, 0)">
                        <path id="top-left-fill_7" fill="#fef2e6" d="M 8.6466e-27 47 C -4.8298e-13 21.0426 20.2333 0 45.1923 0 L 96 0 L 96 47 L 8.6466e-27 47 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:96;h:47"/>
                        <path id="center-fill_7" transform="translate(0, 47)" fill="#fef2e6" d="M 0 0 L 96 0 L 96 2 L 0 2 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:47;w:96;h:2"/>
                        <path id="center-right-fill_7" transform="translate(95.9998550415039, 0)" fill="#fef2e6" d="M 0.0006 0 L 600.0001 0 L 600.0001 96 L 0 96 L 0.0006 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:600;h:96"/>
                        <path id="bottom-left-fill_7" transform="translate(0, 49)" fill="#fef2e6" d="M 0 0.01 L 96 0 L 96 47.0004 L 47 47.0104 C 21 47.0104 0 25.9676 0 0.01 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:96;h:47.010"/>
                        <path id="trop-left-stroke_7" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 47 C 0 21.0426 21.0426 0 47 0 L 100.5 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:100.500;h:47"/>
                        <path id="center-left-stroke_7" transform="translate(0, 46.98046875)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 2.0195 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:46.980;w:0;h:2.020"/>
                        <path id="bottom-left-stroke_7" transform="translate(0, 49)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 C 0 25.9576 21 47.0004 47 47.0004 L 76.5 47.0004 M 197 47.0004 L 173 47.0004" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:197;h:47.000"/>
                        <path id="Vector_7" transform="translate(196.6430206298828, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 499.3572 0 L 499.3572 96 L 0.0003 96" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:196.643;y:0;w:499.357;h:96"/>
                        <g id="index_7" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:96;h:96">
                            <g id="tx-cc-2-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#ff00001a" transform="translate(24, 24)">
                                <text id="2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#db8333" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                            </g>
                        </g>
                        <g id="icon_21" data-resize="horizontal:HUG;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:96" transform="translate(96, 0)">
                            <path id="Rectangle_118_7" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 13.4164 C 0 8.4588 1.6889 3.7576 4.642 0 M 100.642 0 C 97.6889 3.7576 96 8.4587 96 13.4164" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:100.642;h:13.416"/>
                            <path id="Rectangle_120_7" transform="translate(0, 13)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 96 0 L 96 60 M 0 60 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:13;w:96;h:60"/>
                            <path id="Rectangle_122_7" transform="translate(-19.34765625, 72.87109375)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 115.3474 0 C 115.3474 7.4707 110.7018 14.2999 103.3474 17.6409 C 100.4109 18.9749 97.9063 20.865 95.9568 23.1301 M 19.3474 0 C 19.3474 7.4707 14.7018 14.2999 7.3474 17.6409 C 4.4326 18.9651 1.9432 20.8372 0 23.0801" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-19.348;y:72.871;w:115.347;h:23.130"/>
                            <rect id="bt-cc-remove-2" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 36)" width="24" height="24" rx="0" ry="0"/>
                            <g id="ic-cc-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#33de7b1a" transform="translate(24, 24)">
                                <g id="icon_22" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_23" transform="translate(9, 5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:12;primary:MAX;counter:MIN" data-position="x:192;y:6;w:504;h:84" transform="translate(192, 6)">
                            <g id="tx-lt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:12;w:456;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_14" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:456;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:48;w:456;h:24" fill="#ff00001a" transform="translate(24, 48)">
                                <text id="Label_15" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:456;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                        </g>
                        <rect id="bt-cc-add-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, -12)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="g-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:792;w:720;h:96" transform="translate(0, 792)">
                    <g id="border-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:720;h:96">
                        <path id="trop-fill" fill="#fefbdb" d="M 1.04e-26 47 C -5.7958e-13 21.0426 24.2799 0 54.2308 0 L 720 0 L 720 47 L 1.04e-26 47 Z" data-constraints="horizontal:STRETCH;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:720;h:47"/>
                        <path id="center-fill_8" transform="translate(0, 46.98046875)" fill="#fefbdb" d="M 0 0 L 720 0 L 720 2.0195 L 0 2.0195 L 0 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:46.980;w:720;h:2.020"/>
                        <path id="bottom-fill" transform="translate(0, 49)" fill="#fefbdb" d="M 0 0 L 720 0 C 720 25.9576 695.7199 47.0004 665.7688 47.0004 L 54.2312 47.0004 C 24.2801 47.0004 0 25.9576 0 0 Z" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:720;h:47.000"/>
                        <path id="trop-left-stroke_8" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 47 C 0 21.0426 21.0426 0 47 0 L 100.5 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:100.500;h:47"/>
                        <path id="trop-right-stroke" transform="translate(196.6429901123047, 0)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 523.3573 47 L 523.3573 0 L 0 0" data-constraints="horizontal:STRETCH;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:196.643;y:0;w:523.357;h:47"/>
                        <path id="center-stroke" transform="translate(0, 46.98046875)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 720 0 L 720 2.0195 M 0 2.0195 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:46.980;w:720;h:2.020"/>
                        <path id="bottom-stroke" transform="translate(0, 49)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 C 0 25.9576 24.2801 47.0004 54.2312 47.0004 L 665.7688 47.0004 C 695.7198 47.0004 720 25.9576 720 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49;w:720;h:47.000"/>
                        <g id="index_8" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:96;h:96">
                            <g id="tx-cc-1-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#ff00001a" transform="translate(24, 24)">
                                <text id="1" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#d1bd08" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                            </g>
                        </g>
                        <g id="icon_24" data-resize="horizontal:HUG;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:10;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:96" transform="translate(96, 0)">
                            <path id="Rectangle_118_8" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 13.4164 C 0 8.4588 1.6889 3.7576 4.642 0 M 100.642 0 C 97.6889 3.7576 96 8.4587 96 13.4164" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:100.642;h:13.416"/>
                            <path id="Rectangle_120_8" transform="translate(0, 13)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 96 0 L 96 37 M 0 37 L 0 0" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:13;w:96;h:37"/>
                            <path id="Rectangle_119" transform="translate(0, 49.75)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 C 0 25.5427 20.7065 46.2492 46.2492 46.2492 L 49.7508 46.2492 C 75.2935 46.2492 96 25.5427 96 0" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49.750;w:96;h:46.249"/>
                            <rect id="bt-cc-remove-1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 36)" width="24" height="24" rx="0" ry="0"/>
                            <g id="ic-cc-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:48;h:48" fill="#33de7b1a" transform="translate(24, 24)">
                                <g id="icon_25" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_26" transform="translate(9, 5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:12;primary:MAX;counter:MIN" data-position="x:192;y:6;w:528;h:84" transform="translate(192, 6)">
                            <g id="tx-lt-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:12;w:480;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_16" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:480;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:24;y:48;w:480;h:24" fill="#ff00001a" transform="translate(24, 48)">
                                <text id="Label_17" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:480;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                        </g>
                        <rect id="bt-cc-add-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, -12)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
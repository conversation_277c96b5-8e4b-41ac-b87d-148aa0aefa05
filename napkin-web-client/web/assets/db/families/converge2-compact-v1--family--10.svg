<svg xmlns="http://www.w3.org/2000/svg" width="744" height="624">
    <g id="converge2-compact-v1--family--10" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L744 0 L744 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:744;h:24" data-entity-classes="Title Compact">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:744;h:0">
            <g id="body" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:744;h:576">
                <g id="lines" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:175.552;y:84;w:392.896;h:409.980" transform="translate(175.55224609375, 84)">
                    <g id="g-1" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:166.448;y:0;w:60;h:105" transform="translate(166.44775390625, 0)">
                        <g id="cu_Vector" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:105" data-entity-classes="Compact">
                            <path id="Vector" data-entity-classes="Compact" fill="#fefbdb" d="M 19.26 93.37 L 30 105 L 40.74 93.37 L 34.76 93.37 L 34.76 59.57 C 49.06 57.29 60 44.91 60 29.97 C 60 13.42 46.57 0 30 0 C 13.43 0 0 13.42 0 29.97 C 0 44.88 10.9 57.25 25.18 59.56 L 25.18 93.37 L 19.26 93.37 Z"/>
                            <path id="Vector_1" data-entity-classes="Compact" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 19.26 93.37 L 30 105 L 40.74 93.37 L 34.76 93.37 L 34.76 59.57 C 49.06 57.29 60 44.91 60 29.97 C 60 13.42 46.57 0 30 0 C 13.43 0 0 13.42 0 29.97 C 0 44.88 10.9 57.25 25.18 59.56 L 25.18 93.37 L 19.26 93.37 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:253.368;y:33.413;w:75.952;h:90.687" transform="translate(253.36767578125, 33.4130859375)">
                        <g id="cu_Vector_1" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:75.952;h:90.687" data-entity-classes="Compact">
                            <path id="Vector_2" data-entity-classes="Compact" fill="#fef2e6" d="M 0 74.9669 L 1.86 90.6869 L 17.39 87.5968 L 12.55 84.0769 L 32.42 56.7269 C 45.33 63.2869 61.46 59.6969 70.23 47.6169 C 79.96 34.2269 76.98 15.4769 63.58 5.7369 C 50.18 -4.0031 31.42 -1.0431 21.69 12.3569 C 12.92 24.4169 14.47 40.8368 24.67 51.0968 L 4.8 78.4469 L 0.01 74.9669 L 0 74.9669 Z"/>
                            <path id="Vector_3" data-entity-classes="Compact" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 74.9669 L 1.86 90.6869 L 17.39 87.5968 L 12.55 84.0769 L 32.42 56.7269 C 45.33 63.2869 61.46 59.6969 70.23 47.6169 C 79.96 34.2269 76.98 15.4769 63.58 5.7369 C 50.18 -4.0031 31.42 -1.0431 21.69 12.3569 C 12.92 24.4169 14.47 40.8368 24.67 51.0968 L 4.8 78.4469 L 0.01 74.9669 L 0 74.9669 Z"/>
                        </g>
                    </g>
                    <g id="g-3" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:291.548;y:120.908;w:101.347;h:60.003" transform="translate(291.5478515625, 120.908203125)">
                        <g id="cu_Vector_2" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:101.347;h:60.003" data-entity-classes="Compact">
                            <path id="Vector_4" data-entity-classes="Compact" fill="#ffedeb" d="M 7.74 39.372 L 0 53.182 L 14.38 59.812 L 12.53 54.122 L 44.68 43.672 C 51.27 56.572 66.42 63.142 80.63 58.532 C 96.37 53.412 104.99 36.492 99.87 20.742 C 94.75 4.982 77.84 -3.638 62.09 1.472 C 47.91 6.082 39.51 20.272 41.73 34.562 L 9.57 45.012 L 7.74 39.382 L 7.74 39.372 Z"/>
                            <path id="Vector_5" data-entity-classes="Compact" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 7.74 39.372 L 0 53.182 L 14.38 59.812 L 12.53 54.122 L 44.68 43.672 C 51.27 56.572 66.42 63.142 80.63 58.532 C 96.37 53.412 104.99 36.492 99.87 20.742 C 94.75 4.982 77.84 -3.638 62.09 1.472 C 47.91 6.082 39.51 20.272 41.73 34.562 L 9.57 45.012 L 7.74 39.382 L 7.74 39.372 Z"/>
                        </g>
                    </g>
                    <g id="g-4" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:291.548;y:229.086;w:101.348;h:60.010" transform="translate(291.5478515625, 229.0859375)">
                        <g id="cu_Vector_3" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:101.348;h:60.010" data-entity-classes="Compact">
                            <path id="Vector_6" data-entity-classes="Compact" fill="#feecf7" d="M 14.38 0.1841 L 0 6.8141 L 7.74 20.6241 L 9.59 14.9341 L 41.74 25.3841 C 39.49 39.6941 47.89 53.9141 62.09 58.5341 C 77.83 63.6541 94.75 55.0241 99.87 39.2641 C 104.99 23.5041 96.38 6.5841 80.63 1.4741 C 66.45 -3.1359 51.31 3.4141 44.71 16.2741 L 12.55 5.8241 L 14.38 0.1941 L 14.38 0.1841 Z"/>
                            <path id="Vector_7" data-entity-classes="Compact" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 14.38 0.1841 L 0 6.8141 L 7.74 20.6241 L 9.59 14.9341 L 41.74 25.3841 C 39.49 39.6941 47.89 53.9141 62.09 58.5341 C 77.83 63.6541 94.75 55.0241 99.87 39.2641 C 104.99 23.5041 96.38 6.5841 80.63 1.4741 C 66.45 -3.1359 51.31 3.4141 44.71 16.2741 L 12.55 5.8241 L 14.38 0.1941 L 14.38 0.1841 Z"/>
                        </g>
                    </g>
                    <g id="g-5" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:253.358;y:285.890;w:75.952;h:90.680" transform="translate(253.35791015625, 285.8896484375)">
                        <g id="cu_Vector_4" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:75.952;h:90.680" data-entity-classes="Compact">
                            <path id="Vector_8" data-entity-classes="Compact" fill="#faf0ff" d="M 17.39 3.09 L 1.86 0 L 0 15.72 L 4.84 12.2 L 24.71 39.55 C 14.48 49.8 12.91 66.25 21.69 78.33 C 31.42 91.72 50.17 94.68 63.58 84.95 C 76.98 75.21 79.96 56.46 70.23 43.07 C 61.46 31.01 45.37 27.41 32.47 33.93 L 12.6 6.58 L 17.39 3.1 L 17.39 3.09 Z"/>
                            <path id="Vector_9" data-entity-classes="Compact" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 17.39 3.09 L 1.86 0 L 0 15.72 L 4.84 12.2 L 24.71 39.55 C 14.48 49.8 12.91 66.25 21.69 78.33 C 31.42 91.72 50.17 94.68 63.58 84.95 C 76.98 75.21 79.96 56.46 70.23 43.07 C 61.46 31.01 45.37 27.41 32.47 33.93 L 12.6 6.58 L 17.39 3.1 L 17.39 3.09 Z"/>
                        </g>
                    </g>
                    <g id="g-6" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:166.448;y:304.980;w:60;h:105" transform="translate(166.44775390625, 304.98046875)">
                        <g id="cu_Vector_5" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:105" data-entity-classes="Compact">
                            <path id="Vector_10" data-entity-classes="Compact" fill="#f3f0ff" d="M 40.74 11.63 L 30 0 L 19.26 11.63 L 25.24 11.63 L 25.24 45.43 C 10.94 47.71 0 60.09 0 75.03 C 0 91.58 13.43 105 30 105 C 46.57 105 60 91.58 60 75.03 C 60 60.12 49.1 47.75 34.82 45.44 L 34.82 11.63 L 40.74 11.63 Z"/>
                            <path id="Vector_11" data-entity-classes="Compact" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 40.74 11.63 L 30 0 L 19.26 11.63 L 25.24 11.63 L 25.24 45.43 C 10.94 47.71 0 60.09 0 75.03 C 0 91.58 13.43 105 30 105 C 46.57 105 60 91.58 60 75.03 C 60 60.12 49.1 47.75 34.82 45.44 L 34.82 11.63 L 40.74 11.63 Z"/>
                        </g>
                    </g>
                    <g id="g-7" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:63.576;y:285.890;w:75.952;h:90.687" transform="translate(63.57568359375, 285.8896484375)">
                        <g id="cu_Vector_6" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:75.952;h:90.687" data-entity-classes="Compact">
                            <path id="Vector_12" data-entity-classes="Compact" fill="#edf4ff" d="M 75.9521 15.72 L 74.0921 0 L 58.5621 3.09 L 63.4021 6.61 L 43.5321 33.96 C 30.6221 27.4 14.4921 30.99 5.7221 43.07 C -4.0079 56.46 -1.0279 75.21 12.3721 84.95 C 25.7721 94.69 44.5321 91.73 54.2621 78.33 C 63.0321 66.27 61.4821 49.85 51.2821 39.59 L 71.1521 12.24 L 75.9421 15.72 L 75.9521 15.72 Z"/>
                            <path id="Vector_13" data-entity-classes="Compact" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 75.9521 15.72 L 74.0921 0 L 58.5621 3.09 L 63.4021 6.61 L 43.5321 33.96 C 30.6221 27.4 14.4921 30.99 5.7221 43.07 C -4.0079 56.46 -1.0279 75.21 12.3721 84.95 C 25.7721 94.69 44.5321 91.73 54.2621 78.33 C 63.0321 66.27 61.4821 49.85 51.2821 39.59 L 71.1521 12.24 L 75.9421 15.72 L 75.9521 15.72 Z"/>
                        </g>
                    </g>
                    <g id="g-8" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.001;y:229.068;w:101.347;h:60.003" transform="translate(0.0009765625, 229.068359375)">
                        <g id="cu_Vector_7" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:101.347;h:60.003" data-entity-classes="Compact">
                            <path id="Vector_14" data-entity-classes="Compact" fill="#e8f9ff" d="M 93.6069 20.6313 L 101.3469 6.8213 L 86.9669 0.1913 L 88.8169 5.8813 L 56.6669 16.3313 C 50.0769 3.4313 34.9269 -3.1387 20.7169 1.4713 C 4.9769 6.5913 -3.6431 23.5113 1.4769 39.2613 C 6.5969 55.0213 23.5069 63.6413 39.2569 58.5313 C 53.4369 53.9213 61.8369 39.7313 59.6169 25.4413 L 91.7769 14.9913 L 93.6069 20.6213 L 93.6069 20.6313 Z"/>
                            <path id="Vector_15" data-entity-classes="Compact" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 93.6069 20.6313 L 101.3469 6.8213 L 86.9669 0.1913 L 88.8169 5.8813 L 56.6669 16.3313 C 50.0769 3.4313 34.9269 -3.1387 20.7169 1.4713 C 4.9769 6.5913 -3.6431 23.5113 1.4769 39.2613 C 6.5969 55.0213 23.5069 63.6413 39.2569 58.5313 C 53.4369 53.9213 61.8369 39.7313 59.6169 25.4413 L 91.7769 14.9913 L 93.6069 20.6213 L 93.6069 20.6313 Z"/>
                        </g>
                    </g>
                    <g id="g-9" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:120.895;w:101.348;h:60.010" transform="translate(0, 120.89453125)">
                        <g id="cu_Vector_8" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:101.348;h:60.010" data-entity-classes="Compact">
                            <path id="Vector_16" data-entity-classes="Compact" fill="#e7fbf2" d="M 86.9678 59.8258 L 101.3478 53.1958 L 93.6078 39.3858 L 91.7578 45.0758 L 59.6078 34.6258 C 61.8578 20.3158 53.4578 6.0958 39.2578 1.4758 C 23.5178 -3.6442 6.5978 4.9858 1.4778 20.7458 C -3.6422 36.5058 4.9678 53.4258 20.7178 58.5358 C 34.8978 63.1458 50.0378 56.5958 56.6378 43.7358 L 88.7978 54.1858 L 86.9678 59.8158 L 86.9678 59.8258 Z"/>
                            <path id="Vector_17" data-entity-classes="Compact" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 86.9678 59.8258 L 101.3478 53.1958 L 93.6078 39.3858 L 91.7578 45.0758 L 59.6078 34.6258 C 61.8578 20.3158 53.4578 6.0958 39.2578 1.4758 C 23.5178 -3.6442 6.5978 4.9858 1.4778 20.7458 C -3.6422 36.5058 4.9678 53.4258 20.7178 58.5358 C 34.8978 63.1458 50.0378 56.5958 56.6378 43.7358 L 88.7978 54.1858 L 86.9678 59.8158 L 86.9678 59.8258 Z"/>
                        </g>
                    </g>
                    <g id="g-10" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:63.585;y:33.420;w:75.952;h:90.680" transform="translate(63.58544921875, 33.419921875)">
                        <g id="cu_Vector_9" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:75.952;h:90.680" data-entity-classes="Compact">
                            <path id="Vector_18" data-entity-classes="Compact" fill="#f2fae1" d="M 58.5621 87.5897 L 74.0921 90.6797 L 75.9521 74.9597 L 71.1121 78.4797 L 51.2421 51.1297 C 61.4721 40.8797 63.0421 24.4297 54.2621 12.3497 C 44.5321 -1.0403 25.7821 -4.0003 12.3721 5.7297 C -1.0279 15.4697 -4.0079 34.2197 5.7221 47.6097 C 14.4921 59.6697 30.5821 63.2697 43.4821 56.7497 L 63.3521 84.0997 L 58.5621 87.5797 L 58.5621 87.5897 Z"/>
                            <path id="Vector_19" data-entity-classes="Compact" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 58.5621 87.5897 L 74.0921 90.6797 L 75.9521 74.9597 L 71.1121 78.4797 L 51.2421 51.1297 C 61.4721 40.8797 63.0421 24.4297 54.2621 12.3497 C 44.5321 -1.0403 25.7821 -4.0003 12.3721 5.7297 C -1.0279 15.4697 -4.0079 34.2197 5.7221 47.6097 C 14.4921 59.6697 30.5821 63.2697 43.4821 56.7497 L 63.3521 84.0997 L 58.5621 87.5797 L 58.5621 87.5897 Z"/>
                        </g>
                    </g>
                    <g id="g-0" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:124.448;y:132.990;w:144;h:144.000" transform="translate(124.44775390625, 132.990234375)">
                        <g id="cu_Vector_10" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:144.000" data-entity-classes="Compact">
                            <path id="Vector_20" data-entity-classes="Compact" fill="#f6f6f6" d="M 144 72 C 144 32.24 111.76 0 72 0 C 32.24 0 0 32.24 0 72 C 0 111.76 32.23 144 72 144 C 111.77 144 144 111.77 144 72 Z"/>
                            <path id="Vector_21" data-entity-classes="Compact" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 144 72 C 144 32.24 111.76 0 72 0 C 32.24 0 0 32.24 0 72 C 0 111.76 32.23 144 72 144 C 111.77 144 144 111.77 144 72 Z"/>
                        </g>
                    </g>
                </g>
                <g id="text-1" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:MIN;counter:CENTER" data-position="x:318;y:36;w:108;h:30" transform="translate(318, 36)">
                    <g id="tx-cc-1" data-entity-classes="DescTitle Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:108;h:12" fill="#ff00001a">
                        <text id="Label" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#d1bd08" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <g id="tx-cb-1-desc" data-entity-classes="Description Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:108;h:12" fill="#ff00001a" transform="translate(0, 18)">
                        <text id="Label_1" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:BOTTOM"/>
                    </g>
                    <rect id="bt-cc-remove-1" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:42;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 42, 30)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-3" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:MIN;counter:MIN" data-position="x:582;y:216;w:108;h:30" transform="translate(582, 216)">
                    <g id="tx-lc-3" data-entity-classes="DescTitle Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:108;h:12" fill="#ff00001a">
                        <text id="Label_2" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#df5e59" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lb-3-desc" data-entity-classes="Description Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:108;h:12" fill="#ff00001a" transform="translate(0, 18)">
                        <text id="Label_3" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:BOTTOM"/>
                    </g>
                    <rect id="bt-cc-remove-3" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:3;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 3)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-2" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:MIN;counter:MIN" data-position="x:516;y:114;w:108;h:30" transform="translate(516, 114)">
                    <g id="tx-lc-2" data-entity-classes="DescTitle Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:108;h:12" fill="#ff00001a">
                        <text id="Label_4" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#db8333" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lb-2-desc" data-entity-classes="Description Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:108;h:12" fill="#ff00001a" transform="translate(0, 18)">
                        <text id="Label_5" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:BOTTOM"/>
                    </g>
                    <rect id="bt-cc-remove-2" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:6;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 6)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-4" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:MIN;counter:MIN" data-position="x:588;y:330;w:108;h:30" transform="translate(588, 330)">
                    <g id="tx-lc-4" data-entity-classes="DescTitle Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:108;h:12" fill="#ff00001a">
                        <text id="Label_6" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#d95da7" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-4-desc" data-entity-classes="Description Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:108;h:12" fill="#ff00001a" transform="translate(0, 18)">
                        <text id="Label_7" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-4" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:3;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 3)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-8" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:MIN;counter:MAX" data-position="x:48;y:330;w:108;h:30" transform="translate(48, 330)">
                    <g id="tx-rc-8" data-entity-classes="DescTitle Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:108;h:12" fill="#ff00001a">
                        <text id="Label_8" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#17aee1" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rc-8-desc" data-entity-classes="Description Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:108;h:12" fill="#ff00001a" transform="translate(0, 18)">
                        <text id="Label_9" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-8" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:3;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, 3)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-9" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:MIN;counter:MAX" data-position="x:54;y:216;w:108;h:30" transform="translate(54, 216)">
                    <g id="tx-rc-9" data-entity-classes="DescTitle Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:108;h:12" fill="#ff00001a">
                        <text id="Label_10" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#3cc583" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rb-9-desc" data-entity-classes="Description Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:108;h:12" fill="#ff00001a" transform="translate(0, 18)">
                        <text id="Label_11" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:BOTTOM"/>
                    </g>
                    <rect id="bt-cc-remove-9" data-entity-classes="Compact" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:3;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, 3)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-10" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:MIN;counter:MAX" data-position="x:120;y:114;w:108;h:30" transform="translate(120, 114)">
                    <g id="tx-rc-10" data-entity-classes="DescTitle Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:108;h:12" fill="#ff00001a">
                        <text id="Label_12" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#93c332" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rb-10-desc" data-entity-classes="Description Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:108;h:12" fill="#ff00001a" transform="translate(0, 18)">
                        <text id="Label_13" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:BOTTOM"/>
                    </g>
                    <rect id="bt-cc-remove-10" data-entity-classes="Compact" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:6;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, 6)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-5" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:MIN;counter:MIN" data-position="x:516;y:444;w:108;h:30" transform="translate(516, 444)">
                    <g id="tx-lc-5" data-entity-classes="DescTitle Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:108;h:12" fill="#ff00001a">
                        <text id="Label_14" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#b960e2" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lt-5-desc" data-entity-classes="Description Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:108;h:12" fill="#ff00001a" transform="translate(0, 18)">
                        <text id="Label_15" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-5" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-7" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:MIN;counter:MAX" data-position="x:120;y:444;w:108;h:30" transform="translate(120, 444)">
                    <g id="tx-rc-7" data-entity-classes="DescTitle Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:108;h:12" fill="#ff00001a">
                        <text id="Label_16" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#4987ec" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rb-7-desc" data-entity-classes="Description Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:108;h:12" fill="#ff00001a" transform="translate(0, 18)">
                        <text id="Label_17" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:BOTTOM"/>
                    </g>
                    <rect id="bt-cc-remove-7" data-entity-classes="Compact" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-6" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:MIN;counter:CENTER" data-position="x:318;y:510;w:108;h:30" transform="translate(318, 510)">
                    <g id="tx-cc-6" data-entity-classes="DescTitle Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:108;h:12" fill="#ff00001a">
                        <text id="Label_18" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#7e62ec" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <g id="tx-ct-6-desc" data-entity-classes="Description Compact" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:108;h:12" fill="#ff00001a" transform="translate(0, 18)">
                        <text id="Label_19" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:108;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-6" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:42;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 42, -24)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <path id="ic-cc-end" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 251.99609375)" fill="#33de7b1a" d="M 0 0 L 72 0 L 72 72 L 0 72 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:336;y:251.996;w:72;h:72"/>
                <path id="ic-cc-1" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 354, 96)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:354;y:96;w:36;h:36"/>
                <path id="ic-cc-2" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 456, 129)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:456;y:129;w:36;h:36"/>
                <path id="ic-cc-3" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 519, 216)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:519;y:216;w:36;h:36"/>
                <path id="ic-cc-4" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 519, 324)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:519;y:324;w:36;h:36"/>
                <path id="ic-cc-5" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 456, 411)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:456;y:411;w:36;h:36"/>
                <path id="ic-cc-6" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 354, 444)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:354;y:444;w:36;h:36"/>
                <path id="ic-cc-7" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 252, 411)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:252;y:411;w:36;h:36"/>
                <path id="ic-cc-8" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 189, 324)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:189;y:324;w:36;h:36"/>
                <path id="ic-cc-9" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 189, 216)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:189;y:216;w:36;h:36"/>
                <path id="ic-cc-10" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 252, 129)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:252;y:129;w:36;h:36"/>
                <rect id="bt-cc-add-2" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:403;y:144;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 403, 144)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-3" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:468;y:192;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 468, 192)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-4" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:492;y:276;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 492, 276)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-5" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:468;y:360;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 468, 360)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-6" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:408;y:420;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 408, 420)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-7" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:312;y:420;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 312, 420)" width="24" height="24" rx="0" ry="0"/>
                <path id="bt-cc-add-11" data-entity-classes="Compact" transform="translate(312, 144)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:312;y:144;w:24;h:24"/>
                <rect id="bt-cc-add-8" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:252;y:360;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 252, 360)" width="24" height="24" rx="0" ry="0"/>
                <path id="bt-cc-add-10" data-entity-classes="Compact" transform="translate(240, 192)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:240;y:192;w:24;h:24"/>
                <rect id="bt-cc-add-9" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:228;y:276;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 228, 276)" width="24" height="24" rx="0" ry="0"/>
                <path id="bt-cc-add-1" data-entity-classes="Compact" transform="translate(360, 192)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:360;y:192;w:24;h:24"/>
            </g>
        </g>
    </g>
</svg>
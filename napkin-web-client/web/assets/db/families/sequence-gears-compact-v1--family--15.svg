<svg xmlns="http://www.w3.org/2000/svg" width="968" height="354">
    <g id="sequence-gears-v1--family--15" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L968 0 L968 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:968;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:968;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:968;h:306">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:19;y:64;w:925;h:171" transform="translate(19, 64)">
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:2;y:38.892;w:66.758;h:67.994" transform="translate(2, 38.89208984375)">
                        <g id="cu_gear-1" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-0.000;w:66.758;h:67.994">
                            <path id="gear-1" transform="translate(0, -0.000009081141797651071)" fill="#fefbdb" d="M 0 25.8272 L 8.918 31.8845 L 8.918 36.1092 L 0 42.1666 C 0.5237 43.7637 0.6765 44.2301 0.9465 45.0537 C 1.0869 45.4821 1.2589 46.0071 1.5313 46.838 L 12.3381 46.5418 C 12.7077 47.0461 12.9938 47.4364 13.2544 47.7918 C 13.7456 48.4619 14.1458 49.0078 14.8427 49.9591 L 11.2193 60.057 C 12.2611 60.8073 12.7423 61.1539 13.2235 61.5006 C 13.7046 61.8472 14.1858 62.1938 15.2276 62.9442 L 23.7947 56.4072 C 25.0857 56.8232 25.7469 57.0361 26.7211 57.3499 L 27.8468 57.7125 L 30.9017 67.9938 L 35.8558 67.9938 L 38.9107 57.7125 C 40.1982 57.2976 40.8593 57.0847 41.829 56.7723 C 42.159 56.6661 42.5246 56.5483 42.9629 56.4072 L 51.5299 62.9442 C 52.5717 62.1938 53.0529 61.8472 53.5341 61.5006 C 54.0153 61.1539 54.4964 60.8073 55.5383 60.057 L 51.9148 49.9591 C 52.2806 49.46 52.5647 49.0726 52.823 48.7202 C 53.318 48.045 53.7187 47.4984 54.4194 46.5418 L 65.2262 46.838 C 65.7679 45.1858 65.9129 44.7436 66.2011 43.8642 C 66.3364 43.4514 66.5032 42.9423 66.7575 42.1665 L 57.8395 36.1092 L 57.8395 31.8845 L 66.7575 25.8272 C 66.2172 24.1792 66.0716 23.7351 65.7849 22.8602 C 65.6492 22.4462 65.4819 21.9358 65.2262 21.1558 L 54.4194 21.452 C 54.0528 20.9517 53.7682 20.5635 53.5094 20.2104 C 53.0153 19.5364 52.6147 18.99 51.9148 18.0347 L 55.5383 7.9368 C 54.4964 7.1865 54.0153 6.8399 53.5341 6.4932 C 53.0529 6.1466 52.5717 5.8 51.5299 5.0496 L 42.9629 11.5866 C 41.6861 11.1752 41.0254 10.9624 40.0688 10.6543 L 38.9107 10.2813 L 35.8558 0 L 30.9017 4.3132e-7 L 27.8468 10.2813 C 26.1213 10.8373 25.5209 11.0306 23.7947 11.5866 L 15.2276 5.0496 C 14.1858 5.8 13.7046 6.1466 13.2234 6.4932 C 12.7423 6.8399 12.2611 7.1865 11.2193 7.9368 L 14.8427 18.0347 C 14.4722 18.5401 14.1856 18.9311 13.9246 19.2872 C 13.4342 19.9561 13.0341 20.5018 12.3381 21.452 L 1.5313 21.1558 C 1.0078 22.7525 0.8548 23.2191 0.585 24.0423 C 0.4446 24.4708 0.2725 24.9959 0 25.8272 Z M 51.5859 33.9969 C 51.5859 44.0106 43.4343 52.1286 33.3793 52.1286 C 23.3242 52.1286 15.1727 44.0106 15.1727 33.9969 C 15.1727 23.9832 23.3242 15.8652 33.3793 15.8652 C 43.4343 15.8652 51.5859 23.9832 51.5859 33.9969 Z"/>
                            <path id="gear-1_1" transform="translate(0, -0.000009081141797651071)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 25.8272 L 8.918 31.8845 L 8.918 36.1092 L 0 42.1666 C 0.5237 43.7637 0.6765 44.2301 0.9465 45.0537 C 1.0869 45.4821 1.2589 46.0071 1.5313 46.838 L 12.3381 46.5418 C 12.7077 47.0461 12.9938 47.4364 13.2544 47.7918 C 13.7456 48.4619 14.1458 49.0078 14.8427 49.9591 L 11.2193 60.057 C 12.2611 60.8073 12.7423 61.1539 13.2235 61.5006 C 13.7046 61.8472 14.1858 62.1938 15.2276 62.9442 L 23.7947 56.4072 C 25.0857 56.8232 25.7469 57.0361 26.7211 57.3499 L 27.8468 57.7125 L 30.9017 67.9938 L 35.8558 67.9938 L 38.9107 57.7125 C 40.1982 57.2976 40.8593 57.0847 41.829 56.7723 C 42.159 56.6661 42.5246 56.5483 42.9629 56.4072 L 51.5299 62.9442 C 52.5717 62.1938 53.0529 61.8472 53.5341 61.5006 C 54.0153 61.1539 54.4964 60.8073 55.5383 60.057 L 51.9148 49.9591 C 52.2806 49.46 52.5647 49.0726 52.823 48.7202 C 53.318 48.045 53.7187 47.4984 54.4194 46.5418 L 65.2262 46.838 C 65.7679 45.1858 65.9129 44.7436 66.2011 43.8642 C 66.3364 43.4514 66.5032 42.9423 66.7575 42.1665 L 57.8395 36.1092 L 57.8395 31.8845 L 66.7575 25.8272 C 66.2172 24.1792 66.0716 23.7351 65.7849 22.8602 C 65.6492 22.4462 65.4819 21.9358 65.2262 21.1558 L 54.4194 21.452 C 54.0528 20.9517 53.7682 20.5635 53.5094 20.2104 C 53.0153 19.5364 52.6147 18.99 51.9148 18.0347 L 55.5383 7.9368 C 54.4964 7.1865 54.0153 6.8399 53.5341 6.4932 C 53.0529 6.1466 52.5717 5.8 51.5299 5.0496 L 42.9629 11.5866 C 41.6861 11.1752 41.0254 10.9624 40.0688 10.6543 L 38.9107 10.2813 L 35.8558 0 L 30.9017 4.3132e-7 L 27.8468 10.2813 C 26.1213 10.8373 25.5209 11.0306 23.7947 11.5866 L 15.2276 5.0496 C 14.1858 5.8 13.7046 6.1466 13.2234 6.4932 C 12.7423 6.8399 12.2611 7.1865 11.2193 7.9368 L 14.8427 18.0347 C 14.4722 18.5401 14.1856 18.9311 13.9246 19.2872 C 13.4342 19.9561 13.0341 20.5018 12.3381 21.452 L 1.5313 21.1558 C 1.0078 22.7525 0.8548 23.2191 0.585 24.0423 C 0.4446 24.4708 0.2725 24.9959 0 25.8272 Z M 51.5859 33.9969 C 51.5859 44.0106 43.4343 52.1286 33.3793 52.1286 C 23.3242 52.1286 15.1727 44.0106 15.1727 33.9969 C 15.1727 23.9832 23.3242 15.8652 33.3793 15.8652 C 43.4343 15.8652 51.5859 23.9832 51.5859 33.9969 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:57.014;y:71.034;w:79.738;h:78.502" transform="translate(57.013671875, 71.0341796875)">
                        <g id="cu_gear-2" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-0.000;w:79.738;h:78.502">
                            <path id="gear-2" transform="translate(0, -0.00048828125)" fill="#fef2e6" d="M 40.5553 0.0024 C 39.5142 0.0036 39.0208 0.0041 37.0128 0.0065 L 37.0147 0.0068 L 33.9855 11.1986 C 32.9504 11.5022 32.2861 11.6971 31.6217 11.892 C 30.9573 12.0868 30.2929 12.2817 29.2579 12.5853 L 20.601 4.8214 C 19.8018 5.3337 19.2881 5.6631 18.8758 5.9274 C 18.009 6.483 17.5905 6.7513 15.9104 7.8278 L 19.4467 18.8737 L 18.674 19.7631 C 17.8237 20.7419 17.3397 21.2991 16.2244 22.5823 L 4.7222 20.7038 C 4.1101 22.0414 3.8379 22.6365 3.565 23.2332 C 3.2931 23.8275 3.0206 24.4232 2.4111 25.7553 L 11.3907 33.1483 C 11.2385 34.2123 11.1408 34.8948 11.043 35.5778 C 10.9455 36.2588 10.8479 36.9404 10.6962 38.0012 L 0 42.6049 C 0.2121 44.0559 0.3067 44.7034 0.4012 45.3506 C 0.4957 45.9982 0.5902 46.6456 0.8025 48.0978 L 12.3746 49.4904 C 12.9622 50.7645 13.2748 51.4428 13.6717 52.3037 C 13.8824 52.761 14.1169 53.2698 14.4289 53.9467 L 7.9354 63.5704 C 9.4933 65.3539 9.6615 65.5464 10.6777 66.7095 C 10.9237 66.991 11.2194 67.3294 11.5966 67.761 L 22.087 62.7111 L 26.2378 65.3563 L 26.0081 76.9448 C 26.5658 77.1069 27.0018 77.2335 27.3641 77.3388 C 28.8408 77.7678 29.0916 77.8407 31.3656 78.5019 L 37.4431 68.6133 L 42.3726 68.6073 L 48.4799 78.4814 C 49.8943 78.0668 50.5252 77.8818 51.1562 77.6968 C 51.7872 77.5117 52.4182 77.3266 53.8327 76.9116 L 53.5684 65.3236 C 54.4754 64.7422 55.0577 64.3691 55.64 63.996 C 56.2223 63.6228 56.8046 63.2497 57.7116 62.6684 L 68.217 67.6931 L 71.8657 63.4941 L 65.3433 53.8854 C 65.8543 52.7683 66.1562 52.1086 66.488 51.3833 C 66.7366 50.8402 67.002 50.2602 67.3846 49.4239 L 78.9522 48.0038 L 79.7382 42.5093 L 69.0285 37.9314 C 68.8923 36.9989 68.7988 36.3595 68.7106 35.7558 C 68.5978 34.9847 68.4936 34.2717 68.3197 33.0803 L 77.277 25.6662 C 76.6623 24.3326 76.388 23.7379 76.1138 23.1431 C 75.8395 22.5483 75.5653 21.9536 74.9506 20.62 L 63.4537 22.5258 L 60.22 18.8246 L 63.7231 7.7704 L 62.6228 7.0695 C 61.2468 6.1928 61.063 6.0757 59.0232 4.7756 L 50.3897 12.5605 C 48.3428 11.9651 47.7437 11.791 45.7325 11.2066 L 45.6576 11.1849 L 42.5942 0 L 40.5553 0.0024 Z M 39.8698 57.4885 C 29.7504 57.4885 21.5468 49.3231 21.5468 39.251 C 21.5468 29.1788 29.7504 21.0135 39.8698 21.0135 C 49.9891 21.0135 58.1927 29.1788 58.1927 39.251 C 58.1927 49.3231 49.9891 57.4885 39.8698 57.4885 Z"/>
                            <path id="gear-2_1" transform="translate(0, -0.00048828125)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 40.5553 0.0024 C 39.5142 0.0036 39.0208 0.0041 37.0128 0.0065 L 37.0147 0.0068 L 33.9855 11.1986 C 32.9504 11.5022 32.2861 11.6971 31.6217 11.892 C 30.9573 12.0868 30.2929 12.2817 29.2579 12.5853 L 20.601 4.8214 C 19.8018 5.3337 19.2881 5.6631 18.8758 5.9274 C 18.009 6.483 17.5905 6.7513 15.9104 7.8278 L 19.4467 18.8737 L 18.674 19.7631 C 17.8237 20.7419 17.3397 21.2991 16.2244 22.5823 L 4.7222 20.7038 C 4.1101 22.0414 3.8379 22.6365 3.565 23.2332 C 3.2931 23.8275 3.0206 24.4232 2.4111 25.7553 L 11.3907 33.1483 C 11.2385 34.2123 11.1408 34.8948 11.043 35.5778 C 10.9455 36.2588 10.8479 36.9404 10.6962 38.0012 L 0 42.6049 C 0.2121 44.0559 0.3067 44.7034 0.4012 45.3506 C 0.4957 45.9982 0.5902 46.6456 0.8025 48.0978 L 12.3746 49.4904 C 12.9622 50.7645 13.2748 51.4428 13.6717 52.3037 C 13.8824 52.761 14.1169 53.2698 14.4289 53.9467 L 7.9354 63.5704 C 9.4933 65.3539 9.6615 65.5464 10.6777 66.7095 C 10.9237 66.991 11.2194 67.3294 11.5966 67.761 L 22.087 62.7111 L 26.2378 65.3563 L 26.0081 76.9448 C 26.5658 77.1069 27.0018 77.2335 27.3641 77.3388 C 28.8408 77.7678 29.0916 77.8407 31.3656 78.5019 L 37.4431 68.6133 L 42.3726 68.6073 L 48.4799 78.4814 C 49.8943 78.0668 50.5252 77.8818 51.1562 77.6968 C 51.7872 77.5117 52.4182 77.3266 53.8327 76.9116 L 53.5684 65.3236 C 54.4754 64.7422 55.0577 64.3691 55.64 63.996 C 56.2223 63.6228 56.8046 63.2497 57.7116 62.6684 L 68.217 67.6931 L 71.8657 63.4941 L 65.3433 53.8854 C 65.8543 52.7683 66.1562 52.1086 66.488 51.3833 C 66.7366 50.8402 67.002 50.2602 67.3846 49.4239 L 78.9522 48.0038 L 79.7382 42.5093 L 69.0285 37.9314 C 68.8923 36.9989 68.7988 36.3595 68.7106 35.7558 C 68.5978 34.9847 68.4936 34.2717 68.3197 33.0803 L 77.277 25.6662 C 76.6623 24.3326 76.388 23.7379 76.1138 23.1431 C 75.8395 22.5483 75.5653 21.9536 74.9506 20.62 L 63.4537 22.5258 L 60.22 18.8246 L 63.7231 7.7704 L 62.6228 7.0695 C 61.2468 6.1928 61.063 6.0757 59.0232 4.7756 L 50.3897 12.5605 C 48.3428 11.9651 47.7437 11.791 45.7325 11.2066 L 45.6576 11.1849 L 42.5942 0 L 40.5553 0.0024 Z M 39.8698 57.4885 C 29.7504 57.4885 21.5468 49.3231 21.5468 39.251 C 21.5468 29.1788 29.7504 21.0135 39.8698 21.0135 C 49.9891 21.0135 58.1927 29.1788 58.1927 39.251 C 58.1927 49.3231 49.9891 57.4885 39.8698 57.4885 Z"/>
                        </g>
                    </g>
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:112.027;y:7.986;w:92.101;h:92.719" transform="translate(112.02734375, 7.986328125)">
                        <g id="cu_gear-3" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:92.101;h:92.719">
                            <path id="gear-3" fill="#ffedeb" d="M 0.274 41.8546 C 0.3349 41.1961 0.3957 40.5376 0.5481 38.8895 L 0.5474 38.8883 L 10.3347 37.82 C 10.9567 35.8047 11.133 35.2336 11.5724 33.8112 L 11.9776 32.499 L 4.5299 25.9906 L 5.4613 24.315 C 6.0971 23.171 6.2308 22.9304 7.4163 20.7983 L 16.7872 23.8471 L 20.4293 19.6622 L 16.2442 10.654 C 17.0877 10.0194 17.606 9.6295 18.0078 9.3272 C 18.8578 8.6878 19.1869 8.4402 20.9704 7.098 L 28.3046 13.7371 C 29.4985 13.183 30.1542 12.8786 30.8098 12.5743 C 31.4654 12.27 32.121 11.9657 33.3149 11.4116 L 33.1155 1.4607 C 34.7134 1.098 35.3517 0.9532 35.99 0.8082 C 36.6282 0.6633 37.2665 0.5183 38.8638 0.1553 L 42.8927 9.2364 C 44.221 9.2211 44.9438 9.2126 45.673 9.2041 C 46.3866 9.1958 47.1065 9.1875 48.4059 9.1724 L 52.2273 0 C 54.6634 0.4935 54.88 0.5374 56.2444 0.8141 C 56.6785 0.9022 57.2288 1.0138 58.0038 1.171 L 58.0314 11.1238 C 59.2379 11.65 59.9002 11.9389 60.5626 12.2279 C 61.2249 12.5168 61.8873 12.8057 63.0937 13.3319 L 70.2749 6.5243 C 71.6105 7.4815 72.1442 7.864 72.6778 8.2465 C 73.2114 8.6291 73.7451 9.0117 75.0808 9.9693 L 71.102 19.0726 C 71.9925 20.0496 72.4813 20.5859 72.9701 21.1223 C 73.4589 21.6586 73.9477 22.195 74.8381 23.1719 L 84.1369 19.9057 C 84.9723 21.3304 85.3058 21.8992 85.6396 22.4686 C 85.973 23.0372 86.3067 23.6064 87.1412 25.0295 L 79.844 31.7097 C 80.2644 32.9682 80.4952 33.6592 80.726 34.3502 C 80.9567 35.0413 81.1875 35.7323 81.6079 36.9908 L 91.4172 37.8308 C 91.6072 39.475 91.683 40.132 91.7589 40.7889 C 91.8348 41.4459 91.9107 42.1029 92.1007 43.7471 L 82.7467 46.8491 C 82.6244 48.1716 82.5573 48.8978 82.4902 49.6241 C 82.4231 50.3503 82.356 51.0765 82.2338 52.399 L 90.8572 57.2001 C 90.3696 58.7796 90.1745 59.411 89.9796 60.042 C 89.7845 60.6736 89.5895 61.3047 89.1014 62.8856 L 79.3085 61.8732 C 78.664 63.0325 78.3104 63.6684 77.9565 64.3048 C 77.6034 64.9398 77.25 65.5754 76.6071 66.732 L 82.5535 74.6637 L 81.3434 76.0542 C 80.4459 77.0854 80.2879 77.2669 78.662 79.1358 L 70.1225 74.1838 C 69.5439 74.6192 69.1086 74.9469 68.7378 75.226 C 67.758 75.9634 67.2293 76.3614 65.6998 77.5122 L 67.941 87.2038 L 62.5871 89.689 L 56.7785 81.6533 C 55.4965 81.9443 54.7926 82.1042 54.0887 82.2641 C 53.3848 82.4241 52.6809 82.584 51.3989 82.875 L 49.5472 92.6503 L 43.6565 92.7188 L 41.584 82.9885 C 40.2957 82.7273 39.5884 82.5838 38.8811 82.4404 C 38.1737 82.297 37.4664 82.1535 36.1781 81.8923 L 30.5539 90.0611 L 25.1448 87.7015 L 27.1646 77.9604 C 26.0929 77.192 25.5044 76.7701 24.9159 76.3482 C 24.3274 75.9263 23.7389 75.5044 22.6673 74.736 L 14.2432 79.8858 C 13.1336 78.6685 12.6903 78.1821 12.2469 77.6957 C 11.8036 77.2093 11.3603 76.7229 10.2507 75.5056 L 16.0147 67.4376 C 15.3438 66.2933 14.9757 65.6658 14.6073 65.0376 C 14.2399 64.4112 13.872 63.784 13.2029 62.6428 L 3.4357 63.8835 C 2.7937 61.962 2.6242 61.4543 2.3481 60.6278 C 2.1736 60.1053 1.9566 59.4555 1.5507 58.2405 L 10.0619 53.2396 C 9.9096 51.9203 9.8258 51.1959 9.7421 50.4714 C 9.6584 49.7469 9.5747 49.0224 9.4223 47.7031 L 0 44.8198 C 0.1524 43.1717 0.2132 42.5132 0.274 41.8546 Z M 64.1419 46.3593 C 64.1419 36.267 56.0416 28.0853 46.0498 28.0853 C 36.0579 28.0853 27.9576 36.267 27.9576 46.3593 C 27.9576 56.4516 36.0579 64.6334 46.0498 64.6334 C 56.0416 64.6334 64.1419 56.4516 64.1419 46.3593 Z"/>
                            <path id="gear-3_1" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.274 41.8546 C 0.3349 41.1961 0.3957 40.5376 0.5481 38.8895 L 0.5474 38.8883 L 10.3347 37.82 C 10.9567 35.8047 11.133 35.2336 11.5724 33.8112 L 11.9776 32.499 L 4.5299 25.9906 L 5.4613 24.315 C 6.0971 23.171 6.2308 22.9304 7.4163 20.7983 L 16.7872 23.8471 L 20.4293 19.6622 L 16.2442 10.654 C 17.0877 10.0194 17.606 9.6295 18.0078 9.3272 C 18.8578 8.6878 19.1869 8.4402 20.9704 7.098 L 28.3046 13.7371 C 29.4985 13.183 30.1542 12.8786 30.8098 12.5743 C 31.4654 12.27 32.121 11.9657 33.3149 11.4116 L 33.1155 1.4607 C 34.7134 1.098 35.3517 0.9532 35.99 0.8082 C 36.6282 0.6633 37.2665 0.5183 38.8638 0.1553 L 42.8927 9.2364 C 44.221 9.2211 44.9438 9.2126 45.673 9.2041 C 46.3866 9.1958 47.1065 9.1875 48.4059 9.1724 L 52.2273 0 C 54.6634 0.4935 54.88 0.5374 56.2444 0.8141 C 56.6785 0.9022 57.2288 1.0138 58.0038 1.171 L 58.0314 11.1238 C 59.2379 11.65 59.9002 11.9389 60.5626 12.2279 C 61.2249 12.5168 61.8873 12.8057 63.0937 13.3319 L 70.2749 6.5243 C 71.6105 7.4815 72.1442 7.864 72.6778 8.2465 C 73.2114 8.6291 73.7451 9.0117 75.0808 9.9693 L 71.102 19.0726 C 71.9925 20.0496 72.4813 20.5859 72.9701 21.1223 C 73.4589 21.6586 73.9477 22.195 74.8381 23.1719 L 84.1369 19.9057 C 84.9723 21.3304 85.3058 21.8992 85.6396 22.4686 C 85.973 23.0372 86.3067 23.6064 87.1412 25.0295 L 79.844 31.7097 C 80.2644 32.9682 80.4952 33.6592 80.726 34.3502 C 80.9567 35.0413 81.1875 35.7323 81.6079 36.9908 L 91.4172 37.8308 C 91.6072 39.475 91.683 40.132 91.7589 40.7889 C 91.8348 41.4459 91.9107 42.1029 92.1007 43.7471 L 82.7467 46.8491 C 82.6244 48.1716 82.5573 48.8978 82.4902 49.6241 C 82.4231 50.3503 82.356 51.0765 82.2338 52.399 L 90.8572 57.2001 C 90.3696 58.7796 90.1745 59.411 89.9796 60.042 C 89.7845 60.6736 89.5895 61.3047 89.1014 62.8856 L 79.3085 61.8732 C 78.664 63.0325 78.3104 63.6684 77.9565 64.3048 C 77.6034 64.9398 77.25 65.5754 76.6071 66.732 L 82.5535 74.6637 L 81.3434 76.0542 C 80.4459 77.0854 80.2879 77.2669 78.662 79.1358 L 70.1225 74.1838 C 69.5439 74.6192 69.1086 74.9469 68.7378 75.226 C 67.758 75.9634 67.2293 76.3614 65.6998 77.5122 L 67.941 87.2038 L 62.5871 89.689 L 56.7785 81.6533 C 55.4965 81.9443 54.7926 82.1042 54.0887 82.2641 C 53.3848 82.4241 52.6809 82.584 51.3989 82.875 L 49.5472 92.6503 L 43.6565 92.7188 L 41.584 82.9885 C 40.2957 82.7273 39.5884 82.5838 38.8811 82.4404 C 38.1737 82.297 37.4664 82.1535 36.1781 81.8923 L 30.5539 90.0611 L 25.1448 87.7015 L 27.1646 77.9604 C 26.0929 77.192 25.5044 76.7701 24.9159 76.3482 C 24.3274 75.9263 23.7389 75.5044 22.6673 74.736 L 14.2432 79.8858 C 13.1336 78.6685 12.6903 78.1821 12.2469 77.6957 C 11.8036 77.2093 11.3603 76.7229 10.2507 75.5056 L 16.0147 67.4376 C 15.3438 66.2933 14.9757 65.6658 14.6073 65.0376 C 14.2399 64.4112 13.872 63.784 13.2029 62.6428 L 3.4357 63.8835 C 2.7937 61.962 2.6242 61.4543 2.3481 60.6278 C 2.1736 60.1053 1.9566 59.4555 1.5507 58.2405 L 10.0619 53.2396 C 9.9096 51.9203 9.8258 51.1959 9.7421 50.4714 C 9.6584 49.7469 9.5747 49.0224 9.4223 47.7031 L 0 44.8198 C 0.1524 43.1717 0.2132 42.5132 0.274 41.8546 Z M 64.1419 46.3593 C 64.1419 36.267 56.0416 28.0853 46.0498 28.0853 C 36.0579 28.0853 27.9576 36.267 27.9576 46.3593 C 27.9576 56.4516 36.0579 64.6334 46.0498 64.6334 C 56.0416 64.6334 64.1419 56.4516 64.1419 46.3593 Z"/>
                        </g>
                    </g>
                    <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:184.965;y:70.417;w:66.758;h:67.994" transform="translate(184.96484375, 70.41650390625)">
                        <g id="cu_gear-4" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:-0.000;w:66.758;h:67.994">
                            <path id="gear-4" transform="translate(0.00003162955181323923, -0.000029838036425644532)" fill="#feecf7" d="M 66.7575 25.8606 L 57.8356 31.9087 L 57.8315 36.1331 L 66.7414 42.1987 C 66.2163 43.7952 66.063 44.2615 65.7923 45.0847 C 65.6515 45.5129 65.4789 46.0377 65.2058 46.8683 L 54.402 46.5616 C 54.0319 47.0654 53.7455 47.4555 53.4847 47.8106 C 52.9929 48.4802 52.5923 49.0256 51.8946 49.9762 L 55.5071 60.0768 C 54.4648 60.8261 53.9834 61.1722 53.502 61.5184 C 53.0206 61.8645 52.5392 62.2106 51.4969 62.9599 L 42.9384 56.415 C 41.6472 56.8297 40.986 57.042 40.0117 57.3548 L 38.8859 57.7162 L 35.8216 67.9938 L 30.8687 67.9889 L 27.8247 57.7053 C 26.5379 57.2892 25.8772 57.0757 24.908 56.7624 C 24.5783 56.6559 24.2128 56.5377 23.7748 56.3962 L 15.2034 62.9243 C 14.1626 62.173 13.6819 61.8259 13.2011 61.4788 C 12.7204 61.1317 12.2397 60.7847 11.1989 60.0334 L 14.8314 49.9398 C 14.4662 49.4404 14.1826 49.0527 13.9247 48.7 C 13.4305 48.0244 13.0304 47.4774 12.3308 46.5203 L 1.5263 46.8059 C 0.9864 45.1532 0.8419 44.7109 0.5546 43.8313 C 0.4198 43.4184 0.2535 42.9092 0 42.1332 L 8.9219 36.0851 L 8.9261 31.8607 L 0.0162 25.7951 C 0.558 24.1477 0.704 23.7037 0.9915 22.8292 C 1.1276 22.4154 1.2953 21.9051 1.5517 21.1254 L 12.3556 21.4322 C 12.7227 20.9323 13.0075 20.5445 13.2667 20.1917 C 13.7613 19.5182 14.1623 18.9722 14.863 18.0176 L 11.2504 7.9169 C 12.2927 7.1677 12.7741 6.8216 13.2555 6.4754 C 13.7369 6.1293 14.2183 5.7832 15.2607 5.0339 L 23.8191 11.5788 C 25.096 11.1687 25.7568 10.9565 26.7134 10.6494 L 27.8716 10.2776 L 30.9359 0 L 35.8889 0.0049 L 38.9328 10.2884 C 40.6574 10.8461 41.2574 11.0399 42.9827 11.5976 L 51.5541 5.0695 C 52.5949 5.8208 53.0757 6.1679 53.5564 6.515 C 54.0371 6.862 54.5178 7.2091 55.5587 7.9604 L 51.9261 18.054 C 52.296 18.5598 52.5822 18.951 52.8428 19.3073 C 53.3324 19.9767 53.7318 20.5227 54.4268 21.4735 L 65.2312 21.1879 C 65.753 22.785 65.9055 23.2518 66.1744 24.0752 C 66.3144 24.5038 66.4859 25.029 66.7575 25.8606 Z M 15.1765 33.9792 C 15.1666 43.9921 23.3081 52.1175 33.3607 52.1274 C 43.4134 52.1373 51.5709 44.0278 51.5808 34.0149 C 51.5908 24.0019 43.4492 15.8765 33.3966 15.8666 C 23.344 15.8568 15.1864 23.9662 15.1765 33.9792 Z"/>
                            <path id="gear-4_1" transform="translate(0.00003162955181323923, -0.000029838036425644532)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 66.7575 25.8606 L 57.8356 31.9087 L 57.8315 36.1331 L 66.7414 42.1987 C 66.2163 43.7952 66.063 44.2615 65.7923 45.0847 C 65.6515 45.5129 65.4789 46.0377 65.2058 46.8683 L 54.402 46.5616 C 54.0319 47.0654 53.7455 47.4555 53.4847 47.8106 C 52.9929 48.4802 52.5923 49.0256 51.8946 49.9762 L 55.5071 60.0768 C 54.4648 60.8261 53.9834 61.1722 53.502 61.5184 C 53.0206 61.8645 52.5392 62.2106 51.4969 62.9599 L 42.9384 56.415 C 41.6472 56.8297 40.986 57.042 40.0117 57.3548 L 38.8859 57.7162 L 35.8216 67.9938 L 30.8687 67.9889 L 27.8247 57.7053 C 26.5379 57.2892 25.8772 57.0757 24.908 56.7624 C 24.5783 56.6559 24.2128 56.5377 23.7748 56.3962 L 15.2034 62.9243 C 14.1626 62.173 13.6819 61.8259 13.2011 61.4788 C 12.7204 61.1317 12.2397 60.7847 11.1989 60.0334 L 14.8314 49.9398 C 14.4662 49.4404 14.1826 49.0527 13.9247 48.7 C 13.4305 48.0244 13.0304 47.4774 12.3308 46.5203 L 1.5263 46.8059 C 0.9864 45.1532 0.8419 44.7109 0.5546 43.8313 C 0.4198 43.4184 0.2535 42.9092 0 42.1332 L 8.9219 36.0851 L 8.9261 31.8607 L 0.0162 25.7951 C 0.558 24.1477 0.704 23.7037 0.9915 22.8292 C 1.1276 22.4154 1.2953 21.9051 1.5517 21.1254 L 12.3556 21.4322 C 12.7227 20.9323 13.0075 20.5445 13.2667 20.1917 C 13.7613 19.5182 14.1623 18.9722 14.863 18.0176 L 11.2504 7.9169 C 12.2927 7.1677 12.7741 6.8216 13.2555 6.4754 C 13.7369 6.1293 14.2183 5.7832 15.2607 5.0339 L 23.8191 11.5788 C 25.096 11.1687 25.7568 10.9565 26.7134 10.6494 L 27.8716 10.2776 L 30.9359 0 L 35.8889 0.0049 L 38.9328 10.2884 C 40.6574 10.8461 41.2574 11.0399 42.9827 11.5976 L 51.5541 5.0695 C 52.5949 5.8208 53.0757 6.1679 53.5564 6.515 C 54.0371 6.862 54.5178 7.2091 55.5587 7.9604 L 51.9261 18.054 C 52.296 18.5598 52.5822 18.951 52.8428 19.3073 C 53.3324 19.9767 53.7318 20.5227 54.4268 21.4735 L 65.2312 21.1879 C 65.753 22.785 65.9055 23.2518 66.1744 24.0752 C 66.3144 24.5038 66.4859 25.029 66.7575 25.8606 Z M 15.1765 33.9792 C 15.1666 43.9921 23.3081 52.1175 33.3607 52.1274 C 43.4134 52.1373 51.5709 44.0278 51.5808 34.0149 C 51.5908 24.0019 43.4492 15.8765 33.3966 15.8666 C 23.344 15.8568 15.1864 23.9662 15.1765 33.9792 Z"/>
                        </g>
                    </g>
                    <g id="g-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:239.979;y:29.002;w:79.120;h:78.502" transform="translate(239.978515625, 29.00244140625)">
                        <g id="cu_gear-5" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0.000;w:79.120;h:78.502">
                            <path id="gear-5" transform="translate(0, 0.00048828125)" fill="#faf0ff" d="M 40.1938 78.4995 C 39.1925 78.4983 38.6877 78.4977 36.7259 78.4954 L 36.7278 78.4951 L 33.722 67.3033 C 32.694 66.9995 32.0345 66.8045 31.3746 66.6094 C 30.716 66.4147 30.057 66.2199 29.0311 65.9166 L 20.4413 73.6805 C 19.6511 73.17 19.1421 72.8412 18.7337 72.5773 C 17.8708 72.0198 17.4569 71.7524 15.7871 70.6741 L 19.2959 59.6283 L 18.5336 58.7439 C 17.6872 57.7619 17.2072 57.205 16.0986 55.9196 L 4.6856 57.7981 C 4.0794 56.4631 3.8092 55.8677 3.5389 55.2721 C 3.2686 54.6767 2.9984 54.0813 2.3924 52.7466 L 11.3024 45.3537 C 11.1516 44.2912 11.0547 43.6092 10.9578 42.9272 C 10.8609 42.2452 10.7641 41.5631 10.6133 40.5007 L 0 35.897 C 0.2106 34.4454 0.3044 33.798 0.3982 33.1506 C 0.4919 32.5032 0.5857 31.8557 0.7963 30.4041 L 12.2787 29.0115 C 12.861 27.739 13.1712 27.0607 13.5643 26.2012 C 13.7738 25.7432 14.0068 25.2336 14.3171 24.5552 L 7.8739 14.9315 C 9.4205 13.147 9.5868 12.9553 10.5966 11.7906 C 10.8404 11.5094 11.1333 11.1716 11.5067 10.7409 L 21.9158 15.7908 L 26.0344 13.1456 L 25.8065 1.5571 C 26.3587 1.3954 26.7906 1.2689 27.1496 1.1638 C 28.6169 0.7342 28.8649 0.6616 31.1225 0 L 37.1528 9.8886 L 42.0441 9.8946 L 48.1041 0.0205 C 49.5075 0.4351 50.1336 0.6201 50.7596 0.8051 C 51.3858 0.9902 52.0119 1.1753 53.4154 1.5903 L 53.1532 13.1783 C 54.052 13.759 54.6294 14.1319 55.2065 14.5045 C 55.785 14.8781 56.3631 15.2514 57.2642 15.8335 L 67.6882 10.8089 L 71.3086 15.0078 L 64.8368 24.6165 C 65.3449 25.736 65.6446 26.3961 65.9748 27.1232 C 66.2208 27.6651 66.4837 28.2441 66.8622 29.078 L 78.3402 30.4981 L 79.12 35.9926 L 68.4934 40.5705 C 68.3576 41.5078 68.2646 42.1489 68.1766 42.7553 C 68.0652 43.5229 67.9619 44.2349 67.7901 45.4216 L 76.6779 52.8357 C 76.068 54.1693 75.7959 54.764 75.5237 55.3588 C 75.2516 55.9536 74.9795 56.5483 74.3696 57.8819 L 62.9619 55.9761 L 59.7532 59.6773 L 63.2291 70.7315 L 62.1495 71.4246 C 60.7731 72.3084 60.5957 72.4223 58.5657 73.7263 L 49.9991 65.9414 C 47.9681 66.5368 47.3736 66.7109 45.3781 67.2953 L 45.3037 67.317 L 42.264 78.5019 L 40.1938 78.4995 Z M 39.9181 20.6709 C 29.8772 20.6709 21.7372 28.8363 21.7372 38.9084 C 21.7372 48.9806 29.8772 57.1459 39.9181 57.1459 C 49.959 57.1459 58.0991 48.9806 58.0991 38.9084 C 58.0991 28.8363 49.959 20.6709 39.9181 20.6709 Z"/>
                            <path id="gear-5_1" transform="translate(0, 0.00048828125)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 40.1938 78.4995 C 39.1925 78.4983 38.6877 78.4977 36.7259 78.4954 L 36.7278 78.4951 L 33.722 67.3033 C 32.694 66.9995 32.0345 66.8045 31.3746 66.6094 C 30.716 66.4147 30.057 66.2199 29.0311 65.9166 L 20.4413 73.6805 C 19.6511 73.17 19.1421 72.8412 18.7337 72.5773 C 17.8708 72.0198 17.4569 71.7524 15.7871 70.6741 L 19.2959 59.6283 L 18.5336 58.7439 C 17.6872 57.7619 17.2072 57.205 16.0986 55.9196 L 4.6856 57.7981 C 4.0794 56.4631 3.8092 55.8677 3.5389 55.2721 C 3.2686 54.6767 2.9984 54.0813 2.3924 52.7466 L 11.3024 45.3537 C 11.1516 44.2912 11.0547 43.6092 10.9578 42.9272 C 10.8609 42.2452 10.7641 41.5631 10.6133 40.5007 L 0 35.897 C 0.2106 34.4454 0.3044 33.798 0.3982 33.1506 C 0.4919 32.5032 0.5857 31.8557 0.7963 30.4041 L 12.2787 29.0115 C 12.861 27.739 13.1712 27.0607 13.5643 26.2012 C 13.7738 25.7432 14.0068 25.2336 14.3171 24.5552 L 7.8739 14.9315 C 9.4205 13.147 9.5868 12.9553 10.5966 11.7906 C 10.8404 11.5094 11.1333 11.1716 11.5067 10.7409 L 21.9158 15.7908 L 26.0344 13.1456 L 25.8065 1.5571 C 26.3587 1.3954 26.7906 1.2689 27.1496 1.1638 C 28.6169 0.7342 28.8649 0.6616 31.1225 0 L 37.1528 9.8886 L 42.0441 9.8946 L 48.1041 0.0205 C 49.5075 0.4351 50.1336 0.6201 50.7596 0.8051 C 51.3858 0.9902 52.0119 1.1753 53.4154 1.5903 L 53.1532 13.1783 C 54.052 13.759 54.6294 14.1319 55.2065 14.5045 C 55.785 14.8781 56.3631 15.2514 57.2642 15.8335 L 67.6882 10.8089 L 71.3086 15.0078 L 64.8368 24.6165 C 65.3449 25.736 65.6446 26.3961 65.9748 27.1232 C 66.2208 27.6651 66.4837 28.2441 66.8622 29.078 L 78.3402 30.4981 L 79.12 35.9926 L 68.4934 40.5705 C 68.3576 41.5078 68.2646 42.1489 68.1766 42.7553 C 68.0652 43.5229 67.9619 44.2349 67.7901 45.4216 L 76.6779 52.8357 C 76.068 54.1693 75.7959 54.764 75.5237 55.3588 C 75.2516 55.9536 74.9795 56.5483 74.3696 57.8819 L 62.9619 55.9761 L 59.7532 59.6773 L 63.2291 70.7315 L 62.1495 71.4246 C 60.7731 72.3084 60.5957 72.4223 58.5657 73.7263 L 49.9991 65.9414 C 47.9681 66.5368 47.3736 66.7109 45.3781 67.2953 L 45.3037 67.317 L 42.264 78.5019 L 40.1938 78.4995 Z M 39.9181 20.6709 C 29.8772 20.6709 21.7372 28.8363 21.7372 38.9084 C 21.7372 48.9806 29.8772 57.1459 39.9181 57.1459 C 49.959 57.1459 58.0991 48.9806 58.0991 38.9084 C 58.0991 28.8363 49.959 20.6709 39.9181 20.6709 Z"/>
                        </g>
                    </g>
                    <g id="g-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:294.373;y:77.216;w:92.719;h:92.101" transform="translate(294.373046875, 77.21630859375)">
                        <g id="cu_gear-6" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:92.719;h:92.101">
                            <path id="gear-6" fill="#f3f0ff" d="M 0.3065 51.0263 C 0.3745 51.6805 0.4426 52.3347 0.613 53.9717 L 0.6124 53.973 L 10.4752 54.9331 C 11.1187 56.9198 11.3036 57.4904 11.7552 58.8836 L 12.184 60.2066 L 4.7546 66.7556 L 5.7163 68.4239 C 6.3622 69.5444 6.5026 69.788 7.7137 71.8881 L 17.1149 68.759 L 20.8242 72.8821 L 16.7047 81.8826 C 17.5652 82.5083 18.0921 82.8914 18.5007 83.1885 C 19.3577 83.8116 19.6946 84.0567 21.4989 85.369 L 28.8127 78.6914 C 30.0203 79.2299 30.6833 79.5256 31.3464 79.8213 C 32.0095 80.1171 32.6725 80.4128 33.8801 80.9513 L 33.7823 90.8476 C 35.3945 91.1915 36.0385 91.3289 36.6825 91.4663 C 37.3265 91.6038 37.9704 91.7414 39.582 92.0856 L 43.5436 83.0142 C 44.8811 83.0157 45.6088 83.0165 46.3431 83.0173 C 47.0613 83.0182 47.7859 83.019 49.0937 83.0204 L 53.0352 92.1007 C 55.4817 91.5847 55.6998 91.5387 57.0695 91.2495 C 57.5057 91.1574 58.0587 91.0406 58.8377 90.8762 L 58.7625 80.9798 C 59.9715 80.444 60.6352 80.1498 61.2989 79.8556 C 61.9626 79.5614 62.6263 79.2673 63.8353 78.7315 L 71.1343 85.4254 C 72.4688 84.4597 73.002 84.0739 73.5351 83.688 C 74.0684 83.3021 74.6015 82.9161 75.9362 81.9499 L 71.837 72.94 C 72.7256 71.9567 73.2123 71.4181 73.7001 70.8782 C 74.1853 70.3413 74.6715 69.8032 75.5553 68.8252 L 84.9492 71.9758 C 85.7749 70.5513 86.1048 69.9821 86.4347 69.4129 C 86.7646 68.8437 87.0945 68.2744 87.9203 66.8499 L 80.5059 60.2839 C 80.916 59.0285 81.1411 58.339 81.3662 57.6496 C 81.5914 56.96 81.8165 56.2704 82.2268 55.0145 L 92.092 54.077 C 92.2662 52.4402 92.3358 51.7862 92.4054 51.1322 C 92.475 50.4782 92.5445 49.8242 92.7188 48.1874 L 83.2711 45.2005 C 83.1343 43.8869 83.0592 43.1655 82.9842 42.4441 C 82.9091 41.7227 82.8341 41.0013 82.6973 39.6877 L 91.3279 34.824 C 90.8208 33.2587 90.6179 32.6329 90.4151 32.0075 C 90.2122 31.3816 90.0094 30.7561 89.5018 29.1892 L 79.6548 30.298 C 78.9956 29.1547 78.6333 28.5265 78.2713 27.8988 C 77.9085 27.2696 77.546 26.6411 76.8853 25.4951 L 82.7889 17.5466 L 81.5688 16.1905 C 80.6437 15.1622 80.4889 14.9902 78.8254 13.1406 L 70.2809 18.1534 C 69.7033 17.7332 69.2665 17.4154 68.8943 17.1446 C 67.8889 16.4132 67.3554 16.0251 65.7946 14.8901 L 67.9503 5.2304 L 62.5354 2.8151 L 56.7717 10.8656 C 55.4782 10.5896 54.768 10.4379 54.0578 10.2863 C 53.3477 10.1346 52.6375 9.9829 51.344 9.7069 L 49.3789 0.0067 L 43.4487 0 L 41.4632 9.6964 C 38.875 10.2427 38.6212 10.2964 36.033 10.8427 L 30.2872 2.7791 L 24.8669 5.1817 L 27.0008 14.8462 C 24.8592 16.3965 24.6487 16.5487 22.5072 18.0991 L 13.9743 13.0665 L 12.9643 14.1841 C 11.8385 15.4297 11.7887 15.485 10.0008 17.4633 L 15.8863 25.4252 C 15.2236 26.5684 14.8598 27.196 14.4959 27.8236 C 14.132 28.4511 13.7682 29.0787 13.1056 30.2219 L 3.2611 29.0901 C 2.6384 30.9964 2.4713 31.5081 2.2047 32.3244 C 2.0337 32.8482 1.8217 33.4973 1.4221 34.7207 L 10.0411 39.6042 C 9.9014 40.9176 9.8246 41.6388 9.7479 42.3601 C 9.6711 43.0813 9.5943 43.8025 9.4546 45.1159 L 0 48.081 C 0.1704 49.718 0.2385 50.3722 0.3065 51.0263 Z M 64.5301 46.0895 C 64.5301 56.1248 56.376 64.2603 46.3177 64.2603 C 36.2595 64.2603 28.1053 56.1248 28.1053 46.0895 C 28.1053 36.0542 36.2595 27.9186 46.3177 27.9186 C 56.376 27.9186 64.5301 36.0542 64.5301 46.0895 Z"/>
                            <path id="gear-6_1" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.3065 51.0263 C 0.3745 51.6805 0.4426 52.3347 0.613 53.9717 L 0.6124 53.973 L 10.4752 54.9331 C 11.1187 56.9198 11.3036 57.4904 11.7552 58.8836 L 12.184 60.2066 L 4.7546 66.7556 L 5.7163 68.4239 C 6.3622 69.5444 6.5026 69.788 7.7137 71.8881 L 17.1149 68.759 L 20.8242 72.8821 L 16.7047 81.8826 C 17.5652 82.5083 18.0921 82.8914 18.5007 83.1885 C 19.3577 83.8116 19.6946 84.0567 21.4989 85.369 L 28.8127 78.6914 C 30.0203 79.2299 30.6833 79.5256 31.3464 79.8213 C 32.0095 80.1171 32.6725 80.4128 33.8801 80.9513 L 33.7823 90.8476 C 35.3945 91.1915 36.0385 91.3289 36.6825 91.4663 C 37.3265 91.6038 37.9704 91.7414 39.582 92.0856 L 43.5436 83.0142 C 44.8811 83.0157 45.6088 83.0165 46.3431 83.0173 C 47.0613 83.0182 47.7859 83.019 49.0937 83.0204 L 53.0352 92.1007 C 55.4817 91.5847 55.6998 91.5387 57.0695 91.2495 C 57.5057 91.1574 58.0587 91.0406 58.8377 90.8762 L 58.7625 80.9798 C 59.9715 80.444 60.6352 80.1498 61.2989 79.8556 C 61.9626 79.5614 62.6263 79.2673 63.8353 78.7315 L 71.1343 85.4254 C 72.4688 84.4597 73.002 84.0739 73.5351 83.688 C 74.0684 83.3021 74.6015 82.9161 75.9362 81.9499 L 71.837 72.94 C 72.7256 71.9567 73.2123 71.4181 73.7001 70.8782 C 74.1853 70.3413 74.6715 69.8032 75.5553 68.8252 L 84.9492 71.9758 C 85.7749 70.5513 86.1048 69.9821 86.4347 69.4129 C 86.7646 68.8437 87.0945 68.2744 87.9203 66.8499 L 80.5059 60.2839 C 80.916 59.0285 81.1411 58.339 81.3662 57.6496 C 81.5914 56.96 81.8165 56.2704 82.2268 55.0145 L 92.092 54.077 C 92.2662 52.4402 92.3358 51.7862 92.4054 51.1322 C 92.475 50.4782 92.5445 49.8242 92.7188 48.1874 L 83.2711 45.2005 C 83.1343 43.8869 83.0592 43.1655 82.9842 42.4441 C 82.9091 41.7227 82.8341 41.0013 82.6973 39.6877 L 91.3279 34.824 C 90.8208 33.2587 90.6179 32.6329 90.4151 32.0075 C 90.2122 31.3816 90.0094 30.7561 89.5018 29.1892 L 79.6548 30.298 C 78.9956 29.1547 78.6333 28.5265 78.2713 27.8988 C 77.9085 27.2696 77.546 26.6411 76.8853 25.4951 L 82.7889 17.5466 L 81.5688 16.1905 C 80.6437 15.1622 80.4889 14.9902 78.8254 13.1406 L 70.2809 18.1534 C 69.7033 17.7332 69.2665 17.4154 68.8943 17.1446 C 67.8889 16.4132 67.3554 16.0251 65.7946 14.8901 L 67.9503 5.2304 L 62.5354 2.8151 L 56.7717 10.8656 C 55.4782 10.5896 54.768 10.4379 54.0578 10.2863 C 53.3477 10.1346 52.6375 9.9829 51.344 9.7069 L 49.3789 0.0067 L 43.4487 0 L 41.4632 9.6964 C 38.875 10.2427 38.6212 10.2964 36.033 10.8427 L 30.2872 2.7791 L 24.8669 5.1817 L 27.0008 14.8462 C 24.8592 16.3965 24.6487 16.5487 22.5072 18.0991 L 13.9743 13.0665 L 12.9643 14.1841 C 11.8385 15.4297 11.7887 15.485 10.0008 17.4633 L 15.8863 25.4252 C 15.2236 26.5684 14.8598 27.196 14.4959 27.8236 C 14.132 28.4511 13.7682 29.0787 13.1056 30.2219 L 3.2611 29.0901 C 2.6384 30.9964 2.4713 31.5081 2.2047 32.3244 C 2.0337 32.8482 1.8217 33.4973 1.4221 34.7207 L 10.0411 39.6042 C 9.9014 40.9176 9.8246 41.6388 9.7479 42.3601 C 9.6711 43.0813 9.5943 43.8025 9.4546 45.1159 L 0 48.081 C 0.1704 49.718 0.2385 50.3722 0.3065 51.0263 Z M 64.5301 46.0895 C 64.5301 56.1248 56.376 64.2603 46.3177 64.2603 C 36.2595 64.2603 28.1053 56.1248 28.1053 46.0895 C 28.1053 36.0542 36.2595 27.9186 46.3177 27.9186 C 56.376 27.9186 64.5301 36.0542 64.5301 46.0895 Z"/>
                        </g>
                    </g>
                    <g id="g-7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:367.312;y:38.892;w:66.758;h:67.994" transform="translate(367.3125, 38.89208984375)">
                        <g id="cu_gear-7" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:66.758;h:67.994">
                            <path id="gear-7" fill="#edf4ff" d="M 66.7575 25.8272 L 57.8395 31.8845 L 57.8395 36.1092 L 66.7575 42.1665 C 66.2339 43.7637 66.081 44.2301 65.8111 45.0537 C 65.6707 45.4821 65.4986 46.0071 65.2262 46.838 L 54.4195 46.5418 C 54.0498 47.0461 53.7637 47.4364 53.5032 47.7918 C 53.0119 48.4619 52.6118 49.0078 51.9148 49.9591 L 55.5383 60.057 C 54.4964 60.8073 54.0153 61.1539 53.5341 61.5006 C 53.0529 61.8472 52.5717 62.1938 51.5299 62.9442 L 42.9629 56.4072 C 41.6718 56.8232 41.0106 57.0361 40.0364 57.3499 L 38.9107 57.7125 L 35.8558 67.9938 L 30.9017 67.9938 L 27.8468 57.7125 C 26.5593 57.2976 25.8982 57.0847 24.9285 56.7724 C 24.5985 56.6661 24.2329 56.5483 23.7947 56.4072 L 15.2276 62.9442 C 14.1858 62.1938 13.7046 61.8472 13.2235 61.5006 C 12.7423 61.1539 12.2611 60.8073 11.2193 60.057 L 14.8427 49.9591 C 14.4769 49.46 14.1929 49.0726 13.9345 48.7202 C 13.4396 48.045 13.0388 47.4984 12.3381 46.5418 L 1.5313 46.838 C 0.9896 45.1858 0.8447 44.7436 0.5564 43.8642 C 0.4212 43.4515 0.2543 42.9424 0 42.1665 L 8.918 36.1092 L 8.918 31.8845 L 0 25.8272 C 0.5403 24.1792 0.6859 23.7351 0.9727 22.8602 C 1.1084 22.4462 1.2756 21.9358 1.5313 21.1558 L 12.3381 21.452 C 12.7048 20.9517 12.9893 20.5635 13.2482 20.2104 C 13.7422 19.5364 14.1428 18.99 14.8427 18.0347 L 11.2193 7.9368 C 12.2611 7.1865 12.7423 6.8399 13.2235 6.4932 C 13.7046 6.1466 14.1858 5.8 15.2276 5.0496 L 23.7947 11.5866 C 25.0714 11.1752 25.7321 10.9624 26.6887 10.6543 L 27.8468 10.2813 L 30.9017 0 L 35.8558 0 L 38.9107 10.2813 C 40.6362 10.8373 41.2366 11.0306 42.9629 11.5866 L 51.5299 5.0496 C 52.5717 5.8 53.0529 6.1466 53.5341 6.4932 C 54.0153 6.8399 54.4964 7.1865 55.5383 7.9368 L 51.9148 18.0347 C 52.2853 18.5401 52.5719 18.9311 52.8329 19.2872 C 53.3234 19.9561 53.7234 20.5018 54.4195 21.452 L 65.2262 21.1558 C 65.7497 22.7525 65.9027 23.2191 66.1725 24.0423 C 66.3129 24.4708 66.485 24.9959 66.7575 25.8272 Z M 15.1722 33.9969 C 15.1722 44.0106 23.3237 52.1286 33.3788 52.1286 C 43.4338 52.1286 51.5854 44.0106 51.5854 33.9969 C 51.5854 23.9832 43.4338 15.8652 33.3788 15.8652 C 23.3237 15.8652 15.1722 23.9832 15.1722 33.9969 Z"/>
                            <path id="gear-7_1" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 66.7575 25.8272 L 57.8395 31.8845 L 57.8395 36.1092 L 66.7575 42.1665 C 66.2339 43.7637 66.081 44.2301 65.8111 45.0537 C 65.6707 45.4821 65.4986 46.0071 65.2262 46.838 L 54.4195 46.5418 C 54.0498 47.0461 53.7637 47.4364 53.5032 47.7918 C 53.0119 48.4619 52.6118 49.0078 51.9148 49.9591 L 55.5383 60.057 C 54.4964 60.8073 54.0153 61.1539 53.5341 61.5006 C 53.0529 61.8472 52.5717 62.1938 51.5299 62.9442 L 42.9629 56.4072 C 41.6718 56.8232 41.0106 57.0361 40.0364 57.3499 L 38.9107 57.7125 L 35.8558 67.9938 L 30.9017 67.9938 L 27.8468 57.7125 C 26.5593 57.2976 25.8982 57.0847 24.9285 56.7724 C 24.5985 56.6661 24.2329 56.5483 23.7947 56.4072 L 15.2276 62.9442 C 14.1858 62.1938 13.7046 61.8472 13.2235 61.5006 C 12.7423 61.1539 12.2611 60.8073 11.2193 60.057 L 14.8427 49.9591 C 14.4769 49.46 14.1929 49.0726 13.9345 48.7202 C 13.4396 48.045 13.0388 47.4984 12.3381 46.5418 L 1.5313 46.838 C 0.9896 45.1858 0.8447 44.7436 0.5564 43.8642 C 0.4212 43.4515 0.2543 42.9424 0 42.1665 L 8.918 36.1092 L 8.918 31.8845 L 0 25.8272 C 0.5403 24.1792 0.6859 23.7351 0.9727 22.8602 C 1.1084 22.4462 1.2756 21.9358 1.5313 21.1558 L 12.3381 21.452 C 12.7048 20.9517 12.9893 20.5635 13.2482 20.2104 C 13.7422 19.5364 14.1428 18.99 14.8427 18.0347 L 11.2193 7.9368 C 12.2611 7.1865 12.7423 6.8399 13.2235 6.4932 C 13.7046 6.1466 14.1858 5.8 15.2276 5.0496 L 23.7947 11.5866 C 25.0714 11.1752 25.7321 10.9624 26.6887 10.6543 L 27.8468 10.2813 L 30.9017 0 L 35.8558 0 L 38.9107 10.2813 C 40.6362 10.8373 41.2366 11.0306 42.9629 11.5866 L 51.5299 5.0496 C 52.5717 5.8 53.0529 6.1466 53.5341 6.4932 C 54.0153 6.8399 54.4964 7.1865 55.5383 7.9368 L 51.9148 18.0347 C 52.2853 18.5401 52.5719 18.9311 52.8329 19.2872 C 53.3234 19.9561 53.7234 20.5018 54.4195 21.452 L 65.2262 21.1558 C 65.7497 22.7525 65.9027 23.2191 66.1725 24.0423 C 66.3129 24.4708 66.485 24.9959 66.7575 25.8272 Z M 15.1722 33.9969 C 15.1722 44.0106 23.3237 52.1286 33.3788 52.1286 C 43.4338 52.1286 51.5854 44.0106 51.5854 33.9969 C 51.5854 23.9832 43.4338 15.8652 33.3788 15.8652 C 23.3237 15.8652 15.1722 23.9832 15.1722 33.9969 Z"/>
                        </g>
                    </g>
                    <g id="g-8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:427.271;y:60.527;w:79.120;h:78.502" transform="translate(427.271484375, 60.52685546875)">
                        <g id="cu_gear-8" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:79.120;h:78.502">
                            <path id="gear-8" fill="#e8f9ff" d="M 40.1938 78.4995 C 39.1925 78.4983 38.6877 78.4977 36.7259 78.4954 L 36.7278 78.4951 L 33.722 67.3033 C 32.694 66.9995 32.0345 66.8045 31.3746 66.6094 C 30.716 66.4147 30.057 66.2199 29.0311 65.9166 L 20.4413 73.6805 C 19.6511 73.17 19.1421 72.8412 18.7337 72.5773 C 17.8708 72.0198 17.4569 71.7524 15.7871 70.6741 L 19.2959 59.6283 L 18.5336 58.7439 C 17.6872 57.7619 17.2072 57.205 16.0986 55.9196 L 4.6856 57.7981 C 4.0794 56.4631 3.8092 55.8677 3.5389 55.2721 C 3.2686 54.6767 2.9984 54.0813 2.3924 52.7466 L 11.3024 45.3537 C 11.1516 44.2912 11.0547 43.6092 10.9578 42.9272 C 10.8609 42.2452 10.7641 41.5631 10.6133 40.5007 L 0 35.897 C 0.2106 34.4454 0.3044 33.798 0.3982 33.1506 C 0.4919 32.5032 0.5857 31.8557 0.7963 30.4041 L 12.2787 29.0115 C 12.861 27.739 13.1712 27.0607 13.5643 26.2012 C 13.7738 25.7432 14.0068 25.2336 14.3171 24.5552 L 7.8739 14.9315 C 9.4205 13.147 9.5868 12.9553 10.5966 11.7906 C 10.8404 11.5094 11.1333 11.1716 11.5067 10.7409 L 21.9158 15.7908 L 26.0344 13.1456 L 25.8065 1.5571 C 26.3587 1.3954 26.7906 1.2689 27.1496 1.1638 C 28.6169 0.7342 28.8649 0.6616 31.1225 0 L 37.1528 9.8886 L 42.0441 9.8946 L 48.1041 0.0205 C 49.5075 0.4351 50.1336 0.6201 50.7596 0.8051 C 51.3858 0.9902 52.0119 1.1753 53.4154 1.5903 L 53.1532 13.1783 C 54.052 13.759 54.6294 14.1319 55.2065 14.5045 C 55.785 14.8781 56.3631 15.2514 57.2642 15.8335 L 67.6882 10.8089 L 71.3086 15.0078 L 64.8368 24.6165 C 65.3449 25.736 65.6446 26.3961 65.9748 27.1232 C 66.2208 27.6651 66.4837 28.2441 66.8622 29.078 L 78.3402 30.4981 L 79.12 35.9926 L 68.4934 40.5705 C 68.3576 41.5078 68.2646 42.1489 68.1766 42.7553 C 68.0652 43.5229 67.9619 44.2349 67.7901 45.4216 L 76.6779 52.8357 C 76.068 54.1693 75.7959 54.764 75.5237 55.3588 C 75.2516 55.9536 74.9795 56.5483 74.3696 57.8819 L 62.9619 55.9761 L 59.7532 59.6773 L 63.2291 70.7315 L 62.1495 71.4246 C 60.7731 72.3084 60.5957 72.4223 58.5657 73.7263 L 49.9991 65.9414 C 47.9681 66.5368 47.3736 66.7109 45.3781 67.2953 L 45.3037 67.317 L 42.264 78.5019 L 40.1938 78.4995 Z M 39.9181 20.6709 C 29.8772 20.6709 21.7372 28.8363 21.7372 38.9084 C 21.7372 48.9806 29.8772 57.1459 39.9181 57.1459 C 49.959 57.1459 58.0991 48.9806 58.0991 38.9084 C 58.0991 28.8363 49.959 20.6709 39.9181 20.6709 Z"/>
                            <path id="gear-8_1" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 40.1938 78.4995 C 39.1925 78.4983 38.6877 78.4977 36.7259 78.4954 L 36.7278 78.4951 L 33.722 67.3033 C 32.694 66.9995 32.0345 66.8045 31.3746 66.6094 C 30.716 66.4147 30.057 66.2199 29.0311 65.9166 L 20.4413 73.6805 C 19.6511 73.17 19.1421 72.8412 18.7337 72.5773 C 17.8708 72.0198 17.4569 71.7524 15.7871 70.6741 L 19.2959 59.6283 L 18.5336 58.7439 C 17.6872 57.7619 17.2072 57.205 16.0986 55.9196 L 4.6856 57.7981 C 4.0794 56.4631 3.8092 55.8677 3.5389 55.2721 C 3.2686 54.6767 2.9984 54.0813 2.3924 52.7466 L 11.3024 45.3537 C 11.1516 44.2912 11.0547 43.6092 10.9578 42.9272 C 10.8609 42.2452 10.7641 41.5631 10.6133 40.5007 L 0 35.897 C 0.2106 34.4454 0.3044 33.798 0.3982 33.1506 C 0.4919 32.5032 0.5857 31.8557 0.7963 30.4041 L 12.2787 29.0115 C 12.861 27.739 13.1712 27.0607 13.5643 26.2012 C 13.7738 25.7432 14.0068 25.2336 14.3171 24.5552 L 7.8739 14.9315 C 9.4205 13.147 9.5868 12.9553 10.5966 11.7906 C 10.8404 11.5094 11.1333 11.1716 11.5067 10.7409 L 21.9158 15.7908 L 26.0344 13.1456 L 25.8065 1.5571 C 26.3587 1.3954 26.7906 1.2689 27.1496 1.1638 C 28.6169 0.7342 28.8649 0.6616 31.1225 0 L 37.1528 9.8886 L 42.0441 9.8946 L 48.1041 0.0205 C 49.5075 0.4351 50.1336 0.6201 50.7596 0.8051 C 51.3858 0.9902 52.0119 1.1753 53.4154 1.5903 L 53.1532 13.1783 C 54.052 13.759 54.6294 14.1319 55.2065 14.5045 C 55.785 14.8781 56.3631 15.2514 57.2642 15.8335 L 67.6882 10.8089 L 71.3086 15.0078 L 64.8368 24.6165 C 65.3449 25.736 65.6446 26.3961 65.9748 27.1232 C 66.2208 27.6651 66.4837 28.2441 66.8622 29.078 L 78.3402 30.4981 L 79.12 35.9926 L 68.4934 40.5705 C 68.3576 41.5078 68.2646 42.1489 68.1766 42.7553 C 68.0652 43.5229 67.9619 44.2349 67.7901 45.4216 L 76.6779 52.8357 C 76.068 54.1693 75.7959 54.764 75.5237 55.3588 C 75.2516 55.9536 74.9795 56.5483 74.3696 57.8819 L 62.9619 55.9761 L 59.7532 59.6773 L 63.2291 70.7315 L 62.1495 71.4246 C 60.7731 72.3084 60.5957 72.4223 58.5657 73.7263 L 49.9991 65.9414 C 47.9681 66.5368 47.3736 66.7109 45.3781 67.2953 L 45.3037 67.317 L 42.264 78.5019 L 40.1938 78.4995 Z M 39.9181 20.6709 C 29.8772 20.6709 21.7372 28.8363 21.7372 38.9084 C 21.7372 48.9806 29.8772 57.1459 39.9181 57.1459 C 49.959 57.1459 58.0991 48.9806 58.0991 38.9084 C 58.0991 28.8363 49.959 20.6709 39.9181 20.6709 Z"/>
                        </g>
                    </g>
                    <g id="g-9" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:488.465;y:19.113;w:68.612;h:68.612" transform="translate(488.46484375, 19.11279296875)">
                        <g id="cu_gear-9" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:68.612;h:68.612">
                            <path id="gear-9" fill="#e7fbf2" d="M 67.6163 24.0969 L 58.9526 30.7174 L 59.21 34.962 L 68.6119 40.5133 C 68.1788 42.1494 68.0524 42.6272 67.8292 43.4709 C 67.7131 43.9097 67.5708 44.4475 67.3455 45.2987 L 56.3817 45.6489 C 56.038 46.1777 55.772 46.587 55.5298 46.9597 C 55.073 47.6624 54.701 48.2349 54.053 49.2324 L 58.3384 59.1608 C 57.3288 59.9771 56.8626 60.3542 56.3963 60.7313 C 55.9301 61.1084 55.4638 61.4856 54.4543 62.3019 L 45.3788 56.2476 C 44.0964 56.7429 43.4397 56.9965 42.4721 57.3702 L 41.354 57.8019 L 38.8863 68.3149 L 33.8684 68.6119 L 30.1478 58.4651 C 28.8185 58.1255 28.1359 57.9512 27.1347 57.6955 C 26.794 57.6085 26.4165 57.5121 25.964 57.3966 L 17.6851 64.478 C 16.5841 63.7865 16.0756 63.4671 15.5671 63.1477 C 15.0586 62.8283 14.5502 62.5088 13.4492 61.8174 L 16.504 51.4547 C 16.1031 50.9752 15.7918 50.6029 15.5086 50.2643 C 14.9662 49.6156 14.527 49.0904 13.7589 48.1714 L 2.8312 49.1168 C 2.1818 47.4892 2.0081 47.0536 1.6626 46.1874 C 1.5004 45.7808 1.3004 45.2793 0.9956 44.515 L 9.6593 37.8945 L 9.4018 33.6499 L 0 28.0985 C 0.4469 26.4103 0.5673 25.9554 0.8044 25.0592 C 0.9166 24.6351 1.055 24.1122 1.2664 23.3132 L 12.2302 22.963 C 12.5711 22.4384 12.8357 22.0314 13.0764 21.6611 C 13.5357 20.9543 13.9082 20.3812 14.5589 19.3794 L 10.2735 9.4511 C 11.283 8.6348 11.7493 8.2577 12.2156 7.8806 C 12.6818 7.5034 13.1481 7.1263 14.1576 6.31 L 23.2331 12.3643 C 24.5012 11.8745 25.1575 11.621 26.1076 11.2541 L 27.2579 10.81 L 29.7256 0.297 L 34.7435 0 L 38.4641 10.1468 C 40.2457 10.602 40.8656 10.7602 42.6479 11.2153 L 50.9268 4.1339 C 52.0278 4.8253 52.5363 5.1448 53.0448 5.4642 C 53.5533 5.7836 54.0617 6.103 55.1627 6.7945 L 52.1079 17.1572 C 52.5139 17.6429 52.8281 18.0185 53.1141 18.3606 C 53.6516 19.0033 54.0901 19.5276 54.853 20.4405 L 65.7807 19.4951 C 66.4083 21.068 66.5916 21.5277 66.915 22.3386 C 67.0834 22.7607 67.2897 23.2779 67.6163 24.0969 Z M 15.865 35.3974 C 16.4752 45.4584 25.2262 53.1261 35.4106 52.5234 C 45.595 51.9207 53.3568 43.2757 52.7467 33.2147 C 52.1365 23.1537 43.3855 15.486 33.2011 16.0888 C 23.0167 16.6915 15.2549 25.3365 15.865 35.3974 Z"/>
                            <path id="gear-9_1" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 67.6163 24.0969 L 58.9526 30.7174 L 59.21 34.962 L 68.6119 40.5133 C 68.1788 42.1494 68.0524 42.6272 67.8292 43.4709 C 67.7131 43.9097 67.5708 44.4475 67.3455 45.2987 L 56.3817 45.6489 C 56.038 46.1777 55.772 46.587 55.5298 46.9597 C 55.073 47.6624 54.701 48.2349 54.053 49.2324 L 58.3384 59.1608 C 57.3288 59.9771 56.8626 60.3542 56.3963 60.7313 C 55.9301 61.1084 55.4638 61.4856 54.4543 62.3019 L 45.3788 56.2476 C 44.0964 56.7429 43.4397 56.9965 42.4721 57.3702 L 41.354 57.8019 L 38.8863 68.3149 L 33.8684 68.6119 L 30.1478 58.4651 C 28.8185 58.1255 28.1359 57.9512 27.1347 57.6955 C 26.794 57.6085 26.4165 57.5121 25.964 57.3966 L 17.6851 64.478 C 16.5841 63.7865 16.0756 63.4671 15.5671 63.1477 C 15.0586 62.8283 14.5502 62.5088 13.4492 61.8174 L 16.504 51.4547 C 16.1031 50.9752 15.7918 50.6029 15.5086 50.2643 C 14.9662 49.6156 14.527 49.0904 13.7589 48.1714 L 2.8312 49.1168 C 2.1818 47.4892 2.0081 47.0536 1.6626 46.1874 C 1.5004 45.7808 1.3004 45.2793 0.9956 44.515 L 9.6593 37.8945 L 9.4018 33.6499 L 0 28.0985 C 0.4469 26.4103 0.5673 25.9554 0.8044 25.0592 C 0.9166 24.6351 1.055 24.1122 1.2664 23.3132 L 12.2302 22.963 C 12.5711 22.4384 12.8357 22.0314 13.0764 21.6611 C 13.5357 20.9543 13.9082 20.3812 14.5589 19.3794 L 10.2735 9.4511 C 11.283 8.6348 11.7493 8.2577 12.2156 7.8806 C 12.6818 7.5034 13.1481 7.1263 14.1576 6.31 L 23.2331 12.3643 C 24.5012 11.8745 25.1575 11.621 26.1076 11.2541 L 27.2579 10.81 L 29.7256 0.297 L 34.7435 0 L 38.4641 10.1468 C 40.2457 10.602 40.8656 10.7602 42.6479 11.2153 L 50.9268 4.1339 C 52.0278 4.8253 52.5363 5.1448 53.0448 5.4642 C 53.5533 5.7836 54.0617 6.103 55.1627 6.7945 L 52.1079 17.1572 C 52.5139 17.6429 52.8281 18.0185 53.1141 18.3606 C 53.6516 19.0033 54.0901 19.5276 54.853 20.4405 L 65.7807 19.4951 C 66.4083 21.068 66.5916 21.5277 66.915 22.3386 C 67.0834 22.7607 67.2897 23.2779 67.6163 24.0969 Z M 15.865 35.3974 C 16.4752 45.4584 25.2262 53.1261 35.4106 52.5234 C 45.595 51.9207 53.3568 43.2757 52.7467 33.2147 C 52.1365 23.1537 43.3855 15.486 33.2011 16.0888 C 23.0167 16.6915 15.2549 25.3365 15.865 35.3974 Z"/>
                        </g>
                    </g>
                    <g id="g-10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:534.205;y:64.236;w:79.738;h:77.884" transform="translate(534.205078125, 64.236328125)">
                        <g id="cu_gear-10" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:79.738;h:77.884">
                            <path id="gear-10" fill="#f2fae1" d="M 40.5553 0.0023 C 39.5142 0.0035 39.0208 0.0041 37.0128 0.0065 L 37.0147 0.0068 L 33.9855 11.1104 C 32.9504 11.4116 32.2861 11.6049 31.6217 11.7983 C 30.9573 11.9917 30.2929 12.185 29.2579 12.4862 L 20.601 4.7835 C 19.8018 5.2917 19.2881 5.6185 18.8758 5.8807 C 18.009 6.432 17.5905 6.6981 15.9104 7.7661 L 19.4467 18.725 L 18.674 19.6075 C 17.8237 20.5786 17.3397 21.1314 16.2244 22.4045 L 4.7222 20.5407 C 4.1101 21.8678 3.8379 22.4583 3.565 23.0503 C 3.2931 23.6398 3.0206 24.2309 2.4111 25.5525 L 11.3907 32.8872 C 11.2385 33.9429 11.1408 34.62 11.043 35.2977 C 10.9455 35.9733 10.8479 36.6495 10.6962 37.702 L 0 42.2694 C 0.2121 43.709 0.3067 44.3514 0.4012 44.9935 C 0.4957 45.636 0.5902 46.2783 0.8025 47.719 L 12.3746 49.1007 C 12.9622 50.3647 13.2748 51.0377 13.6717 51.8919 C 13.8824 52.3456 14.1169 52.8503 14.4289 53.5219 L 7.9354 63.0699 C 9.4933 64.8393 9.6615 65.0303 10.6777 66.1842 C 10.9237 66.4635 11.2194 66.7992 11.5966 67.2274 L 22.087 62.2173 L 26.2378 64.8417 L 26.0081 76.339 C 26.5658 76.4997 27.0018 76.6254 27.3641 76.7298 C 28.8408 77.1555 29.0916 77.2278 31.3656 77.8838 L 37.4431 68.073 L 42.3726 68.0671 L 48.4799 77.8634 C 49.8943 77.4521 50.5252 77.2686 51.1562 77.085 C 51.7872 76.9014 52.4182 76.7177 53.8327 76.306 L 53.5684 64.8092 C 54.4754 64.2324 55.0577 63.8622 55.64 63.4921 C 56.2223 63.1219 56.8046 62.7517 57.7116 62.1749 L 68.217 67.16 L 71.8657 62.9941 L 65.3433 53.4611 C 65.8543 52.3528 66.1562 51.6983 66.488 50.9787 C 66.7366 50.4399 67.002 49.8645 67.3846 49.0347 L 78.9522 47.6259 L 79.7382 42.1746 L 69.0285 37.6327 C 68.8923 36.7075 68.7988 36.0732 68.7106 35.4743 C 68.5978 34.7092 68.4936 34.0019 68.3197 32.8198 L 77.277 25.4641 C 76.6623 24.1411 76.388 23.551 76.1138 22.9609 C 75.8395 22.3708 75.5653 21.7807 74.9506 20.4576 L 63.4537 22.3484 L 60.22 18.6764 L 63.7231 7.7092 L 62.6228 7.0138 C 61.2468 6.1441 61.063 6.0279 59.0232 4.738 L 50.3897 12.4616 C 48.3428 11.8709 47.7437 11.6982 45.7325 11.1184 L 45.6576 11.0968 L 42.5942 0 L 40.5553 0.0023 Z M 39.8698 57.0358 C 29.7504 57.0358 21.5468 48.9347 21.5468 38.9419 C 21.5468 28.9491 29.7504 20.848 39.8698 20.848 C 49.9891 20.848 58.1927 28.9491 58.1927 38.9419 C 58.1927 48.9347 49.9891 57.0358 39.8698 57.0358 Z"/>
                            <path id="gear-10_1" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 40.5553 0.0023 C 39.5142 0.0035 39.0208 0.0041 37.0128 0.0065 L 37.0147 0.0068 L 33.9855 11.1104 C 32.9504 11.4116 32.2861 11.6049 31.6217 11.7983 C 30.9573 11.9917 30.2929 12.185 29.2579 12.4862 L 20.601 4.7835 C 19.8018 5.2917 19.2881 5.6185 18.8758 5.8807 C 18.009 6.432 17.5905 6.6981 15.9104 7.7661 L 19.4467 18.725 L 18.674 19.6075 C 17.8237 20.5786 17.3397 21.1314 16.2244 22.4045 L 4.7222 20.5407 C 4.1101 21.8678 3.8379 22.4583 3.565 23.0503 C 3.2931 23.6398 3.0206 24.2309 2.4111 25.5525 L 11.3907 32.8872 C 11.2385 33.9429 11.1408 34.62 11.043 35.2977 C 10.9455 35.9733 10.8479 36.6495 10.6962 37.702 L 0 42.2694 C 0.2121 43.709 0.3067 44.3514 0.4012 44.9935 C 0.4957 45.636 0.5902 46.2783 0.8025 47.719 L 12.3746 49.1007 C 12.9622 50.3647 13.2748 51.0377 13.6717 51.8919 C 13.8824 52.3456 14.1169 52.8503 14.4289 53.5219 L 7.9354 63.0699 C 9.4933 64.8393 9.6615 65.0303 10.6777 66.1842 C 10.9237 66.4635 11.2194 66.7992 11.5966 67.2274 L 22.087 62.2173 L 26.2378 64.8417 L 26.0081 76.339 C 26.5658 76.4997 27.0018 76.6254 27.3641 76.7298 C 28.8408 77.1555 29.0916 77.2278 31.3656 77.8838 L 37.4431 68.073 L 42.3726 68.0671 L 48.4799 77.8634 C 49.8943 77.4521 50.5252 77.2686 51.1562 77.085 C 51.7872 76.9014 52.4182 76.7177 53.8327 76.306 L 53.5684 64.8092 C 54.4754 64.2324 55.0577 63.8622 55.64 63.4921 C 56.2223 63.1219 56.8046 62.7517 57.7116 62.1749 L 68.217 67.16 L 71.8657 62.9941 L 65.3433 53.4611 C 65.8543 52.3528 66.1562 51.6983 66.488 50.9787 C 66.7366 50.4399 67.002 49.8645 67.3846 49.0347 L 78.9522 47.6259 L 79.7382 42.1746 L 69.0285 37.6327 C 68.8923 36.7075 68.7988 36.0732 68.7106 35.4743 C 68.5978 34.7092 68.4936 34.0019 68.3197 32.8198 L 77.277 25.4641 C 76.6623 24.1411 76.388 23.551 76.1138 22.9609 C 75.8395 22.3708 75.5653 21.7807 74.9506 20.4576 L 63.4537 22.3484 L 60.22 18.6764 L 63.7231 7.7092 L 62.6228 7.0138 C 61.2468 6.1441 61.063 6.0279 59.0232 4.738 L 50.3897 12.4616 C 48.3428 11.8709 47.7437 11.6982 45.7325 11.1184 L 45.6576 11.0968 L 42.5942 0 L 40.5553 0.0023 Z M 39.8698 57.0358 C 29.7504 57.0358 21.5468 48.9347 21.5468 38.9419 C 21.5468 28.9491 29.7504 20.848 39.8698 20.848 C 49.9891 20.848 58.1927 28.9491 58.1927 38.9419 C 58.1927 48.9347 49.9891 57.0358 39.8698 57.0358 Z"/>
                        </g>
                    </g>
                    <g id="g-11" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:589.371;y:1.659;w:92.101;h:92.719" transform="translate(589.37109375, 1.6591796875)">
                        <g id="cu_gear-11" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:92.101;h:92.719">
                            <path id="gear-11" fill="#fefbdb" d="M 0.274 41.8546 C 0.3349 41.1961 0.3957 40.5376 0.5481 38.8895 L 0.5474 38.8883 L 10.3347 37.82 C 10.9567 35.8047 11.133 35.2336 11.5724 33.8112 L 11.9776 32.499 L 4.5299 25.9906 L 5.4613 24.315 C 6.0971 23.171 6.2308 22.9304 7.4163 20.7983 L 16.7872 23.8471 L 20.4293 19.6622 L 16.2442 10.654 C 17.0877 10.0194 17.606 9.6295 18.0078 9.3272 C 18.8578 8.6878 19.1869 8.4402 20.9704 7.098 L 28.3046 13.7371 C 29.4985 13.183 30.1542 12.8786 30.8098 12.5743 C 31.4654 12.27 32.121 11.9657 33.3149 11.4116 L 33.1155 1.4607 C 34.7134 1.098 35.3517 0.9532 35.99 0.8082 C 36.6282 0.6633 37.2665 0.5183 38.8638 0.1553 L 42.8927 9.2364 C 44.221 9.2211 44.9438 9.2126 45.673 9.2041 C 46.3866 9.1958 47.1065 9.1875 48.4059 9.1724 L 52.2273 0 C 54.6634 0.4935 54.88 0.5374 56.2444 0.8141 C 56.6785 0.9022 57.2288 1.0138 58.0038 1.171 L 58.0314 11.1238 C 59.2379 11.65 59.9002 11.9389 60.5626 12.2279 C 61.2249 12.5168 61.8873 12.8057 63.0937 13.3319 L 70.2749 6.5243 C 71.6105 7.4815 72.1442 7.864 72.6778 8.2465 C 73.2114 8.6291 73.7451 9.0117 75.0808 9.9693 L 71.102 19.0726 C 71.9925 20.0496 72.4813 20.5859 72.9701 21.1223 C 73.4589 21.6586 73.9477 22.195 74.8381 23.1719 L 84.1369 19.9057 C 84.9723 21.3304 85.3058 21.8992 85.6396 22.4686 C 85.973 23.0372 86.3067 23.6064 87.1412 25.0295 L 79.844 31.7097 C 80.2644 32.9682 80.4952 33.6592 80.726 34.3502 C 80.9567 35.0413 81.1875 35.7323 81.6079 36.9908 L 91.4172 37.8308 C 91.6072 39.475 91.683 40.132 91.7589 40.7889 C 91.8348 41.4459 91.9107 42.1029 92.1007 43.7471 L 82.7467 46.8491 C 82.6244 48.1716 82.5573 48.8978 82.4902 49.6241 C 82.4231 50.3503 82.356 51.0765 82.2338 52.399 L 90.8572 57.2001 C 90.3696 58.7796 90.1745 59.411 89.9796 60.042 C 89.7845 60.6736 89.5895 61.3047 89.1014 62.8856 L 79.3085 61.8732 C 78.664 63.0325 78.3104 63.6684 77.9565 64.3048 C 77.6034 64.9398 77.25 65.5754 76.6071 66.732 L 82.5535 74.6637 L 81.3434 76.0542 C 80.4459 77.0854 80.2879 77.2669 78.662 79.1358 L 70.1225 74.1838 C 69.5439 74.6192 69.1086 74.9469 68.7378 75.226 C 67.758 75.9634 67.2293 76.3614 65.6998 77.5122 L 67.941 87.2038 L 62.5871 89.689 L 56.7785 81.6533 C 55.4965 81.9443 54.7926 82.1042 54.0887 82.2641 C 53.3848 82.4241 52.6809 82.584 51.3989 82.875 L 49.5472 92.6503 L 43.6565 92.7188 L 41.584 82.9885 C 40.2957 82.7273 39.5884 82.5838 38.8811 82.4404 C 38.1737 82.297 37.4664 82.1535 36.1781 81.8923 L 30.5539 90.0611 L 25.1448 87.7015 L 27.1646 77.9604 C 26.0929 77.192 25.5044 76.7701 24.9159 76.3482 C 24.3274 75.9263 23.7389 75.5044 22.6673 74.736 L 14.2432 79.8858 C 13.1336 78.6685 12.6903 78.1821 12.2469 77.6957 C 11.8036 77.2093 11.3603 76.7229 10.2507 75.5056 L 16.0147 67.4376 C 15.3438 66.2933 14.9757 65.6658 14.6073 65.0376 C 14.2399 64.4112 13.872 63.784 13.2029 62.6428 L 3.4357 63.8835 C 2.7937 61.962 2.6242 61.4543 2.3481 60.6278 C 2.1736 60.1053 1.9566 59.4555 1.5507 58.2405 L 10.0619 53.2396 C 9.9096 51.9203 9.8258 51.1959 9.7421 50.4714 C 9.6584 49.7469 9.5747 49.0224 9.4223 47.7031 L 0 44.8198 C 0.1524 43.1717 0.2132 42.5132 0.274 41.8546 Z M 64.1419 46.3593 C 64.1419 36.267 56.0416 28.0853 46.0498 28.0853 C 36.0579 28.0853 27.9576 36.267 27.9576 46.3593 C 27.9576 56.4516 36.0579 64.6334 46.0498 64.6334 C 56.0416 64.6334 64.1419 56.4516 64.1419 46.3593 Z"/>
                            <path id="gear-11_1" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.274 41.8546 C 0.3349 41.1961 0.3957 40.5376 0.5481 38.8895 L 0.5474 38.8883 L 10.3347 37.82 C 10.9567 35.8047 11.133 35.2336 11.5724 33.8112 L 11.9776 32.499 L 4.5299 25.9906 L 5.4613 24.315 C 6.0971 23.171 6.2308 22.9304 7.4163 20.7983 L 16.7872 23.8471 L 20.4293 19.6622 L 16.2442 10.654 C 17.0877 10.0194 17.606 9.6295 18.0078 9.3272 C 18.8578 8.6878 19.1869 8.4402 20.9704 7.098 L 28.3046 13.7371 C 29.4985 13.183 30.1542 12.8786 30.8098 12.5743 C 31.4654 12.27 32.121 11.9657 33.3149 11.4116 L 33.1155 1.4607 C 34.7134 1.098 35.3517 0.9532 35.99 0.8082 C 36.6282 0.6633 37.2665 0.5183 38.8638 0.1553 L 42.8927 9.2364 C 44.221 9.2211 44.9438 9.2126 45.673 9.2041 C 46.3866 9.1958 47.1065 9.1875 48.4059 9.1724 L 52.2273 0 C 54.6634 0.4935 54.88 0.5374 56.2444 0.8141 C 56.6785 0.9022 57.2288 1.0138 58.0038 1.171 L 58.0314 11.1238 C 59.2379 11.65 59.9002 11.9389 60.5626 12.2279 C 61.2249 12.5168 61.8873 12.8057 63.0937 13.3319 L 70.2749 6.5243 C 71.6105 7.4815 72.1442 7.864 72.6778 8.2465 C 73.2114 8.6291 73.7451 9.0117 75.0808 9.9693 L 71.102 19.0726 C 71.9925 20.0496 72.4813 20.5859 72.9701 21.1223 C 73.4589 21.6586 73.9477 22.195 74.8381 23.1719 L 84.1369 19.9057 C 84.9723 21.3304 85.3058 21.8992 85.6396 22.4686 C 85.973 23.0372 86.3067 23.6064 87.1412 25.0295 L 79.844 31.7097 C 80.2644 32.9682 80.4952 33.6592 80.726 34.3502 C 80.9567 35.0413 81.1875 35.7323 81.6079 36.9908 L 91.4172 37.8308 C 91.6072 39.475 91.683 40.132 91.7589 40.7889 C 91.8348 41.4459 91.9107 42.1029 92.1007 43.7471 L 82.7467 46.8491 C 82.6244 48.1716 82.5573 48.8978 82.4902 49.6241 C 82.4231 50.3503 82.356 51.0765 82.2338 52.399 L 90.8572 57.2001 C 90.3696 58.7796 90.1745 59.411 89.9796 60.042 C 89.7845 60.6736 89.5895 61.3047 89.1014 62.8856 L 79.3085 61.8732 C 78.664 63.0325 78.3104 63.6684 77.9565 64.3048 C 77.6034 64.9398 77.25 65.5754 76.6071 66.732 L 82.5535 74.6637 L 81.3434 76.0542 C 80.4459 77.0854 80.2879 77.2669 78.662 79.1358 L 70.1225 74.1838 C 69.5439 74.6192 69.1086 74.9469 68.7378 75.226 C 67.758 75.9634 67.2293 76.3614 65.6998 77.5122 L 67.941 87.2038 L 62.5871 89.689 L 56.7785 81.6533 C 55.4965 81.9443 54.7926 82.1042 54.0887 82.2641 C 53.3848 82.4241 52.6809 82.584 51.3989 82.875 L 49.5472 92.6503 L 43.6565 92.7188 L 41.584 82.9885 C 40.2957 82.7273 39.5884 82.5838 38.8811 82.4404 C 38.1737 82.297 37.4664 82.1535 36.1781 81.8923 L 30.5539 90.0611 L 25.1448 87.7015 L 27.1646 77.9604 C 26.0929 77.192 25.5044 76.7701 24.9159 76.3482 C 24.3274 75.9263 23.7389 75.5044 22.6673 74.736 L 14.2432 79.8858 C 13.1336 78.6685 12.6903 78.1821 12.2469 77.6957 C 11.8036 77.2093 11.3603 76.7229 10.2507 75.5056 L 16.0147 67.4376 C 15.3438 66.2933 14.9757 65.6658 14.6073 65.0376 C 14.2399 64.4112 13.872 63.784 13.2029 62.6428 L 3.4357 63.8835 C 2.7937 61.962 2.6242 61.4543 2.3481 60.6278 C 2.1736 60.1053 1.9566 59.4555 1.5507 58.2405 L 10.0619 53.2396 C 9.9096 51.9203 9.8258 51.1959 9.7421 50.4714 C 9.6584 49.7469 9.5747 49.0224 9.4223 47.7031 L 0 44.8198 C 0.1524 43.1717 0.2132 42.5132 0.274 41.8546 Z M 64.1419 46.3593 C 64.1419 36.267 56.0416 28.0853 46.0498 28.0853 C 36.0579 28.0853 27.9576 36.267 27.9576 46.3593 C 27.9576 56.4516 36.0579 64.6334 46.0498 64.6334 C 56.0416 64.6334 64.1419 56.4516 64.1419 46.3593 Z"/>
                        </g>
                    </g>
                    <g id="g-12" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:664.311;y:64.089;w:66.758;h:67.994" transform="translate(664.310546875, 64.08935546875)">
                        <g id="cu_gear-12" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:66.758;h:67.994">
                            <path id="gear-12" fill="#fef2e6" d="M 66.7575 25.8606 L 57.8356 31.9087 L 57.8315 36.1331 L 66.7414 42.1987 C 66.2163 43.7952 66.063 44.2615 65.7923 45.0847 C 65.6515 45.5129 65.4789 46.0377 65.2058 46.8683 L 54.402 46.5616 C 54.0319 47.0654 53.7455 47.4555 53.4847 47.8106 C 52.9929 48.4802 52.5923 49.0256 51.8946 49.9762 L 55.5071 60.0768 C 54.4648 60.8261 53.9834 61.1722 53.502 61.5184 C 53.0206 61.8645 52.5392 62.2106 51.4969 62.9599 L 42.9384 56.415 C 41.6472 56.8297 40.986 57.042 40.0117 57.3548 L 38.8859 57.7162 L 35.8216 67.9938 L 30.8687 67.9889 L 27.8247 57.7053 C 26.5379 57.2892 25.8772 57.0757 24.908 56.7624 C 24.5783 56.6559 24.2128 56.5377 23.7748 56.3962 L 15.2034 62.9243 C 14.1626 62.173 13.6819 61.8259 13.2011 61.4788 C 12.7204 61.1317 12.2397 60.7847 11.1989 60.0334 L 14.8314 49.9398 C 14.4662 49.4404 14.1826 49.0527 13.9247 48.7 C 13.4305 48.0244 13.0304 47.4774 12.3308 46.5203 L 1.5263 46.8059 C 0.9864 45.1532 0.8419 44.7109 0.5546 43.8313 C 0.4198 43.4184 0.2535 42.9092 0 42.1332 L 8.9219 36.0851 L 8.9261 31.8607 L 0.0162 25.7951 C 0.558 24.1477 0.704 23.7037 0.9915 22.8292 C 1.1276 22.4154 1.2953 21.9051 1.5517 21.1254 L 12.3556 21.4322 C 12.7227 20.9323 13.0075 20.5445 13.2667 20.1917 C 13.7613 19.5182 14.1623 18.9722 14.863 18.0176 L 11.2504 7.9169 C 12.2927 7.1677 12.7741 6.8216 13.2555 6.4754 C 13.7369 6.1293 14.2183 5.7832 15.2607 5.0339 L 23.8191 11.5788 C 25.096 11.1687 25.7568 10.9565 26.7134 10.6494 L 27.8716 10.2776 L 30.9359 0 L 35.8889 0.0049 L 38.9328 10.2884 C 40.6574 10.8461 41.2574 11.0399 42.9827 11.5976 L 51.5541 5.0695 C 52.5949 5.8208 53.0757 6.1679 53.5564 6.515 C 54.0371 6.862 54.5178 7.2091 55.5587 7.9604 L 51.9261 18.054 C 52.296 18.5598 52.5822 18.951 52.8428 19.3073 C 53.3324 19.9767 53.7318 20.5227 54.4268 21.4735 L 65.2312 21.1879 C 65.753 22.785 65.9055 23.2518 66.1744 24.0752 C 66.3144 24.5038 66.4859 25.029 66.7575 25.8606 Z M 15.1765 33.9792 C 15.1666 43.9921 23.3081 52.1175 33.3607 52.1274 C 43.4134 52.1373 51.5709 44.0278 51.5808 34.0149 C 51.5908 24.0019 43.4492 15.8765 33.3966 15.8666 C 23.344 15.8568 15.1864 23.9662 15.1765 33.9792 Z"/>
                            <path id="gear-12_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 66.7575 25.8606 L 57.8356 31.9087 L 57.8315 36.1331 L 66.7414 42.1987 C 66.2163 43.7952 66.063 44.2615 65.7923 45.0847 C 65.6515 45.5129 65.4789 46.0377 65.2058 46.8683 L 54.402 46.5616 C 54.0319 47.0654 53.7455 47.4555 53.4847 47.8106 C 52.9929 48.4802 52.5923 49.0256 51.8946 49.9762 L 55.5071 60.0768 C 54.4648 60.8261 53.9834 61.1722 53.502 61.5184 C 53.0206 61.8645 52.5392 62.2106 51.4969 62.9599 L 42.9384 56.415 C 41.6472 56.8297 40.986 57.042 40.0117 57.3548 L 38.8859 57.7162 L 35.8216 67.9938 L 30.8687 67.9889 L 27.8247 57.7053 C 26.5379 57.2892 25.8772 57.0757 24.908 56.7624 C 24.5783 56.6559 24.2128 56.5377 23.7748 56.3962 L 15.2034 62.9243 C 14.1626 62.173 13.6819 61.8259 13.2011 61.4788 C 12.7204 61.1317 12.2397 60.7847 11.1989 60.0334 L 14.8314 49.9398 C 14.4662 49.4404 14.1826 49.0527 13.9247 48.7 C 13.4305 48.0244 13.0304 47.4774 12.3308 46.5203 L 1.5263 46.8059 C 0.9864 45.1532 0.8419 44.7109 0.5546 43.8313 C 0.4198 43.4184 0.2535 42.9092 0 42.1332 L 8.9219 36.0851 L 8.9261 31.8607 L 0.0162 25.7951 C 0.558 24.1477 0.704 23.7037 0.9915 22.8292 C 1.1276 22.4154 1.2953 21.9051 1.5517 21.1254 L 12.3556 21.4322 C 12.7227 20.9323 13.0075 20.5445 13.2667 20.1917 C 13.7613 19.5182 14.1623 18.9722 14.863 18.0176 L 11.2504 7.9169 C 12.2927 7.1677 12.7741 6.8216 13.2555 6.4754 C 13.7369 6.1293 14.2183 5.7832 15.2607 5.0339 L 23.8191 11.5788 C 25.096 11.1687 25.7568 10.9565 26.7134 10.6494 L 27.8716 10.2776 L 30.9359 0 L 35.8889 0.0049 L 38.9328 10.2884 C 40.6574 10.8461 41.2574 11.0399 42.9827 11.5976 L 51.5541 5.0695 C 52.5949 5.8208 53.0757 6.1679 53.5564 6.515 C 54.0371 6.862 54.5178 7.2091 55.5587 7.9604 L 51.9261 18.054 C 52.296 18.5598 52.5822 18.951 52.8428 19.3073 C 53.3324 19.9767 53.7318 20.5227 54.4268 21.4735 L 65.2312 21.1879 C 65.753 22.785 65.9055 23.2518 66.1744 24.0752 C 66.3144 24.5038 66.4859 25.029 66.7575 25.8606 Z M 15.1765 33.9792 C 15.1666 43.9921 23.3081 52.1175 33.3607 52.1274 C 43.4134 52.1373 51.5709 44.0278 51.5808 34.0149 C 51.5908 24.0019 43.4492 15.8765 33.3966 15.8666 C 23.344 15.8568 15.1864 23.9662 15.1765 33.9792 Z"/>
                        </g>
                    </g>
                    <g id="g-13" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:721.322;y:23.675;w:79.120;h:78.502" transform="translate(721.322265625, 23.67529296875)">
                        <g id="cu_gear-13" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:79.120;h:78.502">
                            <path id="gear-13" fill="#ffedeb" d="M 40.1938 78.4995 C 39.1925 78.4983 38.6877 78.4977 36.7259 78.4954 L 36.7278 78.4951 L 33.722 67.3033 C 32.694 66.9995 32.0345 66.8045 31.3746 66.6094 C 30.716 66.4147 30.057 66.2199 29.0311 65.9166 L 20.4413 73.6805 C 19.6511 73.17 19.1421 72.8412 18.7337 72.5773 C 17.8708 72.0198 17.4569 71.7524 15.7871 70.6741 L 19.2959 59.6283 L 18.5336 58.7439 C 17.6872 57.7619 17.2072 57.205 16.0986 55.9196 L 4.6856 57.7981 C 4.0794 56.4631 3.8092 55.8677 3.5389 55.2721 C 3.2686 54.6767 2.9984 54.0813 2.3924 52.7466 L 11.3024 45.3537 C 11.1516 44.2912 11.0547 43.6092 10.9578 42.9272 C 10.8609 42.2452 10.7641 41.5631 10.6133 40.5007 L 0 35.897 C 0.2106 34.4454 0.3044 33.798 0.3982 33.1506 C 0.4919 32.5032 0.5857 31.8557 0.7963 30.4041 L 12.2787 29.0115 C 12.861 27.739 13.1712 27.0607 13.5643 26.2012 C 13.7738 25.7432 14.0068 25.2336 14.3171 24.5552 L 7.8739 14.9315 C 9.4205 13.147 9.5868 12.9553 10.5966 11.7906 C 10.8404 11.5094 11.1333 11.1716 11.5067 10.7409 L 21.9158 15.7908 L 26.0344 13.1456 L 25.8065 1.5571 C 26.3587 1.3954 26.7906 1.2689 27.1496 1.1638 C 28.6169 0.7342 28.8649 0.6616 31.1225 0 L 37.1528 9.8886 L 42.0441 9.8946 L 48.1041 0.0205 C 49.5075 0.4351 50.1336 0.6201 50.7596 0.8051 C 51.3858 0.9902 52.0119 1.1753 53.4154 1.5903 L 53.1532 13.1783 C 54.052 13.759 54.6294 14.1319 55.2065 14.5045 C 55.785 14.8781 56.3631 15.2514 57.2642 15.8335 L 67.6882 10.8089 L 71.3086 15.0078 L 64.8368 24.6165 C 65.3449 25.736 65.6446 26.3961 65.9748 27.1232 C 66.2208 27.6651 66.4837 28.2441 66.8622 29.078 L 78.3402 30.4981 L 79.12 35.9926 L 68.4934 40.5705 C 68.3576 41.5078 68.2646 42.1489 68.1766 42.7553 C 68.0652 43.5229 67.9619 44.2349 67.7901 45.4216 L 76.6779 52.8357 C 76.068 54.1693 75.7959 54.764 75.5237 55.3588 C 75.2516 55.9536 74.9795 56.5483 74.3696 57.8819 L 62.9619 55.9761 L 59.7532 59.6773 L 63.2291 70.7315 L 62.1495 71.4246 C 60.7731 72.3084 60.5957 72.4223 58.5657 73.7263 L 49.9991 65.9414 C 47.9681 66.5368 47.3736 66.7109 45.3781 67.2953 L 45.3037 67.317 L 42.264 78.5019 L 40.1938 78.4995 Z M 39.9181 20.6709 C 29.8772 20.6709 21.7372 28.8363 21.7372 38.9084 C 21.7372 48.9806 29.8772 57.1459 39.9181 57.1459 C 49.959 57.1459 58.0991 48.9806 58.0991 38.9084 C 58.0991 28.8363 49.959 20.6709 39.9181 20.6709 Z"/>
                            <path id="gear-13_1" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 40.1938 78.4995 C 39.1925 78.4983 38.6877 78.4977 36.7259 78.4954 L 36.7278 78.4951 L 33.722 67.3033 C 32.694 66.9995 32.0345 66.8045 31.3746 66.6094 C 30.716 66.4147 30.057 66.2199 29.0311 65.9166 L 20.4413 73.6805 C 19.6511 73.17 19.1421 72.8412 18.7337 72.5773 C 17.8708 72.0198 17.4569 71.7524 15.7871 70.6741 L 19.2959 59.6283 L 18.5336 58.7439 C 17.6872 57.7619 17.2072 57.205 16.0986 55.9196 L 4.6856 57.7981 C 4.0794 56.4631 3.8092 55.8677 3.5389 55.2721 C 3.2686 54.6767 2.9984 54.0813 2.3924 52.7466 L 11.3024 45.3537 C 11.1516 44.2912 11.0547 43.6092 10.9578 42.9272 C 10.8609 42.2452 10.7641 41.5631 10.6133 40.5007 L 0 35.897 C 0.2106 34.4454 0.3044 33.798 0.3982 33.1506 C 0.4919 32.5032 0.5857 31.8557 0.7963 30.4041 L 12.2787 29.0115 C 12.861 27.739 13.1712 27.0607 13.5643 26.2012 C 13.7738 25.7432 14.0068 25.2336 14.3171 24.5552 L 7.8739 14.9315 C 9.4205 13.147 9.5868 12.9553 10.5966 11.7906 C 10.8404 11.5094 11.1333 11.1716 11.5067 10.7409 L 21.9158 15.7908 L 26.0344 13.1456 L 25.8065 1.5571 C 26.3587 1.3954 26.7906 1.2689 27.1496 1.1638 C 28.6169 0.7342 28.8649 0.6616 31.1225 0 L 37.1528 9.8886 L 42.0441 9.8946 L 48.1041 0.0205 C 49.5075 0.4351 50.1336 0.6201 50.7596 0.8051 C 51.3858 0.9902 52.0119 1.1753 53.4154 1.5903 L 53.1532 13.1783 C 54.052 13.759 54.6294 14.1319 55.2065 14.5045 C 55.785 14.8781 56.3631 15.2514 57.2642 15.8335 L 67.6882 10.8089 L 71.3086 15.0078 L 64.8368 24.6165 C 65.3449 25.736 65.6446 26.3961 65.9748 27.1232 C 66.2208 27.6651 66.4837 28.2441 66.8622 29.078 L 78.3402 30.4981 L 79.12 35.9926 L 68.4934 40.5705 C 68.3576 41.5078 68.2646 42.1489 68.1766 42.7553 C 68.0652 43.5229 67.9619 44.2349 67.7901 45.4216 L 76.6779 52.8357 C 76.068 54.1693 75.7959 54.764 75.5237 55.3588 C 75.2516 55.9536 74.9795 56.5483 74.3696 57.8819 L 62.9619 55.9761 L 59.7532 59.6773 L 63.2291 70.7315 L 62.1495 71.4246 C 60.7731 72.3084 60.5957 72.4223 58.5657 73.7263 L 49.9991 65.9414 C 47.9681 66.5368 47.3736 66.7109 45.3781 67.2953 L 45.3037 67.317 L 42.264 78.5019 L 40.1938 78.4995 Z M 39.9181 20.6709 C 29.8772 20.6709 21.7372 28.8363 21.7372 38.9084 C 21.7372 48.9806 29.8772 57.1459 39.9181 57.1459 C 49.959 57.1459 58.0991 48.9806 58.0991 38.9084 C 58.0991 28.8363 49.959 20.6709 39.9181 20.6709 Z"/>
                        </g>
                    </g>
                    <g id="g-14" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:777.719;y:70.890;w:92.719;h:92.101" transform="translate(777.71875, 70.8896484375)">
                        <g id="cu_gear-14" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:92.719;h:92.101">
                            <path id="gear-14" fill="#feecf7" d="M 0.3065 51.0263 C 0.3745 51.6805 0.4426 52.3347 0.613 53.9717 L 0.6124 53.973 L 10.4752 54.9331 C 11.1187 56.9198 11.3036 57.4904 11.7552 58.8836 L 12.184 60.2066 L 4.7546 66.7556 L 5.7163 68.4239 C 6.3622 69.5444 6.5026 69.788 7.7137 71.8881 L 17.1149 68.759 L 20.8242 72.8821 L 16.7047 81.8826 C 17.5652 82.5083 18.0921 82.8914 18.5007 83.1885 C 19.3577 83.8116 19.6946 84.0567 21.4989 85.369 L 28.8127 78.6914 C 30.0203 79.2299 30.6833 79.5256 31.3464 79.8213 C 32.0095 80.1171 32.6725 80.4128 33.8801 80.9513 L 33.7823 90.8476 C 35.3945 91.1915 36.0385 91.3289 36.6825 91.4663 C 37.3265 91.6038 37.9704 91.7414 39.582 92.0856 L 43.5436 83.0142 C 44.8811 83.0157 45.6088 83.0165 46.3431 83.0173 C 47.0613 83.0182 47.7859 83.019 49.0937 83.0204 L 53.0352 92.1007 C 55.4817 91.5847 55.6998 91.5387 57.0695 91.2495 C 57.5057 91.1574 58.0587 91.0406 58.8377 90.8762 L 58.7625 80.9798 C 59.9715 80.444 60.6352 80.1498 61.2989 79.8556 C 61.9626 79.5614 62.6263 79.2673 63.8353 78.7315 L 71.1343 85.4254 C 72.4688 84.4597 73.002 84.0739 73.5351 83.688 C 74.0684 83.3021 74.6015 82.9161 75.9362 81.9499 L 71.837 72.94 C 72.7256 71.9567 73.2123 71.4181 73.7001 70.8782 C 74.1853 70.3413 74.6715 69.8032 75.5553 68.8252 L 84.9492 71.9758 C 85.7749 70.5513 86.1048 69.9821 86.4347 69.4129 C 86.7646 68.8437 87.0945 68.2744 87.9203 66.8499 L 80.5059 60.2839 C 80.916 59.0285 81.1411 58.339 81.3662 57.6496 C 81.5914 56.96 81.8165 56.2704 82.2268 55.0145 L 92.092 54.077 C 92.2662 52.4402 92.3358 51.7862 92.4054 51.1322 C 92.475 50.4782 92.5445 49.8242 92.7188 48.1874 L 83.2711 45.2005 C 83.1343 43.8869 83.0592 43.1655 82.9842 42.4441 C 82.9091 41.7227 82.8341 41.0013 82.6973 39.6877 L 91.3279 34.824 C 90.8208 33.2587 90.6179 32.6329 90.4151 32.0075 C 90.2122 31.3816 90.0094 30.7561 89.5018 29.1892 L 79.6548 30.298 C 78.9956 29.1547 78.6333 28.5265 78.2713 27.8988 C 77.9085 27.2696 77.546 26.6411 76.8853 25.4951 L 82.7889 17.5466 L 81.5688 16.1905 C 80.6437 15.1622 80.4889 14.9902 78.8254 13.1406 L 70.2809 18.1534 C 69.7033 17.7332 69.2665 17.4154 68.8943 17.1446 C 67.8889 16.4132 67.3554 16.0251 65.7946 14.8901 L 67.9503 5.2304 L 62.5354 2.8151 L 56.7717 10.8656 C 55.4782 10.5896 54.768 10.4379 54.0578 10.2863 C 53.3477 10.1346 52.6375 9.9829 51.344 9.7069 L 49.3789 0.0067 L 43.4487 0 L 41.4632 9.6964 C 38.875 10.2427 38.6212 10.2964 36.033 10.8427 L 30.2872 2.7791 L 24.8669 5.1817 L 27.0008 14.8462 C 24.8592 16.3965 24.6487 16.5487 22.5072 18.0991 L 13.9743 13.0665 L 12.9643 14.1841 C 11.8385 15.4297 11.7887 15.485 10.0008 17.4633 L 15.8863 25.4252 C 15.2236 26.5684 14.8598 27.196 14.4959 27.8236 C 14.132 28.4511 13.7682 29.0787 13.1056 30.2219 L 3.2611 29.0901 C 2.6384 30.9964 2.4713 31.5081 2.2047 32.3244 C 2.0337 32.8482 1.8217 33.4973 1.4221 34.7207 L 10.0411 39.6042 C 9.9014 40.9176 9.8246 41.6388 9.7479 42.3601 C 9.6711 43.0813 9.5943 43.8025 9.4546 45.1159 L 0 48.081 C 0.1704 49.718 0.2385 50.3722 0.3065 51.0263 Z M 64.5301 46.0895 C 64.5301 56.1248 56.376 64.2603 46.3177 64.2603 C 36.2595 64.2603 28.1053 56.1248 28.1053 46.0895 C 28.1053 36.0542 36.2595 27.9186 46.3177 27.9186 C 56.376 27.9186 64.5301 36.0542 64.5301 46.0895 Z"/>
                            <path id="gear-14_1" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.3065 51.0263 C 0.3745 51.6805 0.4426 52.3347 0.613 53.9717 L 0.6124 53.973 L 10.4752 54.9331 C 11.1187 56.9198 11.3036 57.4904 11.7552 58.8836 L 12.184 60.2066 L 4.7546 66.7556 L 5.7163 68.4239 C 6.3622 69.5444 6.5026 69.788 7.7137 71.8881 L 17.1149 68.759 L 20.8242 72.8821 L 16.7047 81.8826 C 17.5652 82.5083 18.0921 82.8914 18.5007 83.1885 C 19.3577 83.8116 19.6946 84.0567 21.4989 85.369 L 28.8127 78.6914 C 30.0203 79.2299 30.6833 79.5256 31.3464 79.8213 C 32.0095 80.1171 32.6725 80.4128 33.8801 80.9513 L 33.7823 90.8476 C 35.3945 91.1915 36.0385 91.3289 36.6825 91.4663 C 37.3265 91.6038 37.9704 91.7414 39.582 92.0856 L 43.5436 83.0142 C 44.8811 83.0157 45.6088 83.0165 46.3431 83.0173 C 47.0613 83.0182 47.7859 83.019 49.0937 83.0204 L 53.0352 92.1007 C 55.4817 91.5847 55.6998 91.5387 57.0695 91.2495 C 57.5057 91.1574 58.0587 91.0406 58.8377 90.8762 L 58.7625 80.9798 C 59.9715 80.444 60.6352 80.1498 61.2989 79.8556 C 61.9626 79.5614 62.6263 79.2673 63.8353 78.7315 L 71.1343 85.4254 C 72.4688 84.4597 73.002 84.0739 73.5351 83.688 C 74.0684 83.3021 74.6015 82.9161 75.9362 81.9499 L 71.837 72.94 C 72.7256 71.9567 73.2123 71.4181 73.7001 70.8782 C 74.1853 70.3413 74.6715 69.8032 75.5553 68.8252 L 84.9492 71.9758 C 85.7749 70.5513 86.1048 69.9821 86.4347 69.4129 C 86.7646 68.8437 87.0945 68.2744 87.9203 66.8499 L 80.5059 60.2839 C 80.916 59.0285 81.1411 58.339 81.3662 57.6496 C 81.5914 56.96 81.8165 56.2704 82.2268 55.0145 L 92.092 54.077 C 92.2662 52.4402 92.3358 51.7862 92.4054 51.1322 C 92.475 50.4782 92.5445 49.8242 92.7188 48.1874 L 83.2711 45.2005 C 83.1343 43.8869 83.0592 43.1655 82.9842 42.4441 C 82.9091 41.7227 82.8341 41.0013 82.6973 39.6877 L 91.3279 34.824 C 90.8208 33.2587 90.6179 32.6329 90.4151 32.0075 C 90.2122 31.3816 90.0094 30.7561 89.5018 29.1892 L 79.6548 30.298 C 78.9956 29.1547 78.6333 28.5265 78.2713 27.8988 C 77.9085 27.2696 77.546 26.6411 76.8853 25.4951 L 82.7889 17.5466 L 81.5688 16.1905 C 80.6437 15.1622 80.4889 14.9902 78.8254 13.1406 L 70.2809 18.1534 C 69.7033 17.7332 69.2665 17.4154 68.8943 17.1446 C 67.8889 16.4132 67.3554 16.0251 65.7946 14.8901 L 67.9503 5.2304 L 62.5354 2.8151 L 56.7717 10.8656 C 55.4782 10.5896 54.768 10.4379 54.0578 10.2863 C 53.3477 10.1346 52.6375 9.9829 51.344 9.7069 L 49.3789 0.0067 L 43.4487 0 L 41.4632 9.6964 C 38.875 10.2427 38.6212 10.2964 36.033 10.8427 L 30.2872 2.7791 L 24.8669 5.1817 L 27.0008 14.8462 C 24.8592 16.3965 24.6487 16.5487 22.5072 18.0991 L 13.9743 13.0665 L 12.9643 14.1841 C 11.8385 15.4297 11.7887 15.485 10.0008 17.4633 L 15.8863 25.4252 C 15.2236 26.5684 14.8598 27.196 14.4959 27.8236 C 14.132 28.4511 13.7682 29.0787 13.1056 30.2219 L 3.2611 29.0901 C 2.6384 30.9964 2.4713 31.5081 2.2047 32.3244 C 2.0337 32.8482 1.8217 33.4973 1.4221 34.7207 L 10.0411 39.6042 C 9.9014 40.9176 9.8246 41.6388 9.7479 42.3601 C 9.6711 43.0813 9.5943 43.8025 9.4546 45.1159 L 0 48.081 C 0.1704 49.718 0.2385 50.3722 0.3065 51.0263 Z M 64.5301 46.0895 C 64.5301 56.1248 56.376 64.2603 46.3177 64.2603 C 36.2595 64.2603 28.1053 56.1248 28.1053 46.0895 C 28.1053 36.0542 36.2595 27.9186 46.3177 27.9186 C 56.376 27.9186 64.5301 36.0542 64.5301 46.0895 Z"/>
                        </g>
                    </g>
                    <g id="g-15" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:857;y:31;w:66.758;h:67.994" transform="translate(857, 31)">
                        <g id="cu_gear-15" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:66.758;h:67.994">
                            <path id="gear-15" fill="#faf0ff" d="M 0 25.8272 L 8.918 31.8845 L 8.918 36.1092 L 0 42.1666 C 0.5237 43.7637 0.6765 44.2301 0.9465 45.0537 C 1.0869 45.4821 1.2589 46.0071 1.5313 46.838 L 12.3381 46.5418 C 12.7077 47.0461 12.9938 47.4364 13.2544 47.7918 C 13.7456 48.4619 14.1458 49.0078 14.8427 49.9591 L 11.2193 60.057 C 12.2611 60.8073 12.7423 61.1539 13.2235 61.5006 C 13.7046 61.8472 14.1858 62.1938 15.2276 62.9442 L 23.7947 56.4072 C 25.0857 56.8232 25.7469 57.0361 26.7211 57.3499 L 27.8468 57.7125 L 30.9017 67.9938 L 35.8558 67.9938 L 38.9107 57.7125 C 40.1982 57.2976 40.8593 57.0847 41.829 56.7723 C 42.159 56.6661 42.5246 56.5483 42.9629 56.4072 L 51.5299 62.9442 C 52.5717 62.1938 53.0529 61.8472 53.5341 61.5006 C 54.0153 61.1539 54.4964 60.8073 55.5383 60.057 L 51.9148 49.9591 C 52.2806 49.46 52.5647 49.0726 52.823 48.7202 C 53.318 48.045 53.7187 47.4984 54.4194 46.5418 L 65.2262 46.838 C 65.7679 45.1858 65.9129 44.7436 66.2011 43.8642 C 66.3364 43.4514 66.5032 42.9423 66.7575 42.1665 L 57.8395 36.1092 L 57.8395 31.8845 L 66.7575 25.8272 C 66.2172 24.1792 66.0716 23.7351 65.7849 22.8602 C 65.6492 22.4462 65.4819 21.9358 65.2262 21.1558 L 54.4194 21.452 C 54.0528 20.9517 53.7682 20.5635 53.5094 20.2104 C 53.0153 19.5364 52.6147 18.99 51.9148 18.0347 L 55.5383 7.9368 C 54.4964 7.1865 54.0153 6.8399 53.5341 6.4932 C 53.0529 6.1466 52.5717 5.8 51.5299 5.0496 L 42.9629 11.5866 C 41.6861 11.1752 41.0254 10.9624 40.0688 10.6543 L 38.9107 10.2813 L 35.8558 0 L 30.9017 4.3132e-7 L 27.8468 10.2813 C 26.1213 10.8373 25.5209 11.0306 23.7947 11.5866 L 15.2276 5.0496 C 14.1858 5.8 13.7046 6.1466 13.2234 6.4932 C 12.7423 6.8399 12.2611 7.1865 11.2193 7.9368 L 14.8427 18.0347 C 14.4722 18.5401 14.1856 18.9311 13.9246 19.2872 C 13.4342 19.9561 13.0341 20.5018 12.3381 21.452 L 1.5313 21.1558 C 1.0078 22.7525 0.8548 23.2191 0.585 24.0423 C 0.4446 24.4708 0.2725 24.9959 0 25.8272 Z M 51.5859 33.9969 C 51.5859 44.0106 43.4343 52.1286 33.3793 52.1286 C 23.3242 52.1286 15.1727 44.0106 15.1727 33.9969 C 15.1727 23.9832 23.3242 15.8652 33.3793 15.8652 C 43.4343 15.8652 51.5859 23.9832 51.5859 33.9969 Z"/>
                            <path id="gear-15_1" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 25.8272 L 8.918 31.8845 L 8.918 36.1092 L 0 42.1666 C 0.5237 43.7637 0.6765 44.2301 0.9465 45.0537 C 1.0869 45.4821 1.2589 46.0071 1.5313 46.838 L 12.3381 46.5418 C 12.7077 47.0461 12.9938 47.4364 13.2544 47.7918 C 13.7456 48.4619 14.1458 49.0078 14.8427 49.9591 L 11.2193 60.057 C 12.2611 60.8073 12.7423 61.1539 13.2235 61.5006 C 13.7046 61.8472 14.1858 62.1938 15.2276 62.9442 L 23.7947 56.4072 C 25.0857 56.8232 25.7469 57.0361 26.7211 57.3499 L 27.8468 57.7125 L 30.9017 67.9938 L 35.8558 67.9938 L 38.9107 57.7125 C 40.1982 57.2976 40.8593 57.0847 41.829 56.7723 C 42.159 56.6661 42.5246 56.5483 42.9629 56.4072 L 51.5299 62.9442 C 52.5717 62.1938 53.0529 61.8472 53.5341 61.5006 C 54.0153 61.1539 54.4964 60.8073 55.5383 60.057 L 51.9148 49.9591 C 52.2806 49.46 52.5647 49.0726 52.823 48.7202 C 53.318 48.045 53.7187 47.4984 54.4194 46.5418 L 65.2262 46.838 C 65.7679 45.1858 65.9129 44.7436 66.2011 43.8642 C 66.3364 43.4514 66.5032 42.9423 66.7575 42.1665 L 57.8395 36.1092 L 57.8395 31.8845 L 66.7575 25.8272 C 66.2172 24.1792 66.0716 23.7351 65.7849 22.8602 C 65.6492 22.4462 65.4819 21.9358 65.2262 21.1558 L 54.4194 21.452 C 54.0528 20.9517 53.7682 20.5635 53.5094 20.2104 C 53.0153 19.5364 52.6147 18.99 51.9148 18.0347 L 55.5383 7.9368 C 54.4964 7.1865 54.0153 6.8399 53.5341 6.4932 C 53.0529 6.1466 52.5717 5.8 51.5299 5.0496 L 42.9629 11.5866 C 41.6861 11.1752 41.0254 10.9624 40.0688 10.6543 L 38.9107 10.2813 L 35.8558 0 L 30.9017 4.3132e-7 L 27.8468 10.2813 C 26.1213 10.8373 25.5209 11.0306 23.7947 11.5866 L 15.2276 5.0496 C 14.1858 5.8 13.7046 6.1466 13.2234 6.4932 C 12.7423 6.8399 12.2611 7.1865 11.2193 7.9368 L 14.8427 18.0347 C 14.4722 18.5401 14.1856 18.9311 13.9246 19.2872 C 13.4342 19.9561 13.0341 20.5018 12.3381 21.452 L 1.5313 21.1558 C 1.0078 22.7525 0.8548 23.2191 0.585 24.0423 C 0.4446 24.4708 0.2725 24.9959 0 25.8272 Z M 51.5859 33.9969 C 51.5859 44.0106 43.4343 52.1286 33.3793 52.1286 C 23.3242 52.1286 15.1727 44.0106 15.1727 33.9969 C 15.1727 23.9832 23.3242 15.8652 33.3793 15.8652 C 43.4343 15.8652 51.5859 23.9832 51.5859 33.9969 Z"/>
                        </g>
                    </g>
                </g>
                <g id="Frame_469" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:69;y:234;w:825;h:66" transform="translate(69, 234)">
                    <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:0;y:18;w:96;h:30" transform="translate(0, 18)">
                        <g id="tx-cc-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#db8333" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:120;y:6;w:96;h:30" transform="translate(120, 6)">
                        <g id="tx-cc-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#d95da7" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-4" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:243;y:36;w:96;h:30" transform="translate(243, 36)">
                        <g id="tx-cc-6" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#7e62ec" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-6-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-6" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:369;y:6;w:96;h:30" transform="translate(369, 6)">
                        <g id="tx-cc-8" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#17aee1" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-8-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-8" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-10" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:477;y:12;w:96;h:30" transform="translate(477, 12)">
                        <g id="tx-cc-10" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#93c332" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-10-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-10" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-12" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:597;y:0;w:96;h:30" transform="translate(597, 0)">
                        <g id="tx-cc-12" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#db8333" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-12-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_11" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-12" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-14" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:729;y:24;w:96;h:30" transform="translate(729, 24)">
                        <g id="tx-cc-14" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_12" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#d95da7" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-14-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_13" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-14" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="Frame_468" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:6;w:948;h:66" transform="translate(6, 6)">
                    <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MIN" data-position="x:0;y:36;w:96;h:30" transform="translate(0, 36)">
                        <g id="tx-cc-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_14" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#d1bd08" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_15" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-1" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:123;y:0;w:96;h:30" transform="translate(123, 0)">
                        <g id="tx-cc-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_16" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#df5e59" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_17" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-3" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:246;y:18;w:96;h:30" transform="translate(246, 18)">
                        <g id="tx-cc-5" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_18" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#b960e2" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-5-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_19" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-5" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:366;y:30;w:96;h:30" transform="translate(366, 30)">
                        <g id="tx-cc-7" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_20" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#4987ec" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-7-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_21" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-7" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-9" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:486;y:12;w:96;h:30" transform="translate(486, 12)">
                        <g id="tx-cc-9" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_22" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#3cc583" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-9-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_23" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-9" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-11" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:600;y:0;w:96;h:30" transform="translate(600, 0)">
                        <g id="tx-cc-11" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_24" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#d1bd08" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-11-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_25" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-11" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-13" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:726;y:18;w:96;h:30" transform="translate(726, 18)">
                        <g id="tx-cc-13" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_26" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#df5e59" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-13-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_27" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-13" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-15" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:MAX" data-position="x:852;y:30;w:96;h:30" transform="translate(852, 30)">
                        <g id="tx-cc-15" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_28" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#b960e2" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-15-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_29" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-15" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <rect id="bt-cc-add-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 18, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:78;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 78, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:126;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 126, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 204, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:252;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 252, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:318;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 318, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:378;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 378, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:444;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 444, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-9" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:504;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 504, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:552;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 552, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-11" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:606;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 606, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-12" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:678;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 678, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-13" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:732;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 732, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-14" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:804;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 804, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-15" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:864;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 864, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:924;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 924, 132)" width="24" height="24" rx="0" ry="0"/>
            </g>
        </g>
    </g>
</svg>
<svg xmlns="http://www.w3.org/2000/svg" width="774" height="720">
    <g id="sequence-agenda-slide-v1--family--7" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L774 0 L774 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:774;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:774;h:0">
            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:6;primary:MIN;counter:MIN" data-position="x:0;y:0;w:774;h:672">
                <g id="row-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:6;primary:MIN;counter:CENTER" data-position="x:24;y:24;w:726;h:84" transform="translate(24, 24)">
                    <g id="lines" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 36 24 48;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:120;h:84">
                        <path id="centre" transform="translate(0, 41)" fill="#fefbdb" d="M 0 1 L 48 1 L 48 0 L 0 0 L 0 1 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:48;h:1"/>
                        <path id="box-stroke" transform="translate(48, 0)" fill="#fefbdb" d="M 0 0 L 72 0 L 72 84 L 0 84 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="bottom" transform="translate(0, 42)" fill="#fefbdb" d="M 41.9971 41.5 L 48 41.5 L 48 0 L 0 0 C 0.2679 22.9656 18.968 41.5 41.9971 41.5 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:41.500"/>
                        <path id="top" transform="translate(0, -1)" fill="#fefbdb" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 19.0344 0 42 L 48 42 L 48 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-1;w:48;h:42"/>
                        <path id="box-fill" transform="translate(48, 0)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 72 0 L 72 84 L 0 84" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="bottom-stroke" transform="translate(0, 42)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 42 L 41.9971 42 C 18.968 42 0.2679 22.9656 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:42"/>
                        <path id="centre-stroke" transform="translate(0, 41)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:2"/>
                        <path id="centre-stroke_1" transform="translate(0, 41)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:1"/>
                        <path id="top-stroke" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 18.5344 0 41.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:41.500"/>
                        <g id="tx-cc-1-number" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:24;w:36;h:36" fill="#ff00001a" transform="translate(48, 24)">
                            <text id="1" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#d1bd08" font-size="22.5" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                        </g>
                    </g>
                    <g id="g-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:CENTER;counter:MIN" data-position="x:126;y:0;w:600;h:84" transform="translate(126, 0)">
                        <g id="cu_Rectangle_11" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:600;h:84">
                            <path id="Rectangle_11" fill="#fefbdb" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                            <path id="Rectangle_11_1" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                        </g>
                        <g id="tx-lt-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:12;w:576;h:24" fill="#ff00001a" transform="translate(12, 12)">
                            <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:48;w:576;h:24" fill="#ff00001a" transform="translate(12, 48)">
                            <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-1" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:588;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 588, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:54;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 54, -12)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:54;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 54, 72)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="row-2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:6;primary:MIN;counter:CENTER" data-position="x:24;y:114;w:726;h:84" transform="translate(24, 114)">
                    <g id="lines_1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 36 24 48;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:120;h:84">
                        <path id="bottom_1" transform="translate(0, 42)" fill="#fef2e6" d="M 41.9971 41.5 L 48 41.5 L 48 0 L 0 0 C 0.2679 22.9656 18.968 41.5 41.9971 41.5 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:41.500"/>
                        <path id="top_1" transform="translate(0, -1)" fill="#fef2e6" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 19.0344 0 42 L 48 42 L 48 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-1;w:48;h:42"/>
                        <path id="centre_1" transform="translate(0, 41)" fill="#fef2e6" d="M 0 1 L 48 1 L 48 0 L 0 0 L 0 1 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:48;h:1"/>
                        <path id="box-stroke_1" transform="translate(48, 0)" fill="#fef2e6" d="M 0 0 L 72 0 L 72 84 L 0 84 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="bottom-stroke_1" transform="translate(0, 42)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 42 L 41.9971 42 C 18.968 42 0.2679 22.9656 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:42"/>
                        <path id="box-fill_1" transform="translate(48, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 72 0 L 72 84 L 0 84" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="centre-stroke_2" transform="translate(0, 41)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:2"/>
                        <path id="centre-stroke_3" transform="translate(0, 41)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:1"/>
                        <path id="top-stroke_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 18.5344 0 41.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:41.500"/>
                        <g id="tx-cc-2-number" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:24;w:36;h:36" fill="#ff00001a" transform="translate(48, 24)">
                            <text id="2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#db8333" font-size="22.5" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                        </g>
                    </g>
                    <g id="g-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:CENTER;counter:MIN" data-position="x:126;y:0;w:600;h:84" transform="translate(126, 0)">
                        <g id="cu_Rectangle_11_1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:600;h:84">
                            <path id="Rectangle_11_2" fill="#fef2e6" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                            <path id="Rectangle_11_3" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                        </g>
                        <g id="tx-lt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:12;w:576;h:24" fill="#ff00001a" transform="translate(12, 12)">
                            <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:48;w:576;h:24" fill="#ff00001a" transform="translate(12, 48)">
                            <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:588;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 588, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:54;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 54, 72)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="row-3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:6;primary:MIN;counter:CENTER" data-position="x:24;y:204;w:726;h:84" transform="translate(24, 204)">
                    <g id="lines_2" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 36 24 48;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:120;h:84">
                        <path id="bottom_2" transform="translate(0, 42)" fill="#ffedeb" d="M 41.9971 41.5 L 48 41.5 L 48 0 L 0 0 C 0.2679 22.9656 18.968 41.5 41.9971 41.5 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:41.500"/>
                        <path id="top_2" transform="translate(0, -1)" fill="#ffedeb" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 19.0344 0 42 L 48 42 L 48 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-1;w:48;h:42"/>
                        <path id="centre_2" transform="translate(0, 41)" fill="#ffedeb" d="M 0 1 L 48 1 L 48 0 L 0 0 L 0 1 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:48;h:1"/>
                        <path id="box-stroke_2" transform="translate(48, 0)" fill="#ffedeb" d="M 0 0 L 72 0 L 72 84 L 0 84 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="bottom-stroke_2" transform="translate(0, 42)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 42 L 41.9971 42 C 18.968 42 0.2679 22.9656 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:42"/>
                        <path id="box-fill_2" transform="translate(48, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 72 0 L 72 84 L 0 84" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="centre-stroke_4" transform="translate(0, 41)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:2"/>
                        <path id="centre-stroke_5" transform="translate(0, 41)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:1"/>
                        <path id="top-stroke_2" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 18.5344 0 41.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:41.500"/>
                        <g id="tx-cc-3-number" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:24;w:36;h:36" fill="#ff00001a" transform="translate(48, 24)">
                            <text id="3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#df5e59" font-size="22.5" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">3</text>
                        </g>
                    </g>
                    <g id="g-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:CENTER;counter:MIN" data-position="x:126;y:0;w:600;h:84" transform="translate(126, 0)">
                        <g id="cu_Rectangle_11_2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:600;h:84">
                            <path id="Rectangle_11_4" fill="#ffedeb" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                            <path id="Rectangle_11_5" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                        </g>
                        <g id="tx-lt-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:12;w:576;h:24" fill="#ff00001a" transform="translate(12, 12)">
                            <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:48;w:576;h:24" fill="#ff00001a" transform="translate(12, 48)">
                            <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:588;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 588, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:54;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 54, 72)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="row-4" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:6;primary:MIN;counter:CENTER" data-position="x:24;y:294;w:726;h:84" transform="translate(24, 294)">
                    <g id="lines_3" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 36 24 48;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:120;h:84">
                        <g id="cu_Rectangle_11_3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:126;y:0;w:600;h:84">
                            <path id="Rectangle_11_6" transform="translate(126, 0)" fill="#feecf7" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                            <path id="Rectangle_11_7" transform="translate(126, 0)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                        </g>
                        <path id="bottom_3" transform="translate(0, 43)" fill="#feecf7" d="M 41.9971 41.5 L 48 41.5 L 48 0 L 0 0 C 0.2679 22.9656 18.968 41.5 41.9971 41.5 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:43;w:48;h:41.500"/>
                        <path id="top_3" fill="#feecf7" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 19.0344 0 42 L 48 42 L 48 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:42"/>
                        <path id="centre_3" transform="translate(0, 42)" fill="#feecf7" d="M 0 1 L 48 1 L 48 0 L 0 0 L 0 1 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:1"/>
                        <path id="box-stroke_3" transform="translate(48, 0)" fill="#feecf7" d="M 0 0 L 72 0 L 72 84 L 0 84 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="bottom-stroke_3" transform="translate(0, 42)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 42 L 41.9971 42 C 18.968 42 0.2679 22.9656 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:42"/>
                        <path id="box-fill_3" transform="translate(48, 0)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 72 0 L 72 84 L 0 84" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="centre-stroke_6" transform="translate(0, 41)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:2"/>
                        <path id="centre-stroke_7" transform="translate(0, 41)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:1"/>
                        <path id="top-stroke_3" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 18.5344 0 41.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:41.500"/>
                        <g id="tx-cc-4-number" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:24;w:36;h:36" fill="#ff00001a" transform="translate(48, 24)">
                            <text id="4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#d95da7" font-size="22.5" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">4</text>
                        </g>
                    </g>
                    <g id="g-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:CENTER;counter:MIN" data-position="x:126;y:0;w:600;h:84" transform="translate(126, 0)">
                        <g id="tx-lt-4" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:12;w:576;h:24" fill="#ff00001a" transform="translate(12, 12)">
                            <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:48;w:576;h:24" fill="#ff00001a" transform="translate(12, 48)">
                            <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:588;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 588, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <rect id="bt-cc-add-5" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:54;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 54, 72)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="row-5" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:6;primary:MIN;counter:CENTER" data-position="x:24;y:384;w:726;h:84" transform="translate(24, 384)">
                    <g id="lines_4" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 36 24 48;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:120;h:84">
                        <path id="bottom_4" transform="translate(0, 42)" fill="#faf0ff" d="M 41.9971 41.5 L 48 41.5 L 48 0 L 0 0 C 0.2679 22.9656 18.968 41.5 41.9971 41.5 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:41.500"/>
                        <path id="top_4" transform="translate(0, -1)" fill="#faf0ff" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 19.0344 0 42 L 48 42 L 48 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-1;w:48;h:42"/>
                        <path id="centre_4" transform="translate(0, 41)" fill="#faf0ff" d="M 0 1 L 48 1 L 48 0 L 0 0 L 0 1 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:48;h:1"/>
                        <path id="box-stroke_4" transform="translate(48, 0)" fill="#faf0ff" d="M 0 0 L 72 0 L 72 84 L 0 84 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="bottom-stroke_4" transform="translate(0, 42)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 42 L 41.9971 42 C 18.968 42 0.2679 22.9656 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:42"/>
                        <path id="box-fill_4" transform="translate(48, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 72 0 L 72 84 L 0 84" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="centre-stroke_8" transform="translate(0, 41)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:2"/>
                        <path id="centre-stroke_9" transform="translate(0, 41)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:1"/>
                        <path id="top-stroke_4" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 18.5344 0 41.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:41.500"/>
                        <g id="tx-cc-5-number" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:24;w:36;h:36" fill="#ff00001a" transform="translate(48, 24)">
                            <text id="5" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#b960e2" font-size="22.5" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">5</text>
                        </g>
                    </g>
                    <g id="g-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:CENTER;counter:MIN" data-position="x:126;y:0;w:600;h:84" transform="translate(126, 0)">
                        <g id="cu_Rectangle_11_4" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:600;h:84">
                            <path id="Rectangle_11_8" fill="#faf0ff" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                            <path id="Rectangle_11_9" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                        </g>
                        <g id="tx-lt-5" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:12;w:576;h:24" fill="#ff00001a" transform="translate(12, 12)">
                            <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-5-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:48;w:576;h:24" fill="#ff00001a" transform="translate(12, 48)">
                            <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-5" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:588;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 588, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <rect id="bt-cc-add-6" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:54;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 54, 72)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="row-6" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:6;primary:MIN;counter:CENTER" data-position="x:24;y:474;w:726;h:84" transform="translate(24, 474)">
                    <g id="lines_5" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 36 24 48;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:120;h:84">
                        <path id="bottom_5" transform="translate(0, 42)" fill="#f3f0ff" d="M 41.9971 41.5 L 48 41.5 L 48 0 L 0 0 C 0.2679 22.9656 18.968 41.5 41.9971 41.5 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:41.500"/>
                        <path id="top_5" transform="translate(0, -1)" fill="#f3f0ff" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 19.0344 0 42 L 48 42 L 48 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-1;w:48;h:42"/>
                        <path id="centre_5" transform="translate(0, 41)" fill="#f3f0ff" d="M 0 1 L 48 1 L 48 0 L 0 0 L 0 1 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:48;h:1"/>
                        <path id="box-stroke_5" transform="translate(48, 0)" fill="#f3f0ff" d="M 0 0 L 72 0 L 72 84 L 0 84 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="bottom-stroke_5" transform="translate(0, 42)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 42 L 41.9971 42 C 18.968 42 0.2679 22.9656 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:42"/>
                        <path id="centre-stroke_10" transform="translate(0, 41)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:2"/>
                        <path id="centre-stroke_11" transform="translate(0, 41)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:1"/>
                        <path id="box-fill_5" transform="translate(48, 0)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 72 0 L 72 84 L 0 84" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="top-stroke_5" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 18.5344 0 41.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:41.500"/>
                        <g id="tx-cc-6-number" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:24;w:36;h:36" fill="#ff00001a" transform="translate(48, 24)">
                            <text id="6" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#7e62ec" font-size="22.5" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">6</text>
                        </g>
                    </g>
                    <g id="g-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:CENTER;counter:MIN" data-position="x:126;y:0;w:600;h:84" transform="translate(126, 0)">
                        <g id="cu_Rectangle_11_5" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:600;h:84">
                            <path id="Rectangle_11_10" fill="#f3f0ff" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                            <path id="Rectangle_11_11" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                        </g>
                        <g id="tx-lt-6" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:12;w:576;h:24" fill="#ff00001a" transform="translate(12, 12)">
                            <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-6-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:48;w:576;h:24" fill="#ff00001a" transform="translate(12, 48)">
                            <text id="Label_11" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-6" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:588;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 588, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <rect id="bt-cc-add-7" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:54;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 54, 72)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="row-7" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:6;primary:MIN;counter:CENTER" data-position="x:24;y:564;w:726;h:84" transform="translate(24, 564)">
                    <g id="lines_6" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:24 36 24 48;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:120;h:84">
                        <path id="bottom_6" transform="translate(0, 42)" fill="#edf4ff" d="M 41.9971 41.5 L 48 41.5 L 48 0 L 0 0 C 0.2679 22.9656 18.968 41.5 41.9971 41.5 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:41.500"/>
                        <path id="top_6" transform="translate(0, -1)" fill="#edf4ff" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 19.0344 0 42 L 48 42 L 48 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-1;w:48;h:42"/>
                        <path id="centre_6" transform="translate(0, 41)" fill="#edf4ff" d="M 0 1 L 48 1 L 48 0 L 0 0 L 0 1 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:48;h:1"/>
                        <path id="box-stroke_6" transform="translate(48, 0)" fill="#edf4ff" d="M 0 0 L 72 0 L 72 84 L 0 84 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="bottom-stroke_6" transform="translate(0, 42)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 42 L 41.9971 42 C 18.968 42 0.2679 22.9656 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:42;w:48;h:42"/>
                        <path id="centre-stroke_12" transform="translate(0, 41)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:2"/>
                        <path id="box-fill_6" transform="translate(48, 0)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 72 0 L 72 84 L 0 84" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:0;w:72;h:84"/>
                        <path id="centre-stroke_13" transform="translate(0, 41)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:41;w:0;h:1"/>
                        <path id="top-stroke_6" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 0 L 41.9971 0 C 18.968 0 0.2679 18.5344 0 41.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:41.500"/>
                        <g id="tx-cc-7-number" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:24;w:36;h:36" fill="#ff00001a" transform="translate(48, 24)">
                            <text id="7" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#4987ec" font-size="22.5" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">7</text>
                        </g>
                    </g>
                    <g id="g-7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:CENTER;counter:MIN" data-position="x:126;y:0;w:600;h:84" transform="translate(126, 0)">
                        <g id="cu_Rectangle_11_6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:600;h:84">
                            <path id="Rectangle_11_12" fill="#edf4ff" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                            <path id="Rectangle_11_13" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 600 0 L 600 84 L 0 84 L 0 0 Z"/>
                        </g>
                        <g id="tx-lt-7" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:12;w:576;h:24" fill="#ff00001a" transform="translate(12, 12)">
                            <text id="Label_12" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-7-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:12;y:48;w:576;h:24" fill="#ff00001a" transform="translate(12, 48)">
                            <text id="Label_13" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:576;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-7" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:588;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 588, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <rect id="bt-cc-add-8" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:54;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 54, 72)" width="24" height="24" rx="0" ry="0"/>
                </g>
            </g>
        </g>
    </g>
</svg>
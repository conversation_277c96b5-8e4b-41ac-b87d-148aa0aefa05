<svg xmlns="http://www.w3.org/2000/svg" width="840" height="744">
    <g id="sequence-cards-v1--family--12" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L840 0 L840 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:840;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:840;h:0">
            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:48;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:840;h:696">
                <g id="top" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 18 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:24;w:792;h:126" transform="translate(24, 24)">
                    <g id="Frame_716" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:112;primary:MIN;counter:CENTER" data-position="x:32;y:0;w:728;h:48" transform="translate(32, 0)">
                        <g id="label-1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:168;h:48">
                            <path id="body" transform="translate(18, 0)" fill="#fefbdb" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke" transform="translate(18, 0)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top" fill="#fefbdb" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle" transform="translate(0, 16)" fill="#fefbdb" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom" transform="translate(0, 32)" fill="#fefbdb" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke" transform="translate(0, 32)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke" transform="translate(0, 16)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_1" transform="translate(0, 1)" fill="#fefbdb" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_1" transform="translate(0, 17)" fill="#fefbdb" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_1" transform="translate(0, 33)" fill="#fefbdb" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_1" transform="translate(0, 1)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_1" transform="translate(0, 33)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_1" transform="translate(18, 17)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                        <g id="label-2" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:280;y:0;w:168;h:48" transform="translate(280, 0)">
                            <path id="body_1" transform="translate(18, 0)" fill="#fef2e6" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke_1" transform="translate(18, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left_1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top_2" fill="#fef2e6" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle_2" transform="translate(0, 16)" fill="#fef2e6" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom_2" transform="translate(0, 32)" fill="#fef2e6" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke_2" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_2" transform="translate(0, 32)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke_2" transform="translate(0, 16)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right_1" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_3" transform="translate(0, 1)" fill="#fef2e6" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_3" transform="translate(0, 17)" fill="#fef2e6" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_3" transform="translate(0, 33)" fill="#fef2e6" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_3" transform="translate(0, 1)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_3" transform="translate(0, 33)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_3" transform="translate(18, 17)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                        <g id="label-3" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:560;y:0;w:168;h:48" transform="translate(560, 0)">
                            <path id="body_2" transform="translate(18, 0)" fill="#ffedeb" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke_2" transform="translate(18, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left_2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top_4" fill="#ffedeb" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle_4" transform="translate(0, 16)" fill="#ffedeb" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom_4" transform="translate(0, 32)" fill="#ffedeb" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke_4" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_4" transform="translate(0, 32)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke_4" transform="translate(0, 16)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right_2" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_5" transform="translate(0, 1)" fill="#ffedeb" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_5" transform="translate(0, 17)" fill="#ffedeb" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_5" transform="translate(0, 33)" fill="#ffedeb" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_5" transform="translate(0, 1)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_5" transform="translate(0, 33)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_5" transform="translate(18, 17)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                    </g>
                    <g id="text" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:48;primary:MIN;counter:CENTER" data-position="x:0;y:60;w:792;h:48" transform="translate(0, 60)">
                        <g id="Vector_58-with-terminator" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:232;y:16;w:44;h:16" transform="translate(232, 16)">
                            <path id="Vector_58" marker-start="url(#arrow)" transform="translate(1, 7.999996185302734)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 42 0 L 0 0" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:8.000;w:42;h:0.000"/>
                        </g>
                        <g id="Vector_58-with-terminator_1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:512;y:16;w:44;h:16" transform="translate(512, 16)">
                            <path id="Vector_58_1" marker-start="url(#arrow)" transform="translate(1, 7.999996185302734)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 42 0 L 0 0" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:8.000;w:42;h:0.000"/>
                        </g>
                        <g id="text-1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:232;h:48">
                            <g id="lines" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract" transform="translate(0, 25)" fill="#fefbdb" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_1" fill="#fefbdb" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_2" transform="translate(182, 0)" fill="#fefbdb" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center" transform="translate(0, 48)" fill="#fefbdb" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom" transform="translate(0, 84)" fill="#fefbdb" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_3" transform="translate(0, 36)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke" transform="translate(0, 84)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right" transform="translate(200, 0)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-1" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="text-2" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:280;y:0;w:232;h:48" transform="translate(280, 0)">
                            <g id="lines_1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract_3" transform="translate(0, 25)" fill="#fef2e6" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_4" fill="#fef2e6" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_5" transform="translate(182, 0)" fill="#fef2e6" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center_1" transform="translate(0, 48)" fill="#fef2e6" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom_1" transform="translate(0, 84)" fill="#fef2e6" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_4" transform="translate(0, 36)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke_1" transform="translate(0, 84)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right_1" transform="translate(200, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-2" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="text-3" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:560;y:0;w:232;h:48" transform="translate(560, 0)">
                            <g id="Vector_58-with-terminator_2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:66;w:16;h:44" transform="translate(108, 66)">
                                <path id="Vector_58_2" marker-start="url(#arrow)" transform="translate(7.999994277954102, 1)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 42 L 0 0" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8.000;y:1;w:0.000;h:42"/>
                            </g>
                            <g id="lines_2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract_6" transform="translate(0, 25)" fill="#ffedeb" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_7" fill="#ffedeb" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_8" transform="translate(182, 0)" fill="#ffedeb" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center_2" transform="translate(0, 48)" fill="#ffedeb" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom_2" transform="translate(0, 84)" fill="#ffedeb" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_5" transform="translate(0, 36)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke_2" transform="translate(0, 84)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left_2" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right_2" transform="translate(200, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-3" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                </g>
                <g id="bottom_3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 18 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:198;w:792;h:126" transform="translate(24, 198)">
                    <g id="Frame_716_1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:112;primary:MIN;counter:CENTER" data-position="x:32;y:0;w:728;h:48" transform="translate(32, 0)">
                        <g id="label-6" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:168;h:48">
                            <path id="body_3" transform="translate(18, 0)" fill="#f3f0ff" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke_6" transform="translate(18, 0)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-6" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left_3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top_6" fill="#f3f0ff" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle_6" transform="translate(0, 16)" fill="#f3f0ff" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom_6" transform="translate(0, 32)" fill="#f3f0ff" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke_6" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_6" transform="translate(0, 32)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke_6" transform="translate(0, 16)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right_3" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_7" transform="translate(0, 1)" fill="#f3f0ff" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_7" transform="translate(0, 17)" fill="#f3f0ff" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_7" transform="translate(0, 33)" fill="#f3f0ff" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_7" transform="translate(0, 1)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_7" transform="translate(0, 33)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_7" transform="translate(18, 17)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                        <g id="label-5" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:280;y:0;w:168;h:48" transform="translate(280, 0)">
                            <path id="body_4" transform="translate(18, 0)" fill="#faf0ff" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke_7" transform="translate(18, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-5" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left_4" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top_8" fill="#faf0ff" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle_8" transform="translate(0, 16)" fill="#faf0ff" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom_8" transform="translate(0, 32)" fill="#faf0ff" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke_8" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_8" transform="translate(0, 32)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke_8" transform="translate(0, 16)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right_4" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_9" transform="translate(0, 1)" fill="#faf0ff" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_9" transform="translate(0, 17)" fill="#faf0ff" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_9" transform="translate(0, 33)" fill="#faf0ff" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_9" transform="translate(0, 1)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_9" transform="translate(0, 33)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_9" transform="translate(18, 17)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                        <g id="label-4" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:560;y:0;w:168;h:48" transform="translate(560, 0)">
                            <path id="body_5" transform="translate(18, 0)" fill="#feecf7" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke_8" transform="translate(18, 0)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left_5" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top_10" fill="#feecf7" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle_10" transform="translate(0, 16)" fill="#feecf7" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom_10" transform="translate(0, 32)" fill="#feecf7" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke_10" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_10" transform="translate(0, 32)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke_10" transform="translate(0, 16)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right_5" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_11" transform="translate(0, 1)" fill="#feecf7" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_11" transform="translate(0, 17)" fill="#feecf7" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_11" transform="translate(0, 33)" fill="#feecf7" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_11" transform="translate(0, 1)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_11" transform="translate(0, 33)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_11" transform="translate(18, 17)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                    </g>
                    <g id="text-4" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:48;primary:MIN;counter:CENTER" data-position="x:0;y:60;w:792;h:48" transform="translate(0, 60)">
                        <g id="Vector_58-with-terminator_3" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:516;y:16;w:44;h:16" transform="translate(516, 16)">
                            <path id="Vector_58_3" marker-start="url(#arrow)" transform="translate(1, 8)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 42 0" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:8;w:42;h:0.000"/>
                        </g>
                        <g id="Vector_58-with-terminator_4" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:236;y:16;w:44;h:16" transform="translate(236, 16)">
                            <path id="Vector_58_4" marker-start="url(#arrow)" transform="translate(1, 8)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 42 0" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:8;w:42;h:0.000"/>
                        </g>
                        <g id="text-6" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:232;h:48">
                            <g id="lines_3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract_9" transform="translate(0, 25)" fill="#f3f0ff" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_10" fill="#f3f0ff" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_11" transform="translate(182, 0)" fill="#f3f0ff" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center_3" transform="translate(0, 48)" fill="#f3f0ff" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom_4" transform="translate(0, 84)" fill="#f3f0ff" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_9" transform="translate(0, 36)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke_3" transform="translate(0, 84)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left_3" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right_3" transform="translate(200, 0)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-6-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-6" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                            <g id="Vector_58-with-terminator_5" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:66;w:16;h:44" transform="translate(108, 66)">
                                <path id="Vector_58_5" marker-start="url(#arrow)" transform="translate(7.999994277954102, 1)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 42 L 0 0" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8.000;y:1;w:0.000;h:42"/>
                            </g>
                        </g>
                        <g id="text-5" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:280;y:0;w:232;h:48" transform="translate(280, 0)">
                            <g id="lines_4" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract_12" transform="translate(0, 25)" fill="#faf0ff" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_13" fill="#faf0ff" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_14" transform="translate(182, 0)" fill="#faf0ff" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center_4" transform="translate(0, 48)" fill="#faf0ff" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom_5" transform="translate(0, 84)" fill="#faf0ff" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_10" transform="translate(0, 36)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke_4" transform="translate(0, 84)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left_4" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right_4" transform="translate(200, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-5-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-5" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="text-4_1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:560;y:0;w:232;h:48" transform="translate(560, 0)">
                            <g id="lines_5" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract_15" transform="translate(0, 25)" fill="#feecf7" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_16" fill="#feecf7" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_17" transform="translate(182, 0)" fill="#feecf7" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center_5" transform="translate(0, 48)" fill="#feecf7" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom_6" transform="translate(0, 84)" fill="#feecf7" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_11" transform="translate(0, 36)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke_5" transform="translate(0, 84)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left_5" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right_5" transform="translate(200, 0)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_11" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                </g>
                <g id="bottom_7" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 18 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:372;w:792;h:126" transform="translate(24, 372)">
                    <g id="Frame_716_2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:112;primary:MIN;counter:CENTER" data-position="x:32;y:0;w:728;h:48" transform="translate(32, 0)">
                        <g id="label-7" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:168;h:48">
                            <path id="body_6" transform="translate(18, 0)" fill="#edf4ff" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke_12" transform="translate(18, 0)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-7" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label_12" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left_6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top_12" fill="#edf4ff" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle_12" transform="translate(0, 16)" fill="#edf4ff" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom_12" transform="translate(0, 32)" fill="#edf4ff" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke_12" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_12" transform="translate(0, 32)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke_12" transform="translate(0, 16)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right_6" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_13" transform="translate(0, 1)" fill="#edf4ff" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_13" transform="translate(0, 17)" fill="#edf4ff" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_13" transform="translate(0, 33)" fill="#edf4ff" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_13" transform="translate(0, 1)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_13" transform="translate(0, 33)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_13" transform="translate(18, 17)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                        <g id="label-8" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:280;y:0;w:168;h:48" transform="translate(280, 0)">
                            <path id="body_7" transform="translate(18, 0)" fill="#e8f9ff" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke_13" transform="translate(18, 0)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-8" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label_13" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left_7" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top_14" fill="#e8f9ff" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle_14" transform="translate(0, 16)" fill="#e8f9ff" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom_14" transform="translate(0, 32)" fill="#e8f9ff" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke_14" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_14" transform="translate(0, 32)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke_14" transform="translate(0, 16)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right_7" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_15" transform="translate(0, 1)" fill="#e8f9ff" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_15" transform="translate(0, 17)" fill="#e8f9ff" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_15" transform="translate(0, 33)" fill="#e8f9ff" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_15" transform="translate(0, 1)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_15" transform="translate(0, 33)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_15" transform="translate(18, 17)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                        <g id="label-9" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:560;y:0;w:168;h:48" transform="translate(560, 0)">
                            <path id="body_8" transform="translate(18, 0)" fill="#e7fbf2" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke_14" transform="translate(18, 0)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-9" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label_14" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#3cc583" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left_8" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top_16" fill="#e7fbf2" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle_16" transform="translate(0, 16)" fill="#e7fbf2" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom_16" transform="translate(0, 32)" fill="#e7fbf2" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke_16" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_16" transform="translate(0, 32)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke_16" transform="translate(0, 16)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right_8" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_17" transform="translate(0, 1)" fill="#e7fbf2" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_17" transform="translate(0, 17)" fill="#e7fbf2" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_17" transform="translate(0, 33)" fill="#e7fbf2" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_17" transform="translate(0, 1)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_17" transform="translate(0, 33)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_17" transform="translate(18, 17)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                    </g>
                    <g id="text-4_2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:48;primary:MIN;counter:CENTER" data-position="x:0;y:60;w:792;h:48" transform="translate(0, 60)">
                        <g id="text-7" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:232;h:48">
                            <g id="lines_6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract_18" transform="translate(0, 25)" fill="#edf4ff" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_19" fill="#edf4ff" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_20" transform="translate(182, 0)" fill="#edf4ff" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center_6" transform="translate(0, 48)" fill="#edf4ff" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom_8" transform="translate(0, 84)" fill="#edf4ff" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_15" transform="translate(0, 36)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke_6" transform="translate(0, 84)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left_6" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right_6" transform="translate(200, 0)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-7-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_15" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-7" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="text-8" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:280;y:0;w:232;h:48" transform="translate(280, 0)">
                            <g id="lines_7" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract_21" transform="translate(0, 25)" fill="#e8f9ff" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_22" fill="#e8f9ff" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_23" transform="translate(182, 0)" fill="#e8f9ff" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center_7" transform="translate(0, 48)" fill="#e8f9ff" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom_9" transform="translate(0, 84)" fill="#e8f9ff" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_16" transform="translate(0, 36)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke_7" transform="translate(0, 84)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left_7" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right_7" transform="translate(200, 0)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-8-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_16" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-8" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="text-9" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:560;y:0;w:232;h:48" transform="translate(560, 0)">
                            <g id="lines_8" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract_24" transform="translate(0, 25)" fill="#e7fbf2" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_25" fill="#e7fbf2" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_26" transform="translate(182, 0)" fill="#e7fbf2" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center_8" transform="translate(0, 48)" fill="#e7fbf2" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom_10" transform="translate(0, 84)" fill="#e7fbf2" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_17" transform="translate(0, 36)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke_8" transform="translate(0, 84)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left_8" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right_8" transform="translate(200, 0)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-9-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_17" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-9" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                            <g id="Vector_58-with-terminator_6" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:66;w:16;h:44" transform="translate(108, 66)">
                                <path id="Vector_58_6" marker-start="url(#arrow)" transform="translate(7.999994277954102, 1)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 42 L 0 0" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8.000;y:1;w:0.000;h:42"/>
                            </g>
                        </g>
                        <g id="Vector_58-with-terminator_7" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:232;y:16;w:44;h:16" transform="translate(232, 16)">
                            <path id="Vector_58_7" marker-start="url(#arrow)" transform="translate(1, 7.999996185302734)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 42 0 L 0 0" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:8.000;w:42;h:0.000"/>
                        </g>
                        <g id="Vector_58-with-terminator_8" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:512;y:16;w:44;h:16" transform="translate(512, 16)">
                            <path id="Vector_58_8" marker-start="url(#arrow)" transform="translate(1, 7.999996185302734)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 42 0 L 0 0" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:8.000;w:42;h:0.000"/>
                        </g>
                    </g>
                </g>
                <g id="bottom_11" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 18 0;gap:12;primary:MIN;counter:CENTER" data-position="x:24;y:546;w:792;h:126" transform="translate(24, 546)">
                    <g id="Frame_716_3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:112;primary:MIN;counter:CENTER" data-position="x:32;y:0;w:728;h:48" transform="translate(32, 0)">
                        <g id="label-12" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:168;h:48">
                            <path id="body_9" transform="translate(18, 0)" fill="#fef2e6" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke_18" transform="translate(18, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-12" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label_18" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left_9" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top_18" fill="#fef2e6" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle_18" transform="translate(0, 16)" fill="#fef2e6" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom_18" transform="translate(0, 32)" fill="#fef2e6" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke_18" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_18" transform="translate(0, 32)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke_18" transform="translate(0, 16)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right_9" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_19" transform="translate(0, 1)" fill="#fef2e6" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_19" transform="translate(0, 17)" fill="#fef2e6" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_19" transform="translate(0, 33)" fill="#fef2e6" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_19" transform="translate(0, 1)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_19" transform="translate(0, 33)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_19" transform="translate(18, 17)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                        <g id="label-11" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:280;y:0;w:168;h:48" transform="translate(280, 0)">
                            <path id="body_10" transform="translate(18, 0)" fill="#fefbdb" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke_19" transform="translate(18, 0)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-11" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label_19" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left_10" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top_20" fill="#fefbdb" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle_20" transform="translate(0, 16)" fill="#fefbdb" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom_20" transform="translate(0, 32)" fill="#fefbdb" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke_20" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_20" transform="translate(0, 32)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke_20" transform="translate(0, 16)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right_10" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_21" transform="translate(0, 1)" fill="#fefbdb" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_21" transform="translate(0, 17)" fill="#fefbdb" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_21" transform="translate(0, 33)" fill="#fefbdb" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_21" transform="translate(0, 1)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_21" transform="translate(0, 33)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_21" transform="translate(18, 17)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                        <g id="label-10" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 18 12 18;gap:10;primary:CENTER;counter:CENTER" data-position="x:560;y:0;w:168;h:48" transform="translate(560, 0)">
                            <path id="body_11" transform="translate(18, 0)" fill="#f2fae1" d="M 0 0 L 132 0 L 132 48 L 0 48 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <path id="body-stroke_20" transform="translate(18, 0)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 132 0 M 132 48 L 0 48" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:132;h:48"/>
                            <g id="tx-ct-10" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:12;w:132;h:24" fill="#ff00001a" transform="translate(18, 12)">
                                <text id="Label_20" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132;h:24" fill="#93c332" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <g id="left_11" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:48">
                                <path id="left-top_22" fill="#f2fae1" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16 L 18 16 L 18 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-middle_22" transform="translate(0, 16)" fill="#f2fae1" d="M 18 -9.5367e-7 L 18 16 L 0 16 L 0 0 L 18 -9.5367e-7 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:18;h:16"/>
                                <path id="left-bottom_22" transform="translate(0, 32)" fill="#f2fae1" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0 L 18 0 L 18 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-top-stroke_22" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 0 L 16 0 C 7.1634 0 0 7.1634 0 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_22" transform="translate(0, 32)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 16 L 16 16 C 7.1634 16 0 8.8366 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:18;h:16"/>
                                <path id="left-_middle-stroke_22" transform="translate(0, 16)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:16;w:0;h:16"/>
                            </g>
                            <g id="right_11" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:150;y:-1;w:18;h:50" transform="translate(150, -1)">
                                <path id="left-top_23" transform="translate(0, 1)" fill="#f2fae1" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16 L 0 16 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-middle_23" transform="translate(0, 17)" fill="#f2fae1" d="M 0 16 L 0 0 L 18 0 L 18 16 L 0 16 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:17;w:18;h:16"/>
                                <path id="left-bottom_23" transform="translate(0, 33)" fill="#f2fae1" d="M 2 16 L 0 16 L 0 0 L 18 0 C 18 8.8366 10.8366 16 2 16 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-top-stroke_23" transform="translate(0, 1)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2 0 C 10.8366 0 18 7.1634 18 16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:18;h:16.000"/>
                                <path id="left-_bottom-stroke_23" transform="translate(0, 33)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 2 16 C 10.8366 16 18 8.8366 18 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:33;w:18;h:16"/>
                                <path id="left-_middle-stroke_23" transform="translate(18, 17)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 16 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:17;w:0;h:16"/>
                            </g>
                        </g>
                    </g>
                    <g id="text-4_3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:48;primary:MIN;counter:CENTER" data-position="x:0;y:60;w:792;h:48" transform="translate(0, 60)">
                        <g id="Vector_58-with-terminator_9" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:516;y:16;w:44;h:16" transform="translate(516, 16)">
                            <path id="Vector_58_9" marker-start="url(#arrow)" transform="translate(1, 8)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 42 0" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:8;w:42;h:0.000"/>
                        </g>
                        <g id="Vector_58-with-terminator_10" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:236;y:16;w:44;h:16" transform="translate(236, 16)">
                            <path id="Vector_58_10" marker-start="url(#arrow)" transform="translate(1, 8)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 42 0" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:8;w:42;h:0.000"/>
                        </g>
                        <g id="text-12" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:232;h:48">
                            <g id="lines_9" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract_27" transform="translate(0, 25)" fill="#fef2e6" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_28" fill="#fef2e6" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_29" transform="translate(182, 0)" fill="#fef2e6" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center_9" transform="translate(0, 48)" fill="#fef2e6" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom_12" transform="translate(0, 84)" fill="#fef2e6" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_21" transform="translate(0, 36)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke_9" transform="translate(0, 84)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left_9" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right_9" transform="translate(200, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-12-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_21" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-12" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="text-11" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:280;y:0;w:232;h:48" transform="translate(280, 0)">
                            <g id="lines_10" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract_30" transform="translate(0, 25)" fill="#fefbdb" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_31" fill="#fefbdb" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_32" transform="translate(182, 0)" fill="#fefbdb" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center_10" transform="translate(0, 48)" fill="#fefbdb" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom_13" transform="translate(0, 84)" fill="#fefbdb" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_22" transform="translate(0, 36)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke_10" transform="translate(0, 84)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left_10" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right_10" transform="translate(200, 0)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-11-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_22" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-11" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="text-10" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 24 12 24;gap:10;primary:MIN;counter:CENTER" data-position="x:560;y:0;w:232;h:48" transform="translate(560, 0)">
                            <g id="lines_11" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-36;w:232;h:102" transform="translate(0, -36)">
                                <path id="Subtract_33" transform="translate(0, 25)" fill="#f2fae1" d="M 232 0 L 232 23 L 0 23 L 0 0 L 232 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:25;w:232;h:23"/>
                                <path id="Subtract_34" fill="#f2fae1" d="M 0 25 L 0 16 C 0 7.1633 7.1631 0 16 0 L 31 0 L 31 8 C 31 18 39.1631 25 48 25 L 50 25 L 0 25 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50;h:25"/>
                                <path id="Subtract_35" transform="translate(182, 0)" fill="#f2fae1" d="M 50 25 L 50 16 C 50 7.1633 42.8369 0 34 0 L 19 0 L 19 8 C 19 18 10.8369 25 2 25 L 0 25 L 50 25 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:182;y:0;w:50;h:25"/>
                                <path id="center_11" transform="translate(0, 48)" fill="#f2fae1" d="M 0 0 L 232 0 L 232 36 L 0 36 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:48;w:232;h:36"/>
                                <path id="bottom_14" transform="translate(0, 84)" fill="#f2fae1" d="M 0 0 L 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="body-stroke_23" transform="translate(0, 36)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 48 M 0 48 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:232;h:48"/>
                                <path id="bottom-stroke_11" transform="translate(0, 84)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 232 0 L 232 2 C 232 10.8366 224.8366 18 216 18 L 16 18 C 7.1634 18 0 10.8366 0 2 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84;w:232;h:18"/>
                                <path id="top-stroke-left_11" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 36 L 0 16 C 0 7.1634 7.1634 0 16 0 L 32 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:32;h:36"/>
                                <path id="top-stroke-right_11" transform="translate(200, 0)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 32 36 L 32 16 C 32 7.1634 24.8366 0 16 0 L 0 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:200;y:0;w:32;h:36"/>
                            </g>
                            <g id="tx-ct-10-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:12;w:184;h:24" fill="#ff00001a" transform="translate(24, 12)">
                                <text id="Label_23" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-10" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:104;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 104, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
    <defs >
        <marker id="arrow" viewBox="-13 -13 26 26" refX="0" refY="0" markerWidth="13" markerHeight="13" markerUnits="strokeWidth" orient="auto-start-reverse">
            <path d="M -8 -6.5 L -1.5 0 L -8 6.5" stroke="#666666" fill="none" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"/>
        </marker>
    </defs>
</svg>
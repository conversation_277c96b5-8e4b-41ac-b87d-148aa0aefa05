<svg xmlns="http://www.w3.org/2000/svg" width="912" height="516">
    <g id="list-backpack-v1--family--4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L912 0 L912 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:912;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:912;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 0 108 0;gap:10;primary:MIN;counter:MIN" data-position="x:0;y:0;w:912;h:468">
                <g id="Frame_1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:MIN" data-position="x:0;y:24;w:912;h:144" transform="translate(0, 24)">
                    <g id="top" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:MAX" data-position="x:0;y:0;w:912;h:60">
                        <g id="content-left" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:41;primary:MIN;counter:MAX" data-position="x:0;y:0;w:444;h:60">
                            <g id="row" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:252;y:0;w:192;h:60" transform="translate(252, 0)">
                                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:192;h:60">
                                    <g id="tx-ct-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                    </g>
                                    <g id="tx-ct-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                    </g>
                                    <rect id="bt-cc-remove-1" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 60)" width="24" height="24" rx="0" ry="0"/>
                                    <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                                </g>
                            </g>
                        </g>
                        <g id="content-right" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:40;primary:MIN;counter:MIN" data-position="x:468;y:0;w:444;h:60" transform="translate(468, 0)">
                            <g id="row_1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:192;h:60">
                                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:192;h:60">
                                    <g id="tx-ct-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                    </g>
                                    <g id="tx-ct-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                    </g>
                                    <rect id="bt-cc-remove-2" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 60)" width="24" height="24" rx="0" ry="0"/>
                                    <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                                </g>
                            </g>
                        </g>
                        <g id="lines" data-entity-classes="KeepStroke" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:240;y:114.797;w:432;h:302.400" transform="translate(240, 114.796875)">
                            <g id="g-2" data-entity-classes="KeepStroke" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96.723;y:-0.046;w:229.270;h:66.562" transform="translate(96.7232666015625, -0.0458984375)">
                                <path id="Vector_15-fill" fill="#fef2e6" d="M 9.0219 63.3152 L 9.0219 59.2565 L 1.6403 59.2565 C 0.7344 59.2565 0 58.5297 0 57.6331 L 0 15.4229 C 0 14.5263 0.7344 13.7995 1.6403 13.7995 L 9.0219 13.7995 L 9.0219 9.7408 C 9.0219 7.9476 10.4907 6.4939 12.3026 6.4939 L 66.2445 6.4939 L 66.2445 0 L 80.1875 0 L 80.1875 6.4939 L 149.9023 6.4939 L 149.9023 0 L 163.8453 0 L 163.8453 6.4939 L 216.967 6.4939 C 218.7789 6.4939 220.2477 7.9476 220.2477 9.7408 L 220.2477 13.7995 L 227.6293 13.7995 C 228.5352 13.7995 229.2696 14.5263 229.2696 15.4229 L 229.2696 57.6331 C 229.2696 58.5297 228.5352 59.2565 227.6293 59.2565 L 220.2477 59.2565 L 220.2477 63.3152 C 220.2477 65.1084 218.7789 66.5621 216.967 66.5621 L 200.7532 66.5621 L 200.7532 64.9387 C 200.7532 62.2488 198.5499 60.0683 195.8321 60.0683 L 149.9023 60.0683 L 34.2577 60.0683 C 31.5399 60.0683 29.3367 62.2488 29.3367 64.9387 L 29.3367 66.5621 L 12.3026 66.5621 C 10.4907 66.5621 9.0219 65.1084 9.0219 63.3152 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:229.270;h:66.562"/>
                                <path id="Vector_15-stroke" fill="none" stroke="#db8333" stroke-width="1.7999999523162842" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 12.3026 66.5621 L 18.5274 66.5621 L 29.3367 66.5621 L 29.3367 64.9387 C 29.3367 62.2488 31.5399 60.0683 34.2577 60.0683 L 149.9023 60.0683 L 163.8453 60.0683 L 195.8321 60.0683 C 198.5499 60.0683 200.7532 62.2488 200.7532 64.9387 L 200.7532 66.5621 L 210.0832 66.5621 L 216.967 66.5621 C 218.7789 66.5621 220.2477 65.1084 220.2477 63.3152 L 220.2477 59.2565 L 227.6293 59.2565 C 228.5352 59.2565 229.2696 58.5297 229.2696 57.6331 L 229.2696 15.4229 C 229.2696 14.5263 228.5352 13.7995 227.6293 13.7995 L 220.2477 13.7995 L 220.2477 9.7408 C 220.2477 7.9476 218.7789 6.4939 216.967 6.4939 L 210.0832 6.4939 L 18.5274 6.4939 L 12.3026 6.4939 C 10.4907 6.4939 9.0219 7.9476 9.0219 9.7408 L 9.0219 13.7995 L 1.6403 13.7995 C 0.7344 13.7995 0 14.5263 0 15.4229 L 0 57.6331 C 0 58.5297 0.7344 59.2565 1.6403 59.2565 L 9.0219 59.2565 L 9.0219 63.3152 C 9.0219 65.1084 10.4907 66.5621 12.3026 66.5621 Z M 66.2445 0 L 66.2445 13.7995 L 66.2445 19.4816 L 66.2445 47.0805 L 66.2445 51.9509 L 66.2445 60.0683 L 80.1875 60.0683 L 80.1875 51.9509 L 80.1875 47.0805 L 80.1875 19.4816 L 80.1875 13.7995 L 80.1875 0 L 66.2445 0 Z M 9.0219 59.2565 L 9.0219 13.7995 M 220.2477 13.7995 L 220.2477 59.2565 M 149.9023 60.0683 L 149.9023 51.9509 M 163.8453 60.0683 L 163.8453 51.9509 M 66.2445 13.7995 L 80.1875 13.7995 M 66.2445 19.4816 L 80.1875 19.4816 M 66.2445 47.0805 L 80.1875 47.0805 M 66.2445 51.9509 L 80.1875 51.9509 M 149.9023 13.7995 L 149.9023 0 L 163.8453 0 L 163.8453 13.7995 M 149.9023 13.7995 L 163.8453 13.7995 M 149.9023 13.7995 L 149.9023 19.4816 M 163.8453 13.7995 L 163.8453 19.4816 M 149.9023 19.4816 L 163.8453 19.4816 M 149.9023 19.4816 L 149.9023 47.0805 M 163.8453 19.4816 L 163.8453 47.0805 M 149.9023 47.0805 L 163.8453 47.0805 M 149.9023 47.0805 L 149.9023 51.9509 M 163.8453 47.0805 L 163.8453 51.9509 M 149.9023 51.9509 L 163.8453 51.9509 M 18.5274 6.4939 L 18.5274 17.0464 M 18.5274 26.7872 L 18.5274 47.0805 M 18.5274 57.6331 L 18.5274 66.5621 M 210.0832 6.4939 L 210.0832 17.8581 M 210.0832 26.7872 L 210.0832 47.0805 M 210.0832 57.6331 L 210.0832 66.5621" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:229.270;h:66.562"/>
                            </g>
                            <g id="g-1" data-entity-classes="KeepStroke" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:80.453;y:60.596;w:260.626;h:215.960" transform="translate(80.45263671875, 60.595703125)">
                                <path id="Subtract" transform="translate(0, 0.0078125)" fill="#fefbdb" d="M 212.1182 0 C 214.8426 0 217.0508 2.1975 217.0508 4.9082 L 217.0508 9.8164 C 220.6135 11.4524 227.7393 17.0149 227.7393 26.1768 L 227.7393 50.7168 L 246.6494 63.8057 L 246.6494 76.8936 L 241.7158 76.8936 L 238.4277 104.7061 L 227.7393 104.7061 L 227.7393 114.5225 L 255.0322 125.3848 C 258.4092 126.7288 260.6259 129.9963 260.626 133.6309 L 260.626 137.5977 C 259.9974 137.2618 259.4354 136.8174 258.9678 136.2871 C 257.8837 137.5162 256.2989 138.2928 254.5313 138.293 C 252.7627 138.293 251.175 137.5171 250.0908 136.2871 L 250.0938 197.9609 L 227.7393 197.9609 C 227.7392 204.124 223.3525 209.1191 223.3525 209.1191 C 220.2276 212.9098 215.1405 215.957 207.1855 215.957 L 51.7959 215.957 C 46.3147 215.4115 35.3536 211.0488 35.3535 197.9609 L 12.4766 197.9609 C 11.6957 197.9609 10.9379 197.8572 10.2148 197.667 C 9.5436 196.0796 8.562 194.4678 7.2041 192.8633 C 6.1768 191.6495 4.9655 190.6584 3.6377 189.8613 C 3.6304 189.7777 3.6222 189.6937 3.6172 189.6094 L 2.6396 173.0684 L 15.7549 173.0684 C 18.2284 173.0684 20.2342 171.0374 20.2344 168.5322 C 20.2344 166.0269 18.2285 163.9951 15.7549 163.9951 L 1.8447 163.9951 C 1.7451 163.6871 1.6848 163.3661 1.6621 163.04 L 1.6445 162.7861 L 0 140.6992 L 35.3535 140.6992 L 35.3535 26.1768 C 35.3535 22.6125 37.3262 14.3504 45.2188 9.8164 L 45.2188 4.9082 C 45.2188 2.1975 47.4279 0 50.1523 0 L 212.1182 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0.008;w:260.626;h:215.957"/>
                                <path id="Vector-stroke" transform="translate(0, 0.0078125)" fill="none" stroke="#d1bd08" stroke-width="1.7999999523162842" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 207.1853 215.9572 L 51.7963 215.9572 C 46.3152 215.4119 35.353 211.0491 35.353 197.9608 L 35.353 140.6994 L 35.353 26.1766 C 35.353 22.6123 37.3262 14.3502 45.219 9.8162 L 45.219 4.9081 C 45.219 2.1974 47.4276 0 50.152 0 L 121.6802 0 L 131.5462 0 L 141.4122 0 L 212.1182 0 C 214.8427 0 217.0512 2.1974 217.0512 4.9081 L 217.0512 9.8162 C 220.6139 11.4523 227.7393 17.0148 227.7393 26.1766 L 227.7393 50.7172 L 246.6491 63.8055 L 246.6491 69.9407 L 246.6491 76.8939 L 241.7161 76.8939 L 238.4275 104.7065 L 227.7393 104.7065 L 227.7393 114.5228 L 227.7393 147.2436 L 227.7393 156.2418 L 227.7393 197.9631 C 227.7393 204.2039 223.353 209.1195 223.353 209.1195 C 220.228 212.9102 215.1404 215.9572 207.1853 215.9572 Z M 35.353 197.9608 L 10.3244 197.9608 M 45.219 9.8162 L 45.219 64.6236 C 45.219 70.6303 48.9594 82.9774 63.3066 88.0013 M 217.0512 9.8162 L 217.0512 64.6236 C 217.0512 76.1504 207.5649 84.9505 199.9849 88.0013 M 78.1056 89.1642 L 67.4174 89.1642 C 65.959 88.8417 64.5903 88.4508 63.3066 88.0013 M 78.1056 89.1642 L 78.1056 70.3497 M 78.1056 89.1642 L 78.1056 114.5228 M 87.9715 50.7172 L 78.1056 50.7172 L 78.1056 70.3497 M 87.9715 50.7172 L 97.8375 50.7172 L 97.8375 70.3497 M 87.9715 50.7172 L 87.9715 58.0794 M 87.9715 50.7172 L 87.9715 42.537 M 97.8375 89.1642 L 165.2549 89.1642 M 97.8375 89.1642 L 97.8375 70.3497 M 97.8375 89.1642 L 97.8375 114.5228 M 165.2549 89.1642 L 165.2549 97.3444 M 165.2549 89.1642 L 165.2549 69.9407 M 175.943 50.7172 L 165.2549 50.7172 L 165.2549 69.9407 M 175.943 50.7172 L 184.9868 50.7172 L 184.9868 69.9407 M 175.943 50.7172 L 175.943 42.537 M 175.943 50.7172 L 175.943 58.0794 M 184.9868 89.1642 L 194.8528 89.1642 C 196.328 89.1642 198.0974 88.761 199.9849 88.0013 M 184.9868 89.1642 L 184.9868 97.3444 M 184.9868 89.1642 L 184.9868 69.9407 M 78.1056 122.703 L 73.9947 122.703 C 73.0866 122.703 72.3504 123.4354 72.3504 124.339 L 72.3504 131.7012 C 72.3504 132.6048 73.0866 133.3372 73.9947 133.3372 L 78.1056 133.3372 M 78.1056 122.703 L 97.8375 122.703 M 78.1056 122.703 L 78.1056 114.5228 M 78.1056 133.3372 L 78.1056 137.4273 C 78.1056 138.3309 78.8417 139.0634 79.7499 139.0634 L 96.1932 139.0634 C 97.1013 139.0634 97.8375 138.3309 97.8375 137.4273 L 97.8375 133.3372 M 78.1056 133.3372 L 97.8375 133.3372 M 97.8375 133.3372 L 101.9483 133.3372 C 102.8564 133.3372 103.5926 132.6048 103.5926 131.7012 L 103.5926 124.339 C 103.5926 123.4354 102.8564 122.703 101.9483 122.703 L 97.8375 122.703 M 97.8375 122.703 L 97.8375 114.5228 M 165.2549 97.3444 L 161.1441 97.3444 C 160.2359 97.3444 159.4998 98.0768 159.4998 98.9804 L 159.4998 106.3426 C 159.4998 107.2461 160.2359 107.9786 161.1441 107.9786 L 165.2549 107.9786 M 165.2549 97.3444 L 184.9868 97.3444 M 165.2549 107.9786 L 184.9868 107.9786 M 165.2549 107.9786 L 165.2549 114.5228 M 184.9868 107.9786 L 189.0977 107.9786 C 190.0058 107.9786 190.742 107.2461 190.742 106.3426 L 190.742 98.9804 C 190.742 98.0768 190.0058 97.3444 189.0977 97.3444 L 184.9868 97.3444 M 184.9868 107.9786 L 184.9868 114.5228 M 78.1056 70.3497 L 73.9947 70.3497 C 72.1785 70.3497 70.7061 68.8847 70.7061 67.0776 L 70.7061 45.8091 C 70.7061 44.002 72.1785 42.537 73.9947 42.537 L 78.1056 42.537 M 97.8375 70.3497 L 101.9483 70.3497 C 103.7646 70.3497 105.237 68.8847 105.237 67.0776 L 105.237 45.8091 C 105.237 44.002 103.7646 42.537 101.9483 42.537 L 97.8375 42.537 M 87.9715 42.537 L 87.9715 37.6289 M 87.9715 42.537 L 78.1056 42.537 M 87.9715 42.537 L 97.8375 42.537 M 87.9715 63.8055 L 87.9715 67.0776 M 87.9715 71.1677 L 87.9715 74.4398 M 78.1056 42.537 L 78.1056 28.6307 M 78.1056 28.6307 L 97.8375 28.6307 M 78.1056 28.6307 L 70.7061 28.6307 M 97.8375 28.6307 L 97.8375 42.537 M 97.8375 28.6307 L 105.237 28.6307 M 165.2549 69.9407 L 157.8554 69.9407 L 157.8554 42.537 L 165.2549 42.537 M 184.9868 69.9407 L 192.3863 69.9407 L 192.3863 42.537 L 184.9868 42.537 M 175.943 37.6289 L 175.943 42.537 M 175.943 42.537 L 165.2549 42.537 M 175.943 42.537 L 184.9868 42.537 M 175.943 63.8055 L 175.943 67.0776 M 175.943 71.1677 L 175.943 74.4398 M 165.2549 42.537 L 165.2549 28.6307 M 165.2549 28.6307 L 184.9868 28.6307 M 165.2549 28.6307 L 157.8554 28.6307 M 184.9868 28.6307 L 184.9868 42.537 M 184.9868 28.6307 L 192.3863 28.6307 M 78.1056 114.5228 L 63.3066 114.5228 M 184.9868 114.5228 L 184.9868 137.4273 C 184.9868 138.3309 184.2506 139.0634 183.3425 139.0634 L 166.8992 139.0634 C 165.9911 139.0634 165.2549 138.3309 165.2549 137.4273 L 165.2549 114.5228 M 184.9868 114.5228 L 199.9849 114.5228 M 165.2549 114.5228 L 97.8375 114.5228 M 58.3736 149.6976 L 58.3736 114.5228 L 63.3066 114.5228 M 58.3736 149.6976 L 58.3736 182.4184 C 58.3736 190.5986 69.2262 205.323 83.0385 205.323 L 179.2317 205.323 C 189.0977 205.323 204.7188 195.5067 204.7188 182.4184 L 204.7188 149.6976 M 58.3736 149.6976 L 204.7188 149.6976 M 204.7188 149.6976 L 204.7188 114.5228 L 199.9849 114.5228 M 121.6802 0 L 121.6802 17.9964 C 121.6802 18.9 122.4164 19.6325 123.3246 19.6325 L 139.7678 19.6325 C 140.676 19.6325 141.4122 18.9 141.4122 17.9964 L 141.4122 0 M 131.5462 0 L 131.5462 12.2703 M 63.3066 88.0013 L 63.3066 114.5228 M 199.9849 88.0013 L 199.9849 114.5228 M 227.7393 50.7172 L 227.7393 69.9407 M 246.6491 76.8939 C 246.6491 76.8939 246.6491 84.3121 251.5821 86.8334 M 241.7161 76.8939 L 227.7393 76.8939 M 227.7393 76.8939 L 227.7393 104.7065 M 227.7393 76.8939 L 227.7393 69.9407 M 227.7393 69.9407 L 246.6491 69.9407 M 227.7393 114.5228 L 255.0327 125.3851 C 258.4097 126.7291 260.6259 129.9963 260.6259 133.631 L 260.6259 137.4273 M 227.7393 147.2436 L 250.1058 147.2436 M 227.7393 156.2418 L 250.1058 156.2418 M 35.353 140.6994 L 0 140.6994 L 1.6443 162.7859 L 1.7918 163.9956 M 227.7393 197.9631 L 250.1058 197.9631 M 2.6403 173.0878 L 3.6322 189.8655 M 115.1029 126.7931 L 115.1029 137.4273 C 115.1029 138.3309 115.8391 139.0634 116.7472 139.0634 L 147.9895 139.0634 C 148.8976 139.0634 149.6338 138.3309 149.6338 137.4273 L 149.6338 126.7931 C 149.6338 125.8895 148.8976 125.157 147.9895 125.157 L 116.7472 125.157 C 115.8391 125.157 115.1029 125.8895 115.1029 126.7931 Z M 90.438 163.604 L 90.438 189.7806 C 90.438 191.5877 91.9104 193.0527 93.7267 193.0527 L 170.1879 193.0527 C 172.0042 193.0527 173.4765 191.5877 173.4765 189.7806 L 173.4765 163.604 C 173.4765 161.7968 172.0042 160.3319 170.1879 160.3319 L 93.7267 160.3319 C 91.9104 160.3319 90.438 161.7968 90.438 163.604 Z M 141.4122 70.3497 C 141.4122 75.7711 136.995 80.1659 131.5462 80.1659 C 126.0974 80.1659 121.6802 75.7711 121.6802 70.3497 C 121.6802 64.9283 126.0974 60.5335 131.5462 60.5335 C 136.995 60.5335 141.4122 64.9283 141.4122 70.3497 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0.008;w:260.626;h:215.957"/>
                            </g>
                            <g id="g-3" data-entity-classes="KeepStroke" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:17.375;y:100.536;w:88.405;h:176.020" transform="translate(17.3751220703125, 100.5361328125)">
                                <path id="Vector" transform="translate(5.1341552734375, 1.2048687920854576e-15)" fill="#ffedeb" d="M 13.5036 4.4375 L 13.5036 9.44 C 13.5036 11.8909 15.4905 13.8776 17.9414 13.8775 L 58.7416 13.8744 C 61.1922 13.8742 63.1787 11.8884 63.1787 9.4378 L 63.1787 4.4403 C 63.1787 1.9898 61.1923 0.0039 58.7417 0.0037 L 17.9415 1.871e-8 C 15.4906 -0.0002 13.5036 1.9866 13.5036 4.4375 Z M 8.9578 63.024 L 69.2195 63.024 C 68.6766 60.2748 65.9621 54.7762 59.4473 54.7762 C 58.9044 52.3018 56.5157 47.3531 51.3039 47.3531 L 51.3039 36.6309 L 57.0043 36.6309 C 56.7328 33.8817 54.7241 28.3831 48.8608 28.3831 C 48.8608 26.7335 47.8836 23.4344 43.9748 23.4344 L 34.2026 23.4344 C 30.2937 23.4344 29.3165 26.7335 29.3165 28.3831 C 23.4532 28.3831 21.4445 33.8817 21.173 36.6309 L 26.8734 36.6309 L 26.8734 47.3531 C 21.6616 47.3531 19.2729 52.3018 18.73 54.7762 C 12.2152 54.7762 9.5007 60.2748 8.9578 63.024 Z M 13.0295 152.9256 C 16.6255 148.6765 22.476 147.1521 27.6878 147.1521 L 27.6878 133.1307 L 4.4789 133.1307 C 2.1425 133.1307 0.224 131.3189 0.0183 129.0068 C 0.0062 128.871 0 128.7334 0 128.5944 C 0 126.0891 2.0053 124.0581 4.4789 124.0581 L 27.6878 124.0581 L 29.1675 116.1356 L 34.2069 116.1356 L 34.2069 111.1842 L 43.3836 111.1842 L 43.3836 116.1356 L 49.0172 116.1356 L 50.4916 124.0581 L 73.6984 124.0581 C 76.172 124.0581 78.1773 126.0891 78.1773 128.5944 C 78.1773 131.0997 76.172 133.1307 73.6984 133.1307 L 50.4895 133.1307 L 50.4895 147.1521 C 55.7013 147.1521 61.5518 148.6765 65.1478 152.9256 C 68.7628 157.1972 69.7125 161.52 69.2313 165.2973 C 68.5976 170.2721 65.482 174.3005 62.7047 176.0195 L 15.4726 176.0195 C 12.6953 174.3005 9.5797 170.2721 8.946 165.2973 C 8.4648 161.52 9.4145 157.1972 13.0295 152.9256 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:5.134;y:0;w:78.177;h:176.020"/>
                                <path id="Vector_1" transform="translate(0.2350478321313858, 1.2048687920854576e-15)" fill="none" stroke="#df5e59" stroke-width="1.7999999523162842" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 34.0661 116.131 L 32.5869 124.0581 L 55.3887 124.0581 L 53.9095 116.131 L 48.256 116.131 L 39.1017 116.131 L 34.0661 116.131 Z M 48.256 116.131 L 48.256 111.1729 L 39.1017 111.1729 L 39.1017 116.131 L 48.256 116.131 Z M 32.5869 147.1521 C 27.3751 147.1521 21.5246 148.6765 17.9287 152.9256 C 14.3136 157.1972 13.364 161.52 13.8452 165.2973 M 32.5869 147.1521 L 32.5869 140.5538 M 32.5869 147.1521 L 55.3887 147.1521 M 32.5869 140.5538 L 32.5869 133.1307 M 32.5869 140.5538 L 39.1017 140.5538 M 32.5869 140.5538 L 27.7009 140.5538 M 32.5869 133.1307 L 9.3781 133.1307 C 7.0417 133.1307 5.1231 131.3189 4.9174 129.0068 C 4.9053 128.871 4.8991 128.7334 4.8991 128.5944 M 32.5869 133.1307 L 55.3887 133.1307 M 17.9287 124.0581 L 9.3781 124.0581 C 6.9044 124.0581 4.8991 126.0891 4.8991 128.5944 M 17.9287 124.0581 L 20.3718 63.024 M 17.9287 124.0581 L 32.5869 124.0581 M 20.3718 63.024 L 13.857 63.024 C 14.3999 60.2748 17.1143 54.7762 23.6291 54.7762 M 20.3718 63.024 L 67.6038 63.024 M 23.6291 54.7762 C 24.172 52.3018 26.5608 47.3531 31.7726 47.3531 M 23.6291 54.7762 L 64.3465 54.7762 M 31.7726 47.3531 L 31.7726 36.6309 M 31.7726 47.3531 L 56.203 47.3531 M 31.7726 36.6309 L 26.0722 36.6309 C 26.3436 33.8817 28.3523 28.3831 34.2156 28.3831 M 31.7726 36.6309 L 56.203 36.6309 M 34.2156 28.3831 C 34.2156 26.7335 35.1929 23.4344 39.1017 23.4344 L 48.8739 23.4344 C 52.7828 23.4344 53.76 26.7335 53.76 28.3831 M 34.2156 28.3831 L 53.76 28.3831 M 55.3887 147.1521 C 60.6005 147.1521 66.451 148.6765 70.0469 152.9256 C 73.662 157.1972 74.6117 161.52 74.1305 165.2973 M 55.3887 147.1521 L 55.3887 133.1307 M 55.3887 133.1307 L 78.5976 133.1307 C 81.0712 133.1307 83.0765 131.0997 83.0765 128.5944 C 83.0765 126.0891 81.0712 124.0581 78.5976 124.0581 L 70.0469 124.0581 M 70.0469 124.0581 L 67.6038 63.024 M 70.0469 124.0581 L 55.3887 124.0581 M 67.6038 63.024 L 74.1187 63.024 C 73.5758 60.2748 70.8613 54.7762 64.3465 54.7762 M 64.3465 54.7762 C 63.8036 52.3018 61.4149 47.3531 56.203 47.3531 M 56.203 47.3531 L 56.203 36.6309 M 56.203 36.6309 L 61.9035 36.6309 C 61.632 33.8817 59.6233 28.3831 53.76 28.3831 M 27.7009 140.5538 C 27.7009 139.1791 26.8865 136.4299 23.6291 136.4299 C 20.3717 136.4299 19.5574 139.1791 19.5574 140.5538 C 19.5574 141.9284 20.3717 144.6777 23.6291 144.6777 C 26.8865 144.6777 27.7009 141.9284 27.7009 140.5538 Z M 18.4028 6.9387 L 18.4028 4.4375 C 18.4028 1.9866 20.3897 -0.0002 22.8406 1.871e-8 L 63.6408 0.0037 C 66.0914 0.0039 68.0779 1.9898 68.0779 4.4403 L 68.0779 9.4378 C 68.0779 11.8884 66.0914 13.8742 63.6408 13.8744 L 22.8406 13.8775 C 20.3897 13.8776 18.4028 11.8909 18.4028 9.44 L 18.4028 6.9387 Z M 18.4028 6.9387 L 17.3119 6.9387 C 12.6737 6.9389 8.8179 10.5106 8.4633 15.1353 L 0.0088 125.4078 C -0.1228 127.1244 1.2336 128.5903 2.9552 128.5923 L 4.8991 128.5944 M 13.8452 165.2973 C 14.4789 170.2721 17.5945 174.3005 20.3718 176.0195 L 67.6038 176.0195 C 70.3811 174.3005 73.4967 170.2721 74.1305 165.2973 M 13.8452 165.2973 L 74.1305 165.2973 M 68.0518 6.9389 L 70.6229 6.939 C 75.2612 6.939 79.1172 10.5108 79.4718 15.1355 L 87.9259 125.4043 C 88.0577 127.1222 86.6992 128.5887 84.9763 128.5887 L 83.1148 128.5887" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:88.405;h:176.020"/>
                            </g>
                            <g id="g-4" data-entity-classes="KeepStroke" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:330.543;y:130.119;w:44.381;h:145.740" transform="translate(330.54296875, 130.119140625)">
                                <path id="Union" transform="translate(0, 22.9326171875)" fill="#feecf7" d="M 39.9403 45.8478 C 38.1722 45.8478 36.587 45.0708 35.5028 43.8414 C 34.4186 45.0708 32.8334 45.8478 31.0653 45.8478 C 29.2973 45.8478 27.712 45.0708 26.6278 43.8414 C 25.5436 45.0708 23.9584 45.8478 22.1903 45.8478 C 20.4223 45.8478 18.8371 45.0708 17.7529 43.8414 C 16.6687 45.0708 15.0834 45.8478 13.3154 45.8478 C 11.5473 45.8478 9.9621 45.0708 8.8779 43.8414 C 7.7937 45.0708 6.2085 45.8478 4.4404 45.8478 C 2.6717 45.8478 1.0842 45.0717 0 43.8414 L 0.003 116.8597 C 0.003 120.125 2.6482 122.7729 5.9135 122.7763 L 38.455 122.8069 C 41.725 122.8103 44.3778 120.1603 44.3778 116.8903 L 44.3807 43.8414 C 43.2965 45.0717 41.709 45.8478 39.9403 45.8478 Z M 7.3988 16.2707 L 10.3571 16.2707 L 34.0236 16.2707 L 36.982 16.2707 L 36.982 15.0236 L 36.982 2.2187 C 36.982 0.9934 35.9886 0 34.7632 0 L 10.3571 0 C 8.7233 0 7.3988 1.3245 7.3988 2.9583 L 7.3988 15.0236 L 7.3988 16.2707 Z M 2.9613 15.0236 L 7.3988 15.0236 L 7.3988 2.9583 L 2.9613 2.9583 L 2.9613 15.0236 Z M 41.4194 2.2187 L 36.982 2.2187 L 36.982 15.0236 L 41.4194 15.0236 L 41.4194 2.2187 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:22.933;w:44.381;h:122.807"/>
                                <path id="Union_1" fill="none" stroke="#d95da7" stroke-width="1.7999999523162842" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 39.9403 68.7809 C 38.1722 68.7809 36.587 68.0039 35.5028 66.7745 C 34.4186 68.0039 32.8333 68.7809 31.0653 68.7809 C 29.2973 68.7809 27.712 68.0039 26.6278 66.7745 C 25.5436 68.0039 23.9584 68.7809 22.1903 68.7809 C 20.4223 68.7809 18.8371 68.0039 17.7529 66.7745 C 16.6687 68.0039 15.0834 68.7809 13.3154 68.7809 C 11.5473 68.7809 9.9621 68.0039 8.8779 66.7745 C 7.7937 68.0039 6.2085 68.7809 4.4404 68.7809 C 2.6717 68.7809 1.0842 68.0048 0 66.7745 L 0.003 139.7928 C 0.003 143.058 2.6482 145.706 5.9135 145.7094 L 38.455 145.74 C 41.725 145.7434 44.3778 143.0934 44.3778 139.8234 L 44.3807 66.7745 C 43.2965 68.0048 41.709 68.7809 39.9403 68.7809 Z M 7.3988 39.2038 L 10.3571 39.2038 L 34.0236 39.2038 L 36.982 39.2038 L 36.982 37.9567 L 36.982 25.1518 C 36.982 23.9265 35.9886 22.9331 34.7632 22.9331 L 10.3571 22.9331 C 8.7233 22.9331 7.3988 24.2576 7.3988 25.8914 L 7.3988 37.9567 L 7.3988 39.2038 Z M 2.9613 37.9567 L 7.3988 37.9567 L 7.3988 25.8914 L 2.9613 25.8914 L 2.9613 37.9567 Z M 41.4194 25.1518 L 36.982 25.1518 L 36.982 37.9567 L 41.4194 37.9567 L 41.4194 25.1518 Z M 44.3807 66.7745 L 44.3778 49.761 C 44.3778 46.4933 41.7288 43.8443 38.4611 43.8443 L 34.0236 43.8443 L 34.0236 39.2038 M 0 66.7745 L 0.003 49.761 C 0.003 46.4933 2.652 43.8443 5.9196 43.8443 L 10.3571 43.8443 L 10.3571 39.2038 M 2.9613 25.8914 L 2.9613 17.7499 C 2.9613 7.9469 10.9082 0 20.7112 0 L 23.6695 0 C 33.4725 0 41.4194 7.9469 41.4194 17.7499 L 41.4194 25.1518" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:44.381;h:145.740"/>
                            </g>
                        </g>
                    </g>
                    <g id="bottom" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:MIN;counter:MIN" data-position="x:0;y:84;w:912;h:60" transform="translate(0, 84)">
                        <g id="content-left_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:36;primary:MIN;counter:MIN" data-position="x:0;y:0;w:444;h:60">
                            <g id="row_2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 60;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:0;w:444;h:60">
                                <g id="text-3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:60;y:0;w:192;h:60" transform="translate(60, 0)">
                                    <g id="tx-rt-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                                        <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                                    </g>
                                    <g id="tx-rt-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                        <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                                    </g>
                                    <rect id="bt-cc-remove-3" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, 0)" width="24" height="24" rx="0" ry="0"/>
                                    <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-30;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -30)" width="24" height="24" rx="0" ry="0"/>
                                </g>
                            </g>
                        </g>
                        <g id="content-right_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:36;primary:MIN;counter:MAX" data-position="x:468;y:0;w:444;h:60" transform="translate(468, 0)">
                            <g id="row_3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 60 0 0;gap:10;primary:MIN;counter:MAX" data-position="x:0;y:0;w:444;h:60">
                                <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:192;y:0;w:192;h:60" transform="translate(192, 0)">
                                    <g id="tx-lt-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                                        <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                    </g>
                                    <g id="tx-lt-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                        <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                    </g>
                                    <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                                    <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-30;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -30)" width="24" height="24" rx="0" ry="0"/>
                                    <rect id="bt-cc-add-5" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:66;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 66)" width="24" height="24" rx="0" ry="0"/>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
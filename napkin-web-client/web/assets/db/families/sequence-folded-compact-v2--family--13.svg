<svg xmlns="http://www.w3.org/2000/svg" width="888" height="418">
    <g id="sequence-folded-compact-v2--family--13" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L888 0 L888 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:888;h:24" data-entity-classes="Title Compact">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:888;h:0">
            <g id="body" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:888;h:370">
                <g id="lines" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:95;w:900;h:215" transform="translate(-12, 95)">
                    <g id="g-1" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:114;h:60" transform="translate(0, 1)">
                        <g id="cu" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:60">
                            <g id="cu_Vector" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30;y:0;w:99;h:60" data-entity-classes="Compact">
                                <path id="Vector" data-entity-classes="Compact" transform="translate(30, 0)" fill="#fefbdb" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_1" data-entity-classes="Compact" transform="translate(30, 0)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-2" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:93;y:1;w:114;h:72" transform="translate(93, 1)">
                        <g id="cu_1" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:72">
                            <g id="cu_Vector_1" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12" data-entity-classes="Compact">
                                <path id="Vector_2" data-entity-classes="Compact" fill="#fef2e6" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_3" data-entity-classes="Compact" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_2" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:99;h:60" data-entity-classes="Compact">
                                <path id="Vector_4" data-entity-classes="Compact" transform="translate(0, 12)" fill="#fef2e6" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_5" data-entity-classes="Compact" transform="translate(0, 12)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-3" data-entity-classes="Compact" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:156;y:13;w:114;h:72" transform="translate(156, 13)">
                        <g id="cu_2" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:72">
                            <g id="cu_Vector_3" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12.000" data-entity-classes="Compact">
                                <path id="Vector_6" data-entity-classes="Compact" fill="#ffedeb" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_7" data-entity-classes="Compact" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_4" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:99;h:60" data-entity-classes="Compact">
                                <path id="Vector_8" data-entity-classes="Compact" transform="translate(0, 12)" fill="#ffedeb" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_9" data-entity-classes="Compact" transform="translate(0, 12)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-4" data-entity-classes="Compact" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:219;y:25;w:114;h:72" transform="translate(219, 25)">
                        <g id="cu_3" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:72">
                            <g id="cu_Vector_5" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12" data-entity-classes="Compact">
                                <path id="Vector_10" data-entity-classes="Compact" fill="#feecf7" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_11" data-entity-classes="Compact" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_6" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:99;h:60" data-entity-classes="Compact">
                                <path id="Vector_12" data-entity-classes="Compact" transform="translate(0, 12)" fill="#feecf7" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_13" data-entity-classes="Compact" transform="translate(0, 12)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-5" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:282;y:37;w:114;h:72" transform="translate(282, 37)">
                        <g id="cu_4" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:72">
                            <g id="cu_Vector_7" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12" data-entity-classes="Compact">
                                <path id="Vector_14" data-entity-classes="Compact" fill="#faf0ff" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_15" data-entity-classes="Compact" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_8" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:99;h:60" data-entity-classes="Compact">
                                <path id="Vector_16" data-entity-classes="Compact" transform="translate(0, 12)" fill="#faf0ff" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_17" data-entity-classes="Compact" transform="translate(0, 12)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-6" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:345;y:49;w:114;h:72.000" transform="translate(345, 49)">
                        <g id="cu_5" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:72.000">
                            <g id="cu_Vector_9" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12" data-entity-classes="Compact">
                                <path id="Vector_18" data-entity-classes="Compact" fill="#f3f0ff" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_19" data-entity-classes="Compact" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_10" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:99;h:60.000" data-entity-classes="Compact">
                                <path id="Vector_20" data-entity-classes="Compact" transform="translate(0, 12)" fill="#f3f0ff" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_21" data-entity-classes="Compact" transform="translate(0, 12)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-7" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:408;y:61;w:114;h:72.000" transform="translate(408, 61)">
                        <g id="cu_6" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:72.000">
                            <g id="cu_Vector_11" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12" data-entity-classes="Compact">
                                <path id="Vector_22" data-entity-classes="Compact" fill="#edf4ff" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_23" data-entity-classes="Compact" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_12" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:99;h:60.000" data-entity-classes="Compact">
                                <path id="Vector_24" data-entity-classes="Compact" transform="translate(0, 12)" fill="#edf4ff" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_25" data-entity-classes="Compact" transform="translate(0, 12)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-8" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:471;y:73;w:114;h:72.000" transform="translate(471, 73)">
                        <g id="cu_7" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:72.000">
                            <g id="cu_Vector_13" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12" data-entity-classes="Compact">
                                <path id="Vector_26" data-entity-classes="Compact" fill="#e8f9ff" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_27" data-entity-classes="Compact" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_14" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:99;h:60.000" data-entity-classes="Compact">
                                <path id="Vector_28" data-entity-classes="Compact" transform="translate(0, 12)" fill="#e8f9ff" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_29" data-entity-classes="Compact" transform="translate(0, 12)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-9" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:534;y:85;w:114;h:72.000" transform="translate(534, 85)">
                        <g id="cu_8" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:72.000">
                            <g id="cu_Vector_15" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12" data-entity-classes="Compact">
                                <path id="Vector_30" data-entity-classes="Compact" fill="#e7fbf2" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_31" data-entity-classes="Compact" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_16" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:99;h:60.000" data-entity-classes="Compact">
                                <path id="Vector_32" data-entity-classes="Compact" transform="translate(0, 12)" fill="#e7fbf2" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_33" data-entity-classes="Compact" transform="translate(0, 12)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-10" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:597;y:97;w:114;h:72.000" transform="translate(597, 97)">
                        <g id="cu_9" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:72.000">
                            <g id="cu_Vector_17" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12" data-entity-classes="Compact">
                                <path id="Vector_34" data-entity-classes="Compact" fill="#f2fae1" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_35" data-entity-classes="Compact" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_18" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:99;h:60.000" data-entity-classes="Compact">
                                <path id="Vector_36" data-entity-classes="Compact" transform="translate(0, 12)" fill="#f2fae1" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_37" data-entity-classes="Compact" transform="translate(0, 12)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-11" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:660;y:109;w:114;h:72.000" transform="translate(660, 109)">
                        <g id="cu_10" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:72.000">
                            <g id="cu_Vector_19" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12.000" data-entity-classes="Compact">
                                <path id="Vector_38" data-entity-classes="Compact" fill="#fefbdb" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_39" data-entity-classes="Compact" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_20" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:99;h:60" data-entity-classes="Compact">
                                <path id="Vector_40" data-entity-classes="Compact" transform="translate(0, 12)" fill="#fefbdb" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_41" data-entity-classes="Compact" transform="translate(0, 12)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-12" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:723;y:121;w:114;h:72.000" transform="translate(723, 121)">
                        <g id="cu_11" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:72.000">
                            <g id="cu_Vector_21" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12.000" data-entity-classes="Compact">
                                <path id="Vector_42" data-entity-classes="Compact" fill="#fef2e6" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_43" data-entity-classes="Compact" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_22" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:99;h:60" data-entity-classes="Compact">
                                <path id="Vector_44" data-entity-classes="Compact" transform="translate(0, 12)" fill="#fef2e6" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                                <path id="Vector_45" data-entity-classes="Compact" transform="translate(0, 12)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 99 0 L 63 12 L 63 60 L 0 60 L 0 0 L 99 0 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="g-13" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:786;y:133;w:108;h:80" transform="translate(786, 133)">
                        <g id="cu_12" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:114;h:80.250">
                            <g id="cu_Vector_23" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:12" data-entity-classes="Compact">
                                <path id="Vector_46" data-entity-classes="Compact" fill="#ffedeb" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                                <path id="Vector_47" data-entity-classes="Compact" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 36 0 L 36 12 L 0 12 L 36 0 Z"/>
                            </g>
                            <g id="cu_Vector_24" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:3.740;w:99;h:76.510" data-entity-classes="Compact">
                                <path id="Vector_48" data-entity-classes="Compact" transform="translate(0, 3.740234375)" fill="#ffedeb" d="M 63.29 76.51 L 63.29 68.26 L 0 68.26 L 0 8.26 L 63.29 8.26 L 63.29 0 L 99 38.26 L 63.29 76.51 Z"/>
                                <path id="Vector_49" data-entity-classes="Compact" transform="translate(0, 3.740234375)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="10" d="M 63.29 76.51 L 63.29 68.26 L 0 68.26 L 0 8.26 L 63.29 8.26 L 63.29 0 L 99 38.26 L 63.29 76.51 Z"/>
                            </g>
                        </g>
                    </g>
                </g>
                <path id="ic-cc-1" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 30.115234375, 108)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:SCALE;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30.115;y:108;w:36;h:36"/>
                <path id="ic-cc-2" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 93.115234375, 120)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:93.115;y:120;w:36;h:36"/>
                <path id="ic-cc-3" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 156.115234375, 132)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:156.115;y:132;w:36;h:36"/>
                <path id="ic-cc-4" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 219.115234375, 144)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:219.115;y:144;w:36;h:36"/>
                <path id="ic-cc-5" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 282.115234375, 156)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:282.115;y:156;w:36;h:36"/>
                <path id="ic-cc-6" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 345.115234375, 168)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:345.115;y:168;w:36;h:36"/>
                <path id="ic-cc-7" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 408.115234375, 180)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:408.115;y:180;w:36;h:36"/>
                <path id="ic-cc-8" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 471.115234375, 192)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:471.115;y:192;w:36;h:36"/>
                <path id="ic-cc-9" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 534.115234375, 204)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:534.115;y:204;w:36;h:36"/>
                <path id="ic-cc-10" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 597.115234375, 216)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:597.115;y:216;w:36;h:36"/>
                <path id="ic-cc-11" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 660.115234375, 228)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:660.115;y:228;w:36;h:36"/>
                <path id="ic-cc-12" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 723.115234375, 240)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:723.115;y:240;w:36;h:36"/>
                <path id="ic-cc-13" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 792, 252)" fill="#33de7b1a" d="M 0 0 L 36 0 L 36 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:792;y:252;w:36;h:36"/>
                <g id="Frame_548" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:63;y:204;w:726;h:150" transform="translate(63, 204)">
                    <g id="text-2" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:96;h:30">
                        <g id="tx-cc-2" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#db8333" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-2-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_1" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-2" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-4" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:126;y:24;w:96;h:30" transform="translate(126, 24)">
                        <g id="tx-cc-4" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_2" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#d95da7" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-4-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_3" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-4" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-6" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:252;y:48;w:96;h:30" transform="translate(252, 48)">
                        <g id="tx-cc-6" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_4" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#7e62ec" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-6-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_5" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-6" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-8" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:378;y:72;w:96;h:30" transform="translate(378, 72)">
                        <g id="tx-cc-8" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_6" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#17aee1" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-8-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_7" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-8" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-10" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:504;y:96;w:96;h:30" transform="translate(504, 96)">
                        <g id="tx-cc-10" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_8" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#93c332" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-10-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_9" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-10" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-12" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:630;y:120;w:96;h:30" transform="translate(630, 120)">
                        <g id="tx-cc-12" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_10" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#db8333" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-12-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_11" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-12" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="Frame_547" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:30;w:852;h:162" transform="translate(0, 30)">
                    <g id="text-1" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:96;h:30">
                        <g id="tx-cc-1" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_12" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#d1bd08" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-1-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_13" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-1" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-3" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:126;y:12;w:96;h:30" transform="translate(126, 12)">
                        <g id="tx-cc-3" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_14" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#df5e59" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-3-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_15" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-3" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-5" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:252;y:36;w:96;h:30" transform="translate(252, 36)">
                        <g id="tx-cc-5" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_16" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#b960e2" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-5-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_17" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-5" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-7" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:378;y:60;w:96;h:30" transform="translate(378, 60)">
                        <g id="tx-cc-7" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_18" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#4987ec" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-7-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_19" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-7" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-9" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:504;y:84;w:96;h:30" transform="translate(504, 84)">
                        <g id="tx-cc-9" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_20" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#3cc583" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-9-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_21" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-9" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-11" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:630;y:108;w:96;h:30" transform="translate(630, 108)">
                        <g id="tx-cc-11" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_22" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#d1bd08" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-11-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_23" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-11" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-13" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:756;y:132;w:96;h:30" transform="translate(756, 132)">
                        <g id="tx-cc-13" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_24" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#df5e59" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-13-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_25" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-13" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <rect id="bt-cc-add-1" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:114;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 6, 114)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-2" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:69;y:120;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 69, 120)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-3" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:132;y:132;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 132, 132)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-4" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:144;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 192, 144)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-5" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:258;y:156;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 258, 156)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-6" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:318;y:168;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 318, 168)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-7" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:384;y:180;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 384, 180)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-8" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:444;y:192;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 444, 192)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-9" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:510;y:204;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 510, 204)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-10" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:570;y:216;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 570, 216)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-11" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:636;y:228;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 636, 228)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-12" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:696;y:240;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 696, 240)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-13" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:762;y:252;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 762, 252)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-14" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:852;y:258;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 852, 258)" width="24" height="24" rx="0" ry="0"/>
            </g>
        </g>
    </g>
</svg>
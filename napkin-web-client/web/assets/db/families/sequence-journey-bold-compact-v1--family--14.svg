<svg xmlns="http://www.w3.org/2000/svg" width="930" height="516">
    <g id="sequence-journey-bold-compact-v1--family--14" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L930 0 L930 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:930;h:24" data-entity-classes="Title Compact">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:930;h:0">
            <g id="body" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:930;h:468">
                <g id="lines" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:91;w:884;h:280" transform="translate(36, 91)">
                    <g id="g-1" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:55.859;w:84;h:151.550" transform="translate(0, 55.859375)">
                        <g id="cu_Vector" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:151.550" data-entity-classes="Compact">
                            <path id="Vector" data-entity-classes="Compact" fill="#fefbdb" d="M 84 42 L 84 69.64 L 60 69.64 L 60 42 C 60 32.07 51.92 24 42 24 C 32.07 24 24 32.07 24 42 L 24 151.55 L 0 151.55 L 0 42 C 0 41.18 0.02 40.36 0.07 39.55 C 1.34 17.53 19.66 0 42 0 C 64.34 0 82.66 17.53 83.93 39.55 C 83.98 40.36 84 41.18 84 42 Z"/>
                            <path id="Vector_1" data-entity-classes="Compact" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84 42 L 84 69.64 L 60 69.64 L 60 42 C 60 32.07 51.92 24 42 24 C 32.07 24 24 32.07 24 42 L 24 151.55 L 0 151.55 L 0 42 C 0 41.18 0.02 40.36 0.07 39.55 C 1.34 17.53 19.66 0 42 0 C 64.34 0 82.66 17.53 83.93 39.55 C 83.98 40.36 84 41.18 84 42 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:104.699;w:84;h:144.430" transform="translate(60, 104.69921875)">
                        <g id="cu_Vector_1" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:144.430" data-entity-classes="Compact">
                            <path id="Vector_2" data-entity-classes="Compact" fill="#fef2e6" d="M 84 0 L 84 102.43 C 84 125.59 65.16 144.43 42 144.43 C 18.84 144.43 0 125.59 0 102.43 L 0 20.52 L 24 20.52 L 24 102.43 C 24 112.36 32.08 120.43 42 120.43 C 51.93 120.43 60 112.36 60 102.43 L 60 0 L 84 0 Z"/>
                            <path id="Vector_3" data-entity-classes="Compact" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84 0 L 84 102.43 C 84 125.59 65.16 144.43 42 144.43 C 18.84 144.43 0 125.59 0 102.43 L 0 20.52 L 24 20.52 L 24 102.43 C 24 112.36 32.08 120.43 42 120.43 C 51.93 120.43 60 112.36 60 102.43 L 60 0 L 84 0 Z"/>
                        </g>
                    </g>
                    <g id="g-3" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:120;y:40.600;w:84.010;h:152.970" transform="translate(120, 40.599609375)">
                        <g id="cu_Vector_2" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84.010;h:152.970" data-entity-classes="Compact">
                            <path id="Vector_4" data-entity-classes="Compact" fill="#ffedeb" d="M 84.01 42 L 84.01 152.97 L 60.01 152.97 L 60.01 42 C 60.01 32.07 51.93 24 42.01 24 C 32.08 24 24 32.07 24 42 L 24 64.01 L 0 64.01 L 0 42 C 0 18.84 18.85 3.5527e-15 42.01 3.5527e-15 C 65.17 0 84.01 18.84 84.01 42 Z"/>
                            <path id="Vector_5" data-entity-classes="Compact" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84.01 42 L 84.01 152.97 L 60.01 152.97 L 60.01 42 C 60.01 32.07 51.93 24 42.01 24 C 32.08 24 24 32.07 24 42 L 24 64.01 L 0 64.01 L 0 42 C 0 18.84 18.85 3.5527e-15 42.01 3.5527e-15 C 65.17 0 84.01 18.84 84.01 42 Z"/>
                        </g>
                    </g>
                    <g id="g-4" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:180;y:121.600;w:84.000;h:120.510" transform="translate(180, 121.599609375)">
                        <g id="cu_Vector_3" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84.000;h:120.510" data-entity-classes="Compact">
                            <path id="Vector_6" data-entity-classes="Compact" fill="#feecf7" d="M 84 0 L 84 78.5 C 84 101.66 65.16 120.51 42 120.51 C 18.84 120.51 0 101.66 0 78.5 L 0 71.55 L 24 71.55 L 24 78.5 C 24 88.43 32.07 96.51 42 96.51 C 51.93 96.51 60 88.43 60 78.5 L 60 0 L 84 0 Z"/>
                            <path id="Vector_7" data-entity-classes="Compact" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84 0 L 84 78.5 C 84 101.66 65.16 120.51 42 120.51 C 18.84 120.51 0 101.66 0 78.5 L 0 71.55 L 24 71.55 L 24 78.5 C 24 88.43 32.07 96.51 42 96.51 C 51.93 96.51 60 88.43 60 78.5 L 60 0 L 84 0 Z"/>
                        </g>
                    </g>
                    <g id="g-5" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:240;y:57.600;w:84.010;h:146.260" transform="translate(240, 57.599609375)">
                        <g id="cu_Vector_4" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84.010;h:146.260" data-entity-classes="Compact">
                            <path id="Vector_8" data-entity-classes="Compact" fill="#faf0ff" d="M 84.01 42 L 84.01 146.26 L 60.01 146.26 L 60.01 42 C 60.01 32.08 51.93 24 42 24 C 32.08 24 24 32.08 24 42 L 24 64.49 L 0 64.49 L 0 42 C 0 18.84 18.84 0 42 0 C 65.16 7.1054e-15 84.01 18.84 84.01 42 Z"/>
                            <path id="Vector_9" data-entity-classes="Compact" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84.01 42 L 84.01 146.26 L 60.01 146.26 L 60.01 42 C 60.01 32.08 51.93 24 42 24 C 32.08 24 24 32.08 24 42 L 24 64.49 L 0 64.49 L 0 42 C 0 18.84 18.84 0 42 0 C 65.16 7.1054e-15 84.01 18.84 84.01 42 Z"/>
                        </g>
                    </g>
                    <g id="g-6" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:300;y:81.600;w:84.010;h:172.920" transform="translate(300, 81.599609375)">
                        <g id="cu_Vector_5" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84.010;h:172.920" data-entity-classes="Compact">
                            <path id="Vector_10" data-entity-classes="Compact" fill="#f3f0ff" d="M 84.01 0 L 84.01 130.92 C 84.01 154.08 65.17 172.92 42.01 172.92 C 18.85 172.92 3.5459e-10 154.08 3.5459e-10 130.92 C 3.5459e-10 130.56 -0 130.2 0.01 129.84 L 0.01 122.33 L 24.01 122.33 L 24.01 130.46 C 24 130.61 24 130.77 24 130.92 C 24 140.84 32.08 148.92 42.01 148.92 C 51.93 148.92 60.01 140.84 60.01 130.92 L 60.01 0 L 84.01 0 Z"/>
                            <path id="Vector_11" data-entity-classes="Compact" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84.01 0 L 84.01 130.92 C 84.01 154.08 65.17 172.92 42.01 172.92 C 18.85 172.92 3.5459e-10 154.08 3.5459e-10 130.92 C 3.5459e-10 130.56 -0 130.2 0.01 129.84 L 0.01 122.33 L 24.01 122.33 L 24.01 130.46 C 24 130.61 24 130.77 24 130.92 C 24 140.84 32.08 148.92 42.01 148.92 C 51.93 148.92 60.01 140.84 60.01 130.92 L 60.01 0 L 84.01 0 Z"/>
                        </g>
                    </g>
                    <g id="g-7" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:360;y:27.600;w:84;h:146.010" transform="translate(360, 27.599609375)">
                        <g id="cu_Vector_6" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:146.010" data-entity-classes="Compact">
                            <path id="Vector_12" data-entity-classes="Compact" fill="#edf4ff" d="M 84 42 L 84 146.01 L 60 146.01 L 60 42 C 60 32.08 51.93 24 42 24 C 32.07 24 24 32.08 24 42 L 24 53.9 L 0 53.9 L 0 42 C 0 18.84 18.84 1.7764e-15 42 1.7764e-15 C 65.16 -1.7764e-15 84 18.84 84 42 Z"/>
                            <path id="Vector_13" data-entity-classes="Compact" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84 42 L 84 146.01 L 60 146.01 L 60 42 C 60 32.08 51.93 24 42 24 C 32.07 24 24 32.08 24 42 L 24 53.9 L 0 53.9 L 0 42 C 0 18.84 18.84 1.7764e-15 42 1.7764e-15 C 65.16 -1.7764e-15 84 18.84 84 42 Z"/>
                        </g>
                    </g>
                    <g id="g-8" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:420;y:92.600;w:84.010;h:143.920" transform="translate(420, 92.599609375)">
                        <g id="cu_Vector_7" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84.010;h:143.920" data-entity-classes="Compact">
                            <path id="Vector_14" data-entity-classes="Compact" fill="#e8f9ff" d="M 84.01 0 L 84.01 101.92 C 84.01 125.08 65.16 143.92 42 143.92 C 18.84 143.92 0 125.08 0 101.92 L 0 80.89 L 24 80.89 L 24 101.92 C 24 111.85 32.08 119.92 42 119.92 C 51.93 119.92 60.01 111.85 60.01 101.92 L 60.01 0 L 84.01 0 Z"/>
                            <path id="Vector_15" data-entity-classes="Compact" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84.01 0 L 84.01 101.92 C 84.01 125.08 65.16 143.92 42 143.92 C 18.84 143.92 0 125.08 0 101.92 L 0 80.89 L 24 80.89 L 24 101.92 C 24 111.85 32.08 119.92 42 119.92 C 51.93 119.92 60.01 111.85 60.01 101.92 L 60.01 0 L 84.01 0 Z"/>
                        </g>
                    </g>
                    <g id="g-9" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:480;y:40.600;w:84;h:171.640" transform="translate(480, 40.599609375)">
                        <g id="cu_Vector_8" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:171.640" data-entity-classes="Compact">
                            <path id="Vector_16" data-entity-classes="Compact" fill="#e7fbf2" d="M 84 42 L 84 171.64 L 60 171.64 L 60 42 C 60 32.07 51.92 24 42 24 C 32.07 24 24 32.07 24 42 L 24 52.08 L 0 52.08 L 0 42 C 0 18.84 18.84 0 42 0 C 65.16 0 84 18.84 84 42 Z"/>
                            <path id="Vector_17" data-entity-classes="Compact" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84 42 L 84 171.64 L 60 171.64 L 60 42 C 60 32.07 51.92 24 42 24 C 32.07 24 24 32.07 24 42 L 24 52.08 L 0 52.08 L 0 42 C 0 18.84 18.84 0 42 0 C 65.16 0 84 18.84 84 42 Z"/>
                        </g>
                    </g>
                    <g id="g-10" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:540;y:104.600;w:84;h:159.250" transform="translate(540, 104.599609375)">
                        <g id="cu_Vector_9" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:159.250" data-entity-classes="Compact">
                            <path id="Vector_18" data-entity-classes="Compact" fill="#f2fae1" d="M 84 0 L 84 117.25 C 84 140.41 65.16 159.25 42 159.25 C 18.84 159.25 0 140.41 0 117.25 L 0 107.63 L 24 107.63 L 24 117.25 C 24 127.17 32.08 135.25 42 135.25 C 51.93 135.25 60 127.17 60 117.25 L 60 0 L 84 0 Z"/>
                            <path id="Vector_19" data-entity-classes="Compact" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84 0 L 84 117.25 C 84 140.41 65.16 159.25 42 159.25 C 18.84 159.25 0 140.41 0 117.25 L 0 107.63 L 24 107.63 L 24 117.25 C 24 127.17 32.08 135.25 42 135.25 C 51.93 135.25 60 127.17 60 117.25 L 60 0 L 84 0 Z"/>
                        </g>
                    </g>
                    <g id="g-11" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:600;y:53.600;w:84.010;h:145.860" transform="translate(600, 53.599609375)">
                        <g id="cu_Vector_10" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84.010;h:145.860" data-entity-classes="Compact">
                            <path id="Vector_20" data-entity-classes="Compact" fill="#fefbdb" d="M 84.01 42 L 84.01 145.86 L 60.01 145.86 L 60.01 42 C 60.01 32.08 51.93 24 42.01 24 C 32.08 24 24 32.08 24 42 L 24 51.57 L 0 51.57 L 0 42 C 0 18.84 18.85 0 42.01 0 C 65.16 -1.4211e-14 84.01 18.84 84.01 42 Z"/>
                            <path id="Vector_21" data-entity-classes="Compact" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84.01 42 L 84.01 145.86 L 60.01 145.86 L 60.01 42 C 60.01 32.08 51.93 24 42.01 24 C 32.08 24 24 32.08 24 42 L 24 51.57 L 0 51.57 L 0 42 C 0 18.84 18.85 0 42.01 0 C 65.16 -1.4211e-14 84.01 18.84 84.01 42 Z"/>
                        </g>
                    </g>
                    <g id="g-12" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:660;y:88.600;w:84;h:158.070" transform="translate(660, 88.599609375)">
                        <g id="cu_Vector_11" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:158.070" data-entity-classes="Compact">
                            <path id="Vector_22" data-entity-classes="Compact" fill="#fef2e6" d="M 84 0 L 84 116.07 C 84 117.14 83.96 118.2 83.88 119.25 C 82.25 140.93 64.09 158.07 42 158.07 C 19.91 158.07 1.75 140.93 0.12 119.25 C 0.04 118.2 0 117.14 0 116.07 L 0 111.11 L 24 111.11 L 24 116.07 C 24 125.99 32.07 134.07 42 134.07 C 51.93 134.07 60 125.99 60 116.07 L 60 0 L 84 0 Z"/>
                            <path id="Vector_23" data-entity-classes="Compact" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84 0 L 84 116.07 C 84 117.14 83.96 118.2 83.88 119.25 C 82.25 140.93 64.09 158.07 42 158.07 C 19.91 158.07 1.75 140.93 0.12 119.25 C 0.04 118.2 0 117.14 0 116.07 L 0 111.11 L 24 111.11 L 24 116.07 C 24 125.99 32.07 134.07 42 134.07 C 51.93 134.07 60 125.99 60 116.07 L 60 0 L 84 0 Z"/>
                        </g>
                    </g>
                    <g id="g-13" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:720;y:38.600;w:84;h:168.820" transform="translate(720, 38.599609375)">
                        <g id="cu_Vector_12" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:168.820" data-entity-classes="Compact">
                            <path id="Vector_24" data-entity-classes="Compact" fill="#ffedeb" d="M 84 42 L 84 168.82 L 60 168.82 L 60 42 C 60 32.07 51.93 24 42 24 C 32.08 24 24 32.07 24 42 L 24 51.93 L 0 51.93 L 0 42 C 0 18.84 18.84 0 42 0 C 65.15 0 84 18.84 84 42 Z"/>
                            <path id="Vector_25" data-entity-classes="Compact" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 84 42 L 84 168.82 L 60 168.82 L 60 42 C 60 32.07 51.93 24 42 24 C 32.08 24 24 32.07 24 42 L 24 51.93 L 0 51.93 L 0 42 C 0 18.84 18.84 0 42 0 C 65.15 0 84 18.84 84 42 Z"/>
                        </g>
                    </g>
                    <g id="g-14" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:780.117;y:86;w:90.980;h:177.400" transform="translate(780.1171875, 86)">
                        <g id="cu_Vector_13" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.117;y:-0.394;w:90.980;h:177.400" data-entity-classes="Compact">
                            <path id="Vector_26" data-entity-classes="Compact" transform="translate(-0.11716887354850769, -0.39359813928604126)" fill="#feecf7" d="M 84.0098 135.4004 C 84.0096 158.5602 65.1696 177.4004 42.0098 177.4004 C 18.85 177.4003 0.0002 158.5601 0 135.4004 L 0 121.7803 L 24 121.7803 L 24 135.4004 C 24.0002 145.3301 32.08 153.4003 42.0098 153.4004 C 51.9296 153.4004 60.0096 145.3302 60.0098 135.4004 L 60.0098 24 L 51.9805 24 L 71.4805 0 L 90.9805 24 L 84.0098 24 L 84.0098 135.4004 Z"/>
                            <path id="Vector_27" data-entity-classes="Compact" transform="translate(-0.11716887354850769, -0.39359813928604126)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 84.0098 135.4004 C 84.0096 158.5602 65.1696 177.4004 42.0098 177.4004 C 18.85 177.4003 0.0002 158.5601 0 135.4004 L 0 121.7803 L 24 121.7803 L 24 135.4004 C 24.0002 145.3301 32.08 153.4003 42.0098 153.4004 C 51.9296 153.4004 60.0096 145.3302 60.0098 135.4004 L 60.0098 24 L 51.9805 24 L 71.4805 0 L 90.9805 24 L 84.0098 24 L 84.0098 135.4004 Z"/>
                        </g>
                    </g>
                </g>
                <g id="Frame_589" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30;y:54;w:816;h:53" transform="translate(30, 54)">
                    <g id="text-1" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:0;y:18;w:96;h:30" transform="translate(0, 18)">
                        <g id="tx-cc-1" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#d1bd08" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-1-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_1" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-1" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-3" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:120;y:6;w:96;h:30" transform="translate(120, 6)">
                        <g id="tx-cc-3" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_2" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#df5e59" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-3-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_3" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-3" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-5" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:240;y:23;w:96;h:30" transform="translate(240, 23)">
                        <g id="tx-cc-5" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_4" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#b960e2" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-5-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_5" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-5" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-7" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:360;y:0;w:96;h:30" transform="translate(360, 0)">
                        <g id="tx-cc-7" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_6" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#4987ec" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-7-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_7" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-7" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-9" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:480;y:6;w:96;h:30" transform="translate(480, 6)">
                        <g id="tx-cc-9" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_8" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#3cc583" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-9-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_9" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-9" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-11" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:600;y:23;w:96;h:30" transform="translate(600, 23)">
                        <g id="tx-cc-11" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_10" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#d1bd08" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-11-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_11" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-11" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-13" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:720;y:6;w:96;h:30" transform="translate(720, 6)">
                        <g id="tx-cc-13" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_12" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#df5e59" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-13-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_13" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-13" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:30;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 30)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="Frame_590" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:90;y:372;w:816;h:54" transform="translate(90, 372)">
                    <g id="text-2" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:0;y:12;w:96;h:30" transform="translate(0, 12)">
                        <g id="tx-cc-2" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_14" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#db8333" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-2-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_15" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-2" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-4" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:120;y:6;w:96;h:30" transform="translate(120, 6)">
                        <g id="tx-cc-4" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_16" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#d95da7" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-4-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_17" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-4" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-6" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:240;y:18;w:96;h:30" transform="translate(240, 18)">
                        <g id="tx-cc-6" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_18" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#7e62ec" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-6-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_19" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-6" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-8" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:360;y:0;w:96;h:30" transform="translate(360, 0)">
                        <g id="tx-cc-8" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_20" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#17aee1" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-8-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_21" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-8" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-10" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:480;y:24;w:96;h:30" transform="translate(480, 24)">
                        <g id="tx-cc-10" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_22" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#93c332" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-10-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_23" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-10" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-12" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:600;y:12;w:96;h:30" transform="translate(600, 12)">
                        <g id="tx-cc-12" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_24" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#db8333" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-12-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_25" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-12" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-14" data-entity-classes="Compact" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:6;primary:CENTER;counter:CENTER" data-position="x:720;y:24;w:96;h:30" transform="translate(720, 24)">
                        <g id="tx-cc-14" data-entity-classes="DescTitle Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#ff00001a">
                            <text id="Label_26" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#d95da7" font-size="12" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <g id="tx-cc-14-desc" data-entity-classes="Description Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:18;w:96;h:12" fill="#ff00001a" transform="translate(0, 18)">
                            <text id="Label_27" data-entity-classes="Compact" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12" fill="#484848" font-size="10.5" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                        </g>
                        <rect id="bt-cc-remove-14" data-entity-classes="Compact" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <path id="ic-cc-1" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 66, 180)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:SCALE;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:180;w:24;h:24"/>
                <path id="ic-cc-2" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 126, 282)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:126;y:282;w:24;h:24"/>
                <path id="ic-cc-3" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 186, 168)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:186;y:168;w:24;h:24"/>
                <path id="ic-cc-4" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 246, 276)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:246;y:276;w:24;h:24"/>
                <path id="ic-cc-5" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 306, 180)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:306;y:180;w:24;h:24"/>
                <path id="ic-cc-6" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 366, 288)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:366;y:288;w:24;h:24"/>
                <path id="ic-cc-7" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 426, 150)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:426;y:150;w:24;h:24"/>
                <path id="ic-cc-8" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 486, 270)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:486;y:270;w:24;h:24"/>
                <path id="ic-cc-9" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 546, 162)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:546;y:162;w:24;h:24"/>
                <path id="ic-cc-10" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 606, 300)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:606;y:300;w:24;h:24"/>
                <path id="ic-cc-11" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 666, 180)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:666;y:180;w:24;h:24"/>
                <path id="ic-cc-12" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 726, 282)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:726;y:282;w:24;h:24"/>
                <path id="ic-cc-13" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 786, 162)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:786;y:162;w:24;h:24"/>
                <path id="ic-cc-14" data-entity-classes="Compact" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 846, 294)" fill="#33de7b1a" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:846;y:294;w:24;h:24"/>
                <rect id="bt-cc-add-1" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:288;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 288)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-2" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:204;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 96, 204)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-3" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:156;y:186;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 156, 186)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-4" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:216.010;y:270;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 216.009765625, 270)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-5" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:276;y:204;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 276, 204)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-6" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:336;y:282;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 282)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-7" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:396;y:162;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 396, 162)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-8" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:456;y:252;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 456, 252)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-9" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:516;y:173.120;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 516, 173.1201171875)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-10" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:576;y:294;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 576, 294)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-11" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:636;y:186;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 636, 186)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-12" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:696;y:277.580;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 696, 277.580078125)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-13" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:756;y:168;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 756, 168)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-14" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:816;y:288;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 816, 288)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-15" data-entity-classes="Compact" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:876;y:168;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 876, 168)" width="24" height="24" rx="0" ry="0"/>
            </g>
        </g>
    </g>
</svg>
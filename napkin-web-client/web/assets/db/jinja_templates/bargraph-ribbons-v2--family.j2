{% set minHeight = 0 %}
{% set maxHeight = 180 %}

{#------calculate values from template elements--------#}
{% set values = elementsToValues(elements, 'tx-..-([1-6])-value') %}
{% set n = values | length %}
{% if n == 0 %}
{% set values = [ 0, 0 ] %}
{% set n = 2 %}
{% endif %}
{% if n == 1 %}
{% set values = [ values[0], 0 ] %}
{% set n = 2 %}
{% endif %}

{% for i in range(n) %}
{{ update_array(values, i, clamp(values[i], 0, 100)*(maxHeight-minHeight) / 100 + minHeight) }}
{% endfor %}

{% set height = 384 + values | max %}

{% if n == 2 %}
<svg xmlns="http://www.w3.org/2000/svg" width="600" height="{{height}}">
	<g id="barchart-ribbons-v1--family--2~1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 24 0 24;gap:0;primary:MAX;counter:CENTER" data-position="x:1219;y:-19784;w:600;h:384">
		<g id="title-frame" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 24 0;gap:50;primary:CENTER;counter:MIN" data-position="x:24;y:0;w:552;h:72" fill="#ffffff" transform="translate(24, 0)">
			<g id="tx-cb-title" data-entity-classes="Title" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:552;h:48">
				<path id="rect" fill="#ff00001a" d="M0 0 L552 0 L552 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:552;h:48">
				</path>
				<text id="Label" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:224.090;y:16;w:77.875;h:16" fill="#484848" transform="translate(224.08969116210938, 16)" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
				</text>
			</g>
		</g>
		<g id="template-frame" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 12 0;gap:0;primary:MIN;counter:CENTER" data-position="x:24;y:72;w:552;h:312;wMin:552" fill="#ffffff" transform="translate(24, 72)">
			<g id="Frame_617" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 12 0;gap:0;primary:CENTER;counter:MAX" data-position="x:180;y:0;w:192;h:216" transform="translate(180, 0)">
				<g id="column" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:96;h:204">
					<g id="tx-cc-1-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#df5e59" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc" transform="translate(0, -18)" fill="#ffedeb" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc" fill="#ffedeb" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc" transform="translate(-96, 12)" fill="#ffedeb" d="M192 0 L96 0 L96 72 L0 120 L192 120 L192 72 L192 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-96;y:12;w:192;h:120">
						</path>
						<path id="stroke-tc" transform="translate(0, -18)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc" transform="translate(96, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc" transform="translate(-96, 12)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 96 72 L 192 72 L 192 120 L 0 120 Z M 96 72 L 96 0 M 192 0 L 192 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-96;y:12;w:192;h:120">
						</path>
						<g id="element-height" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[0]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_2" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_1" transform="translate(8, 4)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:204" transform="translate(96, 0)">
					<g id="tx-cc-2-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#b960e2" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_1" transform="translate(0, -18)" fill="#faf0ff" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_1" fill="#faf0ff" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_1" transform="translate(0, 12)" fill="#faf0ff" d="M96 0 L0 0 L0 72 L0 120 L192 120 L96 72 L96 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:192;h:120">
						</path>
						<path id="stroke-tc_1" transform="translate(0, -18)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_1" transform="translate(96, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_1" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_1" transform="translate(0, 12)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 0 72 L 96 72 L 192 120 L 0 120 Z M 0 72 L 0 0 M 96 0 L 96 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:192;h:120">
						</path>
						<g id="element-height_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[1]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_4" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_3" transform="translate(8, 4)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
			</g>
			<g id="column_2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 0 0 0;gap:24;primary:CENTER;counter:MIN" data-position="x:96;y:216;w:360;h:84" transform="translate(96, 216)">
				<g id="content" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:24;w:168;h:60" transform="translate(0, 24)">
					<g id="tx-ct-1-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_5" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_6" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-1" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
				</g>
				<g id="content_1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:192;y:24;w:168;h:60" transform="translate(192, 24)">
					<g id="tx-ct-2-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_7" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_8" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_4" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-2" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
					<path id="bt-cc-add-2_1" transform="translate(168, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:0;w:24.000;h:24.000">
					</path>
				</g>
			</g>
		</g>
	</g>
</svg>
{% endif %}
{% if n == 3 %}
<svg xmlns="http://www.w3.org/2000/svg" width="600" height="{{height}}">
	<g id="barchart-ribbons-v1--family--3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 24 0 24;gap:0;primary:MAX;counter:CENTER" data-position="x:1919;y:-19784;w:600;h:384">
		<g id="title-frame" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 24 0;gap:50;primary:CENTER;counter:MIN" data-position="x:24;y:0;w:552;h:72" fill="#ffffff" transform="translate(24, 0)">
			<g id="tx-cb-title" data-entity-classes="Title" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:552;h:48">
				<path id="rect" fill="#ff00001a" d="M0 0 L552 0 L552 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:552;h:48">
				</path>
				<text id="Label" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:224.090;y:16;w:77.875;h:16" fill="#484848" transform="translate(224.08969116210938, 16)" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
				</text>
			</g>
		</g>
		<g id="template-frame" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 12 0;gap:0;primary:MIN;counter:CENTER" data-position="x:24;y:72;w:552;h:312;wMin:552" fill="#ffffff" transform="translate(24, 72)">
			<g id="Frame_617" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 12 0;gap:0;primary:CENTER;counter:MAX" data-position="x:132;y:0;w:288;h:216" transform="translate(132, 0)">
				<g id="column" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:96;h:204">
					<g id="tx-cc-1-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#df5e59" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc" transform="translate(0, -18)" fill="#ffedeb" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc" fill="#ffedeb" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc" transform="translate(-144, 12)" fill="#ffedeb" d="M240 0 L144 0 L144 72 L0 120 L192 120 L240 72 L240 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-144;y:12;w:240;h:120">
						</path>
						<path id="stroke-tc" transform="translate(0, -18)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc" transform="translate(96, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc" transform="translate(-144, 12)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 144 72 L 240 72 L 192 120 L 0 120 Z M 144 72 L 144 0 M 240 0 L 240 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-144;y:12;w:240;h:120">
						</path>
						<g id="element-height_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[0]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_2" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_1" transform="translate(8, 4)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:204" transform="translate(96, 0)">
					<g id="tx-cc-2-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#b960e2" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_1" transform="translate(0, -18)" fill="#faf0ff" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_1" fill="#faf0ff" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_1" transform="translate(-48, 12)" fill="#faf0ff" d="M144 0 L48 0 L48 72 L0 120 L192 120 L144 72 L144 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-48;y:12;w:192;h:120">
						</path>
						<path id="stroke-tc_1" transform="translate(0, -18)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_1" transform="translate(96, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_1" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_1" transform="translate(-48, 12)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 48 72 L 144 72 L 192 120 L 0 120 Z M 48 72 L 48 0 M 144 0 L 144 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-48;y:12;w:192;h:120">
						</path>
						<g id="element-height_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[1]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_4" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_3" transform="translate(8, 4)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:192;y:0;w:96;h:204" transform="translate(192, 0)">
					<g id="tx-cc-3-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#93c332" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_2" transform="translate(0, -18)" fill="#f2fae1" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_2" fill="#f2fae1" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_2" transform="translate(0, 12)" fill="#f2fae1" d="M96 0 L0 0 L0 72 L48 120 L240 120 L96 72 L96 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:240;h:120">
						</path>
						<path id="stroke-tc_2" transform="translate(0, -18)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_2" transform="translate(96, 0)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_2" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_2" transform="translate(0, 12)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 120 L 0 72 L 96 72 L 240 120 L 48 120 Z M 0 72 L 0 0 M 96 0 L 96 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:240;h:120">
						</path>
						<g id="element-height_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[2]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-3" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_6" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_5" transform="translate(8, 4)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
			</g>
			<g id="column_3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 0 0 0;gap:24;primary:CENTER;counter:MIN" data-position="x:0;y:216;w:552;h:84" transform="translate(0, 216)">
				<g id="content" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:24;w:168;h:60" transform="translate(0, 24)">
					<path id="bt-cc-remove-1" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-1-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_7" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_8" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-1" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
				</g>
				<g id="content_1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:192;y:24;w:168;h:60" transform="translate(192, 24)">
					<path id="bt-cc-remove-2" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-2-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_9" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_10" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_4" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-2" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
				</g>
				<g id="content_2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:384;y:24;w:168;h:60" transform="translate(384, 24)">
					<path id="bt-cc-remove-3" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-3-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_11" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_5" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#93c332" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_12" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-3" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
					<path id="bt-cc-add-4" transform="translate(168, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:0;w:24.000;h:24.000">
					</path>
				</g>
			</g>
		</g>
	</g>
</svg>
{% endif %}
{% if n == 4 %}
<svg xmlns="http://www.w3.org/2000/svg" width="792" height="{{height}}">
	<g id="barchart-ribbons-v1--family--4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 24 0 24;gap:0;primary:MAX;counter:CENTER" data-position="x:2619;y:-19784;w:792;h:384">
		<g id="title-frame" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 24 0;gap:50;primary:CENTER;counter:MIN" data-position="x:24;y:0;w:744;h:72" fill="#ffffff" transform="translate(24, 0)">
			<g id="tx-cb-title" data-entity-classes="Title" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:552;h:48" transform="translate(96, 0)">
				<path id="rect" fill="#ff00001a" d="M0 0 L552 0 L552 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:552;h:48">
				</path>
				<text id="Label" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:224.090;y:16;w:77.875;h:16" fill="#484848" transform="translate(224.08969116210938, 16)" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
				</text>
			</g>
		</g>
		<g id="template-frame" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 12 0;gap:0;primary:MIN;counter:CENTER" data-position="x:24;y:72;w:744;h:312;wMin:552" fill="#ffffff" transform="translate(24, 72)">
			<g id="Frame_617" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 12 0;gap:0;primary:CENTER;counter:MAX" data-position="x:180;y:0;w:384;h:216" transform="translate(180, 0)">
				<g id="column" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:96;h:204">
					<g id="tx-cc-1-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#df5e59" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc" transform="translate(0, -18)" fill="#ffedeb" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc" fill="#ffedeb" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc" transform="translate(-192, 12)" fill="#ffedeb" d="M288 0 L192 0 L192 72 L0 120 L192 120 L288 72 L288 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-192;y:12;w:288;h:120">
						</path>
						<path id="stroke-tc" transform="translate(0, -18)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc" transform="translate(96, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc" transform="translate(-192, 12)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 192 72 L 288 72 L 192 120 L 0 120 Z M 192 72 L 192 0 M 288 0 L 288 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-192;y:12;w:288;h:120">
						</path>
						<g id="element-height_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[0]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_2" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_1" transform="translate(8, 4)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:204" transform="translate(96, 0)">
					<g id="tx-cc-2-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#b960e2" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_1" transform="translate(0, -18)" fill="#faf0ff" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_1" fill="#faf0ff" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_1" transform="translate(-96, 12)" fill="#faf0ff" d="M192 0 L96 0 L96 72 L0 120 L192 120 L192 72 L192 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-96;y:12;w:192;h:120">
						</path>
						<path id="stroke-tc_1" transform="translate(0, -18)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_1" transform="translate(96, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_1" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_1" transform="translate(-96, 12)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 96 72 L 192 72 L 192 120 L 0 120 Z M 96 72 L 96 0 M 192 0 L 192 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-96;y:12;w:192;h:120">
						</path>
						<g id="element-height_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[1]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_4" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_3" transform="translate(8, 4)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:192;y:0;w:96;h:204" transform="translate(192, 0)">
					<g id="tx-cc-3-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#93c332" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_2" transform="translate(0, -18)" fill="#f2fae1" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_2" fill="#f2fae1" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_2" transform="translate(0, 12)" fill="#f2fae1" d="M96 0 L0 0 L0 72 L0 120 L192 120 L96 72 L96 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:192;h:120">
						</path>
						<path id="stroke-tc_2" transform="translate(0, -18)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_2" transform="translate(96, 0)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_2" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_2" transform="translate(0, 12)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 0 72 L 96 72 L 192 120 L 0 120 Z M 0 72 L 0 0 M 96 0 L 96 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:192;h:120">
						</path>
						<g id="element-height_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[2]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-3" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_6" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_5" transform="translate(8, 4)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:288;y:0;w:96;h:204" transform="translate(288, 0)">
					<g id="tx-cc-4-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#db8333" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_3" transform="translate(0, -18)" fill="#fef2e6" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_3" fill="#fef2e6" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_3" transform="translate(0, 12)" fill="#fef2e6" d="M96 0 L0 0 L0 72 L96 120 L288 120 L96 72 L96 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:288;h:120">
						</path>
						<path id="stroke-tc_3" transform="translate(0, -18)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_3" transform="translate(96, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_3" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_3" transform="translate(0, 12)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 120 L 0 72 L 96 72 L 288 120 L 96 120 Z M 0 72 L 0 0 M 96 0 L 96 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:288;h:120">
						</path>
						<g id="element-height_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[3]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-4" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_8" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_7" transform="translate(8, 4)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
			</g>
			<g id="column_4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 0 0 0;gap:24;primary:CENTER;counter:MIN" data-position="x:0;y:216;w:744;h:84" transform="translate(0, 216)">
				<g id="content" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:24;w:168;h:60" transform="translate(0, 24)">
					<path id="bt-cc-remove-1" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-1-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_9" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_10" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-1" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
				</g>
				<g id="content_1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:192;y:24;w:168;h:60" transform="translate(192, 24)">
					<path id="bt-cc-remove-2" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-2-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_11" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_12" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_4" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-2" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
				</g>
				<g id="content_2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:384;y:24;w:168;h:60" transform="translate(384, 24)">
					<path id="bt-cc-remove-3" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-3-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_13" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_5" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#93c332" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_14" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-3" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
				</g>
				<g id="content_3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:576;y:24;w:168;h:60" transform="translate(576, 24)">
					<path id="bt-cc-remove-4" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-4-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_15" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_7" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_16" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_8" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-4" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
					<path id="bt-cc-add-5" transform="translate(168, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:0;w:24.000;h:24.000">
					</path>
				</g>
			</g>
		</g>
	</g>
</svg>
{% endif %}
{% if n == 5 %}
<svg xmlns="http://www.w3.org/2000/svg" width="984" height="{{height}}">
	<g id="barchart-ribbons-v1--family--5" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 24 0 24;gap:0;primary:MAX;counter:CENTER" data-position="x:3511;y:-19784;w:984;h:384">
		<g id="title-frame" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 24 0;gap:50;primary:CENTER;counter:MIN" data-position="x:24;y:0;w:936;h:72" fill="#ffffff" transform="translate(24, 0)">
			<g id="tx-cb-title" data-entity-classes="Title" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:0;w:552;h:48" transform="translate(192, 0)">
				<path id="rect" fill="#ff00001a" d="M0 0 L552 0 L552 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:552;h:48">
				</path>
				<text id="Label" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:224.090;y:16;w:77.875;h:16" fill="#484848" transform="translate(224.08969116210938, 16)" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
				</text>
			</g>
		</g>
		<g id="template-frame" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 12 0;gap:0;primary:MIN;counter:CENTER" data-position="x:24;y:72;w:936;h:312;wMin:552" fill="#ffffff" transform="translate(24, 72)">
			<g id="Frame_617" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 12 0;gap:0;primary:CENTER;counter:MAX" data-position="x:228;y:0;w:480;h:216" transform="translate(228, 0)">
				<g id="column" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:96;h:204">
					<g id="tx-cc-1-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#df5e59" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc" transform="translate(0, -18)" fill="#ffedeb" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc" fill="#ffedeb" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc" transform="translate(-240, 12)" fill="#ffedeb" d="M336 0 L240 0 L240 72 L0 120 L192 120 L336 72 L336 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-240;y:12;w:336;h:120">
						</path>
						<path id="stroke-tc" transform="translate(0, -18)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc" transform="translate(96, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc" transform="translate(-240, 12)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 240 72 L 336 72 L 192 120 L 0 120 Z M 240 72 L 240 0 M 336 0 L 336 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-240;y:12;w:336;h:120">
						</path>
						<g id="element-height_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[0]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_2" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_1" transform="translate(8, 4)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:204" transform="translate(96, 0)">
					<g id="tx-cc-2-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#b960e2" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_1" transform="translate(0, -18)" fill="#faf0ff" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_1" fill="#faf0ff" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_1" transform="translate(-144, 12)" fill="#faf0ff" d="M240 0 L144 0 L144 72 L0 120 L192 120 L240 72 L240 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-144;y:12;w:240;h:120">
						</path>
						<path id="stroke-tc_1" transform="translate(0, -18)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_1" transform="translate(96, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_1" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_1" transform="translate(-144, 12)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 144 72 L 240 72 L 192 120 L 0 120 Z M 144 72 L 144 0 M 240 0 L 240 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-144;y:12;w:240;h:120">
						</path>
						<g id="element-height_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[1]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_4" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_3" transform="translate(8, 4)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:192;y:0;w:96;h:204" transform="translate(192, 0)">
					<g id="tx-cc-3-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#93c332" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_2" transform="translate(0, -18)" fill="#f2fae1" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_2" fill="#f2fae1" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_2" transform="translate(-48, 12)" fill="#f2fae1" d="M144 0 L48 0 L48 72 L0 120 L192 120 L144 72 L144 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-48;y:12;w:192;h:120">
						</path>
						<path id="stroke-tc_2" transform="translate(0, -18)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_2" transform="translate(96, 0)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_2" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_2" transform="translate(-48, 12)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 48 72 L 144 72 L 192 120 L 0 120 Z M 48 72 L 48 0 M 144 0 L 144 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-48;y:12;w:192;h:120">
						</path>
						<g id="element-height_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[2]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-3" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_6" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_5" transform="translate(8, 4)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:288;y:0;w:96;h:204" transform="translate(288, 0)">
					<g id="tx-cc-4-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#db8333" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_3" transform="translate(0, -18)" fill="#fef2e6" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_3" fill="#fef2e6" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_3" transform="translate(0, 12)" fill="#fef2e6" d="M96 0 L0 0 L0 72 L48 120 L240 120 L96 72 L96 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:240;h:120">
						</path>
						<path id="stroke-tc_3" transform="translate(0, -18)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_3" transform="translate(96, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_3" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_3" transform="translate(0, 12)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 48 120 L 0 72 L 96 72 L 240 120 L 48 120 Z M 0 72 L 0 0 M 96 0 L 96 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:240;h:120">
						</path>
						<g id="element-height_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[3]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-4" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_8" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_7" transform="translate(8, 4)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:384;y:0;w:96;h:204" transform="translate(384, 0)">
					<g id="tx-cc-5-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_9" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#17aee1" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_4" transform="translate(0, -18)" fill="#e8f9ff" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_4" fill="#e8f9ff" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_4" transform="translate(0, 12)" fill="#e8f9ff" d="M96 0 L0 0 L0 72 L144 120 L336 120 L96 72 L96 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:336;h:120">
						</path>
						<path id="stroke-tc_4" transform="translate(0, -18)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_4" transform="translate(96, 0)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_4" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_4" transform="translate(0, 12)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 144 120 L 0 72 L 96 72 L 336 120 L 144 120 Z M 0 72 L 0 0 M 96 0 L 96 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:336;h:120">
						</path>
						<g id="element-height_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[4]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-5" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_10" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_9" transform="translate(8, 4)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
			</g>
			<g id="column_5" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 0 0 0;gap:24;primary:CENTER;counter:MIN" data-position="x:0;y:216;w:936;h:84" transform="translate(0, 216)">
				<g id="content" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:24;w:168;h:60" transform="translate(0, 24)">
					<path id="bt-cc-remove-1" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-1-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_11" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_12" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-1" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
				</g>
				<g id="content_1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:192;y:24;w:168;h:60" transform="translate(192, 24)">
					<path id="bt-cc-remove-2" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-2-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_13" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_14" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_4" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-2" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
				</g>
				<g id="content_2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:384;y:24;w:168;h:60" transform="translate(384, 24)">
					<path id="bt-cc-remove-3" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-3-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_15" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_5" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#93c332" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_16" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-3" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
				</g>
				<g id="content_3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:576;y:24;w:168;h:60" transform="translate(576, 24)">
					<path id="bt-cc-remove-4" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-4-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_17" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_7" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_18" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_8" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-4" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
				</g>
				<g id="content_4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:768;y:24;w:168;h:60" transform="translate(768, 24)">
					<path id="bt-cc-remove-5" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-5-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_19" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_9" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-5-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_20" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_10" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<path id="bt-cc-add-5" transform="translate(-24, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24.000;h:24.000">
					</path>
					<path id="bt-cc-add-6" transform="translate(168, 0)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:0;w:24.000;h:24.000">
					</path>
				</g>
			</g>
		</g>
	</g>
</svg>
{% endif %}
{% if n == 6 %}
<svg xmlns="http://www.w3.org/2000/svg" width="1176" height="{{height}}">
	<g id="barchart-ribbons-v1--family--6" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 24 0 24;gap:0;primary:MAX;counter:CENTER" data-position="x:4595;y:-19784;w:1176;h:384">
		<g id="title-frame" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 24 0;gap:50;primary:CENTER;counter:MIN" data-position="x:24;y:0;w:1128;h:72" fill="#ffffff" transform="translate(24, 0)">
			<g id="tx-cb-title" data-entity-classes="Title" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:288;y:0;w:552;h:48" transform="translate(288, 0)">
				<path id="rect" fill="#ff00001a" d="M0 0 L552 0 L552 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:552;h:48">
				</path>
				<text id="Label" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:224.090;y:16;w:77.875;h:16" fill="#484848" transform="translate(224.08969116210938, 16)" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
				</text>
			</g>
		</g>
		<g id="template-frame" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 12 0;gap:0;primary:MIN;counter:CENTER" data-position="x:24;y:72;w:1128;h:312;wMin:552" fill="#ffffff" transform="translate(24, 72)">
			<g id="Frame_617" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 12 0;gap:0;primary:CENTER;counter:MAX" data-position="x:276;y:0;w:576;h:216" transform="translate(276, 0)">
				<g id="column" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:96;h:204">
					<g id="tx-cc-1-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#df5e59" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc" transform="translate(0, -18)" fill="#ffedeb" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc" fill="#ffedeb" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc" transform="translate(-288, 12)" fill="#ffedeb" d="M384 0 L288 0 L288 72 L0 120 L192 120 L384 72 L384 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-288;y:12;w:384;h:120">
						</path>
						<path id="stroke-tc" transform="translate(0, -18)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc" transform="translate(96, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc" transform="translate(-288, 12)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 288 72 L 384 72 L 192 120 L 0 120 Z M 288 72 L 288 0 M 384 0 L 384 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-288;y:12;w:384;h:120">
						</path>
						<g id="element-height_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[0]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_2" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_1" transform="translate(8, 4)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:96;y:0;w:96;h:204" transform="translate(96, 0)">
					<g id="tx-cc-2-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#b960e2" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_1" transform="translate(0, -18)" fill="#faf0ff" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_1" fill="#faf0ff" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_1" transform="translate(-192, 12)" fill="#faf0ff" d="M288 0 L192 0 L192 72 L0 120 L192 120 L288 72 L288 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-192;y:12;w:288;h:120">
						</path>
						<path id="stroke-tc_1" transform="translate(0, -18)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_1" transform="translate(96, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_1" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_1" transform="translate(-192, 12)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 192 72 L 288 72 L 192 120 L 0 120 Z M 192 72 L 192 0 M 288 0 L 288 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-192;y:12;w:288;h:120">
						</path>
						<g id="element-height_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[1]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_4" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_3" transform="translate(8, 4)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:192;y:0;w:96;h:204" transform="translate(192, 0)">
					<g id="tx-cc-3-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#93c332" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_2" transform="translate(0, -18)" fill="#f2fae1" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_2" fill="#f2fae1" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_2" transform="translate(-96, 12)" fill="#f2fae1" d="M192 0 L96 0 L96 72 L0 120 L192 120 L192 72 L192 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-96;y:12;w:192;h:120">
						</path>
						<path id="stroke-tc_2" transform="translate(0, -18)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_2" transform="translate(96, 0)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_2" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_2" transform="translate(-96, 12)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 96 72 L 192 72 L 192 120 L 0 120 Z M 96 72 L 96 0 M 192 0 L 192 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-96;y:12;w:192;h:120">
						</path>
						<g id="element-height_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[2]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-3" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_6" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_5" transform="translate(8, 4)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:288;y:0;w:96;h:204" transform="translate(288, 0)">
					<g id="tx-cc-4-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#db8333" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_3" transform="translate(0, -18)" fill="#fef2e6" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_3" fill="#fef2e6" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_3" transform="translate(0, 12)" fill="#fef2e6" d="M96 0 L0 0 L0 72 L0 120 L192 120 L96 72 L96 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:192;h:120">
						</path>
						<path id="stroke-tc_3" transform="translate(0, -18)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_3" transform="translate(96, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_3" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_3" transform="translate(0, 12)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 0 72 L 96 72 L 192 120 L 0 120 Z M 0 72 L 0 0 M 96 0 L 96 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:192;h:120">
						</path>
						<g id="element-height_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[3]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-4" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_8" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_7" transform="translate(8, 4)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:384;y:0;w:96;h:204" transform="translate(384, 0)">
					<g id="tx-cc-5-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_9" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#17aee1" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_4" transform="translate(0, -18)" fill="#e8f9ff" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_4" fill="#e8f9ff" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_4" transform="translate(0, 12)" fill="#e8f9ff" d="M96 0 L0 0 L0 72 L96 120 L288 120 L96 72 L96 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:288;h:120">
						</path>
						<path id="stroke-tc_4" transform="translate(0, -18)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_4" transform="translate(96, 0)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_4" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_4" transform="translate(0, 12)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 120 L 0 72 L 96 72 L 288 120 L 96 120 Z M 0 72 L 0 0 M 96 0 L 96 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:288;h:120">
						</path>
						<g id="element-height_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[4]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-5" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_10" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_9" transform="translate(8, 4)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
				<g id="column_5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 120 0;gap:36;primary:MIN;counter:CENTER" data-position="x:480;y:0;w:96;h:204" transform="translate(480, 0)">
					<g id="tx-cc-6-value" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36;wMin:96;wMax:96">
						<rect id="rect_11" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#ff00001a" width="96" height="36" rx="0" ry="0">
						</rect>
						<text id="%_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:36" fill="#d1bd08" font-size="25" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER">
						</text>
					</g>
					<g id="g-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:0;y:72;w:96;h:12" transform="translate(0, 72)">
						<path id="fill-tc_5" transform="translate(0, -18)" fill="#fefbdb" d="M48.0252 0 L0 17 L0 18 L96 18 L96 17 L48.0252 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="fill-cc_5" fill="#fefbdb" d="M96 0 L0 0 L0 12 L96 12 L96 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:12">
						</path>
						<path id="fill-bc_5" transform="translate(0, 12)" fill="#fefbdb" d="M96 0 L0 0 L0 72 L192 120 L384 120 L96 72 L96 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:384;h:120">
						</path>
						<path id="stroke-tc_5" transform="translate(0, -18)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 96 18 L 96 17 L 48.0252 0 L 0 17 L 0 18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-18;w:96;h:18">
						</path>
						<path id="stroke-rc_5" transform="translate(96, 0)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 12" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:0;w:0;h:12">
						</path>
						<path id="stroke-lc_5" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:12">
						</path>
						<path id="stroke-bc_5" transform="translate(0, 12)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 192 120 L 0 72 L 96 72 L 384 120 L 192 120 Z M 0 72 L 0 0 M 96 0 L 96 72" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:384;h:120">
						</path>
						<g id="element-height_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:96;h:{{values[5]}};wMin:96;wMax:96">
						</g>
					</g>
					<g id="ic-cc-6" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:84;w:48;h:48" transform="translate(24, 84)">
						<path id="rect_12" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 8.215650382226158e-14, 0)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48">
						</path>
						<g id="icon_10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:0;w:48;h:48" transform="translate(8.215650382226158e-14, 0)">
							<path id="icon_11" transform="translate(8, 4)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5" d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:4;w:32;h:40">
							</path>
						</g>
					</g>
				</g>
			</g>
			<g id="column_6" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 0 0 0;gap:24;primary:CENTER;counter:MIN" data-position="x:0;y:216;w:1128;h:84" transform="translate(0, 216)">
				<g id="content" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:24;w:168;h:60" transform="translate(0, 24)">
					<path id="bt-cc-remove-1" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-1-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_13" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_14" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
				</g>
				<g id="content_1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:192;y:24;w:168;h:60" transform="translate(192, 24)">
					<path id="bt-cc-remove-2" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-2-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_15" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_16" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_4" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
				</g>
				<g id="content_2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:384;y:24;w:168;h:60" transform="translate(384, 24)">
					<path id="bt-cc-remove-3" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-3-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_17" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_5" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#93c332" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_18" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
				</g>
				<g id="content_3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:576;y:24;w:168;h:60" transform="translate(576, 24)">
					<path id="bt-cc-remove-4" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-4-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_19" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_7" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_20" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_8" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
				</g>
				<g id="content_4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:768;y:24;w:168;h:60" transform="translate(768, 24)">
					<path id="bt-cc-remove-5" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-5-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_21" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_9" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-5-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_22" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_10" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
				</g>
				<g id="content_5" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:960;y:24;w:168;h:60" transform="translate(960, 24)">
					<path id="bt-cc-remove-6" transform="translate(72, -24)" fill="#1ac6ff33" d="M1.0491e-6 24 L0 1.0491e-6 L24 0 L24 24 L1.0491e-6 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24.000;h:24.000">
					</path>
					<g id="tx-ct-6-label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						<path id="rect_23" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_11" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
					<g id="tx-ct-6-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" transform="translate(0, 36)">
						<path id="rect_24" fill="#ff00001a" d="M0 0 L168 0 L168 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24">
						</path>
						<text id="Label_12" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP">
						</text>
					</g>
				</g>
			</g>
		</g>
	</g>
</svg>
{% endif %}

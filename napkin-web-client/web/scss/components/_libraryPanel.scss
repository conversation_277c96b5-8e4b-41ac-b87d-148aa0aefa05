@use '../theme/colors' as colors;

//---------------
// library-panel
//---------------

.library_panel_button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  height: 32px;
  margin: 16px 8px;
  padding-left: 0px;
  line-height: 32px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 400;
  color: #000;
  opacity: 0.42;
  cursor: pointer;
  transition: opacity 0.2s;

  opacity: colors.$app-root-enabled-opacity;
  transition: opacity colors.$app-root-hover-transition-duration;

  // Day Mode
  color: #000; // to match the icon color

  // Night Mode
  @at-root .night #{&} {
    color: #fff; // to match the icon color
  }

  &:hover {
    opacity: colors.$app-root-hover-opacity;
  }

  &.disabled {
    opacity: colors.$app-root-disabled-opacity;
    cursor: default;
  }

  &.hidden {
    opacity: 0;
  }

  &.icon {
    background-position: left center;
    background-repeat: no-repeat;
    background-size: 20px;
    padding-left: 24px;
  }

  &::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;

    background-size: 20px;
    background-position: 50% 50%;
    background-repeat: no-repeat;

    transform: rotate(0deg);
    transition: transform 0.3s ease-out;

    // Day Mode
    background-image: url('/assets/library-20px.svg');

    // Night Mode
    @at-root .night #{&} {
      background-image: url('/assets/library-20px-w.svg');
    }
  }
}

.library-panel {
  & .library {
    z-index: 5;
    position: fixed;
    display: flex;
    float: right;
    flex-direction: column;
    width: 300px;
    height: 100%;
    padding-top: 64px;
    transform: translate(-300px);
    transition: transform 0.3s ease-out;

    & .top-buttons {
      display: none;
    }

    // Overlapping case
    @at-root .overlapping#{&} {
      padding-top: 0px;

      box-shadow: colors.$day-page-box-shadow;
      background: colors.$day-page-behind-background-color;
      border-right: 1px solid colors.$day-page-border-color;
      @at-root .night #{&} {
        box-shadow: colors.$night-page-box-shadow;
        background: colors.$night-page-behind-background-color;
        border-right: 1px solid colors.$night-page-border-color;
      }

      & .top-buttons {
        height: 64px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
        padding: 0px 16px;
        & > * {
          margin: 16px 8px;
        }
      }
    }

    // It's a div to place the tutorial bubble
    & .tab-before-items {
      position: relative;
    }

    & .library-teamspace-selector {
      padding: 7px 8px 0 16px;
      position: relative;

      &.loading {
        .pending {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 10;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 8px;
          padding: 8px 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          @at-root .night #{&} {
            background: rgba(0, 0, 0, 0.9);
            color: #fff;
          }

          .spinner {
            --size: 24px;
            --dot: 4px;
          }

          p {
            margin: 8px 0 0 0;
            font-size: 12px;
            text-align: center;
            opacity: 0.8;
          }
        }
      }
    }

    & .library-teamspace-dropdown {
      width: 100%;
      display: flex;
      flex-direction: column;

      & .cx-selected-item-holder {
        max-width: 90%;
        overflow: hidden;
      }
    }

    & .divider {
      width: 100%;
      height: 1px;
      margin-top: 6px;
      transition: color colors.$theme-transition-duration;

      background: rgba(0, 0, 0, 0.1);
      // Night Mode (TODO)
      @at-root .night #{&} {
        background: rgba(255, 255, 255, 0.1);
      }
    }

    & .lists,
    & .tab-content-holder {
      flex: 1 0;
      // scroll bar on the left
      direction: rtl;
      overflow-y: auto;
      & > * {
        direction: ltr;
      }
    }

    & .no_add_page {
      flex: 0 0;
      font-size: 14px;
      margin: 0px 8px 8px 8px;
      border-radius: 8px;
      text-align: center;
      background: #ffedd6;
      color: #883f00;

      & p {
        padding: 8px;
      }

      & code {
        display: block;
        font-size: 1.4em;
        font-weight: 500;
        padding: 4px 0;
      }
    }
  }
  // take over the screen
  & .message {
    display: none;

    &.active {
      display: block;
      position: fixed;

      width: 80vw;
      top: 30vh;
      left: 10vw;
      text-align: center;
      padding: 16px;
      box-sizing: border-box;
      z-index: 100;
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 0 32px #0002;

      & p {
        padding: 16px;
      }

      & code {
        display: block;
        font-size: 1.4em;
        font-weight: 500;
        padding: 4px 0;
      }
    }
  }
  &.showing .library {
    transform: translate(0px);
  }
}

div.library-teamspace-dropdown-container {
  min-width: 290px;
  margin-left: 6px;
  margin-top: 6px;

  & .cx-items-holder {
    padding: 2px;
  }
}

@media print {
  .library-panel {
    display: none !important;
  }
}

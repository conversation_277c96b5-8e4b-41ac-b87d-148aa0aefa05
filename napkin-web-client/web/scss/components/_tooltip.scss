@use '../theme/colors' as colors;

/*
 ---------
  Tooltip
 ---------
*/

.tooltip.attach-root {
  z-index: 1000;
}

.tooltip-tester {
  position: absolute;
  top: calc(50% - 36px);
  left: calc(50% - 36px);
  width: 72px;
  height: 72px;
  background-color: #f004;
  &::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background-color: #f008;
  }
}

.tooltip_holder {
  &.tooltip_on_hover_only {
    position: relative;
    & > .tooltip {
      pointer-events: none;
      opacity: 0;
      transform: scale(0.5);
      transition: opacity colors.$onboarding-duration-out, transform colors.$onboarding-duration-out, background-color colors.$theme-transition-duration,
        color colors.$theme-transition-duration, border-color colors.$theme-transition-duration;
    }
    &:hover > .tooltip {
      opacity: 1;
      transform: scale(1);
      transition: opacity colors.$onboarding-duration-in colors.$onboarding-ease-in, transform colors.$onboarding-duration-in colors.$onboarding-ease-in,
        background-color colors.$theme-transition-duration, color colors.$theme-transition-duration, border-color colors.$theme-transition-duration;
    }
  }
}

.tooltip-upgrade-nudge {
  width: 115px;
}

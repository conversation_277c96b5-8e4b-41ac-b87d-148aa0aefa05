@use '../theme/colors' as colors;

.cx-tooltip {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: min(70vh, 100%);
  //background-color: #f004;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cx-tooltip {
  display: flex;

  // Positioning
  --nudge-x: 0;
  --nudge-y: 0;
  --absolute-x: unset;
  --absolute-y: unset;
  --max-width: 300px;
  --border-width: 0px;

  position: absolute;
  cursor: default;
  z-index: 6;

  //transition
  opacity: 0;
  transform: scale(0.5);
  transition: opacity colors.$onboarding-duration-out, transform colors.$onboarding-duration-out, background-color colors.$theme-transition-duration,
  color colors.$theme-transition-duration, border-color colors.$theme-transition-duration;

  // default color is dark
  background-color: colors.$day-menu-background-color;
  border-color: colors.$day-menu-border-color;
  color: colors.$day-menu-text-color;

  @at-root .night #{&} {
    background-color: colors.$night-menu-background-color;
    border-color: colors.$night-menu-border-color;
    color: colors.$night-menu-text-color;
  }

  &.inverted {
    background-color: colors.$night-menu-background-color;
    border-color: colors.$night-menu-border-color;
    color: colors.$night-menu-text-color;

    @at-root .night #{&} {
      background-color: colors.$day-menu-background-color;
      border-color: colors.$day-menu-border-color;
      color: colors.$day-menu-text-color;
    }
  }

  &.onboarding {
    border-color: colors.$recording-color;
    background-color: colors.$recording-lightest-color;
    color: colors.$recording-dark-color;

    // Night Mode
    @at-root .night #{&} {
      background-color: colors.$recording-dark-bg-color;
      color: colors.$recording-lighter-color;
      border-color: colors.$recording-color;
    }
  }

  &.active {
    opacity: 1;
    transform: scale(1);
    transition: opacity colors.$onboarding-duration-in colors.$onboarding-ease-in, transform colors.$onboarding-duration-in colors.$onboarding-ease-in,
    background-color colors.$theme-transition-duration, color colors.$theme-transition-duration, border-color colors.$theme-transition-duration;
  }

  & .tooltip-border {
    padding: 4px 6px;

    position: relative;
    border-radius: 8px;
    border-width: var(--border-width);
    border-style: solid;

    font-family: geomanist;
    font-size: 12px;
    font-weight: 500;
    line-height: 14px;

    box-sizing: content-box;
    width: fit-content;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 12px;
    text-align: center;

    background-color: inherit;
    border-color: inherit;

    max-width: var(--max-width);
  }

  // Positioning

  // pointy
  &:after {
    content: '';
    display: none;
    position: relative;
    width: 8px;
    height: 8px;
    padding: 4px;
    box-sizing: border-box;
    z-index: 1;
    border: var(--border-width) solid;
    border-width: 0 var(--border-width) var(--border-width) 0;
    border-top-left-radius: 10px; // to not covert the content inside
    background-color: inherit; // follow parent transition
    border-color: inherit; // follow parent transition
  }

  &.no-wrap {
    & .instructions {
      white-space: nowrap;
    }
  }

  // Side-dependent properties
  //--------------------------

  &.pointy-none {
    left: 50%;
    width: 0; // to center the children

    flex-direction: column;
    align-items: center;

    margin-left: var(--nudge-x);
    margin-bottom: var(--nudge-y);

    &.absolute {
      left: var(--absolute-x);
      top: var(--absolute-y);
    }
  }

  // Bottom
  //--------
  &.pointy-bottomLeft,
  &.pointy-bottomCenter,
  &.pointy-bottomRight {
    // Default to the center top of the parent
    left: 50%;
    bottom: 100%;
    top: unset;
    margin-left: var(--nudge-x);
    margin-bottom: var(--nudge-y);

    width: 0; // to center the children
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;

    &.absolute {
      left: var(--absolute-x);
      bottom: calc(100% - var(--absolute-y));
    }

    & .tooltip-border {
      box-shadow: 0px -4px 4px 0px rgba(0, 0, 0, 0.1);
    }

    &:after {
      right: unset;
      top: unset;
      display: block;
      bottom: 0;
      transform: translate(0, -5px) rotate(45deg);
    }

    &.pointy-bottomCenter {
      transform-origin: center bottom;
    }

    &.pointy-bottomLeft {
      transform-origin: left 24px;
      & .tooltip-border {
        left: -24px;
        align-self: flex-start;
      }
    }

    &.pointy-bottomRight {
      transform-origin: right 24px;
      & .tooltip-border {
        left: 24px;
        align-self: flex-end;
      }
    }
  }

  // Right
  //------
  &.pointy-rightTop,
  &.pointy-rightCenter,
  &.pointy-rightBottom {
    // Default to the center left of the parent
    right: 100%;
    top: 50%;
    margin-right: var(--nudge-x);
    margin-top: var(--nudge-y);

    height: 0; // to center the children
    flex-direction: row;
    align-items: center;

    &.absolute {
      right: calc(100% - var(--absolute-x));
      top: var(--absolute-y);
    }

    &:after {
      left: unset;
      bottom: unset;
      display: block;
      right: 0;
      transform: translate(-5px, -4.25px) rotate(315deg);
    }

    &.pointy-rightCenter {
      transform-origin: right center;
    }

    &.pointy-rightTop {
      transform-origin: right 24px;
      & .tooltip-border {
        top: -24px;
        align-self: flex-start;
      }
    }

    &.pointy-rightBottom {
      transform-origin: right calc(100% - 24px);
      & .tooltip-border {
        top: 24px;
        align-self: flex-end;
      }
    }
  }

  // Left
  //------
  &.pointy-leftTop,
  &.pointy-leftCenter,
  &.pointy-leftBottom {
    // Default to the center right of the parent
    left: 100%;
    top: 50%;
    margin-left: var(--nudge-x);
    margin-top: var(--nudge-y);

    height: 0; // to center the children
    flex-direction: row-reverse;
    align-items: center;

    &.absolute {
      left: var(--absolute-x);
      top: var(--absolute-y);
    }

    &:after {
      right: unset;
      bottom: unset;
      display: block;
      left: 0;
      transform: translate(5px, 4px) rotate(135deg);
    }

    &.pointy-leftCenter {
      transform-origin: left center;
    }

    &.pointy-leftTop {
      transform-origin: left 24px;
      & .tooltip-border {
        top: -24px;
        align-self: flex-start;
      }
    }

    &.pointy-leftBottom {
      transform-origin: left calc(100% - 24px);
      & .tooltip-border {
        top: 24px;
        align-self: flex-end;
      }
    }
  }

  // Top
  //-----
  &.pointy-topLeft,
  &.pointy-topCenter,
  &.pointy-topRight {
    // Default to the center bottom of the parent
    left: 50%;
    top: 100%;
    margin-left: var(--nudge-x);
    margin-top: var(--nudge-y);

    width: 0; // to center the children
    flex-direction: column-reverse;
    justify-content: flex-end;
    align-items: center;

    --viewport-left: 0;
    --viewport-top: 0;

    &.absolute {
      left: var(--absolute-x);
      top: var(--absolute-y);
    }

    & .tooltip-border {
      box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.1);
    }

    &:after {
      right: unset;
      bottom: unset;
      display: block;
      top: 0;
      //transform: translate(4px, 5px) rotate(225deg);
      transform: translate(0px, 5px) rotate(225deg);
    }

    &.pointy-topCenter {
      transform-origin: center top;
    }

    &.pointy-topLeft {
      transform-origin: left 24px;
      & .border {
        left: -24px;
        align-self: flex-start;
      }
    }

    &.pointy-topRight {
      transform-origin: right 24px;
      & .border {
        left: 24px;
        align-self: flex-end;
      }
    }
  }

  &.fixed {
    position: fixed;
    left: var(--viewport-left) !important;
    top: var(--viewport-top) !important;
    right: unset !important;
    bottom: unset !important;
    margin: unset !important;
    height: unset !important;
  }
}
import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;

import 'package:ai_web_lib/ai_web_lib.dart';
import 'package:analytics_web_lib/analytics_web_lib.dart';
import 'package:collection/collection.dart';
import 'package:io_web_lib/logging.dart';
import 'package:meta/meta.dart';
import 'package:util_web_lib/browser_storage.dart';
import 'package:util_web_lib/create_svg.dart';
import 'package:util_web_lib/dialog.dart';
import 'package:util_web_lib/ecs.dart';
import 'package:util_web_lib/environment.dart';
import 'package:util_web_lib/has_subscription.dart';
import 'package:util_web_lib/helper.dart' as helper;
import 'package:util_web_lib/html_root.dart';
import 'package:util_web_lib/js_interop.dart';
import 'package:util_web_lib/privateer.dart';
import 'package:util_web_lib/releaser.dart';
import 'package:util_web_lib/ui-kit/cx_enums.dart' show CxPointyPlacement;
import 'package:util_web_lib/ui-kit/cx_tooltip.dart';
import 'package:util_web_lib/ui-kit/cx_toggle.dart';

import '../../../app/user_config.dart';
import '../../graph/graph.dart';
import '../../graph/tool/tool.dart';
import '../../page/orchestrator/feature_disabler.dart';
import '../../page/page_export_markers.dart';
import '../../page/pdf/page_pdf.dart';
import '../../plans/plan_enums.dart' show PlanFeatureKey;
import '../workspace/upgrade_nudge.dart';

part 'export_format.dart';
part 'export_settings.dart';

const previewMaxWidth = 376.0; // min width of the container - 1 pixel border
const previewMaxHeight = 376.0;

class ExportDialog with HtmlRoot, HasSubscription, Releaser, Privateer<ExportDialogPrivateer> {
  static final Logger _log = Logger('ExportDialog', browser: Logger.DEBUG);

  static final String _settingsStorageKeyDocument = 'exportDocumentModal_settings';
  static final String _settingsStorageKeyVisual = 'exportVisualModal_settings';

  Dialog? _dialog;
  bool _released = false;
  CxTooltip? _tooltipEl;

  ToolDownload? _host;
  Graph? get _graph => _host?.graph;

  final List<ReadOnlyEntity>? entities;
  bool get _isExportingDocument => entities == null;

  ExportSettings? _settings;
  bool _settingsAreLoaded = false;
  String get _settingsStorageKey => _isExportingDocument ? _settingsStorageKeyDocument : _settingsStorageKeyVisual;

  // Computed properties
  bool get useSolidBackground => _settings?.useSolidBackground == true && _selectedExportType?.type != DownloadType.pptx;
  bool get useWatermark => _useWatermarkOverride ?? _settings?.useWatermark == true;
  bool? get _useWatermarkOverride => _selectedExportType?.type == DownloadType.pptx ? false : null;

  static bool? get useWatermarkForDocument {
    return ExportSettings.load(_settingsStorageKeyDocument)?.useWatermark;
  }

  final List<ExportFormat> _exportTypes = [];
  ExportFormat? _selectedExportType;
  set selectedExportType(ExportFormat? value) {
    if (_selectedExportType != value) {
      _disableAllExportTypes();
      _selectedExportType = value;
      _selectedExportType?.isActive = true;
      _settings = _settings?.copyWith(exportFormat: value?.type.name);
      _refreshUI();

      _sendSettingsAnalyticsEvent('export_type', 'set', data: {'value': value?.type.name});
    }
  }

  String? _hoveredExportTypeDescription;
  String? get _exportTypeDescription => _hoveredExportTypeDescription ?? _selectedExportType?.description;

  String? _defaultPdfFormat;

  Future? _waitBeforeExport; // E.g. wait for all the saves to complete
  Function(PageExportMarkersType)? _onExportMarkersTypeUpdated;

  // UI elements
  HTMLElement? _previewElement;

  final List<HTMLInputElement> _formatInputs = [];

  bool _hasBeenCopiedToClipboardPrivate = false;
  bool get _hasBeenCopiedToClipboard => _hasBeenCopiedToClipboardPrivate;
  set _hasBeenCopiedToClipboard(bool value) {
    _hasBeenCopiedToClipboardPrivate = value;

    if (value) {
      helper.delayedCall(3, () {
        if (_hasBeenCopiedToClipboardPrivate) {
          _hasBeenCopiedToClipboardPrivate = false;
          rebuild();
        }
      }, channel: '_hasBeenCopiedToClipboard_delayedCall');
    }
  }

  String get _analyticsMonitoringEventKey => _isExportingDocument ? 'EXPORT_DOCUMENT_DIALOG' : 'EXPORT_IMAGE_DIALOG';
  final Map<String, int> _settingsAnalyticsCounters = {};
  bool _fromFrameMenu = false;
  bool _isFirstExport = false;

  static ExportDialog? _singleton;
  factory ExportDialog(
    ToolDownload host, {
    List<ReadOnlyEntity>? entities,
    bool fromFrameMenu = false,
    String? defaultPdfFormat,
    Function(PageExportMarkersType)? onExportMarkersTypeUpdated,
  }) {
    // Make sure we only have one instance of ExportVisualModal
    if (_singleton != null) {
      _singleton?.release();
      _singleton = null;
    }

    _singleton = ExportDialog._(
      host,
      entities: entities,
      fromFrameMenu: fromFrameMenu,
      defaultPdfFormat: defaultPdfFormat,
      onExportMarkersTypeUpdated: onExportMarkersTypeUpdated,
    );
    return _singleton!;
  }

  ExportDialog._(
    ToolDownload host, {
    this.entities,
    bool fromFrameMenu = false,
    String? defaultPdfFormat,
    Function(PageExportMarkersType)? onExportMarkersTypeUpdated,
  }) {
    _host = host;
    _fromFrameMenu = fromFrameMenu;
    _defaultPdfFormat = defaultPdfFormat;
    _onExportMarkersTypeUpdated = onExportMarkersTypeUpdated;

    _exportTypes.addAll(
      _isExportingDocument
          ? [ExportFormatPDF(onClick: _onFormatSelected, onMouseEnter: _onFormatMouseEnter, onMouseLeave: _onFormatMouseLeave)]
          : [
              ExportFormatPNG(onClick: _onFormatSelected, onMouseEnter: _onFormatMouseEnter, onMouseLeave: _onFormatMouseLeave),
              ExportFormatSVG(onClick: _onFormatSelected, onMouseEnter: _onFormatMouseEnter, onMouseLeave: _onFormatMouseLeave),
              ExportFormatPDF(onClick: _onFormatSelected, onMouseEnter: _onFormatMouseEnter, onMouseLeave: _onFormatMouseLeave),
              ExportFormatPPT(onClick: _onFormatSelected, onMouseEnter: _onFormatMouseEnter, onMouseLeave: _onFormatMouseLeave),
            ],
    );

    if (_exportTypes.length == 1) {
      selectedExportType = _exportTypes.first;
    }

    final defaultFormatType = _exportTypes.first.type;
    _settings = ExportSettings(
      exportFormat: defaultFormatType.name,
      colorMode: _graph?.isInvertLightness == true ? ExportModeSetting.night : ExportModeSetting.day,
      useWatermark: getUseWatermarkDefaultValue(_graph, defaultFormatType, _graph?.watermarkSetting.toBool()),
    );
    final savedSettings = _loadSettings();
    if (savedSettings == null) {
      _isFirstExport = true;
    }

    if (_exportTypes.length > 1) {
      final downloadType = savedSettings?.exportFormat != null ? DownloadType.fromString(savedSettings?.exportFormat ?? '') : null;
      selectedExportType = downloadType != null ? _exportTypes.firstWhereOrNull((e) => e.type == downloadType) : null;
    }
  }

  void release() {
    if (_released) return;
    _released = true;

    _formatInputs.clear();

    _settingsAnalyticsCounters.clear();

    _previewElement?.remove();
    _previewElement = null;

    _dialog?.close();
    _dialog = null;

    _tooltipEl?.releaseComponent();
    _tooltipEl = null;

    helper.terminateDelayedCall(channel: '_hasBeenCopiedToClipboard_delayedCall');

    releaseSubscriptions();

    for (final exportFormat in _exportTypes) {
      exportFormat.release();
    }
    _exportTypes.clear();
    releaseAll();

    _host = null;
    _singleton = null;
  }

  void _refreshUI() {
    _hasBeenCopiedToClipboard = false;
    super.rebuild();
  }

  void _refreshPreview() {
    _hasBeenCopiedToClipboard = false;
    if (_previewElement != null) {
      // rebuild just the preview element
      final newPreviewElement = _buildPreview();
      if (newPreviewElement != null) {
        _previewElement?.replaceWith(newPreviewElement);
      }
      _previewElement = newPreviewElement;
    } else {
      super.rebuild();
    }
  }

  //=============================================================================
  // Build
  //=============================================================================

  @override
  HTMLElement build() {
    _previewElement?.remove();
    _previewElement = _buildPreview();
    return HTMLDivElement()..appendAllNotNull([_buildHeader(), _buildContent()]);
  }

  // ********** Header **********
  HTMLElement _buildHeader() {
    return HTMLDivElement()
      ..classList.addAll(['export-dialog__header'])
      ..appendAllNotNull([_buildHeaderTitle(), _buildHeaderFormatSelector()]);
  }

  HTMLElement _buildHeaderTitle() {
    return HTMLDivElement()
      ..classList.addAll(['icon', 'download', 'title-container'])
      ..appendChild(
        HTMLParagraphElement()
          ..classList.add('title')
          ..textContent = _isExportingDocument ? 'Export PDF' : 'Export Visual',
      );
  }

  HTMLElement? _buildHeaderFormatSelector() {
    // Only show format selector if there are multiple formats
    if (_exportTypes.length < 2 && _selectedExportType != null) return null;

    final container = HTMLDivElement()..classList.addAll(['export-dialog__format-options']);

    final exportTypeContainer = HTMLDivElement()..classList.add('export-dialog__format-options__items');
    container.appendChild(exportTypeContainer);

    for (final exportFormat in _exportTypes) {
      final exportLeft = _host?.context?.workSpaceManager.getActiveWorkspaceCredits(exportFormat.featureKey);
      final showExportLeft = exportLeft != null && exportLeft < Int.maxSafe;
      exportFormat.exportLeft = showExportLeft ? exportLeft : null;
      if (_selectedExportType == null) exportFormat.isActive = true;
      final format = exportFormat.build();
      exportTypeContainer.appendChild(format);
    }

    final description = HTMLParagraphElement()
      ..classList.add('export-dialog__format-options__description')
      ..textContent = _exportTypeDescription ?? '';
    container.appendChild(description);

    return container;
  }

  // ********** Content **********
  HTMLElement _buildContent() {
    return HTMLDivElement()
      ..classList.addAll(['export-dialog__content'])
      ..appendAllNotNull([_buildSettings(), _previewElement, _buildExportButtons()]);
  }

  //=============================================================================
  // Settings
  //=============================================================================

  HTMLElement? _buildSettings() {
    if (!_settingsAreLoaded) {
      return null;
    }

    final selectedFormat = _selectedExportType;
    if (selectedFormat == null) {
      return HTMLDivElement()
        ..classList.addAll(['export-dialog__instructions'])
        ..textContent = 'Pick a file format';
    }

    final container = HTMLDivElement()..classList.addAll(['export-dialog__settings']);

    final inlineSettings = HTMLDivElement()
      ..classList.addAll(['content-row', 'type-settings'])
      ..appendNotNull(selectedFormat.getSettings(_isExportingDocument).contains(ExportSettingsEnum.exportWatermark) ? _buildWatermarkSelector() : null)
      ..appendNotNull(selectedFormat.getSettings(_isExportingDocument).contains(ExportSettingsEnum.exportColorMode) ? _buildExportColorModeSelector() : null)
      ..appendNotNull(
        selectedFormat.getSettings(_isExportingDocument).contains(ExportSettingsEnum.exportWithBackground) ? _buildExportWithBackgroundSelector() : null,
      )
      ..appendNotNull(selectedFormat.getSettings(_isExportingDocument).contains(ExportSettingsEnum.exportResolution) ? _buildResolutionSelector() : null);

    container.appendChild(inlineSettings);
    container.appendNotNull(selectedFormat.getSettings(_isExportingDocument).contains(ExportSettingsEnum.exportPdfFormat) ? _buildPdfFormatOptions() : null);

    return container;
  }

  HTMLElement _buildExportColorModeSelector() {
    final colorModeToggle = _createColorModeToggle(
      _settings?.colorMode ?? ExportModeSetting.day,
      (value) {
        _settings = _settings?.copyWith(colorMode: value);
        _refreshPreview();
      },
      _sendSettingsAnalyticsEvent,
      this,
    );

    return _createToggleSettingContainer('Color mode', 'export-dialog__color-mode-options', colorModeToggle.root);
  }

  HTMLElement _buildExportWithBackgroundSelector() {
    final backgroundToggle = _createBackgroundToggle(
      _settings?.useSolidBackground ?? true,
      (value) {
        _settings = _settings?.copyWith(useSolidBackground: value);
        _refreshPreview();
      },
      _sendSettingsAnalyticsEvent,
      this,
    );

    return _createToggleSettingContainer('Background', 'export-dialog__background-options', backgroundToggle.root);
  }

  HTMLElement? _buildWatermarkSelector() {
    final watermarkToggle = _createWatermarkToggle(
      _settings?.useWatermark ?? true,
      (value) {
        _settings = _settings?.copyWith(useWatermark: value);
        _refreshPreview();
      },
      _sendSettingsAnalyticsEvent,
      this,
    );

    if (watermarkToggle == null) return null;

    watermarkToggle.setOnRight(!useWatermark, triggerCallback: false);

    return _createToggleSettingContainer('Napkin Logo', 'export-dialog__watermark-options', watermarkToggle.root..classList.addAll(['icon', 'pro-icon']));
  }

  HTMLElement _buildResolutionSelector() {
    final resolutionToggle = _createResolutionToggle(
      _settings?.resolution ?? 3,
      (value) {
        _settings = _settings?.copyWith(resolution: value);
        _refreshPreview();
      },
      _sendSettingsAnalyticsEvent,
      this,
    );

    return _createToggleSettingContainer('Resolution', 'export-dialog__background-options', resolutionToggle.root);
  }

  HTMLElement _buildPdfFormatOptions() {
    final formatOptions = [
      {'label': 'Single continuous page', 'value': FORMAT_CONTINUOUS},
      {'label': 'A4 (standard)', 'value': FORMAT_A4},
      {'label': 'US Letter (8.5 x 11 inches)', 'value': FORMAT_US_LETTER},
      {'label': 'Presentation (16:9)', 'value': FORMAT_PRESENTATION},
    ];

    final formatContent = HTMLDivElement()..classList.add('radio-group');

    _formatInputs.clear();

    for (final option in formatOptions) {
      final formatValue = option['value'] ?? '';

      // TODO: store marker type in settings
      // Also, merge markerstyle and pdf format enums
      final markerType = PagePdf.pdfFormatToPageExportMarkersType(formatValue) ?? PageExportMarkers.defaultSetting;

      final input = HTMLInputElement()
        ..type = 'radio'
        ..name = 'format'
        ..id = 'format_$formatValue'
        ..value = formatValue
        ..checked = formatValue == _defaultPdfFormat;

      _formatInputs.add(input);

      autoUnsubscribe(
        input.onChange.listen((e) {
          _settings = _settings?.copyWith(exportFormat: formatValue);
          _onExportMarkersTypeUpdated?.call(markerType); // TODO: Remove this logic from the modal
        }),
      );

      final optionElement = HTMLDivElement()
        ..classList.add('option')
        ..appendAll([
          input,
          HTMLLabelElement()
            ..setAttribute('for', 'format_$formatValue')
            ..textContent = option['label'] ?? '',
        ]);

      formatContent.appendChild(optionElement);
    }

    final header = HTMLParagraphElement()
      ..classList.add('setting-name')
      ..textContent = 'Page Format';

    return HTMLDivElement()
      ..appendChild(header)
      ..appendChild(formatContent);
  }

  //=============================================================================
  // Preview
  //=============================================================================

  HTMLElement? _buildPreview() {
    if (_isExportingDocument) return null;

    final selectedFormat = _selectedExportType;
    if (selectedFormat == null) return null;

    final container = HTMLDivElement()..classList.add('export-dialog__preview');

    final graph = _graph;
    if (graph == null) {
      return container;
    }

    final exportInNight = _settings?.colorMode == ExportModeSetting.night;
    final addSolidBackground = useSolidBackground || !selectedFormat.getSettings(_isExportingDocument).contains(ExportSettingsEnum.exportWithBackground);
    final resolutionScale = selectedFormat.getSettings(_isExportingDocument).contains(ExportSettingsEnum.exportResolution) ? _settings?.resolution ?? 1 : 1;
    final exportData = ExportData.create(
      graph,
      entities ?? [],
      onlySvg: true,
      exportInNight: exportInNight,
      addSolidBackground: addSolidBackground,
      addWatermark: useWatermark,
      resolutionScale: resolutionScale,
    );
    final svgElement = CreateSvg.parse(exportData.svg.$1);

    final originalWidth = Double.safe(svgElement.getAttribute('width')) ?? 100;
    final originalHeight = Double.safe(svgElement.getAttribute('height')) ?? 100;

    var computedWidth = originalWidth;
    var computedHeight = originalHeight;
    var scale = 1.0;

    if (computedWidth < computedHeight) {
      computedHeight = (math.min(previewMaxHeight, originalHeight));
      computedWidth = (originalWidth * (computedHeight / originalHeight));
      scale = math.min(scale, previewMaxHeight / originalHeight);
    } else {
      computedWidth = (math.min(previewMaxWidth, originalWidth));
      computedHeight = (originalHeight * (computedWidth / originalWidth));
      scale = math.min(scale, previewMaxWidth / originalWidth);
    }

    final svgContainer = HTMLDivElement()
      ..classList.add('export-dialog__preview__svg-container')
      ..style.setProperty('transform', 'scale($scale)')
      ..setAttribute(
        'data-background',
        addSolidBackground
            ? 'solid'
            : exportInNight
            ? 'transparent-night'
            : 'transparent-day',
      )
      ..appendChild(svgElement);

    final previewContainer = HTMLDivElement()
      ..classList.add('export-dialog__preview__container')
      ..style.setProperty('width', '${computedWidth}px')
      ..style.setProperty('height', '${computedHeight}px')
      ..appendChild(svgContainer);

    container
      ..style.setProperty('width', '${computedWidth}px')
      ..style.setProperty('minHeight', '${computedHeight}px');

    container.appendChild(previewContainer);

    final resolution = HTMLParagraphElement()
      ..classList.addAll([
        'export-dialog__preview__resolution',
        if (!selectedFormat.getSettings(_isExportingDocument).contains(ExportSettingsEnum.exportResolution)) 'export-dialog__preview__resolution--hidden',
      ])
      ..textContent = '${(originalWidth * resolutionScale).ceil()} x ${(originalHeight * resolutionScale).ceil()}';
    container.appendChild(resolution);

    return container;
  }

  //=============================================================================
  // Buttons
  //=============================================================================

  HTMLElement? _buildExportButtons() {
    final selectedFormat = _selectedExportType;
    if (selectedFormat == null) return null;

    final container = HTMLDivElement()..classList.addAll(['export-dialog__export-options', 'options']);

    // Safari does not support copying to clipboard
    if (!Environment.browser.isSafari) {
      // PDF cannot be copied to clipboard
      if (selectedFormat.canCopyToClipboard && !FeatureDisabler.isPlanFeatureDisabled(selectedFormat.featureKey)) {
        if (_hasBeenCopiedToClipboard) {
          final copiedToClipboard = HTMLParagraphElement()
            ..classList.add('export-dialog__copied-to-clipboard')
            ..textContent = 'Copied';
          container.appendChild(copiedToClipboard);
        } else {
          final copyToClipboardButton = HTMLButtonElement()
            ..classList.addAll(['secondary', 'button', 'icon', 'clipboard'])
            ..textContent = 'Clipboard';
          autoUnsubscribeBuild(copyToClipboardButton.onClick.listen(_copyToClipboard));
          container.appendChild(copyToClipboardButton);
        }
      }
    }

    final downloadButton = HTMLButtonElement()
      ..classList.addAll(['primary', 'button', 'icon', 'download', 'inverted'])
      ..textContent = 'Download';
    autoUnsubscribeBuild(downloadButton.onClick.listen(_export));
    container.appendChild(downloadButton);

    container.setAttribute('data-options', container.children.length.toString());

    return container;
  }

  //=============================================================================
  // Listeners
  //=============================================================================

  void _disableAllExportTypes() {
    for (final exportFormat in _exportTypes) {
      exportFormat.isActive = false;
    }
  }

  void _onFormatSelected(ExportFormat format) {
    if (_selectedExportType != format) {
      selectedExportType = format;
    }
  }

  void _onFormatMouseEnter(ExportFormat format) {
    if (_hoveredExportTypeDescription != format.description) {
      _hoveredExportTypeDescription = format.description;
      _refreshUI();
    }
  }

  void _onFormatMouseLeave(ExportFormat format) {
    if (_hoveredExportTypeDescription != null) {
      _hoveredExportTypeDescription = null;
      _refreshUI();
    }
  }

  //=============================================================================
  // Export logic
  //=============================================================================

  Future<bool> _export(_) async {
    final host = _host;
    final graph = _graph;
    if (host == null || graph == null) {
      _log.warning(() => '[export] graph is null');
      return false;
    }

    final selectedFormat = _selectedExportType;
    if (selectedFormat != null) {
      final isFeatureDisabled = FeatureDisabler.isPlanFeatureDisabled(selectedFormat.featureKey);
      _log.info(() => '[export] selectedFormat: ${selectedFormat.type}, featureKey: ${selectedFormat.featureKey}, isFeatureDisabled: $isFeatureDisabled');
      if (isFeatureDisabled) {
        // Free Tag is hard-coded and will not scale with custom plans.
        final message = HTMLDivElement()..dangerouslySetInnerHtml('You\'ve ran out of&nbsp;<span class="cx-icon free-tag inverted" style="display:contents"></span>&nbsp;${selectedFormat.name.capitalize()} exports.<br/>Upgrade to a paid plan for unlimited exports<br/>and other exciting features');
        await WorkspaceUpgradeNudgeDialog(host.context?.workSpaceManager.activeWorkspaceDetails?.planClass.next, message).show();
        return false;
      }

      _saveSettings();

      host.download(
        selectedFormat.type,
        entities: entities ?? [],
        exportInNight: host.getExportInNight(_settings?.colorMode),
        addSolidBackground: useSolidBackground,
        addWatermark: useWatermark,
        resolutionScale: _settings?.resolution,
        waitBeforeExport: _waitBeforeExport,
        pdfFormat: _settings?.exportFormat,
      );

      host.context?.workSpaceManager.tryConsumeCreditsFromActiveWorkspace(selectedFormat.featureKey);
      _sendExportAnalyticsEvent();
      close();
      return true;
    }
    return false;
  }

  void _copyToClipboard(_) {
    final host = _host;
    final graph = _graph;
    if (host == null || graph == null) {
      _log.warning(() => '[copyToClipboard] graph is null');
      return;
    }

    final selectedFormat = _selectedExportType;
    if (selectedFormat != null) {
      host.copyToClipboard(
        selectedFormat.type,
        entities: entities ?? [],
        exportInNight: host.getExportInNight(_settings?.colorMode),
        addSolidBackground: useSolidBackground,
        addWatermark: useWatermark,
        resolutionScale: _settings?.resolution,
      );

      _saveSettings();

      _sendExportAnalyticsEvent(isCopy: true);
      _hasBeenCopiedToClipboard = true;
      rebuild();
    }
  }

  //=============================================================================
  // Dialog
  //=============================================================================

  void open({Future? waitBeforeExport}) async {
    _waitBeforeExport = waitBeforeExport;
    _dialog = Dialog.custom([root], closeButton: true, containerClasses: ['export-dialog', 'gen2'])..show();

    _sendDialogAnalyticsEvent('OPEN');
  }

  void close() {
    _sendDialogAnalyticsEvent('CLOSE');

    _dialog?.close();
    release();
  }

  //=============================================================================
  // Settings
  //=============================================================================

  void _saveSettings() {
    _settings?.save(_settingsStorageKey);
  }

  ExportSettings? _loadSettings() {
    final savedSettings = ExportSettings.load(_settingsStorageKey);
    if (savedSettings != null) {
      _settings = savedSettings.copyWith();
    }

    _settingsAreLoaded = true;
    _refreshUI();
    return savedSettings;
  }

  static void clearSettings() {
    ExportSettings.clear(_settingsStorageKeyDocument);
    ExportSettings.clear(_settingsStorageKeyVisual);
  }

  static bool getUseWatermarkDefaultValue(Graph? graph, DownloadType exportType, bool? defaultWatermark) {
    return WatermarkSetting.fromFlags().toBool(overrideDefault: defaultWatermark);
  }

  //=============================================================================
  // Analytics
  //=============================================================================

  void _sendSettingsAnalyticsEvent(String name, String action, {JsonMap data = const {}}) {
    final analyticsParams = {
      'name': name,
      'action': action,
      'count': _settingsAnalyticsCounters[name] ?? 0,
      'is_exporting_document': _isExportingDocument,
      'is_first_export': _isFirstExport,
      ...data,
    };
    Analytics().trackEvents(
      AnalyticsEvents(
        monitoring: AnalyticsEvent('${_analyticsMonitoringEventKey}/SETTINGS/${name.toUpperCase()}/${action.toUpperCase()}', analyticsParams),
        tagManager: AnalyticsEvent('napkin_document_export_settings_action', GoogleTagManager.buildPayload(eventParams: analyticsParams)),
      ),
    );

    _settingsAnalyticsCounters[name] = (_settingsAnalyticsCounters[name] ?? 0) + 1;
  }

  void _sendDialogAnalyticsEvent(String action, {JsonMap? data}) {
    Analytics().trackEvents(
      AnalyticsEvents(
        monitoring: AnalyticsEvent('${_analyticsMonitoringEventKey}/${action.toUpperCase()}', {
          ...?data,
          'exportFormat': _selectedExportType?.type.type,
          'exportMode': _settings?.colorMode.name,
          'exportWithSolidBackground': useSolidBackground,
        }),
        tagManager: AnalyticsEvent(
          'napkin_document_export_dialog',
          GoogleTagManager.buildPayload(
            eventParams: {
              'action': action,
              'is_exporting_document': _isExportingDocument,
              'is_first_export': _isFirstExport,
              'color_mode': _settings?.colorMode.name,
              'resolution': _settings?.resolution,
              'napkin_watermark': _settings?.useWatermark,
              'solid_background': useSolidBackground,
              if (_isExportingDocument) 'pdf_format': _settings?.exportFormat,
            },
          ),
        ),
      ),
    );
  }

  void _sendExportAnalyticsEvent({bool isCopy = false}) async {
    if (_selectedExportType?.type.type != 'pdf') {
      final exportedEntities = entities ?? [];

      final visualTypes = Graph.getSceneNamesFromEntities(exportedEntities)
          .map((e) {
            final parts = e.split('-');
            return (parts.firstOrNull ?? e).toLowerCase();
          })
          .toSet()
          .toList();

      final themeFrameEntity = AiComponentThemeFrame.getThemeFrameEntities(exportedEntities, includeParentThemeFrame: true).firstOrNull;
      final visualStyle = themeFrameEntity?.readOnlyComponent<AiComponentTheme>()?.themeBaseName;
      final visualHasBuiltInStyle = visualStyle != null ? _graph?.themesRepository.isBuiltInTheme(visualStyle) : null;
      final visualHasCustomStyle = visualHasBuiltInStyle == null ? null : !visualHasBuiltInStyle;

      final selectedFormatType = _selectedExportType?.type.type.toLowerCase();
      Analytics().trackEvents(
        AnalyticsEvents(
          context: AnalyticsContextEvent(
            ContextEventName.export_image,
            parameters: {'source': 'selection', 'format': selectedFormatType, 'visual_types': visualTypes},
          ),
          tagManager: AnalyticsEvent('napkin_document_export_${selectedFormatType}', {
            'source': 'selection',
            'is_copy': isCopy,
            'format': selectedFormatType,
            'entities_count': entities?.length,
            'visual_types': json.encode(visualTypes),
            'visual_style': visualStyle,
            'visual_has_custom_style': ?visualHasCustomStyle,
            'user_has_custom_styles': (await _graph?.themesRepository.getAllCustom())?.isNotEmpty == true,
            'export_mode': _settings?.colorMode.name,
            'export_with_solid_background': useSolidBackground,
            'resolution_scale': _settings?.resolution,
            'napkin_watermark': useWatermark,
            'is_from_theme_frame_menu': _fromFrameMenu,
            'is_first_export': _isFirstExport,
            'theme_frame_id': themeFrameEntity?.id.string,
            'hash': helper.hashAll(exportedEntities.map((e) => e.id.string).sorted()),
          }),
        ),
      );
    }

    _sendDialogAnalyticsEvent('EXPORT${isCopy ? '/COPY' : ''}', data: {'download': _selectedExportType?.type.name, 'count': entities?.length});
  }

  //=============================================================================
  // Settings toggles
  //=============================================================================

  /// Creates a color mode selector toggle element
  CxToggle2 _createColorModeToggle(
    ExportModeSetting currentValue,
    void Function(ExportModeSetting) onChanged,
    void Function(String, String, {JsonMap data}) sendAnalytics,
    Releaser releaser,
  ) {
    final leftElement = HTMLDivElement()
      ..classList.addAll(['icon', 'day'])
      ..textContent = 'Light'
      ..title = 'Light theme';

    final rightElement = HTMLDivElement()
      ..classList.addAll(['icon', 'night'])
      ..textContent = 'Dark'
      ..title = 'Dark theme';

    return CxToggle2(
      (newOnRight) {
        final newValue = newOnRight ? ExportModeSetting.night : ExportModeSetting.day;
        onChanged(newValue);
        sendAnalytics('color_mode', 'set', data: {'value': newValue.name});
      },
      releaser: releaser,
      startOnRight: currentValue == ExportModeSetting.night,
      withIcons: true,
      leftElement: leftElement,
      rightElement: rightElement,
    );
  }

  /// Creates a resolution selector toggle element
  CxToggle3 _createResolutionToggle(
    int currentValue,
    void Function(int) onChanged,
    void Function(String, String, {JsonMap data}) sendAnalytics,
    Releaser releaser,
  ) {
    final leftElement = HTMLDivElement()
      ..textContent = '1x'
      ..title = '1x';

    final middleElement = HTMLDivElement()
      ..textContent = '2x'
      ..title = '2x';

    final rightElement = HTMLDivElement()
      ..textContent = '3x'
      ..title = '3x';

    return CxToggle3(
      (newState) {
        final newValue = switch (newState) {
          Toggle3State.onLeft => 1,
          Toggle3State.onMiddle => 2,
          Toggle3State.onRight => 3,
        };
        onChanged(newValue);
        sendAnalytics('resolution', 'set', data: {'value': newValue});
      },
      releaser: releaser,
      startState: switch (currentValue) {
        1 => Toggle3State.onLeft,
        2 => Toggle3State.onMiddle,
        _ => Toggle3State.onRight,
      },
      leftElement: leftElement,
      middleElement: middleElement,
      rightElement: rightElement,
    );
  }

  /// Creates a watermark selector toggle element
  CxToggle2? _createWatermarkToggle(
    bool currentValue,
    void Function(bool) onChanged,
    void Function(String, String, {JsonMap data}) sendAnalytics,
    Releaser releaser,
  ) {
    final forced = _graph?.watermarkSetting.isForced ?? WatermarkSetting.fromFlags().isForced;

    final leftElement = HTMLDivElement()
      ..textContent = 'On'
      ..title = 'On';

    final rightElement = HTMLDivElement()
      ..textContent = 'Off'
      ..title = 'Off';

    final toggleEl = CxToggle2(
      (newOnRight) {
        final newValue = !newOnRight;
        onChanged(newValue);
        sendAnalytics('napkin_watermark', 'set', data: {'value': newValue});
      },
      releaser: releaser,
      startOnRight: !currentValue,
      leftElement: leftElement,
      rightElement: rightElement,
      disabled: forced
    );

    if (forced) {
      final content = HTMLDivElement()..innerHTML='Available in<br/><span class="cx-icon plus-tag translucent" style="display:contents"></span> and <span class="cx-icon pro-tag translucent" style="display:contents; height: 14px; width: 38px;"></span>'.toJS;
      _tooltipEl = CxTooltip.withAutoHover(
          releaser: this,
          targetElement: toggleEl.root,
          placement: CxPointyPlacement.topCenter,
          htmlContent: content,
          innerClasses: ['tooltip-upgrade-nudge']
      );
    }
    return toggleEl;
  }

  /// Creates a background selector toggle element
  CxToggle2 _createBackgroundToggle(
    bool currentValue,
    void Function(bool) onChanged,
    void Function(String, String, {JsonMap data}) sendAnalytics,
    Releaser releaser,
  ) {
    final leftElement = HTMLDivElement()
      ..textContent = 'On'
      ..title = 'On';

    final rightElement = HTMLDivElement()
      ..textContent = 'Off'
      ..title = 'Off';

    return CxToggle2(
      (newOnRight) {
        final newValue = !newOnRight;
        onChanged(newValue);
        sendAnalytics('solid_background', 'set', data: {'value': newValue});
      },
      releaser: releaser,
      startOnRight: !currentValue,
      leftElement: leftElement,
      rightElement: rightElement,
    );
  }

  /// Creates UI container for a setting
  HTMLElement _createToggleSettingContainer(String title, String className, HTMLElement content) {
    return HTMLDivElement()
      ..classList.addAll(['toggle-options', className])
      ..appendChild(
        HTMLParagraphElement()
          ..classList.add('setting-name')
          ..textContent = title,
      )
      ..appendChild(content);
  }

  @override
  ExportDialogPrivateer get createPrivateer => ExportDialogPrivateer._(this);
}

@visibleForTesting
class ExportDialogPrivateer {
  final ExportDialog _dialog;

  ExportDialogPrivateer._(this._dialog);

  Future<bool> export() {
    return _dialog._export(null);
  }

  ExportFormat? get selectedExportType => _dialog._selectedExportType;

  void selectExportType(DownloadType type) {
    final format = _dialog._exportTypes.firstWhereOrNull((format) => format.type == type);
    if (format != null) {
      _dialog.selectedExportType = format;
    }
  }
}

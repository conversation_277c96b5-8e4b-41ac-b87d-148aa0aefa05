part of 'export_dialog.dart';

typedef CallbackWithExportFormat = void Function(ExportFormat);

abstract class ExportFormat with HtmlRoot, HasSubscription, Releaser {
  DownloadType get type;
  String get name => type.name;
  String get description;
  String get color;
  PlanFeatureKey get featureKey => PlanFeatureKey.unrecognized;

  bool get canCopyToClipboard;
  List<ExportSettingsEnum> getSettings(bool isExportingDocument);

  final CallbackWithExportFormat onClick;
  final CallbackWithExportFormat onMouseEnter;
  final CallbackWithExportFormat onMouseLeave;

  HTMLDivElement? _container;
  bool _isHovered = false;
  bool _isActive = false;
  set isActive(bool value) {
    _isActive = value;
    rebuild();
  }

  int? _exportLeft;
  set exportLeft(int? value) {
    _exportLeft = value;
    rebuild();
  }
  CxTooltip? _tooltipEl;

  ExportFormat({required this.onClick, required this.onMouseEnter, required this.onMouseLeave});

  void release() {
    _tooltipEl?.releaseComponent();
    releaseAll(); // Release all disposables including tooltips
    cancelAllSubscriptions();
  }

  @override
  HTMLElement build() {
    if (_container == null) {
      _container = HTMLDivElement()
        ..setAttribute('data-color', color)
        ..classList.add('export-dialog__format-options__item');

      final title = HTMLDivElement()
        ..classList.add('title')
        ..textContent = name.toUpperCase();
      _container?.appendChild(title);

      final countLeftBadge = _exportLeft != null ? (HTMLDivElement()..classList.add('count-left-badge')..textContent=_exportLeft.toString()) : null;

      if (countLeftBadge != null) {
        _tooltipEl = CxTooltip.withAutoHover(
            releaser: this,
            targetElement: countLeftBadge,
            placement: CxTooltipPlacement.bottomCenter,
            textContent: 'You have $_exportLeft Free ${name.toUpperCase()} exports left',
            innerClasses: ['tooltip-upgrade-nudge']
        );
      }

      _container?.appendNotNull(countLeftBadge);

      autoUnsubscribeBuild(_container?.onClick.listen(_onClick));
      autoUnsubscribeBuild(_container?.onMouseEnter.listen(_onMouseEnter));
      autoUnsubscribeBuild(_container?.onMouseLeave.listen(_onMouseLeave));
    }

    if (_isHovered) {
      _container?.classList.add('hovered');
    } else {
      _container?.classList.remove('hovered');
    }

    if (_isActive) {
      _container?.classList.add('selected');
    } else {
      _container?.classList.remove('selected');
    }

    return _container!;
  }

  void _onClick(_) {
    onClick(this);
  }

  void _onMouseEnter(_) {
    _isHovered = true;
    rebuild();

    onMouseEnter(this);
  }

  void _onMouseLeave(_) {
    _isHovered = false;
    rebuild();

    onMouseLeave(this);
  }
}

class ExportFormatPNG extends ExportFormat {
  @override
  DownloadType get type => DownloadType.png;

  @override
  String get description => 'Pixel image. Use in emails, docs, or web.';

  @override
  String get color => 'blue';

  @override
  bool get canCopyToClipboard => true;

  @override
  PlanFeatureKey get featureKey => PlanFeatureKey.exportPng;

  @override
  List<ExportSettingsEnum> getSettings(bool isExportingDocument) => [
    ExportSettingsEnum.exportWatermark,
    ExportSettingsEnum.exportColorMode,
    ExportSettingsEnum.exportWithBackground,
    ExportSettingsEnum.exportResolution,
  ];

  ExportFormatPNG({required super.onClick, required super.onMouseEnter, required super.onMouseLeave});
}

class ExportFormatSVG extends ExportFormat {
  @override
  DownloadType get type => DownloadType.svg;

  @override
  String get description => 'Vector image. Editable and scalable.';

  @override
  String get color => 'purple';

  @override
  bool get canCopyToClipboard => true;

  @override
  PlanFeatureKey get featureKey => PlanFeatureKey.exportSvg;

  @override
  List<ExportSettingsEnum> getSettings(bool isExportingDocument) => [
    ExportSettingsEnum.exportWatermark,
    ExportSettingsEnum.exportColorMode,
    ExportSettingsEnum.exportWithBackground,
  ];

  ExportFormatSVG({required super.onClick, required super.onMouseEnter, required super.onMouseLeave});
}

class ExportFormatPDF extends ExportFormat {
  @override
  DownloadType get type => DownloadType.pdf;

  @override
  String get description => 'For printing, archiving or universal sharing.';

  @override
  String get color => 'red';

  @override
  bool get canCopyToClipboard => false;

  @override
  PlanFeatureKey get featureKey => PlanFeatureKey.exportPdf;

  @override
  List<ExportSettingsEnum> getSettings(bool isExportingDocument) => [
    ExportSettingsEnum.exportWatermark,
    ExportSettingsEnum.exportColorMode,
    if (isExportingDocument) ExportSettingsEnum.exportPdfFormat,
  ];

  ExportFormatPDF({required super.onClick, required super.onMouseEnter, required super.onMouseLeave});
}

class ExportFormatPPT extends ExportFormat {
  @override
  DownloadType get type => DownloadType.pptx;

  @override
  String get description => 'Editable and animatable PowerPoint slide.';

  @override
  String get color => 'orange';

  @override
  bool get canCopyToClipboard => false;

  @override
  PlanFeatureKey get featureKey => PlanFeatureKey.exportPpt;

  @override
  List<ExportSettingsEnum> getSettings(bool isExportingDocument) => [ExportSettingsEnum.exportColorMode];

  ExportFormatPPT({required super.onClick, required super.onMouseEnter, required super.onMouseLeave});
}

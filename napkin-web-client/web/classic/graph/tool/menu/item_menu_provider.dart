import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:ai_web_lib/ai_web_lib.dart';
import 'package:analytics_web_lib/analytics_web_lib.dart';
import 'package:collection/collection.dart';
import 'package:io_web_lib/logging.dart';
import 'package:meta/meta.dart';
import 'package:text_web_lib/text_web_lib.dart';
import 'package:util_web_lib/color_hsl.dart';
import 'package:util_web_lib/create_svg.dart';
import 'package:util_web_lib/custom_svg_font.dart';
import 'package:util_web_lib/debouncer.dart';
import 'package:util_web_lib/dialog.dart';
import 'package:util_web_lib/ecs.dart';
import 'package:util_web_lib/environment.dart';
import 'package:util_web_lib/function.dart';
import 'package:util_web_lib/has_subscription.dart';
import 'package:util_web_lib/helper.dart' as helper;
import 'package:util_web_lib/releaser.dart';

import 'package:util_web_lib/js_interop.dart';
import 'package:util_web_lib/loader.dart';
import 'package:util_web_lib/matrix23.dart';
import 'package:util_web_lib/privateer.dart';
import 'package:util_web_lib/svg_font.dart';
import 'package:util_web_lib/svg_scene_parser.dart';
import 'package:util_web_lib/text_types.dart';
import 'package:util_web_lib/ui-kit/cx_enums.dart' show CxPointyPlacement;
import 'package:util_web_lib/ui-kit/cx_tooltip.dart';
import 'package:util_web_lib/undo.dart';
import 'package:vector_math/vector_math.dart';

import '../../../dialogs/add_custom_font_dialog.dart';
import '../../../dialogs/export/export_dialog.dart';
import '../../../graph/ecs/item_stage.dart';
import '../../../page/page.dart';
import '../../../page/theme/theme_editor_graph_host.dart';
import '../../../page/theme/theme_header.dart';
import '../../builder/builder_autographics.dart';
import '../../ecs/event/systems/behavior/behavior.dart';
import '../../ecs/event/systems/behavior/behavior_heading_icon.dart';
import '../../ecs/event/systems/behavior/behavior_label.dart';
import '../../ecs/event/systems/behavior/behavior_scene.dart';
import '../../ecs/item/components/component_autographics_generation.dart';
import '../../ecs/item/services/service_brush.dart';
import '../../ecs/item/services/service_menu.dart';
import '../../graph.dart';
import '../../item/item_event.dart';
import '../../item/item_helpers.dart';
import '../../layer/layer.dart';
import '../../utils/scene_swap.dart';
import '../../utils/service_decoration.dart';
import '../tool.dart';
import 'components/components.dart';
import 'generic_menu.dart';
import 'generic_menu_list.dart';

// Main editor
part 'item_menu_aspect_ratio.dart';
part 'item_menu_color.dart';
part 'item_menu_color_base.dart';
part 'item_menu_color_theme_editor.dart';
part 'item_menu_comment.dart';
part 'item_menu_decorator.dart';
part 'item_menu_export.dart';
part 'item_menu_rotate.dart';
part 'item_menu_icon_flip.dart';
part 'item_menu_font_list.dart';
part 'item_menu_font_list_theme_editor.dart';
part 'item_menu_font_size.dart';
part 'item_menu_font_size_theme_editor.dart';
part 'item_menu_group.dart';
part 'item_menu_sync_with_text.dart';
// Theme editor
part 'item_menu_icon_style_theme_editor.dart';
part 'item_menu_image_invert.dart';
part 'item_menu_items_align.dart';
part 'item_menu_padding.dart';
part 'item_menu_spark.dart';
part 'item_menu_spark_sketch_gestures.dart';
part 'item_menu_terminator.dart';
part 'item_menu_terminator_theme_editor.dart';
part 'item_menu_text_align.dart';
part 'item_menu_text_bold.dart';
part 'item_menu_text_bold_theme_editor.dart';
part 'item_menu_text_vertical_align.dart';
part 'item_menu_theme.dart';

enum ItemMenuProviderGroup {
  font,
  text,
  color,
  syncVisualWithText,
  imageInvert,
  align,
  padding,
  decorator,
  terminator,
  group,
  exportSpark,
  theme,
  aspectRatio,
  iconStyle,
  rotate,
  iconFlip,
}

abstract class ItemMenuProviderHost {
  void activateMenu(ItemMenuProvider provider);
  void deactivateMenu(ItemMenuProvider provider);
  Graph get graph;
  bool get fromFrameMenu;
}

sealed class ItemMenuProvider with Releaser {
  bool get isRounded => false;
  HTMLElement? _root;

  final ItemMenuProviderHost _host;
  Graph get graph => _host.graph;

  final bool fromFrameMenu;

  ItemMenuProvider(this._host) : fromFrameMenu = _host.fromFrameMenu;

  void release() {
    cleanup();
    releaseAll();
  }

  void cleanup();

  HTMLElement? _build(Iterable<ReadOnlyEntity> items) {
    final root = buildContainer();
    final icon = buildIcon(items);

    if (fromFrameMenu) {
      final hoverTooltipInner = buildHoverTooltip(items);
      if (hoverTooltipInner != null) {
        final hoverTooltip = CxTooltip(
          releaser: this,
          placement: CxPointyPlacement.bottomCenter,
          htmlContent: hoverTooltipInner,
        );
        root.appendNotNull(hoverTooltip.root);
      }
    }

    root.appendNotNull(icon);
    root.appendNotNull(buildMenu(items));

    return root;
  }

  void rebuild(Iterable<ReadOnlyEntity> items) {
    final oldRoot = _root;
    if (oldRoot == null) return;
    _root = oldRoot.replaceOrRemove(_patchRoot(_build(items)));
  }

  HTMLElement? gatherTool(Iterable<ReadOnlyEntity> items) {
    return _root = _patchRoot(_build(items));
  }

  HTMLElement? _patchRoot(HTMLElement? root) => root?..classList.addAll(['group', if (isRounded) 'rounded']);

  HTMLElement buildContainer() => HTMLDivElement();
  Element? buildIcon(Iterable<ReadOnlyEntity> items) => null;
  Element? buildMenu(Iterable<ReadOnlyEntity> items) => null;
  HTMLElement? buildHoverTooltip(Iterable<ReadOnlyEntity> items) => null;

  void enableMenu() => _host.activateMenu(this);
  void disableMenu() => _host.deactivateMenu(this);

  void onMenuDisabled() {}
  void onMenuEnabled() {}

  void onItemsDomPositionUpdated(Vector2 position, Aabb2? box) {}

  static void sendAnalyticsEvent(String name, String action, JsonMap data) {
    final analyticsParams = {
      'name': name,
      'action': action,
      ...data,
      if (data.containsKey('value') && data['value'] is JsonMap) 'value': json.encode(data['value']),
    };
    Analytics().trackEvents(AnalyticsEvents(tagManager: AnalyticsEvent('napkin_document_item_menu_action', analyticsParams)));
  }

  // To be overridden
  void invalidate(Stage stage, InvalidationPropagatorReturn changedEntities) {}
}

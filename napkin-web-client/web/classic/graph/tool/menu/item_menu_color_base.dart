part of 'item_menu_provider.dart';

enum _ColorType { automatic, transparent, ai, color }

enum MenuMode {
  fill,
  stroke,
  strokeSettings;

  bool get isFill => this == MenuMode.fill;
  bool get isStroke => this == MenuMode.stroke || this == MenuMode.strokeSettings;
}

enum _ActionType {
  strokeColor,
  strokeLightness,
  strokeTreatment,
  strokeStyle,
  strokeScheme,
  strokeWidth,
  strokeOpacity,
  fillColor,
  fillScheme,
  fillLightness,
  fillOpacity,
  fillTreatment,
}

class MenuConfig {
  final bool onlyContainsThemeFrame;
  final bool onlyContainsText;
  final bool onlyContainsIcon;

  final bool hasFillMenu;
  final bool hasStrokeMenu;
  final bool hasStrokeSettingsMenu;

  factory MenuConfig.compute(Graph graph, List<ReadOnlyEntity> items) {
    final requiredFillMenu = _requiredFillMenu(graph, items);
    final requiredStrokeMenu = _requiredStrokeMenu(graph, items);
    final requiredStrokeSettings = !(requiredFillMenu && requiredStrokeMenu) && _requiredStrokeSettingsMenu(graph, items);

    return MenuConfig(items, hasStrokeMenu: requiredStrokeMenu, hasFillMenu: requiredFillMenu, hasStrokeSettingsMenu: requiredStrokeSettings);
  }

  bool showColors(MenuMode menuMode) => switch (menuMode) {
    MenuMode.fill => true,
    MenuMode.stroke => true,
    MenuMode.strokeSettings => false,
  };

  bool showSettings(MenuMode menuMode) {
    if (onlyContainsThemeFrame) return false;
    if (onlyContainsText) return false;

    return switch (menuMode) {
      MenuMode.fill => true,
      MenuMode.stroke => !hasStrokeSettingsMenu,
      MenuMode.strokeSettings => hasStrokeSettingsMenu,
    };
  }

  bool get _showFillAsStroke => false;
  bool get _showStrokeAsFill => onlyContainsIcon || onlyContainsText;

  bool showAsFill(MenuMode menuMode) => switch (menuMode) {
    MenuMode.fill => !_showFillAsStroke,
    MenuMode.stroke => _showStrokeAsFill,
    MenuMode.strokeSettings => _showStrokeAsFill,
  };

  bool showAsStroke(MenuMode menuMode) => switch (menuMode) {
    MenuMode.fill => _showFillAsStroke,
    MenuMode.stroke => !_showStrokeAsFill,
    MenuMode.strokeSettings => !_showStrokeAsFill,
  };

  bool get hasFillAndStroke => hasFillMenu && hasStrokeMenu;

  MenuConfig(List<ReadOnlyEntity> items, {required this.hasStrokeSettingsMenu, required this.hasFillMenu, required this.hasStrokeMenu})
    : onlyContainsThemeFrame = MenuConfig._onlyContainsThemeFrames(items),
      onlyContainsText = MenuConfig._onlyContainsText(items),
      onlyContainsIcon = MenuConfig._onlyContainsIcon(items);

  static bool _onlyContainsThemeFrames(List<ReadOnlyEntity> items) => items.isNotEmpty && items.every((entity) => HelperAiComponent.isEntityThemeFrame(entity));
  static bool _onlyContainsText(List<ReadOnlyEntity> items) => items.isNotEmpty && items.every((entity) => HelperAiComponent.isEntityText(entity));
  static bool _onlyContainsIcon(List<ReadOnlyEntity> items) => items.isNotEmpty && items.every((entity) => HelperAiComponent.isEntityDb(entity));

  static bool _requiredFillMenu(Graph graph, List<ReadOnlyEntity> items) => items.any((e) {
    return serviceCanHaveFill(graph.previewStage.readOnlyEntityFromId(e.id), recursive: true);
  });

  static bool _requiredStrokeMenu(Graph graph, List<ReadOnlyEntity> items) =>
      !_checkSingleIconWithFill(graph, items) && items.any((item) => serviceCanHaveStroke(graph.previewStage.readOnlyEntityFromId(item.id), recursive: true));

  static bool _requiredStrokeSettingsMenu(Graph graph, List<ReadOnlyEntity> items) =>
      !_checkSingleIconWithFill(graph, items) &&
      items.any((item) {
        final entity = graph.previewStage.readOnlyEntityFromId(item.id);
        return !HelperAiComponent.isEntityText(entity) && serviceCanHaveStroke(entity, recursive: true);
      });

  static bool _checkSingleIconWithFill(Graph graph, List<ReadOnlyEntity> items) =>
      ItemMenuColorBase._computeHasOnlySingleIcon(items) && serviceCanHaveFill(graph.previewStage.readOnlyEntityFromId(items.first.id), recursive: true);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! MenuConfig) return false;

    return hasFillMenu == other.hasFillMenu &&
        hasStrokeMenu == other.hasStrokeMenu &&
        hasStrokeSettingsMenu == other.hasStrokeSettingsMenu &&
        onlyContainsThemeFrame == other.onlyContainsThemeFrame &&
        onlyContainsText == other.onlyContainsText &&
        onlyContainsIcon == other.onlyContainsIcon;
  }

  @override
  int get hashCode => hasFillMenu.hashCode ^ hasStrokeMenu.hashCode ^ hasStrokeSettingsMenu.hashCode ^ onlyContainsThemeFrame.hashCode;

  @override
  String toString() =>
      'MenuConfig{hasFillMenu: $hasFillMenu, hasStrokeMenu: $hasStrokeMenu, hasStrokeSettingsMenu: $hasStrokeSettingsMenu, onlyContainsThemeFrame: $onlyContainsThemeFrame}';
}

sealed class ItemMenuColorBase extends ItemMenuProvider with HasSubscription {
  static final Logger _logger = Logger('ItemMenuColor', browser: Logger.INFO);
  Logger get _log => _logger;

  static const bool _debugUI = false;

  static const referenceTarget = ColorTarget.dayBlend;

  @override
  bool get isRounded => true;

  // Parent Elements
  HTMLDivElement? _groupElement;

  HTMLDivElement? _fillMenuElement;
  HTMLDivElement? _strokeMenuElement;
  HTMLDivElement? _strokeSettingsMenuElement;

  MenuMode? _hoveringParentMenu;
  MenuMode _lastActiveSubmenu = MenuMode.fill;

  final List<ReadOnlyEntity> _items = [];
  Timer? _scheduledClearPreview;

  bool _submenuShouldStayOpen = false;
  ColorPicker? _openedColorPicker;

  late MenuConfig _menuConfig;

  bool get _isThemeFrame => AiComponentThemeFrame.areAllEntitiesThemeFrames(_items);

  // CustomSliders
  late final CustomSlider _fillBrightnessSlider = CustomSlider(
    values: ColorLightness.editorRange.map((e) => e.normalized!.value).toList(growable: false),
    valueTooltips: ColorLightness.editorRange.map((e) => e.name).toList(growable: false),
    onChange: (value) => _applyPreviewAndRefresh(_items, _ActionType.fillLightness, ColorLightness.fromNormalized(NormalizedLightness.safe(value))),
    onMouseMove: (value) => _updatePreview(_items, _ActionType.fillLightness, ColorLightness.fromNormalized(NormalizedLightness.safe(value))),
    sliderBaseType: SliderBaseType.brightness,
    arrow: true,
  );
  late final CustomSlider _strokeBrightnessSlider = CustomSlider(
    values: ColorLightness.editorRange.map((e) => e.normalized!.value).toList(growable: false),
    valueTooltips: ColorLightness.editorRange.map((e) => e.name).toList(growable: false),
    onChange: (value) => _applyPreviewAndRefresh(_items, _ActionType.strokeLightness, ColorLightness.fromNormalized(NormalizedLightness.safe(value))),
    onMouseMove: (value) => _updatePreview(_items, _ActionType.strokeLightness, ColorLightness.fromNormalized(NormalizedLightness.safe(value))),
    sliderBaseType: SliderBaseType.brightness,
    arrow: true,
  );
  late final CustomSlider _strokeThicknessSlider = CustomSlider(
    values: StrokeWidth.values.map((e) => e.index.toDouble()).toList(growable: false),
    valueTooltips: StrokeWidth.values.map((e) => e.thickness.round().toString()).toList(growable: false),
    onChange: (value) => _applyPreviewAndRefresh(_items, _ActionType.strokeWidth, StrokeWidth.values[value?.round() ?? 0]),
    onMouseMove: (value) => _updatePreview(_items, _ActionType.strokeWidth, StrokeWidth.values[value?.round() ?? 0]),
    sliderBaseType: SliderBaseType.thickness,
    arrow: false,
  );

  bool get _sliderIsDragging => _fillBrightnessSlider.isDragging || _strokeBrightnessSlider.isDragging || _strokeThicknessSlider.isDragging;

  String get analyticsPrefix => switch (this) {
    ItemMenuColorThemeEditor() => 'THEME_EDITOR',
    ItemMenuColor() => 'ITEM',
  };

  // Constructor
  ItemMenuColorBase(super._host);

  static bool _computeHasOnlyText(Iterable<ReadOnlyEntity> items) => items.every((item) => HelperAiComponent.isEntityText(item));
  static bool _computeHasOnlyIcons(Iterable<ReadOnlyEntity> items) => items.every((item) => HelperAiComponent.isEntityDb(item));
  static bool _computeHasAnyCurve(Iterable<ReadOnlyEntity> items) =>
      items.any((item) => !(HelperAiComponent.isEntityText(item) || HelperAiComponent.isEntityImage(item)));
  static bool _computeHasOnlySingleIcon(Iterable<ReadOnlyEntity> items) => items.length == 1 && _computeHasOnlyIcons(items);

  @override
  void invalidate(Stage stage, InvalidationPropagatorReturn invalidationResult) {
    if (!invalidationResult.all && !_items.any((e) => invalidationResult.ids.contains(e.id))) return;

    // check if the config changed
    if (MenuConfig.compute(graph, _items) == _menuConfig) return;

    // we need to pass a copy of the items to the menu, to ne be empty when cleanup is called
    rebuild(List.of(_items));
  }

  @override
  void cleanup() {
    cancelAllSubscriptions();

    _submenuShouldStayOpen = false;

    _openedColorPicker?.release();
    _openedColorPicker = null;

    _groupElement?.remove();
    _groupElement = null;

    _fillMenuElement?.remove();
    _fillMenuElement = null;

    _strokeMenuElement?.remove();
    _strokeMenuElement = null;

    _strokeSettingsMenuElement?.remove();
    _strokeSettingsMenuElement = null;

    _fillBrightnessSlider.cleanup();
    _strokeBrightnessSlider.cleanup();
    _strokeThicknessSlider.cleanup();

    _items.clear();
  }

  @override
  void release() {
    super.release();
    releaseSubscriptions();
    disableMenu();

    _fillBrightnessSlider.release();
    _strokeBrightnessSlider.release();
    _strokeThicknessSlider.release();
  }

  @override
  HTMLElement? _build(Iterable<ReadOnlyEntity> items) {
    cleanup();

    // Filer all the text items
    if (items.isEmpty) return null;

    if (graph.toolsPolicy.canItemColor == false) return null;

    // Make sure all items are from this stage and that they support color
    _items.clearAddAll(items.where((item) => serviceMenuColorPermission(item)));

    if (_items.isEmpty) return null;

    _buildMenuElements();
    _refreshHeaderAttributes();

    return HTMLDivElement()..appendNotNull(_groupElement);
  }

  // Do not use while in menu building functions
  HTMLElement? _menuModeElement(MenuMode? menuMode) => switch (menuMode) {
    MenuMode.fill => _fillMenuElement,
    MenuMode.stroke => _strokeMenuElement,
    MenuMode.strokeSettings => _strokeSettingsMenuElement,
    null => null,
  };
  HTMLElement? _subMenuElement(MenuMode? menuMode) => _menuModeElement(menuMode)?.querySelector('.submenu_color')?.tryCast<HTMLElement>();
  HTMLElement? _mainSubMenuElement(MenuMode? menuMode) => _menuModeElement(menuMode)?.querySelector('.submenu_container')?.tryCast<HTMLElement>();
  HTMLElement? _pickerSubMenuElement(MenuMode? menuMode) => _menuModeElement(menuMode)?.querySelector('.picker_submenu')?.tryCast<HTMLElement>();

  //
  // Build functions
  //

  void _buildMenuElements() {
    if (_groupElement == null) {
      _groupElement = HTMLDivElement();
    } else {
      _groupElement?.clear();
      _groupElement?.classList.clear();
    }
    _groupElement?.classList.addAll(['color_menu_container']);

    _menuConfig = MenuConfig.compute(graph, _items);

    // Fill

    _fillMenuElement?.remove();
    _fillMenuElement = _menuConfig.hasFillMenu ? _buildMenuElement(MenuMode.fill) : null;

    // Stroke

    _strokeMenuElement?.remove();
    _strokeMenuElement = _menuConfig.hasStrokeMenu ? _buildMenuElement(MenuMode.stroke) : null;

    // Stroke Settings

    _strokeSettingsMenuElement?.remove();
    _strokeSettingsMenuElement = _menuConfig.hasStrokeSettingsMenu ? _buildMenuElement(MenuMode.strokeSettings) : null;

    _groupElement?.appendAll([_fillMenuElement, _strokeMenuElement, _strokeSettingsMenuElement].nonNulls);
  }

  HTMLDivElement _buildMenuElement(MenuMode menuMode) {
    final subMenuElement = _buildSubMenuElements(menuMode);

    final menuElement = HTMLDivElement()
      ..classList.addAll(['icon', 'bg24', 'menu_color', menuMode.name])
      ..appendChild(subMenuElement);

    _updateMenuElementClasses(menuElement, menuMode, source: '${menuMode.name} creation');

    autoUnsubscribe(
      menuElement.onMouseLeave.listen((e) {
        if (_debugUI) return;
        if (_sliderIsDragging) return;
        if (_submenuShouldStayOpen) return;
        _scheduleClearPreview();
        // clear the hovering parent
        if (menuMode == _hoveringParentMenu) _hoveringParentMenu = null;
        menuElement.classList.removeAll(['hover']);
        subMenuElement.classList.toggle('active', false);
        disableMenu();
      }),
    );

    autoUnsubscribe(
      menuElement.onMouseEnter.listen((e) {
        if (_submenuShouldStayOpen) return;

        // deactivate the other menu
        _subMenuElement(_lastActiveSubmenu)?.classList.toggle('active', false);

        _hoveringParentMenu = menuMode;
        _lastActiveSubmenu = menuMode;

        // activate the current menu
        subMenuElement.classList.toggle('active', true);

        _updateMenuElementClasses(menuElement, menuMode, source: '${menuMode.name} mouseEnter');
        enableMenu();
      }),
    );

    return menuElement;
  }

  /// Refresh the icons on the item menu bar
  void _updateMenuElementClasses(HTMLElement? menuElement, MenuMode menuMode, {required String source}) {
    final target = ColorTarget.blend(graph.isInvertLightness);
    final styleValue = computeActiveColor(menuMode);
    final palette = computePalette();
    final color = styleValue?.valueOrNull;
    final aiColor = styleValue?.commandOrNull == StyleCommand.ai;
    final lightness = _computeLocalLightness(menuMode);
    final automatic = color == null;
    final transparent = switch (menuMode) {
      MenuMode.fill => _computeLocalIsFillTransparent(),
      MenuMode.stroke => _computeLocalIsStrokeTransparent(),
      MenuMode.strokeSettings => false,
    };

    final colorType = transparent
        ? _ColorType.transparent
        : automatic
        ? _ColorType.automatic
        : aiColor
        ? _ColorType.ai
        : _ColorType.color;

    menuElement?.classList.removeAll([
      'auto_color',
      'ai_color',
      'stroke-setting',
      'transparent',
      'transparent_fill',
      'transparent_stroke',
      'fill',
      'stroke',
      'icon',
      'needs_border',
    ]);

    final showSettings = _menuConfig.showSettings(menuMode);
    // Defines the icon to show on the menu bar
    switch (colorType) {
      case _ColorType.ai:
      case _ColorType.automatic:
        if (menuMode == MenuMode.strokeSettings && showSettings) {
          menuElement?.classList.addAll(['stroke-setting', 'icon']);
        } else if (menuMode == MenuMode.stroke && _menuConfig.hasFillMenu && showSettings) {
          menuElement?.classList.addAll(['stroke-setting', 'icon']);
        } else {
          // text & fill & sometimes stroke
          menuElement?.classList.addAll([colorType == _ColorType.automatic ? 'auto_color' : 'ai_color', 'icon']);
        }
      case _ColorType.transparent:
        if (menuMode == MenuMode.fill) {
          menuElement?.classList.addAll(['transparent', 'transparent_fill', 'icon']);
        } else {
          menuElement?.classList.addAll(['transparent', 'transparent_stroke', 'icon']);
        }
      case _ColorType.color:
        if (menuMode == MenuMode.fill || _menuConfig.hasStrokeSettingsMenu || !showSettings || !_strokeIsLinkedToFill) {
          final currentColorRGB = (color ?? NamedColor.gray).toRgbString(lightness?.normalized, palette, target);
          menuElement?.style.setProperty('--color-fill', currentColorRGB);
          menuElement?.style.setProperty('--color-stroke', currentColorRGB);
          menuElement?.classList.toggle('fill', _menuConfig.showAsFill(menuMode));
          menuElement?.classList.toggle('stroke', _menuConfig.showAsStroke(menuMode));
          menuElement?.classList.toggle('active', !automatic);
          menuElement?.classList.toggle('needs_border', colorNeedsBorder(graph, color, palette, isUiNightMode: graph.isNightMode));
        } else {
          menuElement?.classList.addAll(['stroke-setting', 'icon']);
        }
    }

    // Update which color circle is highlighted
    final submenuElement = menuElement?.querySelector('.submenu_color')?.tryCast<HTMLElement>();
    final focusableElements = submenuElement?.querySelectorAll('.automatic_color, .transparent, .color_circle').toList() ?? [];
    final hexReference = tryDataHex(color, palette);
    for (final focusableElement in focusableElements) {
      if (!focusableElement.isA<HTMLElement>()) continue;
      focusableElement as HTMLElement;

      final active = switch (colorType) {
        _ColorType.ai => focusableElement.classList.contains('ai_color'),
        _ColorType.automatic => focusableElement.classList.contains('automatic_color'),
        _ColorType.transparent => focusableElement.classList.contains('transparent'),
        _ColorType.color => focusableElement.getAttribute('data-hex') == hexReference,
      };

      focusableElement.classList.toggle('active', active);
    }

    _refreshHeaderAttributes();
  }

  static String dataHex(Color color, Palette? palette) => color.toRgbString(null, palette, referenceTarget).toUpperCase();
  static String? tryDataHex(Color? color, Palette? palette) => color == null ? null : dataHex(color, palette);

  HTMLDivElement _buildSubMenuElements(MenuMode menuMode) {
    final borderElement = HTMLDivElement()
      ..classList.addAll(['border'])
      ..appendChild(
        HTMLDivElement()
          ..classList.add('submenu_container')
          ..appendAll(_buildMainSubMenuElements(menuMode).toList(growable: false)),
      )
      ..appendChild(HTMLDivElement()..classList.add('picker_submenu'));

    final submenuRoot = HTMLDivElement()
      ..classList.addAll(['generic_menu', 'dark', 'bottom', 'submenu_color', menuMode == MenuMode.fill ? 'fill_generic_menu' : 'stroke_generic_menu'])
      ..appendChild(borderElement);

    Graph.domCaptureInput(submenuRoot, captureMouse: true);

    autoUnsubscribe(
      submenuRoot.onMouseEnter.listen((e) {
        submenuRoot.classList.toggle('active', true);
      }),
    );
    autoUnsubscribe(
      submenuRoot.onMouseLeave.listen((e) {
        if (_debugUI) return;
        if (_sliderIsDragging) return;
        final hoveringParent = _hoveringParentMenu == menuMode;
        submenuRoot.classList.toggle('active', _submenuShouldStayOpen || hoveringParent);
      }),
    );

    return submenuRoot;
  }

  void _openColorPicker(MenuMode menuMode) {
    final currentColor = _computeLocalColor(menuMode)?.valueOrNull;
    final palette = computePalette();
    final colorPicker = ColorPicker(
      graph,
      currentColor?.toRgbString(null, palette, ColorTarget.dayBlend) ?? 'FF0000', // color picker does not do inverted colors
      fillDisplay: _menuConfig.showAsFill(menuMode),
      previewColorFunc: (String hexColor) => _showSuggestionPreview(CustomColor(hexColor), menuMode, resetLightness: true),
      clearPreviewColorFunc: () => _scheduleClearPreview(),
      setColorFunc: (String hexColor) => _actionSetColorFromColorCircle(CustomColor(hexColor), menuMode, resetLightness: true),
      closeFunc: () => _closeColorPicker(menuMode),
    );

    // Hide the main submenu
    _mainSubMenuElement(menuMode)?.style.display = 'none';

    // install the picker
    _pickerSubMenuElement(menuMode)?.appendChild(colorPicker.root);
    _openedColorPicker = colorPicker;
    _submenuShouldStayOpen = true;
  }

  void _closeColorPicker(MenuMode menuMode) {
    // remove the picker
    _pickerSubMenuElement(menuMode)?.clear();
    _openedColorPicker?.release();
    _openedColorPicker = null;
    _submenuShouldStayOpen = false;

    // Show the main submenu and refresh the custom colors
    _mainSubMenuElement(menuMode)
      ?..style.display = ''
      ..querySelector('.custom_colors')?.replaceWith(_buildCustomColorsElement(menuMode));
  }

  Iterable<HTMLElement> _buildMainSubMenuElements(MenuMode menuMode) sync* {
    final currentColor = _computeLocalColor(menuMode)?.valueOrNull;

    if (_isThemeFrame && menuMode == MenuMode.fill) {
      yield HTMLDivElement()
        ..classList.addAll(['title'])
        ..textContent = 'Background color';
    }

    // Header controls
    if (menuMode == MenuMode.fill) {
      yield* _buildFillHeaderElements(currentColor, menuMode);
    } else {
      yield* _buildStrokeHeaderElements(currentColor, menuMode);
    }

    if (_menuConfig.showColors(menuMode)) {
      // Main Color
      yield _buildColorsElement(menuMode);

      if (_enableCustomColors) {
        // Divider
        yield HTMLDivElement()..classList.add('divider');

        // Custom colors
        yield _buildCustomColorsElement(menuMode);
      }
    }
  }

  HTMLElement? _tryAddTooltip({required HTMLElement? trigger, Node? htmlContent, String? textContent, bool bottom = true}) {
    if (trigger == null) return null;
    return _addTooltip(trigger: trigger, textContent: textContent, htmlContent: htmlContent, bottom: bottom);
  }

  HTMLElement _addTooltip({required HTMLElement trigger, Node? htmlContent, String? textContent, bool bottom = true}) {
    return HTMLDivElement()
      ..classList.addAll(['tooltip_holder', 'tooltip_on_hover_only'])
      ..appendAll([
        trigger,
        CxTooltip(
          releaser: this,
          placement: bottom ? CxTooltipPlacement.topCenter : CxTooltipPlacement.bottomCenter,
          textContent: textContent,
          htmlContent: htmlContent,
          nudgeY: '4px',
        ).root..classList.add('inverted'),
      ]);
  }

  void _actionSetAiColor(MenuMode menuMode) {
    final actionType = _setColorStyleCommandOnItems(StyleCommand.ai, menuMode);
    _applyPreview(_items, actionType, null);
    _updateMenuElementClasses(_menuModeElement(menuMode), menuMode, source: '_actionSetAiColor');
    _sendAnalyticsEvent('set_stroke', data: {'value': 'ai'});
  }

  void _actionSetAutomaticColor(MenuMode menuMode) {
    final actionType = _setColorOnItems(null, menuMode);
    _applyPreview(_items, actionType, null);
    _updateMenuElementClasses(_menuModeElement(menuMode), menuMode, source: '_actionSetAutomaticColor');
    _sendAnalyticsEvent('set_stroke', data: {'value': 'automatic'});
  }

  void _actionSetTransparentColor(MenuMode menuMode) {
    assert(_menuConfig.hasFillAndStroke);
    // reverse the transparency on the other color mode if necessary
    final (actionType, value) = _setColorStrokeFill(stroke: menuMode.isFill, fill: !menuMode.isFill);
    _applyPreview(_items, actionType, value);

    _updateMenuElementClasses(_fillMenuElement, MenuMode.fill, source: '_setTransparentColor');
    _updateMenuElementClasses(_strokeMenuElement, MenuMode.stroke, source: '_setTransparentColor');

    _sendAnalyticsEvent('set_stroke', data: {'value': 'transparent'});
  }

  bool showTransparentButton() {
    return _menuConfig.hasFillAndStroke;
  }

  HTMLElement _buildColorsElement(MenuMode menuMode) {
    // Automatic color icon
    HTMLDivElement? automaticColorElement;
    if (serviceMenuShowAutomaticColor(_items)) {
      automaticColorElement = HTMLDivElement()..classList.addAll(['automatic_color', 'icon', 'color_menu_icon']);
      autoUnsubscribe(automaticColorElement.onClick.listen((_) => _actionSetAutomaticColor(menuMode)));
    }

    // Ai Color icon
    HTMLDivElement? aiColorElement;
    if (showAiColorButton(menuMode)) {
      aiColorElement = HTMLDivElement()..classList.addAll(['ai_color', 'icon', 'color_menu_icon']);
      autoUnsubscribe(aiColorElement.onClick.listen((_) => _actionSetAiColor(menuMode)));
    }

    // Transparent color icon
    HTMLDivElement? transparentColorElement;
    if (showTransparentButton()) {
      transparentColorElement = HTMLDivElement()
        ..classList.addAll([
          'transparent',
          if (_menuConfig.showAsFill(menuMode)) 'transparent_fill',
          if (_menuConfig.showAsStroke(menuMode)) 'transparent_stroke',
          'icon',
          'color_menu_icon',
        ]);
      autoUnsubscribe(transparentColorElement.onClick.listen((_) => _actionSetTransparentColor(menuMode)));
    }

    // all built-in Colors
    final target = ColorTarget.blend(graph.isInvertLightness);
    final palette = computePalette();
    final colorUsage = menuMode == MenuMode.fill ? ColorUsage.fill : ColorUsage.penStroke;
    return HTMLDivElement()
      ..classList.addAll(['colors', 'built_in_colors'])
      ..appendAll([
        ?_tryAddTooltip(trigger: automaticColorElement, textContent: 'Automatic', bottom: false),
        ?_tryAddTooltip(trigger: aiColorElement, textContent: 'AI color', bottom: false),
        ?_tryAddTooltip(trigger: transparentColorElement, textContent: 'Transparent', bottom: false),
        ..._userColor.map((e) => _buildColorCircle(e, menuMode, palette, target, colorUsage, resetLightness: false)),
      ]);
  }

  void _actionSetColorFromColorCircle(Color color, MenuMode menuMode, {required bool resetLightness}) {
    // reset the preview stage with the new color
    _setPreview(_items, color, menuMode, resetLightness: resetLightness); // in case there was no mouse enter event (like in tests)
    _setColorStrokeFill(stroke: menuMode.isStroke ? true : null, fill: menuMode.isFill ? true : null);
    _applyPreview(_items, menuMode == MenuMode.fill ? _ActionType.fillColor : _ActionType.strokeColor, color);
    _updateMenuElementClasses(_menuModeElement(menuMode), menuMode, source: 'color circle');

    // setting the color on the fill menu will also update the stroke menu if they are linked
    if (menuMode == MenuMode.fill && _strokeIsLinkedToFill) {
      _updateMenuElementClasses(_strokeMenuElement, MenuMode.stroke, source: 'color circle');
    }

    _sendAnalyticsEvent('set_color', data: {'value': color.toString()});
    if (color is CustomColor) {
      _sendAnalyticsEvent('set_custom_color', data: {'value': color.hex});
    }
  }

  HTMLElement _buildColorCircle(Color color, MenuMode menuMode, Palette? palette, ColorTarget target, ColorUsage colorUsage, {required bool resetLightness}) {
    final isGenerated = _showGeneratedColorTag ? (palette?.isGeneratedColor(color) ?? false) : false;
    final needsBorder = colorNeedsBorder(graph, color, palette, isUiNightMode: graph.isNightMode);
    final element = HTMLDivElement()
      ..classList.addAll([
        'color_circle',
        if (color is NamedColor) color.name.name, // useful for unit tests
        _menuConfig.showAsStroke(menuMode) ? 'stroke' : 'fill',
        if (isGenerated) 'ai',
        if (needsBorder) 'needs_border',
      ])
      ..setAttribute('data-hex', dataHex(color, palette))
      ..style.setProperty('--color', color.toRgbString(null, palette, target))
      ..style.setProperty('--overlay-color', isGenerated ? _computeGeneratedOverlayColor(color) : 'transparent');

    autoUnsubscribe(element.onClick.listen((_) => _actionSetColorFromColorCircle(color, menuMode, resetLightness: resetLightness)));
    autoUnsubscribe(element.onMouseEnter.listen((_) => _showSuggestionPreview(color, menuMode, resetLightness: resetLightness)));
    autoUnsubscribe(element.onMouseLeave.listen((_) => _scheduleClearPreview()));

    final inverted = graph.isInvertLightness && (color is CustomColor || color == NamedColor.white || color == NamedColor.pitchBlack);
    Node tooltipContent = Text()..textContent = color.tooltip(palette);
    if (inverted) {
      tooltipContent = HTMLParagraphElement()
        ..appendAll([
          HTMLSpanElement()..classList.addAll(['icon', 'moon']),
          HTMLSpanElement()..textContent = 'Inverted',
        ]);
    }
    return _addTooltip(trigger: element, htmlContent: tooltipContent, bottom: false);
  }

  /// Determines if a color needs a border because it's too close to the UI background color
  static bool colorNeedsBorder(Graph graph, Color? color, Palette? palette, {required bool isUiNightMode}) {
    if (color == null) return false;

    // Get the menu background color from the CSS system, with fallback to known values
    final backgroundColorHex = graph.getCssValue('menu-background-color', isNightMode: isUiNightMode);
    if (backgroundColorHex == null) {
      ItemMenuColorBase._logger.warning(() => 'Could not get menu background color from CSS system');
      return false;
    }

    final backgroundColor = CustomColor(backgroundColorHex);
    final resolvedColor = Palette.resolve(palette, color);

    final foregroundHsl = resolvedColor.hsl;
    final backgroundHsl = backgroundColor.hsl;

    final diffLightness = (foregroundHsl.l.value - backgroundHsl.l.value).abs();
    if (diffLightness > 5) return false;

    final diffSaturation = (foregroundHsl.s - backgroundHsl.s).abs();
    if (diffSaturation > 30) return false;

    final diffHue = Hsl.hueDistance(foregroundHsl.h, backgroundHsl.h);
    if (diffHue > 30) return false;

    return true;
  }

  static String _computeGeneratedOverlayColor(Color color) {
    final hsl = color.hsl.rawHsl;
    if (hsl.l.value < 50.0) {
      // make the color brighter by 50%
      return '${Hsl(Vector3(hsl.h, min(hsl.s, 60.0), hsl.l.value + 40.0)).rgb255.hexString}aa';
    } else {
      // make the color darker by 50%
      return '${Hsl(Vector3(hsl.h, min(hsl.s, 60.0), hsl.l.value - 40.0)).rgb255.hexString}aa';
    }
  }

  HTMLElement _buildCustomColorsElement(MenuMode menuMode) {
    // Add color button
    final addColor = HTMLDivElement()..classList.addAll(['insert_color', 'icon', 'color_menu_icon', 'pro_icon']);
    autoUnsubscribe(addColor.onClick.listen((_) => _openColorPicker(menuMode)));

    // all custom Colors
    final target = ColorTarget.blend(graph.isInvertLightness);
    final colorUsage = menuMode == MenuMode.fill ? ColorUsage.fill : ColorUsage.penStroke;
    final customColors = serviceCustomColorsGet(graph).map((color) => CustomColor(color)).toList();

    final tooltipContent = HTMLParagraphElement()
      ..appendAll([
        HTMLSpanElement()..classList.addAll(['pro-tag']),
        HTMLSpanElement()..textContent = 'Add custom colors',
      ]);

    return HTMLDivElement()
      ..classList.addAll(['colors', 'custom_colors'])
      ..appendAll([
        ...customColors.map((c) => _buildColorCircle(c, menuMode, null, target, colorUsage, resetLightness: _resetLightnessOnCustomColorChange)),
        _addTooltip(trigger: addColor, htmlContent: tooltipContent, bottom: false),
      ]);
  }

  void _sendAnalyticsEvent(String action, {JsonMap? data}) {
    final eventData = {...?data, 'count': _items.length, if (_isThemeFrame) 'is_theme_frame': true};
    ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.color', action, eventData);
  }

  void _setValue(EntityStage stage, List<ReadOnlyEntity> items, _ActionType type, dynamic value) {
    final ids = items.map((item) => item.id);
    if (value is StrokeTreatment) _serviceMenuStrokeTreatmentSetAll(stage, ids, value, includeInGroup: true);
    if (value is StrokeStyle || (value == null && type == _ActionType.strokeStyle)) {
      _serviceMenuStrokeStyleSetAll(stage, ids, value as StrokeStyle?, includeInGroup: true);
    }
    if (value is StrokeWidth) _serviceMenuStrokeWidthSetAll(stage, ids, value, includeInGroup: true);
    if (value is StrokeScheme) _serviceMenuStrokeSchemeSetAll(stage, ids, value, includeInGroup: true);
    if (value is FillTreatment) _serviceMenuFillTreatmentSetAll(stage, ids, value, includeInGroup: true);
    if (value is FillScheme) _serviceMenuFillSchemeSetAll(stage, ids, value, includeInGroup: true);

    if (value is ColorOpacity && type == _ActionType.strokeOpacity) _serviceMenuStrokeOpacitySetAll(stage, ids, value, includeInGroup: true);
    if (value is ColorOpacity && type == _ActionType.fillOpacity) _serviceMenuFillOpacitySetAll(stage, ids, value, includeInGroup: true);
    if (value is ColorLightness && type == _ActionType.strokeLightness) _serviceMenuStrokeLightnessSetAll(stage, ids, value, includeInGroup: true);
    if (value is ColorLightness && type == _ActionType.fillLightness) _serviceMenuFillLightnessSetAll(stage, ids, value, includeInGroup: true);
  }

  void _createPatch(List<ReadOnlyEntity> items, Stage stage, Color color, MenuMode menuMode, {required bool resetLightness}) {
    // custom rule for coming from black or white
    if (!resetLightness) {
      final previousColor = _computeLocalColor(menuMode);
      resetLightness =
          (color != NamedColor.pitchBlack && color != NamedColor.white) &&
          (previousColor == null || previousColor.value == NamedColor.pitchBlack || previousColor.value == NamedColor.white);
    }

    final colorStyleValue = StyleValue<Color>(color);

    if (menuMode == MenuMode.fill || _isLinkingForced) {
      _serviceMenuFillColorSetAll(stage, _items.map((e) => e.id), colorStyleValue, isLinkingForced: _isLinkingForced);
    }

    if (menuMode == MenuMode.stroke || _strokeIsLinkedToFill) {
      _serviceMenuStrokeColorSetAll(stage, _items.map((e) => e.id), colorStyleValue);
    }

    if (resetLightness) {
      if (menuMode == MenuMode.fill) {
        _serviceMenuFillLightnessSetAll(stage, _items.map((e) => e.id), ColorLightness.initial, includeInGroup: true);
      } else if (menuMode == MenuMode.stroke) {
        // Note: do not update the lightness when linked
        _serviceMenuStrokeLightnessSetAll(stage, _items.map((e) => e.id), ColorLightness.initial, includeInGroup: true);
      }
    }
  }

  void _showSuggestionPreview(Color color, MenuMode menuMode, {required bool resetLightness}) {
    // Avoid clearing the preview while we are waiting for the icon to load
    _scheduledClearPreview?.cancel();
    _scheduledClearPreview = null;

    _setPreview(_items, color, menuMode, resetLightness: resetLightness);

    _sendAnalyticsEvent('preview', data: {'value': color.toString()});
  }

  void _scheduleClearPreview() {
    // Post to avoid flickering if we are hovering over multiple items
    _scheduledClearPreview?.cancel();
    _scheduledClearPreview = helper.delayedCall(0, () => _clearPreview(_items), synchronousZeroDelay: false);
  }

  void _applyPreviewAndRefresh(List<ReadOnlyEntity> items, _ActionType type, dynamic value, {bool addUndo = true}) {
    _applyPreview(items, type, value, addUndo: addUndo);
    _refreshHeaderAttributes();
    _updateMenuElementClasses(_menuModeElement(_lastActiveSubmenu), _lastActiveSubmenu, source: '_applyPreviewAndRefresh.${type.name}');
  }

  FillTreatment toggleFillTreatmentValue(FillTreatment desired) {
    if (_computeFillTreatment() != desired) {
      return desired;
    } else {
      // default value
      return FillTreatment.solid;
    }
  }

  FillScheme toggleFillSchemeValue(FillScheme desired) {
    if (_computeFillScheme() != desired) {
      return desired;
    } else {
      // default value
      return FillScheme.single;
    }
  }

  StrokeScheme toggleStrokeSchemeValue(StrokeScheme desired) {
    if (_computeStrokeScheme() != desired) {
      return desired;
    } else {
      // default value
      return StrokeScheme.single;
    }
  }

  StrokeStyle? toggleStrokeStyleValue(StrokeStyle desired) {
    final current = _computeStrokeStyle();
    if (current != desired) {
      return desired;
    } else {
      // Toggle off - return null to remove the style
      return null;
    }
  }

  Iterable<HTMLElement> _buildFillHeaderElements(Color? currentColor, MenuMode menuMode) sync* {
    final showColors = _menuConfig.showColors(menuMode);
    final showSettings = _menuConfig.showSettings(menuMode);

    final hasOnlyText = _computeHasOnlyText(_items);

    final showFillStyle = showSettings && !hasOnlyText;
    final showFillBrightness = showColors && !hasOnlyText;

    // 1. Style
    if (showFillStyle) {
      final gradientElement = HTMLDivElement()..classList.addAll(['icon', 'fill-style', FillScheme.gradient.name]);
      _addListenersToActionElement(gradientElement, _ActionType.fillScheme, () => toggleFillSchemeValue(FillScheme.gradient));

      HTMLElement? indexedElement;
      if (serviceMenuShowIndexedFillSchemeToggle(_items)) {
        indexedElement = HTMLDivElement()..classList.addAll(['icon', 'fill-style', FillScheme.indexed.name]);
        _addListenersToActionElement(indexedElement, _ActionType.fillScheme, () => toggleFillSchemeValue(FillScheme.indexed));
      }

      HTMLElement? handElement;
      if (serviceMenuShowHandFillSchemeToggle(_items) && !_computeHasOnlySingleIcon(_items)) {
        handElement = HTMLDivElement()..classList.addAll(['icon', 'fill-style', FillTreatment.hand.name]);
        _addListenersToActionElement(handElement, _ActionType.fillTreatment, () => toggleFillTreatmentValue(FillTreatment.hand));
      }

      final styleActionElement = HTMLDivElement()
        ..classList.addAll(['action'])
        ..appendAll([
          _addTooltip(trigger: gradientElement, textContent: FillScheme.gradient.tooltip, bottom: true),
          if (indexedElement != null) _addTooltip(trigger: indexedElement, textContent: FillScheme.indexed.tooltip, bottom: true),
          if (handElement != null) _addTooltip(trigger: handElement, textContent: FillTreatment.hand.tooltip, bottom: true),
        ]);
      yield buildActionListItem('style', styleActionElement);
    }

    // divider
    if (showFillBrightness && showFillStyle) {
      yield HTMLDivElement()..classList.add('divider');
    }

    // 2. brightness
    if (showFillBrightness) {
      _updateBrightnessSliderColors(_fillBrightnessSlider, currentColor, null, entityIndex: null);
      yield buildActionListItem('brightness', HTMLDivElement()..appendChild(_fillBrightnessSlider.root));
    }
  }

  Iterable<HTMLElement> _buildStrokeHeaderElements(Color? currentColor, MenuMode menuMode) sync* {
    final showColors = _menuConfig.showColors(menuMode);
    final showSettings = _menuConfig.showSettings(menuMode);

    // stroke actions
    final hasAnyCurve = _computeHasAnyCurve(_items);
    final hasOnlyIcons = _computeHasOnlyIcons(_items);

    final showStrokeScheme = showSettings && _allowStrokeSchemeMenu;
    final showStrokeTreatment = showSettings && hasAnyCurve;
    final showStrokePattern = showSettings && !hasOnlyIcons && hasAnyCurve;
    final showStrokeThickness = showSettings && hasAnyCurve;
    final showStrokeBrightness = showColors;

    // 1. Scheme
    if (showStrokeScheme) {
      if (serviceMenuShowIndexedStrokeSchemeToggle(_items)) {
        final indexedElement = HTMLDivElement()..classList.addAll(['icon', 'stroke-style', StrokeScheme.indexed.name]);
        _addListenersToActionElement(indexedElement, _ActionType.strokeScheme, () => toggleStrokeSchemeValue(StrokeScheme.indexed));

        final solidElement = HTMLDivElement()..classList.addAll(['icon', 'stroke-style', StrokeScheme.single.name]);
        _addListenersToActionElement(solidElement, _ActionType.strokeScheme, () => toggleStrokeSchemeValue(StrokeScheme.single));

        final styleActionElement = HTMLDivElement()
          ..classList.addAll(['action'])
          ..appendAll([
            _addTooltip(trigger: solidElement, textContent: StrokeScheme.single.tooltip, bottom: true),
            _addTooltip(trigger: indexedElement, textContent: StrokeScheme.indexed.tooltip, bottom: true),
          ]);
        yield buildActionListItem('Style', styleActionElement);
      }
    }

    // 1. Treatment
    if (showStrokeTreatment) {
      final strokeTreatmentActionElement = HTMLDivElement()
        ..classList.addAll(['action', 'stroke_treatment'])
        ..appendAll(
          StrokeTreatment.values.map((v) {
            final element = HTMLDivElement()
              ..classList.addAll(['icon', 'stroke-treatment', v.name])
              ..id = v.name;
            _addListenersToActionElement(element, _ActionType.strokeTreatment, () => v);

            return _addTooltip(trigger: element, textContent: v.tooltip, bottom: true);
          }),
        );
      yield buildActionListItem('Shape', strokeTreatmentActionElement);
    }

    // 2. Pattern
    if (showStrokePattern) {
      final strokePatternActionElement = HTMLDivElement()
        ..classList.addAll(['action', 'stroke_style'])
        ..appendAll(
          StrokeStyle.values.map((v) {
            final element = HTMLDivElement()
              ..id = v.name
              ..classList.addAll(['icon', 'stroke-pattern', v.name]);
            _addListenersToActionElement(element, _ActionType.strokeStyle, () => toggleStrokeStyleValue(v));
            return element;
          }),
        );
      yield buildActionListItem('Pattern', strokePatternActionElement);
    }

    // 3. Thickness
    if (showStrokeThickness) {
      yield buildActionListItem('thickness', HTMLDivElement()..appendChild(_strokeThicknessSlider.root));
    }

    // divider
    if (showStrokeBrightness && (showStrokeTreatment || showStrokePattern || showStrokeThickness)) {
      yield HTMLDivElement()..classList.add('divider');
    }

    // 4. brightness
    if (showStrokeBrightness) {
      _updateBrightnessSliderColors(_strokeBrightnessSlider, currentColor, null, entityIndex: null);
      yield buildActionListItem('brightness', HTMLDivElement()..appendChild(_strokeBrightnessSlider.root));
    }
  }

  HTMLElement buildActionListItem(String name, HTMLElement action) {
    final actionText = HTMLDivElement()
      ..classList.addAll(['title'])
      ..textContent = name.toUpperCase();

    return HTMLDivElement()
      ..classList.addAll(['action_container'])
      ..appendChild(actionText)
      ..appendChild(action);
  }

  _ActionType? previewType;
  dynamic previewValue;

  void _addListenersToActionElement(HTMLElement element, _ActionType type, dynamic Function() getToggleValue) {
    autoUnsubscribe(
      element.onMouseEnter.listen((_) {
        previewValue = getToggleValue();
        previewType = type;
        _updatePreview(_items, type, previewValue);
      }),
    );

    autoUnsubscribe(
      element.onMouseLeave.listen((_) {
        _clearPreview(_items);
        previewValue = null;
        previewType = null;
      }),
    );

    autoUnsubscribe(
      element.onClick.listen((_) {
        // Always call getToggleValue() fresh on click to ensure correct toggle behavior
        final newValue = getToggleValue();

        // commit
        _updatePreview(_items, type, newValue); // value => preview stage
        _applyPreviewAndRefresh(_items, type, newValue); // apply the change to layer stage

        // prepare for the next toggle
        previewValue = getToggleValue();
        previewType = type;
      }),
    );
  }

  void _refreshHeaderAttributes() {
    final fillIsTransparent = _computeLocalIsFillTransparent();
    final fillcolor = fillIsTransparent ? null : (_computeLocalFillColor()?.valueOrNull);
    final fillScheme = _computeFillScheme() ?? FillScheme.single;
    final fillLightness = fillIsTransparent ? null : _computeFillLightness();
    final fillTreatment = _computeFillTreatment() ?? FillTreatment.solid;
    final resolvedFillColor = fillcolor ?? _computeResolvedFillColor();

    final strokeIsTransparent = _computeLocalIsStrokeTransparent();
    final strokeColor = strokeIsTransparent ? null : (_computeLocalStrokeColor()?.valueOrNull);
    final strokeScheme = _computeStrokeScheme() ?? StrokeScheme.single;
    final strokeStyle = _computeStrokeStyle();
    final strokeWidth = _computeStrokeWidth() ?? StrokeWidth.medium;
    final strokeLightness = strokeIsTransparent ? null : _computeStrokeLightness();
    final strokeTreatment = _computeStrokeTreatment() ?? StrokeTreatment.rounded;
    final resolvedStrokeColor = strokeColor ?? _computeResolvedStrokeColor();

    final entityIndex = fillScheme == FillScheme.indexed || strokeScheme == StrokeScheme.indexed ? _computeEntityMaxIndex() : null;
    final fillEntityMaxIndex = fillScheme == FillScheme.indexed ? entityIndex : null;
    final strokeEntityMaxIndex = strokeScheme == StrokeScheme.indexed ? entityIndex : null;

    final fillSchemeGradientElement = _fillMenuElement?.querySelector('.fill-style.${FillScheme.gradient.name}');
    fillSchemeGradientElement?.classList.toggle('active', fillScheme == FillScheme.gradient);

    final fillSchemeIndexedElement = _fillMenuElement?.querySelector('.fill-style.${FillScheme.indexed.name}');
    fillSchemeIndexedElement?.classList.toggle('active', fillScheme == FillScheme.indexed);

    final fillTreatmentHandElement = _fillMenuElement?.querySelector('.fill-style.${FillTreatment.hand.name}');
    fillTreatmentHandElement?.classList.toggle('active', fillTreatment == FillTreatment.hand);

    final strokeSchemeSingleElement =
        _strokeMenuElement?.querySelector('.stroke-style.${StrokeScheme.single.name}') ??
        _strokeSettingsMenuElement?.querySelector('.stroke-style.${StrokeScheme.single.name}');
    strokeSchemeSingleElement?.classList.toggle('active', strokeScheme == StrokeScheme.single);

    final strokeSchemeIndexedElement =
        _strokeMenuElement?.querySelector('.stroke-style.${StrokeScheme.indexed.name}') ??
        _strokeSettingsMenuElement?.querySelector('.stroke-style.${StrokeScheme.indexed.name}');
    strokeSchemeIndexedElement?.classList.toggle('active', strokeScheme == StrokeScheme.indexed);

    // fillBrightnessElement
    _updateBrightnessSliderColors(_fillBrightnessSlider, resolvedFillColor, fillLightness, entityIndex: fillEntityMaxIndex);

    // strokeTreatmentElement
    final strokeTreatmentElements = (_strokeSettingsMenuElement ?? _strokeMenuElement)?.querySelectorAll('.stroke-treatment').toList();
    if (strokeTreatmentElements != null) {
      for (final element in strokeTreatmentElements) {
        if (!element.isA<HTMLElement>()) continue;
        element as HTMLElement;
        element.classList.toggle('active', element.id == strokeTreatment.name);
      }
    }

    // strokePatternElement
    final strokePatternElements = (_strokeSettingsMenuElement ?? _strokeMenuElement)?.querySelectorAll('.stroke-pattern').toList();
    if (strokePatternElements != null) {
      for (final element in strokePatternElements) {
        if (!element.isA<HTMLElement>()) continue;
        element as HTMLElement;
        element.classList.toggle('active', strokeStyle != null && element.id == strokeStyle.name);
      }
    }

    // strokeThicknessElement
    // update the slider value
    _strokeThicknessSlider.setValue(strokeWidth.index.toDouble(), newShowValue: true, editable: true);
    // Update the icon
    final menuElement = _strokeSettingsMenuElement ?? _strokeMenuElement;
    if (menuElement?.classList.contains('stroke-setting') == true) {
      for (final width in StrokeWidth.values) {
        menuElement?.classList.toggle(width.name, width.name == strokeWidth.name);
      }
    }

    // strokeBrightnessElement
    _updateBrightnessSliderColors(_strokeBrightnessSlider, resolvedStrokeColor, strokeLightness, entityIndex: strokeEntityMaxIndex);
  }

  void _updateBrightnessSliderColors(
    CustomSlider brightnessSlider,
    Color? currentColor,
    ColorLightness? currentLightness, {
    required EntityIndex? entityIndex,
  }) {
    // null, white, black, gray => gray
    final color = currentColor?.isGrayScale == false ? currentColor! : NamedColor.gray;

    // force lightness to match the color
    if (currentColor == NamedColor.softBlack) {
      currentLightness = ColorLightness.dark3;
    } else if (currentColor == NamedColor.white) {
      currentLightness = ColorLightness.white;
    } else if (currentColor == NamedColor.pitchBlack) {
      currentLightness = ColorLightness.black;
    }

    final indexed = entityIndex != null;
    final index = entityIndex == null || entityIndex.index == -1 ? null : entityIndex.index;
    final target = ColorTarget.blend(graph.isInvertLightness);
    final palette = computePalette();
    final rgbList = ColorLightness.editorRange.map((lightness) => color.toRgbString(lightness.normalized, palette, target));
    final defaultLightness = currentColor != null ? Palette.resolve(palette, currentColor).tryCast<CustomColor>()?.hsl.lightness.value : null;
    final initialColorLightness = currentLightness != ColorLightness.initial ? null : currentColor?.hsl.lightness?.value;
    final indexLightness = index != null
        ? NormalizedLightness.atTone(index, entityIndex!.maxIndex, NormalizedLightness.defaultIndexedLow, NormalizedLightness.defaultIndexedHigh)
        : null;
    final lightnessToSet = indexLightness?.value ?? currentLightness?.normalized?.value ?? defaultLightness ?? initialColorLightness;

    // default value must set first
    brightnessSlider.defaultValues = entityIndex != null ? _computeIndexedLightness(entityIndex.maxIndex) : (<double>[]..addNotNull(defaultLightness));
    brightnessSlider.setValue(lightnessToSet, newShowValue: (lightnessToSet != null || currentLightness != null) && !indexed, editable: !indexed);
    brightnessSlider.updateBrightnessColors(rgbList.toList(growable: false));
  }

  List<double> _computeIndexedLightness(int maxIndex) {
    final n = maxIndex + 1;
    return List.generate(n, (i) => NormalizedLightness.atTone(i, n - 1, NormalizedLightness.defaultIndexedLow, NormalizedLightness.defaultIndexedHigh).value);
  }

  StyleValue<Color>? _computeLocalColor(MenuMode menuMode, {StylePriority? priorityMatch}) => switch (menuMode) {
    MenuMode.fill => _computeLocalFillColor(priorityMatch: priorityMatch),
    MenuMode.stroke => _computeLocalStrokeColor(priorityMatch: priorityMatch),
    MenuMode.strokeSettings => null,
  };

  ColorLightness? _computeLocalLightness(MenuMode menuMode) => switch (menuMode) {
    MenuMode.fill => _computeFillLightness(),
    MenuMode.stroke => _computeStrokeLightness(),
    MenuMode.strokeSettings => null,
  };

  _ActionType _setColorStyleCommandOnItems(StyleCommand? styleCommand, MenuMode menuMode) {
    final stage = EntityStage('styleCommand', graph.previewStage);

    if (menuMode == MenuMode.fill || _isLinkingForced) {
      _serviceMenuFillColorSetAll(stage, _items.map((e) => e.id), styleCommand?.styleValue<Color>(), isLinkingForced: _isLinkingForced);
    }

    if (menuMode == MenuMode.stroke || _strokeIsLinkedToFill) {
      _serviceMenuStrokeColorSetAll(stage, _items.map((e) => e.id), styleCommand?.styleValue<Color>());
    }

    stage.mergeIntoParent(options: {'patchOrigin': PatchOrigin.userMenu.name, 'pushEvent': false});

    return menuMode == MenuMode.fill ? _ActionType.fillColor : _ActionType.strokeColor;
  }

  _ActionType _setColorOnItems(Color? color, MenuMode menuMode) {
    final stage = EntityStage('setColorOnItems', graph.previewStage);
    final styleValue = color == null ? null : StyleValue<Color>(color);

    if (menuMode == MenuMode.fill || _isLinkingForced) {
      _serviceMenuFillColorSetAll(stage, _items.map((e) => e.id), styleValue, isLinkingForced: _isLinkingForced);
      // if we remove the color we need to remove the transparency and lightness as well
      if (color == null) {
        _serviceMenuFillOpacitySetAll(stage, _items.map((e) => e.id), null);
        _serviceMenuFillLightnessSetAll(stage, _items.map((e) => e.id), null);
      }
    }

    if (menuMode == MenuMode.stroke || _strokeIsLinkedToFill) {
      _serviceMenuStrokeColorSetAll(stage, _items.map((e) => e.id), styleValue);
      // if we remove the color we nee to remove the transparency as well
      if (color == null) {
        _serviceMenuStrokeOpacitySetAll(stage, _items.map((e) => e.id), null);
        _serviceMenuStrokeLightnessSetAll(stage, _items.map((e) => e.id), null);
      }
    }

    stage.mergeIntoParent(options: {'patchOrigin': PatchOrigin.userMenu.name, 'pushEvent': false});

    return menuMode == MenuMode.fill ? _ActionType.fillColor : _ActionType.strokeColor;
  }

  (_ActionType, dynamic) _setColorStrokeFill({bool? stroke, bool? fill}) {
    final ids = _items.map((e) => e.id).toList(growable: false);

    final stage = EntityStage('setColorStrokeFill', graph.previewStage);

    if (stroke == false) {
      _serviceMenuStrokeOpacitySetAll(stage, ids, ColorOpacity.transparent);
    } else if (stroke == true) {
      _serviceMenuStrokeOpacitySetAll(stage, ids, ColorOpacity.notTransparent);

      if (_computeStrokeScheme() == StrokeScheme.none) {
        _serviceMenuStrokeSchemeSetAll(stage, ids, StrokeScheme.single);
      }
    }

    if (fill == false) {
      _serviceMenuFillOpacitySetAll(stage, ids, ColorOpacity.transparent);
    } else if (fill == true) {
      _serviceMenuFillOpacitySetAll(stage, ids, ColorOpacity.notTransparent);

      if (_computeFillScheme() == FillScheme.none) {
        _serviceMenuFillSchemeSetAll(stage, ids, FillScheme.single);
      }
    }

    stage.mergeIntoParent(options: {'patchOrigin': PatchOrigin.userMenu.name, 'pushEvent': false});

    if (stroke != null) return (_ActionType.strokeOpacity, stroke ? ColorOpacity.notTransparent : ColorOpacity.transparent);
    if (fill != null) return (_ActionType.fillOpacity, fill ? ColorOpacity.notTransparent : ColorOpacity.transparent);
    return (_ActionType.fillOpacity, ColorOpacity.transparent);
  }

  StyleValue<Color>? _computeLocalFillColor({StylePriority? priorityMatch}) {
    final localColor = _items.map((e) => serviceMenuFillColorGet(e, recursive: false, priorityMatch: priorityMatch)).nonNulls.toSet().singleOrNull;
    if (localColor == null || localColor != _computeResolvedFillColor()) return null;
    return StyleValue<Color>(localColor);
  }

  Color? _computeResolvedFillColor() => _items.map((e) => serviceMenuFillColorGet(e, recursive: true)).nonNulls.toSet().singleOrNull;

  StyleValue<Color>? _computeLocalStrokeColor({StylePriority? priorityMatch}) {
    final localColor = _items.map((e) => serviceMenuStrokeColorGet(e, recursive: false, priorityMatch: priorityMatch)).nonNulls.toSet().singleOrNull;
    if (localColor == null || localColor != _computeResolvedStrokeColor()) return null;
    return StyleValue<Color>(localColor);
  }

  Color? _computeResolvedStrokeColor() => _items.map((e) => serviceMenuStrokeColorGet(e, recursive: true)).nonNulls.toSet().singleOrNull;

  ColorOpacity? _computeLocalFillOpacity() {
    final localOpacity = _items.map((e) => serviceMenuFillOpacityGet(e, recursive: false)).nonNulls.toSet().singleOrNull;
    if (localOpacity == null) return null;
    final resolvedOpacity = _items.map((e) => serviceMenuFillOpacityGet(e, recursive: true)).nonNulls.toSet().singleOrNull;
    return (localOpacity == resolvedOpacity) ? localOpacity : null;
  }

  ColorOpacity? _computeLocalStrokeOpacity() {
    final localOpacity = _items.map((e) => serviceMenuStrokeOpacityGet(e, recursive: false)).nonNulls.toSet().singleOrNull;
    if (localOpacity == null) return null;
    final resolvedOpacity = _items.map((e) => serviceMenuStrokeOpacityGet(e, recursive: true)).nonNulls.toSet().singleOrNull;
    return (localOpacity == resolvedOpacity) ? localOpacity : null;
  }

  bool _computeLocalIsFillTransparent() => _computeLocalFillOpacity() == ColorOpacity.transparent || _computeFillScheme() == FillScheme.none;
  bool _computeLocalIsStrokeTransparent() => _computeLocalStrokeOpacity() == ColorOpacity.transparent || _computeStrokeScheme() == StrokeScheme.none;

  // fill
  FillScheme? _computeFillScheme() => _items.map((e) => serviceMenuFillSchemeGet(e)).nonNulls.toSet().singleOrNull;
  ColorLightness? _computeFillLightness() => _items.map((e) => serviceMenuFillLightnessGet(e)).nonNulls.toSet().singleOrNull;
  FillTreatment? _computeFillTreatment() => _items.map((e) => serviceMenuFillTreatmentGet(e)).nonNulls.toSet().singleOrNull;
  // stroke
  StrokeStyle? _computeStrokeStyle() => _items.map((e) => serviceMenuStrokeStyleGet(e)).nonNulls.toSet().singleOrNull;
  StrokeWidth? _computeStrokeWidth() => _items.map((e) => serviceMenuStrokeWidthGet(e)).nonNulls.toSet().singleOrNull;
  ColorLightness? _computeStrokeLightness() => _items.map((e) => serviceMenuStrokeLightnessGet(e)).nonNulls.toSet().singleOrNull;
  StrokeTreatment? _computeStrokeTreatment() => _items.map((e) => serviceMenuStrokeTreatmentGet(e)).nonNulls.toSet().singleOrNull;
  StrokeScheme? _computeStrokeScheme() => _items.map((e) => serviceMenuStrokeSchemeGet(e)).nonNulls.toSet().singleOrNull;

  EntityIndex? _computeEntityMaxIndex() {
    final indexes = _items.map((e) => serviceEntityIndex(e.stage, e.id)).nonNulls.toSet();
    if (indexes.isEmpty) return null;
    if (indexes.length == 1) return indexes.first;
    return EntityIndex(-1, indexes.first.maxIndex);
  }

  void _updatePreview(List<ReadOnlyEntity> items, _ActionType type, dynamic value) {
    _clearPreview(items);

    _log.fine(() => 'Update preview: $type -> $value (${value.runtimeType})');

    if (items.isEmpty) return;

    graph.previewStage.setOwner('ItemMenuColor', animatedDashPreview: true);

    final stage = EntityStage('itemMenuColor', graph.previewStage);
    _setValue(stage, items, type, value);

    _updatePreviewPostProcess(stage, value);

    stage.mergeIntoParent(options: {'patchOrigin': PatchOrigin.userMenu.name, 'pushEvent': true});
  }

  void _clearPreview(List<ReadOnlyEntity> items) {
    _scheduledClearPreview?.cancel();
    _scheduledClearPreview = null;

    if (items.isEmpty) return;

    graph.previewStage.resetOwnerAndRelease();
    graph.redraw('ItemMenuColor _clearPreview');
  }

  void _applyPreview(List<ReadOnlyEntity> items, _ActionType type, dynamic value, {bool addUndo = true}) {
    _scheduledClearPreview?.cancel();
    _scheduledClearPreview = null;

    if (items.isEmpty) return;

    final layers = items.map((item) => ItemHelpers.tryLayer(item)).nonNulls.toSet();
    final layer = layers.tryFirst;
    final graph = layer?.graph;
    if (layer == null || graph == null) return;

    if (graph.previewStage.isEmpty) {
      graph.previewStage.resetOwnerAndRelease();
      return;
    }

    final snapshotId = graph.snapshotTake();

    for (final item in items) {
      graph.layersStage.requestRendering(item.id, 'ItemMenuColor._apply');
    }

    graph.redraw('serviceSetPadding');

    // The change is stored in the preview stage, so we just need to merge it to apply it
    graph.previewStage.resetOwnerAndMergeIntoParent(options: {'patchOrigin': PatchOrigin.sideEffect.name, 'pushEvent': true});

    final undo = Undo('ItemMenuColor.apply.${type.name}')..add(graph.snapshotBuildUndoCallback(snapshotId), requestSave: layer.host.saver);
    graph.pushUndo(undo);

    // Tracking
    final valueName = value is Enum ? value.name : '${value}';
    if (valueName.isNotEmpty) {
      switch (type) {
        case _ActionType.strokeTreatment:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/STROKE_TREATMENT', {'strokeTreatment': valueName, 'count': items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.strokeTreatment', 'set', {'count': items.length, 'value': valueName});
        case _ActionType.strokeStyle:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/STROKE_STYLE', {'strokeStyle': valueName, 'count': items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.strokeStyle', 'set', {'count': items.length, 'value': valueName});
        case _ActionType.strokeScheme:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/STROKE_SCHEME', {'strokeScheme': valueName, 'count': items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.strokeScheme', 'set', {'count': items.length, 'value': valueName});
        case _ActionType.strokeWidth:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/STROKE_WIDTH', {'strokeWidth': valueName, 'count': items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.strokeWidth', 'set', {'count': items.length, 'value': valueName});
        case _ActionType.strokeLightness:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/STROKE_LIGHTNESS', {'strokeLightness': valueName, 'count': items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.strokeLightness', 'set', {'count': items.length, 'value': valueName});
        case _ActionType.strokeOpacity:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/STROKE_OPACITY', {'strokeOpacity': valueName, 'count': _items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.strokeOpacity', 'set', {'count': items.length, 'value': valueName});
        case _ActionType.fillTreatment:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/FILL_STYLE', {'fillTreatment': valueName, 'count': items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.fillTreatment', 'set', {'count': items.length, 'value': valueName});
        case _ActionType.fillScheme:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/FILL_STYLE', {'fillStyle': valueName, 'count': items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.fillStyle', 'set', {'count': items.length, 'value': valueName});
        case _ActionType.fillLightness:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/FILL_LIGHTNESS', {'fillLightness': valueName, 'count': items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.fillLightness', 'set', {'count': items.length, 'value': valueName});
        case _ActionType.fillOpacity:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/FILL_OPACITY', {'fillOpacity': valueName, 'count': items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.fillOpacity', 'set', {'count': items.length, 'value': valueName});
        case _ActionType.strokeColor:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/STROKE_COLOR', {'strokeColor': valueName, 'count': items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.strokeColor', 'set', {'count': items.length, 'value': valueName});
        case _ActionType.fillColor:
          Analytics().trackMonitoringEvent('$analyticsPrefix/MENU/FILL_COLOR', {'fillColor': valueName, 'count': items.length});
          ItemMenuProvider.sendAnalyticsEvent('${analyticsPrefix.toLowerCase()}.fillColor', 'set', {'count': items.length, 'value': valueName});
      }
    }
  }

  // *** Abstract methods to implement ***

  // This is the list that will be shown to the user, ordered from the top left
  List<Color> get _userColor;

  bool get _showGeneratedColorTag;

  bool get _enableCustomColors;

  bool get _allowStrokeSchemeMenu;

  bool get _strokeIsLinkedToFill;

  bool get _resetLightnessOnCustomColorChange;

  /// Determines whether link override is enforced, meaning any changes to fill will
  /// automatically affect the stroke color and vice versa.
  /// This is triggered when only a single icon is present in the item list.
  bool get _isLinkingForced;

  StyleValue<Color>? computeActiveColor(MenuMode menuMode);

  Palette? computePalette();

  bool showAiColorButton(MenuMode menuMode);

  void _setPreview(List<ReadOnlyEntity> items, Color color, MenuMode menuMode, {required bool resetLightness});

  void _updatePreviewPostProcess(EntityStage stage, dynamic value);

  void _serviceMenuStrokeSchemeSetAll(Stage stage, Iterable<EntityId> entities, StrokeScheme scheme, {bool includeInGroup = true});

  void _serviceMenuStrokeColorSetAll(Stage stage, Iterable<EntityId> entities, StyleValue<Color>? color);

  void _serviceMenuStrokeStyleSetAll(Stage stage, Iterable<EntityId> entities, StrokeStyle? style, {bool includeInGroup = true});

  void _serviceMenuStrokeTreatmentSetAll(Stage stage, Iterable<EntityId> entities, StrokeTreatment treatment, {bool includeInGroup = true});

  void _serviceMenuStrokeLightnessSetAll(Stage stage, Iterable<EntityId> entities, ColorLightness? value, {bool includeInGroup = true});

  void _serviceMenuStrokeWidthSetAll(Stage stage, Iterable<EntityId> entities, StrokeWidth width, {bool includeInGroup = true});

  void _serviceMenuFillColorSetAll(Stage stage, Iterable<EntityId> entities, StyleValue<Color>? color, {bool isLinkingForced = true});

  void _serviceMenuFillSchemeSetAll(Stage stage, Iterable<EntityId> entities, FillScheme scheme, {bool includeInGroup = true});

  void _serviceMenuFillLightnessSetAll(Stage stage, Iterable<EntityId> entities, ColorLightness? value, {bool includeInGroup = true});

  void _serviceMenuFillTreatmentSetAll(Stage stage, Iterable<EntityId> entities, FillTreatment treatment, {bool includeInGroup = true});

  void _serviceMenuFillOpacitySetAll(Stage stage, Iterable<EntityId> entities, ColorOpacity? opacity, {bool includeInGroup = true});

  void _serviceMenuStrokeOpacitySetAll(Stage stage, Iterable<EntityId> entities, ColorOpacity? opacity, {bool includeInGroup = true});
}

class _ItemMenuColorBasePrivateer {
  final ItemMenuColorBase _menu;
  _ItemMenuColorBasePrivateer._(this._menu);

  MenuConfig get menuConfig => _menu._menuConfig;
  HTMLElement? get fillMenuElement => _menu._fillMenuElement;
  HTMLElement? get strokeMenuElement => _menu._strokeMenuElement;
  HTMLElement? get strokeSettingsMenuElement => _menu._strokeSettingsMenuElement;

  HTMLElement? subMenuElement(MenuMode? menuMode) => _menu._menuModeElement(menuMode);
  HTMLElement? mainSubMenuElement(MenuMode? menuMode) => _menu._mainSubMenuElement(menuMode);
  HTMLElement? pickerSubMenuElement(MenuMode? menuMode) => _menu._pickerSubMenuElement(menuMode);

  CustomSlider get fillBrightnessSlider => _menu._fillBrightnessSlider;
  CustomSlider get strokeBrightnessSlider => _menu._strokeBrightnessSlider;
  CustomSlider get strokeThicknessSlider => _menu._strokeThicknessSlider;

  ColorPicker? get colorPicker => _menu._openedColorPicker;
  List<Color> get userColors => _menu._userColor;

  Future clickFillTreatmentHand() async {
    final fillTreatmentHandElement = fillMenuElement!.querySelector('.fill-style.${FillTreatment.hand.name}')!;
    fillTreatmentHandElement.dispatchEvent(MouseEvent('click')..initMouseEvent('click', true, true));
    await Future.delayed(const Duration(milliseconds: 0)); // for the event to be processed
  }

  Future clickStrokeTreatmentHand() async {
    final strokeTreatmentHandElement = (strokeSettingsMenuElement ?? strokeMenuElement)!.querySelector('.stroke-treatment.${StrokeTreatment.hand.name}')!;
    strokeTreatmentHandElement.dispatchEvent(MouseEvent('click')..initMouseEvent('click', true, true));
    await Future.delayed(const Duration(milliseconds: 0)); // for the event to be processed
  }

  Future clickFillSchemeGradient() async {
    final fillSchemeGradientElement = fillMenuElement!.querySelector('.fill-style.${FillScheme.gradient.name}')!;
    fillSchemeGradientElement.dispatchEvent(MouseEvent('click')..initMouseEvent('click', true, true));
    await Future.delayed(const Duration(milliseconds: 0)); // for the event to be processed
  }

  Future setColorFromColorCircle(Color color, MenuMode mode, {required bool resetLightness}) async {
    _menu._actionSetColorFromColorCircle(color, mode, resetLightness: resetLightness);
  }

  Future clickAutomaticColor(MenuMode mode) async {
    final menuElement = _menu._menuModeElement(mode)!;
    final colorElement = menuElement.querySelector('.automatic_color.color_menu_icon')!;
    colorElement.dispatchEvent(MouseEvent('click')..initMouseEvent('click', true, true));
    await Future.delayed(const Duration(milliseconds: 0)); // for the event to be processed
  }

  Future clickTransparentColor(MenuMode mode) async {
    final menuElement = _menu._menuModeElement(mode)!;
    final colorElement = menuElement.querySelector('.transparent.color_menu_icon')!;
    colorElement.dispatchEvent(MouseEvent('click')..initMouseEvent('click', true, true));
    await Future.delayed(const Duration(milliseconds: 0)); // for the event to be processed
  }

  Future clickAiColor(MenuMode mode) async {
    final menuElement = _menu._menuModeElement(mode)!;
    final colorElement = menuElement.querySelector('.ai_color.color_menu_icon')!;
    colorElement.dispatchEvent(MouseEvent('click')..initMouseEvent('click', true, true));
    await Future.delayed(const Duration(milliseconds: 0)); // for the event to be processed
  }

  Future clickCustomColor(MenuMode mode, CustomColor customColor) async {
    final hexReference = ItemMenuColorBase.dataHex(customColor, null);
    final menuElement = _menu._menuModeElement(mode)!;
    final colorElement = menuElement.querySelector('.color_circle[data-hex=${hexReference}]')!;
    colorElement.dispatchEvent(MouseEvent('click')..initMouseEvent('click', true, true));
    await Future.delayed(const Duration(milliseconds: 0)); // for the event to be processed
  }
}

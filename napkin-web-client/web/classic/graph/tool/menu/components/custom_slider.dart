import 'dart:async';

import 'package:analytics_web_lib/analytics_web_lib.dart';
import 'package:collection/collection.dart';
import 'package:util_web_lib/environment.dart';
import 'package:util_web_lib/has_subscription.dart';
import 'package:util_web_lib/helper.dart' as helper;
import 'package:util_web_lib/html_root.dart';
import 'package:util_web_lib/js_interop.dart';
import 'package:util_web_lib/privateer.dart';
import 'package:util_web_lib/releaser.dart';
import 'package:util_web_lib/ui-kit/cx_enums.dart' show CxPointyPlacement;
import 'package:util_web_lib/ui-kit/cx_tooltip.dart';
import 'package:vector_math/vector_math.dart';

enum SliderBaseType { brightness, thickness }

final class CustomSlider with HasSubscription, HtmlRoot, Privateer<CustomSlidePrivateer>, Releaser {
  double? _downValue;

  CxTooltip? _valueTooltip;

  // visual configuration
  static const int slider_width = 80;
  static const int knob_width = 10;
  static const int knob_image_width = 20;
  final bool arrow; // or default circular knob
  final SliderBaseType sliderBaseType;

  bool _isEditable;

  final List<double> values;
  final List<String> valueTooltips;
  double get _minValue => values.first;
  double get _maxValue => values.last;

  // Value to use when the index is null
  final List<double> _defaultValues;
  set defaultValues(Iterable<double> value) {
    _defaultValues.clearAddAll(value);
    _updateDefaultMarker();
  }

  // current state
  int? _index;
  int? _previewIndex;
  int? _defaultValueIndex;
  final void Function(double?) onChange;
  final void Function(double?) onMouseMove;

  double get doubleValue => (_index == null ? null : values[_index!]) ?? _defaultValues.at(_defaultValueIndex ?? 0) ?? values.first;
  double get doublePreviewValue => (_previewIndex == null ? null : values[_previewIndex!]) ?? _defaultValues.at(_defaultValueIndex ?? 0) ?? values.first;
  // null mean default value for the outside world
  double? get doubleValueNoDefault => _index == null ? null : values[_index!];

  bool get isDragging => _downValue != null;

  late final HTMLDivElement _sliderBase;
  HTMLDivElement? _sliderKnob;
  bool _lastShowValue = false;
  final List<HTMLDivElement> _defaultMarkerElements = [];

  StreamSubscription<MouseEvent>? _moveSubscription;
  StreamSubscription<MouseEvent>? _upSubscription;

  CustomSlider({
    required this.values,
    required this.valueTooltips,
    required this.sliderBaseType,
    required this.onMouseMove,
    required this.onChange,
    this.arrow = false,
    double? initialValue,
    List<double>? defaultValues,
    List<String>? brightnessColors,
    HTMLElement? customBase,

    bool showValue = true,
    bool isEditable = true,
  }) : _defaultValues = defaultValues ?? [],
       _lastShowValue = showValue,
       _isEditable = isEditable {
    // Validate the initial value
    final indices = _calculateIndexFromValue(initialValue ?? _defaultValues.tryFirst ?? values.first);
    _index = indices.$1;
    _previewIndex = _index;
    _defaultValueIndex = indices.$2;
    assert(values.isNotEmpty, 'Values should not be empty');
    assert(values.first <= values.last, 'Values should be in increasing order');
    assert(
      initialValue == null || (initialValue >= values.first && initialValue <= values.last),
      'Initial value `$initialValue` should be between `${values.first}` and `${values.last}` (included)',
    );
    _setSliderKnobPosition(doubleValue);

    // Create the slider base
    _sliderBase = HTMLDivElement()
      ..classList.addAll(['icon', 'slider-base', sliderBaseType.name, 'dom_capture_mouse'])
      ..style.setProperty('--slider-width', '${slider_width}px') // Set the width of the slider base
      ..style.setProperty('--slider-base-opacity', (sliderBaseType == SliderBaseType.brightness) ? '1' : '0.42');
  }

  void cleanup() {
    _moveSubscription?.cancel();
    _moveSubscription = null;

    _upSubscription?.cancel();
    _upSubscription = null;

    _valueTooltip?.releaseComponent();
    _valueTooltip = null;

    _downValue = null;
  }

  void release() {
    cleanup();
    releaseSubscriptions();
    releaseAll(); // Release all disposables including tooltips
  }

  void updateBrightnessColors(List<String> colors) {
    if (sliderBaseType != SliderBaseType.brightness) return;

    // trigger a css animation
    helper.triggerCssAnimation(cachedRoot, 'validate-animation', regenerateDuration: 0.5);

    // list to css gradient with smooth limits between colors
    final n = colors.length;
    _sliderBase.style.setProperty(
      '--slider-base-brightness-gradient',
      'linear-gradient(to right, ${colors.mapIndexed((i, c) {
        final start = i / n;
        final end = (i + 1) / n;
        return '$c ${(start * 100).approx(2)}%, $c ${(end * 100).approx(2)}%';
      }).join(', ')})',
    );
  }

  void _updateDefaultMarker() {
    if (_defaultValues.isEmpty) {
      for (final element in _defaultMarkerElements) {
        element.remove();
      }
      _defaultMarkerElements.clear();
    } else {
      for (final element in _defaultMarkerElements) {
        element.remove();
      }
      _defaultMarkerElements.clear();
      final stepWidth = slider_width / values.length;
      final rangeWidth = (slider_width - stepWidth) / (_maxValue - _minValue);
      for (final value in _defaultValues) {
        final percent = (value - _minValue);
        final pos = percent * rangeWidth + stepWidth * 0.5;
        final marker = HTMLDivElement()
          ..classList.addAll(['default-marker', if (!_isEditable && value.isAlmost(doubleValue)) 'selected'])
          ..style.setProperty('--default-marker-left', '${pos.approx(2)}px');

        _sliderBase.appendChild(marker);
        _defaultMarkerElements.add(marker);
      }
    }
  }

  void _updateShowValue(bool showValue) {
    _lastShowValue = showValue;
    _sliderKnob?.style.setProperty('--slider-display', showValue ? 'block' : 'none');
  }

  @override
  HTMLElement build() {
    // Create the slider container
    final sliderContainer = HTMLDivElement()..classList.addAll(['slider-container']);

    autoUnsubscribe(
      _sliderBase.onMouseDown.listen((MouseEvent e) {
        if (!_isEditable) return;

        final newDownValue = e.offsetX.toDouble() - e.pageX.toDouble();
        _downValue = newDownValue;
        _updateShowValue(true);

        // Prevent text selection while dragging
        e.preventDefault();
        _updateFromOffset(e.pageX + newDownValue);

        // Show tooltip
        _updateValueTooltip();

        // Listen to mouse move event
        _moveSubscription?.cancel();
        _moveSubscription = Environment.onMouseMove.listen((MouseEvent? e) {
          if (e == null || !e.isA<MouseEvent>()) return;

          final downValue = _downValue;
          if (downValue == null) return;

          _updateFromOffset(e.pageX + downValue);
          e.stopPropagation();
          e.preventDefault();
        });

        // Listen to mouse up event
        _upSubscription?.cancel();
        _upSubscription = Environment.onMouseUp.listen((MouseEvent? e) {
          if (e == null || !e.isA<MouseEvent>()) return;

          _validateValue(doubleValueNoDefault);
          e.stopPropagation();
          e.preventDefault();

          _moveSubscription?.cancel();
          _moveSubscription = null;

          _upSubscription?.cancel();
          _upSubscription = null;

          _downValue = null;

          // Hide tooltip
          _updateValueTooltip();
        });
      }),
    );

    // Create the slider knob
    _sliderKnob = HTMLDivElement()..classList.addAll(['icon', if (!arrow) 'slider-knob', if (arrow) 'slider-knob-arrow']);
    _sliderKnob?.style.setProperty('--knob-width', '${knob_image_width}px'); // Set the width of the knob
    _sliderKnob?.style.setProperty('--slider-display', _lastShowValue ? 'block' : 'none');

    sliderContainer.appendChild(_sliderBase);
    sliderContainer.appendChild(_sliderKnob!);
    return sliderContainer;
  }

  void _updateFromOffset(double offset) {
    // Calculate the new position of the knob
    final (newIndex, newDefaultIndex) = _calculateIndexFromPosition(offset);
    if (_index == newIndex && _defaultValueIndex == newDefaultIndex) return;

    _index = newIndex;
    _defaultValueIndex = newDefaultIndex;
    _setSliderKnobPosition(doubleValue);
    onMouseMove(doubleValueNoDefault);

    // Update tooltip with current value
    _updateValueTooltip();
  }

  // Add method to manage the tooltip
  void _updateValueTooltip() {
    if (_downValue == null || cachedRoot == null) {
      // Not dragging, remove tooltip
      if (_valueTooltip != null) {
        _valueTooltip!.hideAndRemoveRoot();
        _valueTooltip = null;
      }
      return;
    }

    // Format value based on slider type
    final index = values.indexOf(doubleValue);
    final displayValue = index != -1 ? valueTooltips[index] : 'Auto';

    // Create or update tooltip
    if (_valueTooltip == null) {
      _valueTooltip = CxTooltip(
        releaser: this,
        placement: CxPointyPlacement.bottomCenter,
        textContent: displayValue,
        classes: ['inverted'],
      );
      cachedRoot?.appendChild(_valueTooltip!.root);
    }

    // Update tooltip text
    _valueTooltip?.updateInnerText(displayValue);
    final pos = _computeSlideKnobPosition(doubleValue);
    _valueTooltip!.updateAbsoluteLocation(Vector2(pos, 8));
  }

  void _validateValue(double? value) {
    onChange(value);
    // trigger a css animation
    helper.triggerCssAnimation(cachedRoot, 'validate-animation', regenerateDuration: 1);
  }

  // Function to calculate value based on slider position
  (int?, int?) _calculateIndexFromPosition(double sliderPosition) {
    final t = (sliderPosition / slider_width).clamp(0.0, 1.0);
    // Calculate the corresponding value between minValue and maxValue
    final v = t * (_maxValue - _minValue) + _minValue;
    return _calculateIndexFromValue(v);
  }

  int? _calculateDefaultValueIndex(double v) {
    if (_defaultValues.isEmpty) return null;
    final quantum = (_maxValue - _minValue) / (values.length - 1);
    final defaultIndex = _defaultValues.indexWhere((e) => (v - e).abs() < quantum / 2);
    return defaultIndex == -1 ? null : defaultIndex;
  }

  (int?, int?) _calculateIndexFromValue(double v) {
    final defaultValueIndex = _calculateDefaultValueIndex(v); // snap to default value
    if (defaultValueIndex != null) return (null, defaultValueIndex);

    // return the index of the closest value
    var minIndex = 0;
    var minDistance = (values[minIndex] - v).abs();
    for (var i = 1; i < values.length; i++) {
      final value = values[i];
      final distance = (value - v).abs();
      if (distance < minDistance) {
        minDistance = distance;
        minIndex = i;
      }
    }
    return (minIndex, null);
  }

  // Function to update the slider knob position and move it based on value
  void _setSliderKnobPosition(double newValue) {
    final centerPos = _computeSlideKnobPosition(newValue) - knob_image_width / 2;
    _sliderKnob?.style.left = '${centerPos}px';
  }

  double _computeSlideKnobPosition(double newValue) {
    // Calculate the snap position based on the given value
    final percent = (newValue - _minValue) / (_maxValue - _minValue);
    final numSteps = values.length;
    if (arrow) {
      // arrow point to the center of the step
      final stepWidth = slider_width / numSteps;
      final rangeWidth = slider_width - stepWidth;
      return percent * rangeWidth + stepWidth * 0.5;
    } else {
      // default knob position is at the start pf the range and the end of the range
      final quantumStep = (slider_width - knob_width) / (numSteps - 1);
      return (newValue - _minValue) * quantumStep + knob_width / 2;
    }
  }

  /// This method sets the initial value and updates the UI accordingly.
  void setValue(double? newValue, {required bool newShowValue, required bool editable}) {
    _isEditable = editable;
    _updateShowValue(newShowValue);

    if (newValue == null) {
      _index = null;
      _previewIndex = null;
    } else {
      if (newValue < _minValue || newValue > _maxValue) {
        Monitoring().captureException('Value `$newValue` should be between `$_minValue` and `$_maxValue`');
        return;
      }
      final indices = _calculateIndexFromValue(newValue); // This will update both the value and the knob position.
      _index = indices.$1;
      _defaultValueIndex = indices.$2;
    }
    _setSliderKnobPosition(doubleValue);
  }

  @override
  CustomSlidePrivateer get createPrivateer => CustomSlidePrivateer(this);
}

class CustomSlidePrivateer {
  final CustomSlider _slider;
  CustomSlidePrivateer(this._slider);

  double get pointerPercent => (_slider.doubleValue - _slider._minValue) / (_slider._maxValue - _slider._minValue);
  List<double> get defaultValues => _slider._defaultValues;

  void fakeClick(double percent) {
    final offset = percent * CustomSlider.slider_width;
    _slider._updateFromOffset(offset);
    _slider._validateValue(_slider.doubleValueNoDefault);
  }
}

import 'package:io_web_lib/logging.dart';
import 'package:util_web_lib/extension.dart';
import 'package:util_web_lib/helper.dart' as helper;

import '../../app/context.dart';
import '../library/library_item.dart';
import '../library/library_tab.dart';
import 'page_model.dart';

/// Utility class for loading default page models
/// Shared between PageView and LibraryPanel to avoid code duplication
class PageLoaderUtils {
  static final Logger _log = Logger('PageLoaderUtils', browser: Logger.DEBUG);

  static Future<PageModel?> loadDefaultPageModel(Context context, LibraryCollectionType collectionType) async {
    try {
      // TODO move this to the server, it is very inefficient to load all pages just to get the default one
      final modelHeaders = await LibraryTab.load(context, clearCache: false, collectionType: collectionType);
      // Sort by last edited date (most recent first)
      modelHeaders.sort((a, b) => b.lastEditedOn.compareTo(a.lastEditedOn));

      final pageKey = modelHeaders.tryFirst?.id;
      if (pageKey == null || pageKey.isEmpty) return null;

      _log.info(() => '[loadDefaultPageModel] Loading default page model `$pageKey` from $collectionType');
      final pageModel = await helper.retryAction<PageModel?>(
        () async {
          return await PageResource(context).getResource(pageKey);
        },
        log: _log,
        name: 'load default page model',
      );

      if (pageModel == null) {
        _log.severe(() => 'Failed to load default page (pageModel is null)');
        return null;
      }

      // Don't use pages that have been created before mid-2024
      if (pageModel.lastEditedOn.isBefore(DateTime(2024, 6, 1))) {
        _log.warning(() => 'Failed to load default page (page is too old: ${pageModel.lastEditedOn.toIso8601String()})');
        return null;
      }

      return pageModel;
    } catch (e) {
      _log.warning(() => 'Failed to load default page from $collectionType: $e');
      return null;
    }
  }

  static Future<PageModel?> loadFirstAvailablePage(Context context) async {
    PageModel? pageModel;
    pageModel ??= await loadDefaultPageModel(context, LibraryCollectionType.teamSpace);
    pageModel ??= await loadDefaultPageModel(context, LibraryCollectionType.authoredByMe);
    pageModel ??= await loadDefaultPageModel(context, LibraryCollectionType.allRecent);

    return pageModel;
  }
}

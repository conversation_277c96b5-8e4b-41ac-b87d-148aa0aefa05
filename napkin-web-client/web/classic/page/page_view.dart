import 'dart:async';
import 'dart:typed_data';

import 'package:ai_web_lib/ai_web_lib.dart';
import 'package:analytics_web_lib/analytics_web_lib.dart';
import 'package:io_web_lib/logging.dart';
import 'package:util_web_lib/browser.dart' as browser;
import 'package:util_web_lib/browser_storage.dart';
import 'package:util_web_lib/dialog.dart';
import 'package:util_web_lib/drawer.dart';
import 'package:util_web_lib/e2e_testing.dart';
import 'package:util_web_lib/environment.dart';
import 'package:util_web_lib/function.dart';
import 'package:util_web_lib/has_subscription.dart';
import 'package:util_web_lib/helper.dart' as helper;
import 'package:util_web_lib/html_root.dart';
import 'package:util_web_lib/js_interop.dart';
import 'package:util_web_lib/loader.dart';
import 'package:util_web_lib/privateer.dart';
import 'package:util_web_lib/request.dart';
import 'package:util_web_lib/row.dart';
import 'package:util_web_lib/src/saver.dart';
import 'package:util_web_lib/types.dart';

import '../../app/context.dart';
import '../app_root/app_root.dart';
import '../app_root/model.dart';
import '../app_root/policy.dart';
import '../app_root/view.dart';
import '../auth/auth_helpers.dart';
import '../dialogs/export/export_dialog.dart';
import '../graph/graph.dart';
import '../graph/graph_state_enums.dart';
import '../library/add_page_element.dart';
import '../library/library_item.dart';
import '../library/library_panel.dart';
import 'comment/comment.dart' as comment_import;
import 'comment_navigation.dart';
import 'font/font_repository.dart';
import 'multiplayer/channels.dart';
import 'multiplayer/model_activity_channel.dart';
import 'multiplayer/presence_channel.dart';
import 'page.dart';
import 'page_export_markers.dart';
import 'page_loader_utils.dart';
import 'page_model.dart';
import 'page_multiplayer/page_multiplayer_debugger.dart';
import 'page_settings_box.dart';
import 'page_settings_button.dart';
import 'page_tool_save_status.dart';
import 'page_user_context.dart';
import 'pdf/page_pdf.dart';
import 'presence_activity.dart';
import 'save_notifier.dart';
import 'share/page_access.dart';
import 'share/share_button.dart';
import 'theme/theme_repository.dart';
import 'theme/theme_selector.dart';
import 'theme_button.dart';

enum ReadOnlyReason { cannotUpgradeItems }

class PageView extends View
    with HtmlRoot, HasSubscription, Privateer<PageViewPrivateer>
    implements LibraryHost, ShareButtonHost, PageSettingsButtonHost, ThemeSelectorHost {
  final Logger _log = Logger('PageView', browser: Logger.DEBUG);

  @override
  Context get context => host.context;

  @override
  Graph? get pageGraph => _page?.graph;

  // Public getter for the page
  Page? get page => _page;

  Completer? _navigationCompleter;
  Completer? _enteringPageCompleter;

  Page? _page;
  bool _pageIsStale = false;
  bool _isNewPage = false; // true if the page has just been created
  bool get isNewPage => _isNewPage;

  Policy? _cachedMaxExternalPolicy;
  Future<Policy> get maxExternalPolicy async => _cachedMaxExternalPolicy ??= (await _computeMaxExternalPolicy());

  PageModel? _preloadedPageModel;
  Completer? _preloadedPageModelCompleter;

  final Set<StreamSubscription> _pageSubscriptions = {};

  final Element _pageHolder = HTMLDivElement()..classList.add('page_holder');
  Element get pageHolder => _pageHolder;

  final Element _pageOverlay = HTMLDivElement()..classList.add('page_overlay');
  Element get pageOverlay => _pageOverlay;

  late final LibraryPanel? _libraryPanel;
  Element _libraryPanelButton = HTMLDivElement()..classList.add('library_panel_button_placeholder');

  late final AddPageElement _addPageElement;

  final Element _unreadStatus;
  late final CommentNavigation _commentNavigation;

  late final PresenceActivity presenceActivity;
  String? _userActivityState;

  final Row _appBarCenter = Row();
  final Map<DrawerType, Drawer> _drawers = {};

  HTMLElement? _topRightButtonsContainer;

  // Top right buttons
  ShareButton? _shareButton;
  ShareButton get shareButton => _shareButton ??= ShareButton(this);

  ThemeButton? _themeButton;
  bool get isThemeEditorOpen => _themeButton?.isThemeEditorOpen ?? false;
  PageSettingsButton? _pageSettingsButton;
  PageSettingsBox? get pageSettingsBox => _pageSettingsButton?.pageSettingsBox;

  Element? _staleStatus;
  Element? _severeStatus;

  MultiplayerDebugger? _multiplayerDebugger;
  MultiplayerDebugger? get multiplayerDebugger => _multiplayerDebugger;

  ModelActivityChannel? _modelActivityChannel;
  PresenceStateChannel? _presenceStateChannel;
  PresenceActivityChannel? _presenceActivityChannel;
  final List<Channel> _pageChannels = [];
  final List<PresenceUserInfo> users = [];

  final List<Future> _channelPendingUnsubscribes = [];

  @override
  Ai get ai => _page?.ai ?? Ai();

  ExplicitPolicy _maxPagePolicy = ExplicitPolicy.all;
  Policy get maxPagePolicy => _maxPagePolicy;

  String? get currentPageId => _page?.id;
  @override
  PageModel? get currentPageModel => _page?.model;

  num get leftPanelHeight => _libraryPanel?.root.clientHeight ?? 0;
  LayoutMode get desiredPageLayoutMode => (_libraryPanel?.isShowing ?? false) ? LayoutMode.slim : LayoutMode.normal;

  bool _featureEnabledPageHeaderButtons = true;
  bool _featureEnabledLibrary = true;
  bool _featureEnabledAddPage = true;

  static set showLibrary(bool? value) {
    BrowserStorage().localStorage.set('show_library', value.toString());
  }

  static bool? get showLibrary =>
      BrowserStorage().localStorage.get('show_library') == null ? null : BrowserStorage().localStorage.get('show_library') == 'true';

  @override
  PageViewPrivateer get createPrivateer => PageViewPrivateer._(this);

  bool get isBusy => _navigationCompleter != null && _navigationCompleter!.isCompleted == false;

  static bool _showLibraryPanelByDefault(ViewHost host) {
    if (!host.context.auth.loggedIn) return false;
    if (showLibrary == false) return false;
    return true;
  }

  PageView(ViewHost host) : _unreadStatus = HTMLDivElement()..classList.add('unread_status'), super(host, AppRootState.page) {
    _log.prefix = host.context.name;
    // Display the loader while we are loading the page
    _setLoader('constructor');

    // Note: we don't want to load the entire library panel if we are in CLI mode (PDF export)
    _libraryPanel = context.isCli ? null : LibraryPanel(this, showByDefault: _showLibraryPanelByDefault(host));
    _addPageElement = AddPageElement(this)..toggle(visible: false); // login will show it
    _commentNavigation = CommentNavigation(this);
    presenceActivity = PresenceActivity(host.context, _page?.cachedPolicy?.canEdit ?? false);

    _initDebugger();

    autoUnsubscribe(host.context.auth.loggedInChanged.listen(_onLoggedInChanged));

    autoUnsubscribe(
      _libraryPanel?.onIsShowingChanged.listen((b) {
        showLibrary = b;
        _page?.refreshLayoutMode();
      }),
    );

    applyMaxPolicy(Page.computeMaxDevicePolicy(this));
  }

  void _initDebugger() {
    if (PageModel.isMultiplayerDebuggerEnabled()) {
      _multiplayerDebugger = MultiplayerDebugger();
    }
  }

  //--------------
  // Entry / Exit
  //--------------

  @override
  Future<bool> onNewNavigationData(ParametersMap navigationData) async {
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView.onNewNavigationData.start');
    _log.info(() => 'onNewNavigationData: $navigationData _navigationCompleter: $_navigationCompleter');

    if (!await super.onNewNavigationData(navigationData)) {
      _log.info(() => 'onNewNavigationData early return');
      return false;
    }

    if (_navigationCompleter != null) {
      _log.info(() => 'onNewNavigationData _navigationCompleter is not null $_navigationCompleter');
      return false;
    }
    _navigationCompleter = Completer();

    if (showLibrary == true && context.auth.loggedIn && _featureEnabledLibrary) {
      toggleLibrary(forceTo: true);
    } else if (showLibrary == false || !context.auth.loggedIn || !_featureEnabledLibrary) {
      toggleLibrary(forceTo: false);
    }

    // Update the page if necessary
    final newPageId = navigationData.getString('page_key');
    if (newPageId != null && _page?.id != newPageId) {
      _log.info(() => 'onNewNavigationData: new page id: $newPageId vs existing page id: ${_page?.id}');

      if (_enteringPageCompleter != null && !_enteringPageCompleter!.isCompleted) {
        _log.info(() => 'onNewNavigationData: waiting for _enteringPageCompleter');
        await _enteringPageCompleter!.future;
      }

      // Re-check as the page might have changed while we were waiting
      if (_page?.id != newPageId) {
        // exiting old page
        await _clearPage();
        // entering new page
        await _enteringPage(navigationData);
      }
    }

    _page?.onNewNavigationData(navigationData);

    _log.info(() => 'onNewNavigationData Done');
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView.onNewNavigationData.end');

    _navigationCompleter!.tryComplete();
    _navigationCompleter = null;
    return true;
  }

  void _initActivePageSubscriptions() {
    _pageSubscriptions.add(Environment.onKeyDown.listen(_onKeyDown));

    _pageSubscriptions.add(Environment.onFocus.listen((_) => _checkStale()));

    _pageSubscriptions.add(host.onUncaughtError.listen(_reportSevereError));

    // Try to fast forward saves upon closing the tab
    Environment.onBeforeUnload.listen((e) async => await _page?.onExit());
  }

  void _cancelAllPageSubscription() {
    for (final subscription in _pageSubscriptions) {
      subscription.cancel();
    }
    _pageSubscriptions.clear();
  }

  @override
  Future preloadData(Map<String, String?> parameters) async {
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView.preloadData.start', data: {'parameters': parameters});

    _isNewPage = false;
    if (parameters.getString('is_new_page') == 'true') {
      _isNewPage = true;
    }

    final pageKey = parameters.getString('page_key');
    if (pageKey == null || pageKey.isEmpty) return;

    _log.info(() => 'preload page model: $parameters');
    try {
      final pageId = PageId(pageKey);

      // Preload the user context (it will be saved in the cache and retrieved later)
      Future.microtask(() async {
        try {
          await PageResource(context).getUserContext(pageId);
        } catch (e) {
          _log.warning(() => 'error while preloading user context: $e');
        }
      });

      // Preload the page sharing settings (it will be saved in the cache and retrieved later)
      Future.microtask(() async {
        try {
          await PageAccess.preloadModels(context, pageId);
        } catch (e) {
          _log.warning(() => 'error while preloading page sharing settings: $e');
        }
      });

      // Preload the page model
      _preloadedPageModelCompleter = Completer();
      _preloadedPageModel = await loadPageModel(pageKey, handleErrors: false);
    } catch (e) {
      _log.severe(() => 'error while preloading page model: $e');
    } finally {
      _preloadedPageModelCompleter?.tryComplete();
      PerfLogger().track(PerfLoggerGroup.page_load, 'PageView.preloadData.end');
    }
  }

  @override
  Future entering(ParametersMap parameters) async {
    _log.info(() => 'start entering with ${parameters} page `${_page?.id}` (${_page?.hashCode})');

    // This need to be called here because, this call is skipped when the pageView is not the current view
    _updateUIBasedOnLoggedInStatus(host.context.auth.loggedIn);

    host.appbar.center.appendChild(_appBarCenter.root);

    host.appbar.right.appendNotNull(_pageSettingsButton?.root);
    host.appbar.right.appendNotNull(_themeButton?.root);

    final url = Environment.window.location.href;

    // Parse the URL
    final uri = Uri.parse(url);

    // Get the query parameters
    final queryParams = uri.queryParameters;

    parameters = {...queryParams, ...parameters};

    await _enteringPage(parameters);
    await super.entering(parameters);

    _log.fine(() => 'done entering with ${parameters} page `${_page?.id}` (${_page?.hashCode})');
  }

  static int _enteringCounter = 0;
  static int _clearingCounter = 0;

  Future _enteringPage(ParametersMap parameters) async {
    _enteringPageCompleter = Completer();

    final enteringId = _enteringCounter++;
    _log.info(() => '[enteringPage] [$enteringId] start loading page `${_page?.id}` (${_page?.hashCode}) parameters : ${parameters}');
    PerfLogger().track(PerfLoggerGroup.auth, 'PageView._enteringPage.start', data: {'pageId': _page?.id, 'parameters': parameters});

    PageModel? pageModel;

    // Try to load the page from the preloaded data
    if (_preloadedPageModelCompleter != null && !_preloadedPageModelCompleter!.isCompleted) {
      _log.info(() => '[enteringPage] [$enteringId] waiting for _preloadedPageModelCompleter');
      await _preloadedPageModelCompleter!.future;
    }
    if (_preloadedPageModel != null) {
      _log.info(() => '[enteringPage] [$enteringId] using preloaded page model (${_preloadedPageModel?.pageId.id})');
      pageModel = _preloadedPageModel;
      _preloadedPageModel?.release();
      _preloadedPageModel = null;
    }

    // Try to load the specified page
    //-------------------------------
    final pageKey = parameters.getString('page_key');
    // Make sure the page key is long enough (and not /page/create)
    if (pageKey != null && pageKey.length >= PageModel.getPageIdMinLength(context)) {
      _log.info(() => '[enteringPage] [$enteringId] loading page model from pageKey: $pageKey');
      pageModel ??= await loadPageModel(pageKey);

      if (pageModel == null) {
        _log.warning(() => '[enteringPage] [$enteringId] page model not found for pageKey: $pageKey');
      }
    } else if (pageKey != null && pageKey.length < PageModel.getPageIdMinLength(context)) {
      _log.warning(() => '[enteringPage] [$enteringId] pageKey is too short: $pageKey');
    }

    pageModel ??= await _handleOnboardingTutorial();

    // Else load any recent page
    //------------------------
    pageModel ??= await PageLoaderUtils.loadDefaultPageModel(host.context, LibraryCollectionType.authoredByMe);
    pageModel ??= await PageLoaderUtils.loadDefaultPageModel(host.context, LibraryCollectionType.teamSpace);
    pageModel ??= await PageLoaderUtils.loadDefaultPageModel(host.context, LibraryCollectionType.allRecent);

    // Last resort: create an empty page
    //----------------------------------
    if (pageModel == null) {
      final auth = host.context.auth;
      if ((_libraryPanel?.supportPageCreation ?? false) && auth.loggedIn) {
        host.goToCreatePage('PageView._enteringPage');
      } else {
        _libraryPanel?.libraryIsEmpty();
      }
    }

    if (pageModel == null) return;

    final page = Page.fromModel(context, this, pageModel);
    _pageSubscriptions.add(page.onPolicyChanged.listen(_applyPolicy));

    _installPage(page, parameters);
    _installPageComponents(_page, page);

    _initActivePageSubscriptions();

    if (_pageChannels.isNotEmpty) {
      _log.warning(
        () => '[enteringPage] [$enteringId] PageView._pageChannels not empty: ${_pageChannels.length} channels (${_pageChannels.map((e) => e.topic)})',
      );
    }

    assert(_pageChannels.isEmpty, 'PageView._pageChannels should be empty. race condition?');

    await Future.microtask(() async {
      PerfLogger().track(PerfLoggerGroup.page_load, 'PageView._enteringPage.subscribing.start');

      if (!context.isCli) {
        await _unsubscribeChannels('_enteringPage');
        _presenceStateChannel = PresenceStateChannel(context, pageId: page.resourceId, state: {});
        _modelActivityChannel = ModelActivityChannel(context, page.resourceId, onUpdate: _onModelActivityUpdate, allowedKinds: ['Page']);
        _presenceActivityChannel = PresenceActivityChannel(context, page.resourceId, onUpdate: _onPresenceUpdate);
        _pageChannels.addAll([_presenceStateChannel!, _modelActivityChannel!, _presenceActivityChannel!]);
      }

      // Subscribing to all page channels
      for (final channel in _pageChannels) {
        // Don't wait for the subscription to complete (as these channels are not required to load the page)
        channel
            .subscribeChannel(source: '_enteringPage')
            .onError((error, stackTrace) => _log.warning(() => 'error while subscribing to channel ${channel.topic}: $error'));
      }

      PerfLogger().track(PerfLoggerGroup.page_load, 'PageView._enteringPage.subscribing.end');
    });

    // Initialize presence state
    _presenceStateChannel?.setUserActivityState(_userActivityState!);

    await _checkStale(); // maybe we loaded the page from cache
    await super.entering(parameters);
    _enteringPageCompleter?.tryComplete();
    _enteringPageCompleter = null;

    _triggerAnalyticsEvents(pageModel);

    _log.fine(() => '[enteringPage] [$enteringId] loaded page `${_page?.id}` (${_page?.hashCode})');
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView._enteringPage.end');
  }

  void _triggerAnalyticsEvents(PageModel pageModel) async {
    // Await until we know if the user is logged in or not
    final isLoggedIn = await host.checkUserLoginCompleter.future;
    if (isLoggedIn == false || (pageModel.owner != null && pageModel.owner?.email != context.auth.user?.email)) {
      // Current user is viewing someones else page
      Analytics().trackEvents(
        AnalyticsEvents(
          monitoring: AnalyticsEvent('DOCUMENT/OPEN_OTHER_USER_DOCUMENT'),
          tagManager: AnalyticsEvent('napkin_document_open_other_user_document'),
        ),
      );
    }
  }

  Future<Policy> _computeMaxExternalPolicy() async {
    // If the user did not complete the tutorial, the user can't edit any pages except the tutorial
    final (todo, doing, _) = await _getTutorials();
    if (todo.isNotEmpty || doing.isNotEmpty) {
      return ExplicitPolicy(canView: true, canComment: true);
    } else {
      return ExplicitPolicy.all;
    }
  }

  Future<(List<String> todo, List<String> doing, List<String> done)> _getTutorials() async {
    _log.debug(() => '[_getTutorials] Start');

    if (_page?.isTutorialFinished == true) {
      _log.debug(() => '[_getTutorials] Tutorial already finished');
      return (<String>[], <String>[], <String>[]);
    }

    final tutorialStore = context.userConfig.getUserStore('tutorials');

    final results = await Future.wait([tutorialStore.load<List>('todo').$1, tutorialStore.load<List>('doing').$1, tutorialStore.load<List>('done').$1]);
    final todo = List<String>.from(results[0] ?? []);
    final doing = List<String>.from(results[1] ?? []);
    final done = List<String>.from(results[2] ?? [])..removeDuplicates(); // user data accumulated a lot of duplicates in the past, this is to clean it up

    _log.debug(() => '[_getTutorials] Done: todo: $todo, doing: $doing, done: $done');
    return (todo, doing, done);
  }

  Future _storeTutorials(List<String> todo, List<String> doing, List<String> done) async {
    final tutorialStore = context.userConfig.getUserStore('tutorials');
    await Future.wait([tutorialStore.store('todo', todo), tutorialStore.store('doing', doing), tutorialStore.store('done', done)]);
  }

  static Future<String> resetUserTutorial(ViewHost host) async {
    final tutorialNameFlag = Flags().variation('onboarding-tutorial-name', 'onboarding_1');
    final tutorialStore = host.context.userConfig.getUserStore('tutorials');
    await Future.wait([
      tutorialStore.store('todo', [if (tutorialNameFlag.isNotEmpty) tutorialNameFlag]),
      tutorialStore.store('doing', []),
      tutorialStore.store('done', []),
    ]);
    return tutorialNameFlag;
  }

  Future onTutorialFinished(String name) async {
    final (todo, doing, done) = await _getTutorials();
    todo.remove(name);
    doing.remove(name);
    done.addIfAbsent(name);
    await _storeTutorials(todo, doing, done);

    // invalidate everything based on the tutorial
    _cachedMaxExternalPolicy = null;

    _updateLibraryUI();
    _updateAddPageUI();
    _updatePageHeaderButtons();
  }

  Future<PageModel?> _handleOnboardingTutorial({bool navigate = false}) async {
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView._handleOnboardingTutorial.start');

    _log.info(() => '[handleOnboardingTutorial] Start');

    final auth = context.auth;
    if (!auth.loggedIn) {
      _log.warning(() => '[handleOnboardingTutorial] User not logged in: waiting for login');
      await auth.initialCheckFuture;
    }
    if (!auth.loggedIn) {
      _log.warning(() => '[handleOnboardingTutorial] User not logged in: aborting');
      return null;
    }
    _log.info(() => '[handleOnboardingTutorial] User logged in: continue');

    final user = auth.user;
    final isNewUser = user?.isNewUser;

    if (isNewUser == true) {
      // Fill user data if new user and flag is on

      final tutorialStore = context.userConfig.getUserStore('tutorials');
      final (futureTodo, _) = tutorialStore.load<List>('todo');
      final todo = await futureTodo;
      _log.info(() => '[handleOnboardingTutorial]] User is new: todo: $todo');

      if (todo == null) {
        // Setup tutorial request in user store only if it has never been done before
        final tutorialNameFlag = await resetUserTutorial(host);

        Analytics().trackMonitoringEvent('TUTORIAL/ENABLE', {
          'user': '${user?.email}',
          'tutorials': [tutorialNameFlag],
        });
      }
    }

    // Check user data and create tutorial page if necessary
    final (todo, doing, done) = await _getTutorials();
    _log.info(() => '[handleOnboardingTutorial] Tutorials have been initialized (isNewUser: $isNewUser): todo: $todo, doing: $doing, done: $done');

    if (navigate && todo.isEmpty && doing.isEmpty && done.isEmpty) {
      _log.info(() => '[handleOnboardingTutorial] No tutorial to do or in progress');

      await host.resetTutorial('_getTutorials', forceReload: true);
      return null;
    }

    if (todo.isNotEmpty || doing.isNotEmpty) {
      toggleLibrary(forceTo: false);
    }

    PageModel? pageModel;

    if (doing.isNotEmpty) {
      _log.info(() => '[handleOnboardingTutorial] Found tutorial in progress: $doing');

      //TODO figure out a better way to find the tutorial page than crossing our fingers hoping the last opened page is the tutorial
      pageModel = await PageLoaderUtils.loadDefaultPageModel(host.context, LibraryCollectionType.authoredByMe);
      if (pageModel == null) {
        // The user has no page open yet
        _log.warning(() => '[handleOnboardingTutorial] Found page is null');
      }

      final pageWorkflowName = pageModel?.modelHeader.workflowName ?? currentPageModel?.modelHeader.workflowName;
      _log.info(
        () => '[handleOnboardingTutorial] Found page workflow: $pageWorkflowName (pageModel: ${pageModel?.id} currentPageModel: ${currentPageModel?.id})',
      );

      if (pageWorkflowName == null || !doing.contains(pageWorkflowName)) {
        _log.warning(
          () => '[handleOnboardingTutorial] Found page is not the tutorial we were looking for `$doing` vs `${pageWorkflowName}` -> creating a new page',
        );
        pageModel = await host.createPage(context, navigateToPage: navigate, workflow: doing.first);
      } else if (pageModel != null && pageModel.id != currentPageModel?.id) {
        _log.warning(() => '[handleOnboardingTutorial] No tutorial to do or in progress but page is not the tutorial page (navigate: $navigate)');

        if (navigate) {
          // Go to the tutorial page
          _log.info(() => '[handleOnboardingTutorial] Navigating to the tutorial page');
          await host.goToPage(pageModel.pageId, forceReload: true);
        }
      }
    } else if (todo.isNotEmpty) {
      _log.info(() => '[handleOnboardingTutorial] Found tutorial to do: $todo');

      final tutorialName = todo.removeAt(0);
      doing.add(tutorialName);
      pageModel = await host.createPage(context, navigateToPage: navigate, workflow: tutorialName);

      // Keep the 2 await separate to make sure we don't store the tutorial as done if the page creation failed
      await _storeTutorials(todo, doing, done);
    } else {
      _log.info(() => '[handleOnboardingTutorial] No tutorial to do or in progress and page is the tutorial page: edge case?');
    }

    // debug
    _getTutorials().then((result) => _log.info(() => '[handleOnboardingTutorial] Debug => todo:${result.$1} doing:${result.$2} done:${result.$3}'));

    host.fastForwardUserSave();

    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView._handleOnboardingTutorial.end');

    _log.info(() => '[handleOnboardingTutorial] Done');

    return pageModel;
  }

  void _onModelActivityUpdate(ModelActivityState state) async {
    _log.info(
      () =>
          'got model update for ${state.kind} - ${state.id} at revision ${state.revision} (current model: ${_page?.model.revision}, working: ${_page?.workingRevision})',
    );

    final page = _page;

    if (page == null) return;

    // forcing a refresh of presence
    _presenceActivityChannel?.refresh();

    // The code below is commented out because it's not used in multiplayer mode

    // if (state.id == page.id) {
    //   assert(page.model.revision == page.workingRevision, 'revision should be in sync: model: ${page.model.revision}, working: ${page.workingRevision}');

    //   final knownRevisions = {page.workingRevision};
    //   await _page?.saver?.savingFuture;
    //   knownRevisions.add(page.workingRevision);
    //   final newRevision = state.revision;

    //   // get a list of all revisions that are newer than our new revisions
    //   final revisions = knownRevisions.where((element) => ModelRevision.compareTo(element, newRevision) >= 0);

    //   // do nothing if we already have a newer revision. There is an edge case
    //   // during an int over flow but that just results in a user refresh :-/
    //   if (revisions.isNotEmpty) {
    //     return;
    //   }
    //   _log.warning(() => 'new revision [${newRevision}] old revision [${knownRevisions.toString()}]');
    //   markPageIsStale('_onModelActivityUpdate');
    // }
  }

  void _onPresenceUpdate(PresenceResource presence, String? pageId) {
    _log.info(
      () =>
          '[_onPresenceUpdate] ${presence.userCount} users and ${presence.connectionCount} connections (from page=$pageId topic=${_presenceActivityChannel?.topic})',
    );

    if (pageId != _page?.id) {
      _log.info(() => '[_onPresenceUpdate] ignoring presence update for page=$pageId (current page=${_page?.id})');
      return;
    }

    final currentUsers = users.map((e) => e.user).toList();
    final newUsers = presence.users.map((e) => e.user).toList();

    for (final element in newUsers) {
      if (currentUsers.contains(element)) continue;
      _log.info(
        () => '[_onPresenceUpdate] ${element.email} joined the napkin as ${element.connectionID} (from page=$pageId topic=${_presenceActivityChannel?.topic})',
      );
    }

    for (final element in currentUsers) {
      if (!newUsers.contains(element)) {
        _log.info(
          () => '[_onPresenceUpdate] ${element.email} left the napkin as ${element.connectionID} (from page=$pageId topic=${_presenceActivityChannel?.topic})',
        );
      }
    }
    users.clearAddAll(presence.users);

    presenceActivity.setUsers(presence.users, isCli: context.isCli);

    _page?.onPresenceUpdate(presence.users);
  }

  void _applyPolicy(Policy policy) {
    _log.info(() => '_applyPolicy page `${_page?.id}` (${_page?.hashCode})');

    host.root.classList.toggle('can_edit', policy.canEdit);
    host.root.classList.toggle('can_share', policy.canShare);
    host.root.classList.toggle('can_comment', policy.canComment);

    if (_page != null) _libraryPanel?.markSelected(_page!.id, editable: policy.canEdit);

    _shareButton?.applyPolicy(policy);
    _themeButton?.applyPolicy(policy);
    _pageSettingsButton?.applyPolicy(policy);

    presenceActivity.setPolicy(policy);
    _presenceStateChannel?.setPolicy(policy);

    _appBarCenter.clear();
    if (policy.canEdit) {
      _buildCanEditAppBar();
    } else if (policy.canComment) {
      _buildCanCommentAppBar();
    } else if (policy.canView) {
      _buildCanViewAppBar();
    }
    host.appbar.right.appendChild(_appBarCenter.root);

    _updateUnreadCommentStatus();
  }

  // the policy is the max a page policy can get with the current host.context
  void applyMaxPolicy(Policy maxPolicy) {
    _addPageElement.applyMaxPagePolicy(maxPolicy);
  }

  void _buildCanEditAppBar() {
    // right
    _appBarCenter.right.appendChild(_unreadStatus);

    // left
    _appBarCenter.left.appendChild(presenceActivity.root);
  }

  void _buildCanCommentAppBar() {
    // right
    _appBarCenter.right.appendChild(_unreadStatus);

    // left
    _appBarCenter.left.appendChild(presenceActivity.root);
  }

  void _buildCanViewAppBar() {
    // left
    _appBarCenter.left.appendChild(presenceActivity.root);
  }

  void _setLoader(String source) {
    _log.info(() => 'setting loader from $source');
    _pageHolder.clear();
    _pageHolder.appendChild(
      HTMLDivElement()
        ..classList.add('pending_holder')
        ..setDataAttribute('source', 'page_view_${source}')
        ..appendChild(Loader().root),
    );
  }

  Future<void> _unsubscribeChannels(String source) async {
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView.unsubscribe.start');
    _log.info(() => 'unsubscribing from channels from ${source} ($_pageChannels channels)...');

    final channelsToUnsubscribe = List.of(_pageChannels);
    _pageChannels.clear();

    try {
      helper.retryAction(() async {
        return await Future.wait(
          channelsToUnsubscribe.map((channel) async {
            if (channel.subscribed) {
              await channel.unsubscribeChannel(source: source).timeout(const Duration(seconds: 15));
            }
            channel.release();
          }),
        );
      });
    } catch (e) {
      _log.severe(() => 'error while unsubscribing from channels: $e');
      // Refresh the page to solve the issue (stuck websocket)
      browser.reload(source: '_unsubscribeChannels', reason: 'error while unsubscribing from channels: $e');
    }

    _log.info(() => 'unsubscribing from channels from ${source} ($_pageChannels channels)... Done');
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView.unsubscribe.end');
  }

  @override
  Future exiting() async {
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView.exiting.start');
    _log.info(() => 'exiting: isCompleted:${_navigationCompleter?.isCompleted}');

    await _navigationCompleter?.future;
    _navigationCompleter?.tryComplete();
    _navigationCompleter = null;

    _log.fine(() => 'start exiting page `${_page?.id}` (${_page?.hashCode})');

    await _clearPage();

    _topRightButtonsContainer?.clear();
    _topRightButtonsContainer?.remove();

    _appBarCenter.root.remove();
    _libraryPanelButton.remove();
    _addPageElement.removeRoot();
    _shareButton?.removeRoot();
    _themeButton?.removeRoot();
    _pageSettingsButton?.removeRoot();

    _commentNavigation.removeRoot();
    presenceActivity.removeRoot();
    _unreadStatus.remove();
    _staleStatus?.remove();

    cancelAllSubscriptions();

    await pendingChannelUnsubscribes();

    _log.fine(() => 'start exiting page `${_page?.id}` (${_page?.hashCode})');
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView.exiting.end');
  }

  Future _clearPage() async {
    final clearingId = _clearingCounter++;

    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView._clearPage.start[$clearingId]');
    _log.info(() => 'Clearing page [$clearingId]: Start');

    var showDialog = true;
    // We use a Ref because the value will be set in a future
    final waitDialog = Ref<Dialog?>(null);

    Future process(int clearingId) async {
      PerfLogger().track(PerfLoggerGroup.page_load, 'PageView.process.start[$clearingId]');
      _log.info(() => 'start _clearPage[$clearingId] page `${_page?.id}` (${_page?.hashCode})');

      await _page?.onExit();

      // Unsubscribing from all page channels
      // Don't await for the result as we are exiting the page (it saves time and resources)
      final unsubscribeFuture = _unsubscribeChannels('_clearPage[$clearingId]');
      // keep the future around, we'll wait on it when exiting the pageView
      _channelPendingUnsubscribes.add(unsubscribeFuture);
      unsubscribeFuture.then((_) => _channelPendingUnsubscribes.remove(unsubscribeFuture));

      try {
        helper.onlyOneReset('block_focus_${host.hashCode}');
        helper.onlyOneReset('comment_focus_${host.hashCode}');

        _page = null;
        _clearStale();
        _appBarCenter.clear();
        _setLoader('process');
        _commentNavigation.reset();
        presenceActivity.reset();

        host.updateBrowserTabTitle('');

        _cancelAllPageSubscription();
      } catch (e) {
        _log.warning(() => 'Clearing page: caught exception: $e');
        _page = null; // make sure we don't keep a reference to the page
      }

      showDialog = false;
      _log.info(() => 'Clearing page [$clearingId]: Set showDialog to false');

      _log.info(() => 'end _clearPage[$clearingId] Done');
      PerfLogger().track(PerfLoggerGroup.page_load, 'PageView.process.end[$clearingId]');
    }

    // Display a dialog if the process takes too long
    final dialogDisplayTimer = helper.delayedCall(1, () {
      if (!showDialog) {
        _log.info(() => 'Finishing saving popup [$clearingId]: exit before showing dialog');
        return;
      }
      waitDialog.value = Dialog.wait(
        textContent: _page?.model.deleted == true || _page?.cachedPolicy?.canEdit != null ? 'Finishing...' : 'Finishing saving...',
      );
      _log.info(() => 'Finishing saving popup [$clearingId]: Displayed dialog');
    });

    try {
      await process(clearingId);
    } catch (e) {
      _log.warning(() => 'Clearing page [$clearingId]: caught exception: $e');
    } finally {
      dialogDisplayTimer?.cancel();
      waitDialog.value?.close();
      if (waitDialog.value != null) {
        _log.info(() => 'Clearing page [$clearingId]: Closed dialog');
      } else {
        _log.info(() => 'Clearing page [$clearingId]: dialog was not shown');
      }
    }

    _log.info(() => 'Clearing page [$clearingId]: Stop');
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView._clearPage.end[$clearingId]');
  }

  Future pendingChannelUnsubscribes() async {
    // we make a copy as the list can be modified while we are waiting
    final futureCopies = List.of(_channelPendingUnsubscribes);
    await Future.wait(futureCopies);
  }

  void _installPage(Page page, Map parameters) {
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView._installPage.start');
    _log.info(() => 'installing page `${page.id}` (${page.hashCode})');

    validateRoot();

    host.updateBrowserLocation(additionalParameters: {'page_key': page.id});
    host.updateBrowserTabTitle('${page.model.title} - Napkin AI');

    // Update document title when the page title changes
    _pageSubscriptions.add(page.model.onTitleUpdate.listen((String newTitle) => host.updateBrowserTabTitle('${newTitle} - Napkin AI')));

    _page = page;

    _pageHolder.clear();
    _pageHolder.appendChild(page.root);

    _pageHolder.classList.remove('hidden');
    _pageOverlay.classList.remove('hidden');

    _commentNavigation.setPage(page);

    page.navigate(parameters);

    _libraryPanel?.markSelected(page.id, editable: page.cachedPolicy?.canEdit ?? false);

    final autoHide = (_libraryPanel?.overlapping ?? false) && parameters.containsKey('page_key') && showLibrary != true;
    if (autoHide) toggleLibrary(forceTo: false);

    _updateUnreadCommentStatus();

    // Wait for the orchestrator to be ready before updating the page header buttons
    helper.delayedCall(1, _updatePageHeaderButtons, channel: 'updatePageHeaderButtons');

    unlinkWindowFunction('getModel');
    linkWindowFunction('getModel', page.printModel.toJS);

    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView._installPage.end');
  }

  void _installPageComponents(SaveNotifier? saveNotifier, Page page) async {
    _pageSubscriptions.addNotNull(
      shareButton.onSave.listen((isSave) {
        isSave ? saveNotifier?.startSaving(SaveSource.page_share_saving.name) : saveNotifier?.doneSaving(SaveSource.page_share_saving.name);
      }),
    );
    _pageSubscriptions.addNotNull(
      shareButton.onError.listen((isError) {
        if (isError) saveNotifier?.errorSaving(SaveSource.page_share_saving.name);
      }),
    );

    await shareButton.install(page);
    _themeButton?.install(page.cachedPolicy ?? ExplicitPolicy.none);
    _pageSettingsButton?.install(page);
  }

  Future _checkStale() async {
    // Already stale nothing to check
    if (_pageIsStale || _page == null) return;
    var page = _page!;

    // Only check if the user can edit the page (otherwise users will get updates from websockets)
    if (page.cachedPolicy?.canEdit != true) return;

    if (page.isContentUsingWSOnly() == true) {
      _log.fine(() => 'Page `${page.id}` is using websockets. No need to check for staleness.');
      return;
    }

    // The page was loaded complete but some loading loaded it afterwards and made it incomplete
    // this would only happen if the loaded page was newer than the current page
    if (page.model.incomplete) {
      markPageIsStale('working with an incomplete page model');
      return;
    }

    // Header check
    final pageIsStale = await page.resource.isStale(page.model);
    if (page != _page) {
      _log.warning(() => 'Page `${_page?.id}` has changed while checking if it is up to date');
      return;
    }
    page = _page!; // reload the page as it might have changed while we were waiting

    if (pageIsStale == true) {
      _log.info(() => 'Page `${page.id}` is stale. (model: ${page.model.revision}, workingRevision: ${page.workingRevision})');
      markPageIsStale('failed header check');
    } else if (pageIsStale == false) {
      _log.info(() => 'Page `${page.id}` is up to date. (model: ${page.model.revision}, workingRevision: ${page.workingRevision})');
    } else {
      _log.warning(() => 'failed to check if page `${page.id}` is up to date');
    }
  }

  void markPageIsStale(String source) {
    _pageIsStale = true;
    _log.warning(() => 'markPageIsStale from $source');
    _showAsStale();

    Analytics().trackMonitoringEvent('DOCUMENT/STALE', {'source': source});
  }

  void _showAsStale({ReadOnlyReason? reason}) {
    assert(_pageIsStale);

    _log.warning(() => '[showAsStale] Page ${_page?.id} is stale: $reason');

    // turn the page to read only
    final previouslyCanEdit = _page?.cachedPolicy?.canEdit;
    _maxPagePolicy = ExplicitPolicy.viewOnly;
    _page?.reloadPolicy(tryUseCached: true, reason: 'showAsStale');

    // display a nice message
    _staleStatus ??= HTMLDivElement()
      ..classList.add('stale_status')
      ..appendChild(
        _staleMessage(
          reason: reason,
          couldEdit: previouslyCanEdit ?? false,
          short: false,
          selfUpdated: _page?.model.rawUpdatedBy == host.context.auth.currentOrVisitorToken.email,
        ),
      )
      ..onClick.first.then(
        (_) => _staleStatus?.clearAppend(_staleMessage(short: true, selfUpdated: _page?.model.rawUpdatedBy == host.context.auth.currentOrVisitorToken.email)),
      );

    host.root.appendChild(_staleStatus!);
  }

  // Clear the stale status (should be called when we switch to another page)
  void _clearStale() {
    _log.info(() => 'PageView._clearStale()');

    _pageIsStale = false;
    _page?.reloadPolicy(reason: 'clearStale');
    _maxPagePolicy = ExplicitPolicy.all;
    _staleStatus?.remove();
  }

  static Element _staleMessage({bool couldEdit = false, bool short = true, bool selfUpdated = false, ReadOnlyReason? reason}) {
    var messageElements = <Node>[];

    if (reason != null) {
      switch (reason) {
        case ReadOnlyReason.cannotUpgradeItems:
          messageElements.add(Text()..textContent = 'Another user is currently editing this document with a newer version of Napkin.');
          break;
      }
    } else {
      if (!short) {
        if (couldEdit) {
          if (selfUpdated) {
            messageElements.add(Text()..textContent = 'Looks like this Napkin was edited on another tab.');
          } else {
            messageElements.add(Text()..textContent = 'This Napkin was updated by a fellow Napkiner.');
          }
        } else {
          messageElements.addAll([Text()..textContent = '✨ This napkin has just been updated', HTMLBRElement()]);
        }
      }
    }

    final refreshElement = HTMLAnchorElement()
      ..classList.add('reload')
      ..textContent = 'Reload ${couldEdit ? 'to continue editing' : 'to see the most recent changes'}.'
      ..onClick.first.then((e) {
        e.stopPropagation(); // to not trigger the short message
        // reload the page
        browser.reload(source: '_staleMessage', reason: 'reload button');
      });

    return HTMLDivElement()
      ..classList.add('stale_message')
      ..appendAll(messageElements)
      ..appendChild(
        HTMLDivElement()
          ..classList.add('actions')
          ..appendChild(refreshElement),
      );
  }

  void switchToReadOnly(ReadOnlyReason reason) {
    // If the page was already read only, we don't need to do anything
    final previouslyCanEdit = _page?.cachedPolicy?.canEdit;
    if (previouslyCanEdit != true) return;

    Analytics().trackMonitoringEvent('DOCUMENT/READ_ONLY', {'reason': reason.toString()});

    _pageIsStale = true;
    _showAsStale(reason: reason);
  }

  Future<void> savePageFastForward() async {
    if (_page?.cachedPolicy?.canEdit == true) {
      await _page?.fastForwardAllSaves();
    }
  }

  void _reportSevereError(Object error) {
    // Make the document read only to prevent further editing
    final previouslyCanEdit = _page?.cachedPolicy?.canEdit;
    final previousMaxPagePolicy = _maxPagePolicy;
    _maxPagePolicy = ExplicitPolicy.viewOnly;
    _page?.reloadPolicy(tryUseCached: true, reason: 'reportSevereError $error');

    // display a nice message
    _severeStatus ??= HTMLDivElement()
      ..classList.add('severe_status')
      ..appendChild(
        _severeMessage(
          couldEdit: previouslyCanEdit ?? false,
          lastSuccessfulSave: _page?.saver?.lastSuccessfulSave,
          proceed: () {
            // Restore the previous page policy
            _maxPagePolicy = previousMaxPagePolicy;
            _page?.reloadPolicy(tryUseCached: true, reason: 'reportSevereError.button $error');
            _severeStatus?.remove();
          },
        ),
      );

    host.root.appendChild(_severeStatus!);
  }

  static Element _severeMessage({bool couldEdit = false, DateTime? lastSuccessfulSave, Callback? proceed}) {
    var messageElements = <Node>[Text()..textContent = '😬 Something went wrong 👆.'];
    if (couldEdit) messageElements.add(Text()..textContent = ' For your own safety, we made this napkin read-only 🔒.');

    final reloadRecommended = proceed == null ? null : (HTMLSpanElement()..appendAll([HTMLElement.b()..textContent = ' Recommended']));
    final reloadAgo = lastSuccessfulSave == null
        ? null
        : (HTMLSpanElement()..appendAll([
            HTMLBRElement(),
            Text()..textContent = '(Your last save was ${helper.niceDuration(DateTime.now().difference(lastSuccessfulSave), approx: true)} ago)',
          ]));

    final reloadElement = HTMLAnchorElement()
      ..classList.add('reload')
      ..textContent = 'Reload to continue${couldEdit ? ' editing' : ''}'
      ..onClick.first.then((_) => browser.reload(source: '_severeMessage', reason: 'reload button'))
      ..appendNotNull(reloadRecommended)
      ..appendNotNull(reloadAgo);

    final proceedElement = proceed == null
        ? null
        : (HTMLDivElement()
            ..classList.add('proceed')
            ..appendChild(
              HTMLAnchorElement()
                ..textContent = 'Proceed at your own risk 🙏'
                ..onClick.listen((_) => proceed.call()),
            ));

    final actions = HTMLDivElement()
      ..classList.add('actions')
      ..appendNotNull(proceedElement)
      ..appendNotNull(
        proceedElement != null
            ? (HTMLDivElement()
                ..appendAll([Environment.createNonBreakingSpaceElement(), HTMLElement.b()..textContent = ' or ', Environment.createNonBreakingSpaceElement()]))
            : null,
      )
      ..appendChild(reloadElement);

    return HTMLDivElement()
      ..classList.add('stale_message')
      ..appendAll(messageElements)
      ..appendChild(actions);
  }

  //--------
  // Events
  //--------

  void _onKeyDown(KeyboardEvent? e) {
    if (e == null || !e.isA<KeyboardEvent>()) return;

    // Theme editor has its own key events
    if (isThemeEditorOpen) return;

    _log.fine(() => 'key down during page view');
    if (e.metaKey || e.ctrlKey) {
      // We do not want to perform global undo when editing items
      if (e.keyCode == KeyCode.Z && !e.shiftKey) {
        e.preventDefault();
        final canUndo = _page?.graph?.toolsPolicy.canUndo ?? false;
        if (canUndo) _page?.popUndo();
      } else if (e.keyCode == KeyCode.S) {
        e.preventDefault();
        _page?.fastForwardAllSaves();
      }
    }
  }

  void onStateChanged(STATE state) {
    _userActivityState = state.name;

    _pageHolder.setAttribute('data-state', state.name);

    // Skip events if there is no other user
    if (presenceActivity.userCount == 0) return;

    _presenceStateChannel?.setUserActivityState(_userActivityState!);
  }

  //-------
  // Loading
  //-------

  Future<PageModel?> loadPageModel(String? pageKey, {bool handleErrors = true}) async {
    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView._loadPageModel.start');

    // Make sure the page key is long enough (and not /page/create)
    if (pageKey == null || pageKey.length < PageModel.getPageIdMinLength(context)) {
      if (pageKey != null) {
        _log.warning(() => 'loadPageModel: pageKey is too short: $pageKey');
      }
      return null;
    }

    try {
      final pageModel = await helper.retryAction<PageModel?>(
        () async {
          return await PageResource(host.context).getResource(pageKey);
        },
        log: _log,
        name: 'load page model',
      );

      if (pageModel != null) return pageModel;
      if (!handleErrors) return null;
      await Dialog.generic<bool>([_buildNoPageDialogContent()], classes: {'auth'}, onSuccess: true, onCancel: false);
    } on RequestInvalidAuthenticationException catch (e) {
      if (!handleErrors) return null;
      await handleInvalidAuthentication(context, e, 'PageView._loadPageModel');
    } on RequestForbiddenException catch (_) {
      if (!handleErrors) return null;
      _pageHolder.clear();
      final signOut = await displayCantAccessDialog(host.context);
      if (signOut) {
        host.signOut(pageKey: pageKey);
      }
    } on RequestException catch (e) {
      _log.severe(() => 'Failed to load page `$pageKey`');

      if (!handleErrors) return null;

      if (e.code == 500) {
        await Dialog.generic<bool>([_buildInternalErrorDialogContent()], classes: {'auth', 'deleted'}, onSuccess: true, onCancel: false);
        browser.reload(source: '_loadPageModel', reason: 'internal error while loading page', clearQueryParameters: true);
      }
    }

    PerfLogger().track(PerfLoggerGroup.page_load, 'PageView._loadPageModel.end');
    return null;
  }



  //-------
  // Dom
  //-------

  @override
  HTMLElement build() {
    return HTMLDivElement()
      ..classList.addAll(['page_view'])
      ..appendNotNull(_libraryPanel?.root)
      ..appendChild(
        HTMLDivElement()
          ..classList.add('page_holder_container')
          ..appendChild(_pageHolder),
      )
      ..appendChild(_pageOverlay)
      ..appendNotNull(_multiplayerDebugger?.root);
  }

  Future<bool> _tutorialNotDone() async {
    final (todo, doing, _) = await _getTutorials();
    return todo.isNotEmpty || doing.isNotEmpty;
  }

  Element _buildStartTutorialButton() {
    final button = HTMLDivElement()
      ..classList.addAll(['tutorial_view', 'button', 'icon'])
      ..textContent = 'Unlock the full Napkin Experience';
    autoUnsubscribe(
      button.onClick.listen((e) async {
        await _handleOnboardingTutorial(navigate: true);
      }),
    );
    return button;
  }

  Future _rebuildLibraryPanelButton() async {
    if (!_featureEnabledLibrary || !context.auth.loggedIn || _page?.hasActiveOnboarding == true) {
      _libraryPanelButton.remove();
      return;
    }

    final tutorialNotDone = await _tutorialNotDone();
    final libraryPanelButton = tutorialNotDone
        ? _buildStartTutorialButton()
        : LibraryPanel.buildLibraryPanelButton(toggleLibrary, autoUnsubscribe: autoUnsubscribe);

    if (_libraryPanelButton.parentElement == null) host.appbar.left.insertBefore(_libraryPanelButton, host.appbar.left.childNodes.tryFirst);
    _libraryPanelButton.replaceWith(libraryPanelButton);
    _libraryPanelButton = libraryPanelButton;
  }

  Future _rebuildAddPageButton() async {
    if (!_featureEnabledAddPage || !context.auth.loggedIn) {
      _addPageElement.removeRoot();
      _libraryPanel?.toggleAddPageButton(visible: false);
      return;
    }

    final tutorialStillTodo = await _tutorialNotDone();
    _addPageElement.toggle(visible: !tutorialStillTodo);
    _libraryPanel?.toggleAddPageButton(visible: !tutorialStillTodo);

    host.appbar.left.appendChild(_addPageElement.root);
  }

  //-------
  // Other
  //-------

  void enableLibraryFeature(bool enable) {
    _featureEnabledLibrary = enable;
    _updateLibraryUI();
  }

  void enableAddPageFeature(bool enable) {
    _featureEnabledAddPage = enable;
    _updateAddPageUI();
  }

  void enablePageHeaderButtons(bool enable) {
    _featureEnabledPageHeaderButtons = enable;
    _updatePageHeaderButtons();
  }

  void toggleLibrary({bool? forceTo}) {
    assert(forceTo != true || _featureEnabledLibrary, 'Library feature can\' be forced to true if it is not enabled');

    //Uncomment to get the library view next to the page
    final show = forceTo ?? !(_libraryPanel?.isShowing ?? false);
    if (show) {
      _libraryPanel?.open();
    } else {
      _libraryPanel?.close();
    }
  }

  Future<bool> enforceLoggedIn({required String? message}) async {
    return await host.enforceLoggedIn(message: message);
  }

  void _hideLibraryPanelButton() {
    _libraryPanelButton.classList.add('hidden');
  }

  void _onLoggedInChanged((bool, dynamic) data) {
    // invalidate caches
    _cachedMaxExternalPolicy = null;

    if (!isActive) return;
    final isLoggedIn = data.$1;
    _updateUIBasedOnLoggedInStatus(isLoggedIn);
  }

  void _updateUIBasedOnLoggedInStatus(bool loggedIn) {
    _updateLibraryUI();
    _updateAddPageUI();

    _updatePageHeaderButtons();

    _page?.onLoggedInChanged(loggedIn: loggedIn);

    host.appbar.toggleAuthElement(show: true);
  }

  //-----------------------
  // Unread Comments status
  //-----------------------

  void onCommentUnreadStatusChanged() {
    _updateUnreadCommentStatus();
  }

  void onCommentFocusChanged(comment_import.Comment? comment, {bool invalidate = false}) async {
    if (_page == null) return;

    if (invalidate == true) {
      _page!.countUnreadComments().then((unreadCount) => _commentNavigation.invalidateList(unreadCount: unreadCount));
    }
    _commentNavigation.currentId = comment?.id ?? '';
  }

  void _updateUnreadCommentStatus() async {
    if (_page == null) return;

    final unreadCount = await _page!.countUnreadComments();
    _commentNavigation.invalidateList(unreadCount: unreadCount);
    _log.fine(() => '_updateUnreadCommentStatus -> ${unreadCount}');
  }

  //--------------
  // Bottom Drawer
  //--------------

  bool isDrawerShown(DrawerType type) => _drawers.containsKey(type) || _drawers[type] == null;

  void hideDrawer(DrawerType type) async {
    _log.fine(() => 'Start hide drawer: $type');
    final drawer = _drawers[type];
    if (drawer == null) return;
    // transition out and cleanup
    _drawers.remove(type);
    await drawer.toggle(false);
    drawer.root.remove();
  }

  void showDrawer(DrawerType type, HTMLElement content) {
    _log.fine(() => 'Show drawer: $type');
    final drawer = _drawers.putIfAbsentTyped(type, () => Drawer())
      ..resetWith(content)
      ..toggle(true);
    _pageOverlay.appendChild(drawer.root..classList.addAll(['dom_capture_all', 'key_${type.className}']));
  }

  static Element _buildNoPageDialogContent() {
    final title = HTMLDivElement()
      ..classList.add('title')
      ..textContent = 'Where did you get that link? 🤔';

    final text = HTMLParagraphElement()
      ..appendAll([Text()..textContent = 'The napkin in the link does not exist.', HTMLBRElement(), Text()..textContent = '🤓 Nerds call that a 404.']);

    return HTMLDivElement()
      ..appendChild(title)
      ..appendChild(text);
  }

  static Element _buildInternalErrorDialogContent() {
    final title = HTMLDivElement()
      ..classList.add('title')
      ..textContent = 'Where did you get that link? 🤔';

    final text = HTMLParagraphElement()
      ..appendAll([
        Text()..textContent = 'It looks like something went wrong, the napkin is not available.',
        HTMLBRElement(),
        Text()..textContent = 'The document may have been deleted.',
      ]);

    return HTMLDivElement()
      ..appendChild(title)
      ..appendChild(text);
  }

  @override
  Stream<PageModel> get onPageCreated => host.onPageCreated;

  @override
  Saver? get userContextSaver => _page?.userContextSaver;

  @override
  Future<PageModel?> createPage({required bool navigateToPage, String? workflow, NapkinCreationData? creationData}) =>
      host.createPage(host.context, navigateToPage: navigateToPage, workflow: workflow, creationData: creationData);

  @override
  Future<void> goToCreatePage() {
    return host.goToCreatePage('PageView.goToCreatePage');
  }

  @override
  Future<PageModel?> goToPage(PageId? pageId, {required ParametersMap parameters}) async {
    _log.info(() => 'goToPage ${pageId?.id} $parameters');

    _addPageElement.close();

    if (_page?.id == pageId?.id) {
      _log.info(() => 'goToPage: current page is the same as the requested page ${pageId?.id}');
      closeLibrary();
      return _page?.model;
    }

    _setLoader('goToPage');

    final model = await host.goToPage(pageId, queryParameters: parameters);
    await Future.delayed(Duration.zero); // wait for event to propagate to the library view
    return model;
  }

  @override
  Future<void> onPageDuplicated(String pageId, String pageTitle) async {
    if (_page != null && _page!.id == pageId) {
      await _page!.dataHasBeenRefreshedCompleter.future;

      // Await for the tiptap editor to be loaded from the server data
      Future.delayed(const Duration(milliseconds: 200), () async {
        _page!.model.title = pageTitle;
        _page!.richContent?.updateTitleFromModel();
      });
    }
  }

  @override
  set uploadedFile(dynamic v) => host.uploadedFile;

  Future<Uint8List?> generatePdfExportFile(PdfExportParameters parameters, {bool printSVGLogs = false, bool printPDFLogs = false}) async {
    if (_page != null) {
      return await _page?.generatePdfExportFile(parameters, printSVGLogs: printSVGLogs, printPDFLogs: printPDFLogs);
    }
    return null;
  }

  Future? openThemeSelector({String? source}) => _themeButton?.openThemeSelector(source: source);
  Future? openThemeEditor({bool openThemeSelector = true, List<String>? colors, String? newThemeSource}) =>
      _themeButton?.openThemeEditor(openThemeSelector: openThemeSelector, colors: colors, newThemeSource: newThemeSource);

  @override
  void downloadAsPdf({String? id, String? title}) {
    final page = _page;
    final graph = _page?.graph;
    if (page == null || graph == null) return;

    page.openDownloadPdfDialog(
      PdfExportParameters.fullPage(
        addWatermark: (_page?.watermarkSetting ?? WatermarkSetting.fromFlags()).toBool(overrideDefault: ExportDialog.useWatermarkForDocument),
      ),
      id: id,
      title: title,
    );
  }

  // Used to initialize the page is rendering from the backend
  void initializeCliMode() {
    if (_page != null) {
      _page?.initializeCliMode();
    }
  }

  void _updateLibraryUI() {
    if (!_featureEnabledLibrary || !host.context.auth.loggedIn) {
      toggleLibrary(forceTo: false);
    } else if (showLibrary == true) {
      toggleLibrary(forceTo: true);
    }
    _rebuildLibraryPanelButton();
  }

  void _updateAddPageUI() {
    _rebuildAddPageButton();
  }

  void _updatePageHeaderButtons() async {
    final graph = _page?.graph;
    if (graph == null) return;

    if (_topRightButtonsContainer == null) {
      _topRightButtonsContainer = HTMLDivElement()..classList.addAll(['top_right_buttons_container']);
      host.appbar.right.appendNotNull(_topRightButtonsContainer);
    }

    if (!_updatePageHeaderButtonsVisibility()) return;

    final policy = await _page?.policy;

    // 1- Theme
    _themeButton ??= ThemeButton(this);
    _themeButton?.applyPolicy(policy);
    _topRightButtonsContainer?.appendNotNull(_themeButton?.root);

    // 2- Page settings
    if (_pageSettingsButton == null) {
      _pageSettingsButton ??= PageSettingsButton(this);
      _pageSettingsButton?.applyPolicy(policy);
    }
    _topRightButtonsContainer?.appendNotNull(_pageSettingsButton?.root);

    // 3- Share
    _shareButton ??= ShareButton(this);
    _shareButton?.applyPolicy(policy);
    _topRightButtonsContainer?.appendNotNull(_shareButton?.root);

    // In case the visibility changed
    _updatePageHeaderButtonsVisibility();
  }

  bool _updatePageHeaderButtonsVisibility() {
    if (!_featureEnabledPageHeaderButtons || currentPageId == null || !context.auth.loggedIn || _page?.isReleased == true) {
      _topRightButtonsContainer?.clear();
      return false;
    }
    return true;
  }

  @override
  bool get isNightMode => host.uiTheme == UITheme.night;

  void setDefaultAccess() {
    shareButton.setDefaultAccessOnShareBoxFuture();
  }

  @override
  void closeLibrary() {
    toggleLibrary(forceTo: false);
  }

  Future<void> hideLibrary() async {
    if (_libraryPanel?.isShowing == true) {
      _libraryPanel?.close();
      await Future.delayed(const Duration(milliseconds: 200));
    }
    _addPageElement.toggle(visible: false);
    _hideLibraryPanelButton();
    await Future.delayed(const Duration(milliseconds: 250)); // Wait for the animation to complete
  }

  Future<void> hidePageContent() async {
    _pageHolder.classList.add('hidden');
    _pageOverlay.classList.add('hidden');
    await Future.delayed(const Duration(milliseconds: 250)); // Wait for the animation to complete
  }

  @override
  void updateLibraryOverlapping() {
    _libraryPanel?.updateOverlapping();
  }

  @override
  (PageUserContext?, Future<PageUserContext?>?) get pageUserContext => _page?.pageUserContext ?? (null, null);

  @override
  void toggleOutputLanguage(String language) => _page?.graph?.toggleOutputLanguage(language);

  @override
  void togglePageLines({required bool show}) => _page?.graph?.togglePagePattern(show: show);

  @override
  void toggleExportMarkers(PageExportMarkersType markerType) => _page?.graph?.togglePageExportMarkers(markerType);

  @override
  void toggleNightModeInvertLightness({required bool invert}) => _page?.toggleNightModeInvertLightness(invert: invert);

  @override
  bool get showLanguageDropdown => _page != null && _page?.cachedPolicy?.canEdit == true;

  @override
  bool get showPagePattern => _page != null && _page?.cachedPolicy?.canEdit == true;

  @override
  FontsRepository? get fontsRepository => _page?.graph?.fontsRepository;

  @override
  ThemesRepository? get themesRepository => _page?.graph?.themesRepository;

  @override
  IconDataRequester get iconDataRequester => _page?.graph?.iconDataRequester ?? DbDataBankRequester();

  @override
  IconDataRequester? get requester => _page?.graph?.iconDataRequester;

  @override
  bool get isInvertLightness => _page?.graph?.isInvertLightness ?? false;

  @override
  String? getCssValue(String key, {bool? isNightMode}) => host.getCssValue(key, isNightMode: isNightMode);
}

class PageViewPrivateer {
  final PageView _pageView;
  PageViewPrivateer._(this._pageView);

  LibraryPanel get libraryPanel => _pageView._libraryPanel!;
  AddPageElement get addPage => _pageView._addPageElement;
  CommentNavigation get commentNavigation => _pageView._commentNavigation;
  Page? get page => _pageView._page;
  set page(Page? page) => _pageView._page = page;
  bool get pageIsStale => _pageView._pageIsStale;

  void markPageIsStale(String source) => _pageView.markPageIsStale(source);
}

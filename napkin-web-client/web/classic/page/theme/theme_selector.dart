import 'dart:async';

import 'package:ai_web_lib/ai_web_lib.dart';
import 'package:analytics_web_lib/analytics_web_lib.dart';
import 'package:io_web_lib/logging.dart';
import 'package:util_web_lib/base32.dart';
import 'package:util_web_lib/browser.dart' as browser;
import 'package:util_web_lib/clippy.dart' as clippy;
import 'package:util_web_lib/dialog.dart';
import 'package:util_web_lib/ecs.dart';
import 'package:util_web_lib/environment.dart';
import 'package:util_web_lib/has_subscription.dart';
import 'package:util_web_lib/helper.dart' as helper;
import 'package:util_web_lib/html_root.dart';
import 'package:util_web_lib/js_interop.dart';
import 'package:util_web_lib/mock_input_event.dart';
import 'package:util_web_lib/overlay_menu.dart';
import 'package:util_web_lib/privateer.dart';
import 'package:util_web_lib/releaser.dart';
import 'package:util_web_lib/subscription_plan.dart';
import 'package:util_web_lib/ui-kit/cx_enums.dart';
import 'package:util_web_lib/ui-kit/cx_input.dart';
import 'package:util_web_lib/ui-kit/cx_tooltip.dart';
import 'package:util_web_lib/undo.dart';
import 'package:vector_math/vector_math.dart';

import '../../../app/context.dart';
import '../../dialogs/import_style_dialog.dart';
import '../../dialogs/workspace/upgrade_nudge.dart';
import '../../graph/graph.dart';
import '../../graph/item/item_helpers.dart';
import '../../plans/plan_enums.dart';
import '../font/font_repository.dart';
import 'theme_editor.dart';
import 'theme_header.dart';
import 'theme_model.dart';
import 'theme_repository.dart';
import 'theme_small_preview.dart';

abstract class ThemeSelectorHost {
  Context get context;
  Ai get ai;
  Graph? get pageGraph;
  ThemesRepository? get themesRepository;
  FontsRepository? get fontsRepository;
  IconDataRequester? get requester;
  IconDataRequester get iconDataRequester;
  bool get isInvertLightness;
  bool get isNightMode;
  String? getCssValue(String key, {bool? isNightMode});
}

class ThemeSelector with Releaser, HtmlRoot, HasSubscription, Privateer<ThemeSelectorPrivateer> implements ThemeEditorHost {
  static final Logger _log = Logger('ThemeSelector', browser: Logger.FINE);
  static const String containerClass = 'dialog-theme-selector';

  final ThemeSelectorHost _host;
  ThemeEditor? _themeEditor;
  bool get isThemeEditorOpen => _themeEditor != null;
  @override
  CustomThemeHeader<Theme>? get themeHeader => _themeEditor?.themeHeader;

  bool get _editorHasChanges => (_themeEditor?.undosLength ?? 0) > 0;

  @override
  ThemesRepository? get themesRepository => _host.themesRepository;

  @override
  FontsRepository? get fontsRepository => _host.fontsRepository;

  final HTMLElement _sectionsElement = HTMLDivElement()..classList.add('sections');
  final HTMLUListElement _listCustomElement = HTMLUListElement();
  final HTMLUListElement _listBuiltIntElement = HTMLUListElement();

  // Add lists to store themes
  final List<CustomThemeHeader> _customThemes = [];
  final List<ThemeHeader> _builtInThemes = [];

  Future? _customFuture;
  Future? _builtInFuture;

  Future<bool>? _waitForConfirmation;
  Completer<bool>? _discardTriggerForABTest;

  OverlayMenu? _themeSubMenu;

  Iterable<ReadOnlyEntity> get _selectedThemeFrameEntitiesInPage => _host.pageGraph?.selectedThemeFrames ?? const [];

  // Debug query parameters
  bool get openThemeEditorForDev =>
      browser.getUrlQueryParameter('open-theme-editor')?.isNotEmpty == true || browser.getUrlQueryParameter('edit-preset')?.isNotEmpty == true;

  ThemeSelector(this._host, {ThemeEditor? themeEditor}) : _themeEditor = themeEditor;

  // Called when a new page is loaded
  void install() {
    // we need to get the themes again
    rebuild();
  }

  void release() {
    releaseSubscriptions();
    releaseAll();

    _themeEditor?.release();
    _themeEditor = null;
  }

  Future _onCustomThemesLoad() async {
    // Dev stuff
    if (openThemeEditorForDev) {
      // edit a theme by its index
      final themeIndex = Int.safe(browser.getUrlQueryParameter('open-theme-editor'));
      if (themeIndex != null && themeIndex >= 0 && themeIndex < _customThemes.length) {
        _openThemeEditor(_customThemes[themeIndex]);
      }

      // edit a preset
      final presetName = browser.getUrlQueryParameter('edit-preset');
      if (presetName != null) {
        final preset = ThemePresetComponentBuilders.find(presetName);
        if (preset != null) {
          final customThemeHeader = await _createEditableThemeFromPresetFile(preset);
          if (customThemeHeader != null) _openThemeEditor(customThemeHeader);
        }
      }
    }
  }

  // dev stuff
  Future<CustomThemeHeader?> _createEditableThemeFromPresetFile(ThemePresetComponentBuilderFromFile preset) async {
    final m = await preset.loadThemePresetData();
    if (m == null) {
      _log.severe(() => 'Failed to load the preset file for `${preset.runtimeType}`.');
      return null;
    }

    // Prepare the CustomThemeData
    final customThemeSource = CustomThemeSource.fromJson(m, getCustomFonts: null);
    final id = 'id-edit-${preset.hashCode}';
    m['id'] = id;
    m['displayName'] = preset.runtimeType.toString();
    m['editorStep'] = 4;
    // Load a preset as a theme solver!
    m['themeSolver'] = m.remove('themePreset')..['mode'] = PropertySolverMode.open.name;
    m['themePreset'] = (await ThemePresetDefault().generate(customThemeSource))?.$1.toJson(null); // always use the default preset name for presets edits

    // build a theme form the theme preset
    final customThemeData = CustomThemeData.fromJson(m, id: id);
    final themeHeader = ThemeHeader(
      id,
      customThemeData.customTheme,
      (_) => Future.value(true),
      customThemeData: customThemeData,
      initialActive: false,
      themeModel: null,
    );
    return CustomThemeHeader.fromThemeHeader<Theme>(themeHeader, getCustomFonts: null, saveThemeHeaderFunction: null);
  }

  @override
  void pushUndo(Undo? undo, {String? session, String? replaceSession}) => _themeEditor?.pushUndo(undo, session: session, replaceSession: replaceSession);

  @override
  HTMLElement build() {
    _sectionsElement.clear();

    // Header

    final headerElement = HTMLDivElement()
      ..classList.add('header')
      ..appendChild(
        HTMLHeadingElement.h1()
          ..classList.addAll(['icon', 'theme'])
          ..textContent = 'Styles',
      )
      ..appendChild(_buildNewThemeButton());

    // Subheader explanation

    final subheaderElement = HTMLParagraphElement()
      ..classList.add('subheader')
      ..appendAll([
        HTMLSpanElement()..classList.addAll(['icon', 'spark-circle']),
        Text()..textContent = ' Napkin generates visuals based on your active styles. Toggle them here:',
      ]);

    // custom

    _customFuture = themesRepository?.getAllCustom().then((themes) async {
      _customThemes.clearAddAll(themes);
      final element = _buildStyleSection('Your Styles', themes.toList(growable: false), custom: true);
      if (element != null) _sectionsElement.insertFirst(element);
      await _onCustomThemesLoad();
      _customFuture = null;
    });

    // built-in
    _builtInFuture = themesRepository?.getAllBuiltIn().then((themes) {
      _builtInThemes.clearAddAll(themes);
      final element = _buildStyleSection('Built-in Styles', themes.toList(growable: false), custom: false);
      if (element != null) _sectionsElement.appendChild(element);
      _builtInFuture = null;
    });

    return HTMLDivElement()
      ..classList.addAll(['theme_selector'])
      ..appendChild(headerElement)
      ..appendChild(subheaderElement)
      ..appendChild(_sectionsElement);
  }

  HTMLElement? _buildStyleSection(String headerText, List<ThemeHeader> themes, {required bool custom}) {
    final listElement = (custom ? _listCustomElement : _listBuiltIntElement)..clear();
    listElement.appendAll(themes.map((theme) => _buildThemeListItem(theme)));

    final threeDotsMenu = HTMLDivElement()..classList.addAll(['icon', 'three-dots', 'menu']);
    autoUnsubscribe(
      threeDotsMenu.onMouseEnter.listen((e) {
        // Make sure we do not select the page
        e.stopPropagation();
        if (e.target != e.currentTarget) return; // skip if a child is clicked
        final target = e.target?.tryCast<HTMLElement>();
        if (target == null) return;
        _themeSubMenu?.hide(null);
        _themeSubMenu = _buildSectionSubmenu(custom: custom);
        threeDotsMenu.appendChild(_themeSubMenu!.root);
      }),
    );
    autoUnsubscribe(
      threeDotsMenu.onMouseLeave.listen((e) {
        _themeSubMenu?.hide(null);
        _themeSubMenu = null;
      }),
    );

    // debug import input
    HTMLElement? importInput;
    if (custom && ThemeEditor.debugMode) {
      final input = CxInput(releaser: this, placeholder: 'Paste ThemeData json here', variant: CxInputVariantEnum.lightFilled);
      importInput = input.root;
      autoUnsubscribe(importInput.onPaste.listen((e) => _debugPasteThemeData(e)));
    }

    return HTMLDivElement()
      ..classList.addAll(['section', if (themes.isEmpty) 'empty'])
      ..appendChild(
        HTMLHeadingElement.h2()
          ..textContent = headerText
          ..classList.addAll(['icon', custom ? 'star' : 'napkin'])
          ..appendChild(threeDotsMenu),
      )
      ..appendNotNull(importInput)
      ..appendChild(listElement);
  }

  HTMLElement _buildNewThemeButton() {
    cancelSubscriptions('theme_button');
    final disabled = Flags().variation('enforce-plans', false) && context.workSpaceManager.activeWorkspaceDetails?.planClass == PlanClass.free;

    final element = HTMLButtonElement()
      ..classList.addAll(['button', 'icon', 'create_style', 'pro', if (disabled) 'disabled'])
      ..textContent = 'Create My Style'
      ..disabled = disabled;
    if (!disabled) autoUnsubscribe(element.onClick.listen(_onNewThemeClick), 'theme_button');
    if (disabled) {
      final content = HTMLDivElement()..innerHTML='Available in<br/><span class="cx-icon plus-tag translucent" style="display:contents"></span> and <span class="cx-icon pro-tag translucent" style="display:contents; height: 14px; width: 38px;"></span>'.toJS;
      CxTooltip.withAutoHover(
        releaser: this,
        targetElement: element,
        placement: CxTooltipPlacement.bottomCenter,
        htmlContent: content,
        innerClasses: ['tooltip-upgrade-nudge']
      );
    }
    return element;
  }

  /// Opens the theme editor with a new theme, return a future ending when the editor is closed
  Future? _onNewThemeClick(MouseEvent e) {
    final themeButton = e.currentTarget?.tryCast<HTMLElement>();
    if (themeButton == null || themeButton.classList.contains('disabled')) return null;

    return _openThemeEditor(null, newThemeSource: 'theme_selector');
  }

  HTMLLIElement _buildThemeListItem(ThemeHeader theme) {
    final isDraft = theme.isDraft;

    final threeDotsMenu = HTMLDivElement()..classList.addAll(['menu', 'three-dots', 'icon']);
    autoUnsubscribe(
      threeDotsMenu.onClick.listen(
        (e) => e.stopPropagation(), // to avoid toggling the theme
      ),
    );
    autoUnsubscribe(
      threeDotsMenu.onMouseEnter.listen((e) {
        // Make sure we do not select the page
        e.stopPropagation();
        if (e.target != e.currentTarget) return; // skip if a child is clicked
        final target = e.target?.tryCast<HTMLElement>();
        if (target == null) return;
        _themeSubMenu?.hide(null);
        _themeSubMenu = _buildThemeSubmenu(theme);
        threeDotsMenu.appendChild(_themeSubMenu!.root);
      }),
    );
    autoUnsubscribe(
      threeDotsMenu.onMouseLeave.listen((e) {
        _themeSubMenu?.hide(null);
        _themeSubMenu = null;
      }),
    );

    final checkElement = HTMLDivElement()..classList.addAll(['check']);

    /// invertLightness is made false because we want to see the day theme in the night mode
    final (smallPreviewElement, _) = ThemeSmallPreview.build(
      Vector2(364, 72),
      theme.theme,
      theme.customThemeData?.customFonts,
      displayName: theme.displayName,
      themeDescription: theme.description,
      invertLightness: false,
      requester: requester,
      preset: false,
    );

    final previewElement = HTMLDivElement()
      ..classList.add('preview')
      ..appendAll([
        smallPreviewElement,
        if (isDraft)
          HTMLDivElement()
            ..classList.add('draft')
            ..textContent = 'DRAFT',
      ]);

    final liElement = HTMLLIElement()
      ..classList.addAll(['theme', if (theme.active) 'active', if (isDraft) 'draft'])
      ..id = theme.id
      ..appendChild(previewElement)
      ..appendChild(
        HTMLDivElement()
          ..classList.add('menu-holder')
          ..appendChild(checkElement)
          ..appendChild(threeDotsMenu),
      );

    // do a parallax effect on the preview based on the mouse position inside the element
    autoUnsubscribe(
      previewElement.onMouseMove.listen((e) {
        final dx = e.offsetX - previewElement.clientWidth / 2;
        final dy = e.offsetY - previewElement.clientHeight / 2;
        previewElement.style.setProperty('--mouse-x', '${-dx}px');
        previewElement.style.setProperty('--mouse-y', '${-dy}px');
      }),
    );

    autoUnsubscribe(
      liElement.onClick.listen((_) {
        if (isDraft) {
          _openThemeEditor(
            CustomThemeHeader.fromThemeHeader(
              theme,
              getCustomFonts: fontsRepository?.getLoadedCustomFonts,
              saveThemeHeaderFunction: themesRepository?.saveThemeHeader,
            ),
          );
        } else {
          _toggleActivation(theme.id);
        }
      }),
    );

    return liElement;
  }

  OverlayMenu _buildSectionSubmenu({required bool custom}) {
    final overlayMenu = OverlayMenu(hideOnMouseLeave: true, cssClasses: ['pointy-top-right', 'inverted'], mouseAreaExtension: 24);
    overlayMenu.addItem('Import Style', ['import-style', 'icon'], _importStyle, proFeature: true);
    overlayMenu.addItem('Activate All', ['activate_all', 'icon'], (_) => _toggleActivationAll(activate: true, custom: custom));
    overlayMenu.addItem('Deactivate All', ['deactivate_all', 'icon'], (_) => _toggleActivationAll(activate: false, custom: custom));
    return overlayMenu;
  }

  OverlayMenu _buildThemeSubmenu(ThemeHeader themeHeader) {
    final overlayMenu = OverlayMenu(hideOnMouseLeave: true, cssClasses: ['pointy-top-right', 'inverted'], mouseAreaExtension: 28);

    final customThemeData = themeHeader.customThemeData;
    final builtIn = themeHeader.theme is BuiltInTheme;
    final custom = customThemeData != null && !builtIn;
    final notDraft = !themeHeader.isDraft;

    // Edit
    if (custom) {
      overlayMenu.addItem(
        'Edit',
        ['edit', 'icon'],
        (_) => _openThemeEditor(
          CustomThemeHeader.fromThemeHeader(
            themeHeader,
            getCustomFonts: fontsRepository?.getLoadedCustomFonts,
            saveThemeHeaderFunction: themesRepository?.saveThemeHeader,
          ),
        ),
        proFeature: true,
      );
    }

    // Duplicate - for both custom and built-in themes
    if (notDraft && customThemeData != null) {
      overlayMenu.addItem(custom ? 'Duplicate' : 'Customize', ['duplicate', 'icon'], (_) => _duplicateCustomTheme(customThemeData), proFeature: true);
    }

    if (notDraft && customThemeData != null) {
      overlayMenu.addItem('Copy Style ID', ['share-style', 'icon'], (_) => _copyStyleId(themeHeader));
    }

    if (customThemeData != null && ThemeEditor.debugMode) {
      overlayMenu.addItem('Copy ThemeData', ['share-style', 'icon'], (_) => ThemeEditor.copyThemeDataToClipboard(customThemeData));
    }

    // Apply to Selection
    if (notDraft && _selectedThemeFrameEntitiesInPage.isNotEmpty == true) {
      overlayMenu.addItem('Apply to Selection', ['apply_selection', 'icon'], (_) => _applyToSelection(themeHeader));
    }

    // Apply to All
    if (notDraft) overlayMenu.addItem('Apply to All', ['apply_all', 'icon'], (_) => _applyToAll(themeHeader));

    // Deactivate others
    if (notDraft) overlayMenu.addItem('Deactivate Others', ['deactivate_others', 'icon'], (_) => _deactivateOthers(themeHeader));

    // Delete
    if (custom) overlayMenu.addItem('Delete', ['delete', 'icon'], (_) => _delete(customThemeData, askConfirmation: true));

    return overlayMenu;
  }

  void _debugPasteThemeData(Event? event) {
    if (event == null || !event.isA<ClipboardEvent>()) return;

    try {
      final clipboardData = (event as ClipboardEvent).clipboardData;
      if (clipboardData == null) {
        _log.warning(() => '[_debugPasteThemeData] no clipboard data');
        return;
      }

      // check for json string in clipboard
      final textData = clipboardData.getData('text/plain');
      if (textData.isEmpty) {
        _log.warning(() => '[_debugPasteThemeData] no `text/plain` data in clipboard');
        return;
      }

      // try to parse as json
      final json = helper.decodeJson<JsonMap>(textData);
      if (json == null || json.isEmpty) {
        _log.warning(() => '[_debugPasteThemeData] no json in clipboard');
        return;
      }

      // check for theme data
      if (json.getString('id') == null || json.getInt('editorStep') == null) {
        _log.warning(() => '[_debugPasteThemeData] invalid theme data in clipboard');
        return;
      }

      event.preventDefault();
      event.stopPropagation();

      _newThemeFromJson(json).then((_) => rebuild());
    } catch (e) {
      _log.severe(() => '[onPaste] failed to process paste: $e');
    }
  }

  Future<void> _newThemeFromJson(JsonMap json) async {
    final repository = themesRepository;
    if (repository == null) {
      _log.severe(() => '[newThemeFromJson] fontsRepository is null');
      return;
    }
    final newThemeModel = await repository.createTheme(active: true);
    if (newThemeModel == null) {
      _log.severe(() => '[newThemeFromJson] failed to create a new theme model');
      return;
    }
    final newThemeData = CustomThemeData.fromJson(json, id: newThemeModel.id, getCustomFonts: fontsRepository?.getLoadedCustomFonts);
    final newThemeHeader = await repository.buildCustomThemeHeader(newThemeData, themeModel: newThemeModel);
    await repository.saveTheme(newThemeHeader);
  }

  void _importStyle([_]) {
    if (themesRepository == null) {
      _log.warning(() => '[importStyle] themesRepository is null');
      return;
    } else if (fontsRepository == null) {
      _log.warning(() => '[importStyle] fontsRepository is null');
      return;
    }
    ImportStyleDialog(themesRepository!, fontsRepository!, _onStyleImported).open();
  }

  void _onStyleImported(String? themeId) {
    rebuild();

    // Scroll to the newly imported theme after a delay to ensure it's rendered
    if (themeId != null) {
      Future.delayed(const Duration(milliseconds: 200), () {
        _scrollToTheme(themeId);
      });
    }
  }

  void _scrollToTheme(String themeId) {
    final themeElement = _listCustomElement.querySelector('#$themeId');
    if (themeElement != null) {
      final options = {'behavior': 'smooth', 'block': 'center', 'inline': 'nearest'}.jsify()!;
      themeElement.scrollIntoView(options);
    }
  }

  Future<CustomThemeHeader<Theme>?> _createDuplicatedCustomThemeHeader(CustomThemeData customThemeData, {bool active = true}) async {
    final themeModel = await themesRepository?.createTheme(active: active);
    if (themeModel == null) {
      _log.warning(() => '[duplicateTheme] failed to create a new theme model');
      return null;
    }
    final fontsRepository = this.fontsRepository;
    if (fontsRepository == null) {
      _log.warning(() => '[duplicateTheme] fontsRepository is null');
      return null;
    }

    final newName = _newName(customThemeData.isBuiltIn ? 'My ${customThemeData.displayName}' : customThemeData.displayName);
    final newDescription = customThemeData.isBuiltIn ? 'Copy of ${customThemeData.displayName}, ${customThemeData.description}' : null;
    final getCustomFonts = fontsRepository.getLoadedCustomFonts;

    final duplicatedThemeData = customThemeData.duplicate(themeModel.id, newName, newDescription: newDescription, getCustomFonts: getCustomFonts);

    if (duplicatedThemeData == null) {
      _log.warning(() => '[duplicateTheme] failed to duplicate the theme');
      return null;
    }

    final duplicatedThemeHeader = await themesRepository?.buildCustomThemeHeader(duplicatedThemeData, themeModel: themeModel);
    if (duplicatedThemeHeader == null) {
      _log.warning(() => '[duplicateTheme] failed to build the duplicated theme header');
      return null;
    }
    return duplicatedThemeHeader;
  }

  Future _duplicateCustomTheme(CustomThemeData customThemeData, {bool active = true}) async {
    final maxCustomThemes = context.workSpaceManager.getActiveWorkspaceCredits(PlanFeatureKey.customThemes);
    final canCreateTheme = _customThemes.length < maxCustomThemes;
    if (!canCreateTheme) {
      final message = HTMLDivElement()..dangerouslySetInnerHtml('You’ve reached your plan’s limit of $maxCustomThemes custom styles.<br/>To add more, you can edit or remove an existing style,<br/>or upgrade for unlimited custom styles.');
      await WorkspaceUpgradeNudgeDialog(context.workSpaceManager.activeWorkspaceDetails?.planClass.next, message).show();
      return null;
    }

    final duplicatedThemeHeader = await _createDuplicatedCustomThemeHeader(customThemeData, active: active);
    if (duplicatedThemeHeader == null) {
      Dialog.alert(textContent: 'There was an error while duplicating the theme. Please try again.');
      return null;
    }
    await _openThemeEditor(duplicatedThemeHeader);

    Analytics().trackEvents(
      AnalyticsEvents(
        monitoring: AnalyticsEvent('THEME_EDITOR/DUPLICATE'),
        tagManager: AnalyticsEvent('napkin_theme_editor_duplicate_theme', ThemeEditor.buildAnalyticsEventPayload({}, themeHeader: themeHeader)),
      ),
    );
  }

  String _newName(String exampleName) {
    final existingNames = [..._builtInThemes, ..._customThemes].map((t) => t.displayName);
    return helper.nextName(exampleName, existingNames);
  }

  Future _openThemeEditor(CustomThemeHeader? themeHeader, {List<String>? colors, String? newThemeSource}) async {
    final themesRepository = this.themesRepository;
    if (themesRepository == null) {
      _log.warning(() => '[openThemeEditor] no themes repository to open the theme editor');
      return;
    }

    final fontsRepository = this.fontsRepository;
    if (fontsRepository == null) {
      _log.warning(() => '[openThemeEditor] no fonts repository to open the theme editor');
      return;
    }

    // if trying to create a new theme, and limit exhausted, show dialog
    final maxCustomThemes = context.workSpaceManager.getActiveWorkspaceCredits(PlanFeatureKey.customThemes);
    final canCreateTheme = _customThemes.length < maxCustomThemes;
    if (themeHeader == null && !canCreateTheme) {
      final message = HTMLDivElement()..dangerouslySetInnerHtml('You’ve reached your plan’s limit of $maxCustomThemes custom styles.<br/>To add more, you can edit or remove an existing style,<br/>or upgrade for unlimited custom styles.');
      await WorkspaceUpgradeNudgeDialog(context.workSpaceManager.activeWorkspaceDetails?.planClass.next, message).show();
      return;
    }

    if (newThemeSource != null) {
      Analytics().trackEvents(
        AnalyticsEvents(
          monitoring: AnalyticsEvent('THEME_EDITOR/NEW_THEME', {'source': newThemeSource}),
          tagManager: AnalyticsEvent(
            'napkin_theme_editor_new_theme',
            ThemeEditor.buildAnalyticsEventPayload({'source': newThemeSource}, themeHeader: themeHeader),
          ),
        ),
      );
    }

    Analytics().trackEvents(
      AnalyticsEvents(
        monitoring: AnalyticsEvent('THEME_EDITOR/OPEN_THEME'),
        tagManager: AnalyticsEvent('napkin_theme_editor_open_theme', ThemeEditor.buildAnalyticsEventPayload({}, themeHeader: themeHeader)),
      ),
    );

    final fromNew = themeHeader == null;
    if (themeHeader == null) {
      final themeId = ThemeModel.generateId();
      themeHeader ??= await themesRepository.buildCustomThemeHeader(
        CustomThemeData.defaultNew(themeId, getCustomFonts: fontsRepository.getLoadedCustomFonts),
        themeModel: null,
      );

      if (colors != null) {
        themeHeader.themeData.paletteAsHex.addAll(colors);
      }
    }

    _themeEditor ??= ThemeEditor(this, themeHeader);
    await _themeEditor?.showDialog(
      beforeClose: (CloseSource source) async => await _onThemeEditorClose(source: source, fromNew: fromNew),
      canClose: (CloseSource source) => _canThemeEditorClose(),
    );
  }

  Future<bool> _canThemeEditorClose() {
    // Do not allow close by any other means if unsaved changes dialog is open
    return Future.value(_waitForConfirmation == null);
  }

  Future<bool> _onThemeEditorClose({required CloseSource source, bool fromNew = false}) async {
    // Ask the user if they want to save the theme
    var shouldSave = source == CloseSource.external && _editorHasChanges;
    if (!shouldSave && _editorHasChanges) {
      // Never allow multiple dialogs to open!
      assert(_waitForConfirmation == null);
      _discardTriggerForABTest ??= Completer<bool>();
      _waitForConfirmation = Dialog.confirm(
        [
          Text()..textContent = 'You may have unsaved changes to your style.',
          HTMLBRElement(),
          Text()..textContent = 'Do you want to save them before closing?',
        ],
        title: 'Unsaved changes',
        successText: 'Save',
        cancelText: 'Discard',
        closeButton: false,
        closeOnEsc: false,
        externalCompleter: _discardTriggerForABTest,
      );
      shouldSave = (await _waitForConfirmation)!;
      _waitForConfirmation = null;
      _discardTriggerForABTest = null;
    }

    Analytics().trackEvents(
      AnalyticsEvents(
        monitoring: AnalyticsEvent('THEME_EDITOR/CLOSE_DIALOG', {'save': shouldSave}),
        tagManager: AnalyticsEvent('napkin_theme_editor_close_dialog', ThemeEditor.buildAnalyticsEventPayload({'save': shouldSave}, themeHeader: themeHeader)),
      ),
    );

    if (shouldSave) {
      return _saveTheme(fromNew: fromNew);
    }

    _themeEditor?.release();
    _themeEditor = null;
    return true;
  }

  Future<bool> _saveTheme({bool fromNew = false}) async {
    final themeEditor = _themeEditor;
    if (themeEditor == null) return false;

    final waitDialog = _editorHasChanges ? Dialog.wait(textContent: 'Saving theme...', zIndex: DialogZIndex.top) : null;

    // delete the theme if it was just created and it is empty
    if (fromNew && themeEditor.themeData.isEmpty) {
      await _delete(themeEditor.themeData, askConfirmation: false);
      _themeEditor?.release();
      _themeEditor = null;
      waitDialog?.close();
      return true;
    }

    // Save the theme
    final updatedThemeHeader = await themeEditor.saveTheme();
    if (updatedThemeHeader != null && updatedThemeHeader.id.isNotEmpty) {
      final themeElement = _listCustomElement.querySelector('#${updatedThemeHeader.id}');
      if (themeElement == null) {
        _listCustomElement.appendChild(_buildThemeListItem(updatedThemeHeader));
        _customThemes.add(updatedThemeHeader);
      } else {
        themeElement.replaceWith(_buildThemeListItem(updatedThemeHeader));
      }
      _listCustomElement.parentElement?.classList.toggle('empty', _listCustomElement.children.isEmpty);

      _themeEditor?.release();
      _themeEditor = null;

      waitDialog?.close();

      return true;
    } else if (updatedThemeHeader != null && updatedThemeHeader.id.isEmpty) {
      _log.warning(() => 'The updatedThemeHeader has an empty id (displayName: ${updatedThemeHeader.displayName}).');
    }

    waitDialog?.close();
    Dialog.alert(textContent: 'There was an error while saving the theme. Please try again.', zIndex: DialogZIndex.top);

    return false;
  }

  Future<String?> _copyStyleId(ThemeHeader theme) async {
    try {
      final themeData = theme.customThemeData;
      if (themeData == null) {
        _log.warning(() => '[copyStyleId] no custom theme data found');
        return null;
      }

      final styleId = encodeHexStringBase32(themeData.id);
      clippy.write(styleId);

      Analytics().trackEvents(
        AnalyticsEvents(
          monitoring: AnalyticsEvent('THEME_EDITOR/COPY_STYLE_ID'),
          tagManager: AnalyticsEvent('napkin_theme_editor_copy_style_id', ThemeEditor.buildAnalyticsEventPayload({}, themeHeader: themeHeader)),
        ),
      );
      return styleId;
    } catch (e) {
      _log.warning(() => 'Error while copying style id: $e');
    }
    return null;
  }

  Future _applyToSelection(ThemeHeader theme) async {
    final entities = _getThemeFrameChildren(_selectedThemeFrameEntitiesInPage);
    if (entities.isEmpty) {
      _log.warning(() => '[applyToSelection] no theme frame entities found');
      return;
    }

    await _applyThemeInternal(theme, entities);

    Analytics().trackEvents(
      AnalyticsEvents(
        monitoring: AnalyticsEvent('THEME_EDITOR/APPLY_TO_SELECTION'),
        tagManager: AnalyticsEvent('napkin_theme_editor_apply_to_selection', ThemeEditor.buildAnalyticsEventPayload({}, themeHeader: themeHeader)),
      ),
    );
  }

  Future _applyToAll(ThemeHeader theme) async {
    final allThemeFrameEntitiesInPage = AiComponentThemeFrame.getAllGraphThemeFrameEntities(_host.pageGraph?.activeStage);
    final entities = _getThemeFrameChildren(allThemeFrameEntitiesInPage);
    if (entities.isEmpty) {
      _log.warning(() => '[applyToAll] no theme frame entities found');
      return;
    }

    await _applyThemeInternal(theme, entities);

    Analytics().trackEvents(
      AnalyticsEvents(
        monitoring: AnalyticsEvent('THEME_EDITOR/APPLY_TO_ALL'),
        tagManager: AnalyticsEvent('napkin_theme_editor_apply_to_all', ThemeEditor.buildAnalyticsEventPayload({}, themeHeader: themeHeader)),
      ),
    );
  }

  Future _toggleActivationAll({required bool activate, required bool custom, bool checkOtherActive = true}) async {
    var makeFirsOneActive = false;
    if (checkOtherActive && activate == false) {
      final otherList = custom ? _builtInThemes : _customThemes;
      makeFirsOneActive = otherList.every((t) => !t.active);
    }

    var overrideFirstValue = makeFirsOneActive ? true : null;
    for (final t in (custom ? _customThemes : _builtInThemes)) {
      _toggleActivation(t.id, newActive: overrideFirstValue ?? activate, checkOtherActive: false, sendEvent: false);
      overrideFirstValue = null;
    }

    Analytics().trackEvents(
      AnalyticsEvents(
        monitoring: AnalyticsEvent('THEME_EDITOR/TOGGLE_ALL'),
        tagManager: AnalyticsEvent('napkin_theme_editor_toggle_all', ThemeEditor.buildAnalyticsEventPayload({}, themeHeader: themeHeader)),
      ),
    );
  }

  Future _deactivateOthers(ThemeHeader theme) async {
    // Deactivate all the other themes
    for (final t in [..._customThemes, ..._builtInThemes]) {
      _toggleActivation(t.id, newActive: theme.id == t.id, checkOtherActive: false, sendEvent: false);
    }

    Analytics().trackEvents(
      AnalyticsEvents(
        monitoring: AnalyticsEvent('THEME_EDITOR/DISABLE_OTHERS'),
        tagManager: AnalyticsEvent('napkin_theme_editor_disable_others', ThemeEditor.buildAnalyticsEventPayload({}, themeHeader: themeHeader)),
      ),
    );
  }

  Future _delete(CustomThemeData themeData, {required bool askConfirmation}) async {
    final repository = themesRepository;
    if (repository == null) {
      _log.warning(() => 'No themes repository to delete theme `${themeData.id}`.');
      return;
    }

    final confirmed =
        !askConfirmation ||
        await Dialog.confirm([
          Text()..textContent = 'Are you sure you want to delete the style',
          HTMLElement.b()..textContent = '"${themeData.displayName}"',
          Text()..textContent = '?',
          HTMLBRElement(),
          Text()..textContent = 'This action cannot be undone.',
        ]);
    if (!confirmed) return;

    final removed = await repository.removeTheme(context, themeData.id).then((removedFromRepository) {
      if (!removedFromRepository) return false;
      _customThemes.removeWhere((t) => t.id == themeData.id);
      _listCustomElement.querySelector('#${themeData.id}')?.remove();
      _listCustomElement.parentElement?.classList.toggle('empty', _listCustomElement.children.isEmpty);
      return true;
    });

    if (removed) {
      Analytics().trackEvents(
        AnalyticsEvents(
          monitoring: AnalyticsEvent('THEME_EDITOR/DELETE_THEME'),
          tagManager: AnalyticsEvent('napkin_theme_editor_delete_theme', ThemeEditor.buildAnalyticsEventPayload({}, themeHeader: themeHeader)),
        ),
      );
    } else {
      _log.warning(() => 'Failed to remove theme `${themeData.id}` from the theme repository.');
    }
  }

  void _toggleActivation(String themeId, {bool? newActive, bool checkOtherActive = true, bool sendEvent = true}) {
    final theme = _customThemes.firstWhereOrNull((t) => t.id == themeId) ?? _builtInThemes.firstWhereOrNull((t) => t.id == themeId);
    if (theme == null) {
      _log.warning(() => '[toggleActivation] no theme found with id $themeId');
      return;
    }

    newActive ??= !theme.active;
    if (theme.active == newActive) {
      _log.warning(() => '[toggleActivation] theme $themeId is already ${newActive! ? 'active' : 'inactive'}');
      return;
    }

    if (checkOtherActive && newActive == false) {
      final hasOtherActive = _customThemes.any((t) => t.active && t.id != themeId) || _builtInThemes.any((t) => t.active && t.id != themeId);
      if (!hasOtherActive) {
        _log.warning(() => '[toggleActivation] cannot deactivate the last active theme');
        return;
      }
    }

    theme.active = newActive;
    final liElement = _listCustomElement.querySelector('#$themeId') ?? _listBuiltIntElement.querySelector('#$themeId');
    liElement?.classList.toggle('active', theme.active);

    // _saveTheme(fromNew: false);

    Environment.document.dispatchEvent(CustomEvent(CustomEventThemesSelectionChanged));

    if (sendEvent) {
      Analytics().trackEvents(
        AnalyticsEvents(
          monitoring: AnalyticsEvent('THEME_EDITOR/TOGGLE_THEME_ACTIVE'),
          tagManager: AnalyticsEvent(
            'napkin_theme_editor_toggle_theme_active',
            ThemeEditor.buildAnalyticsEventPayload({'active': theme.active}, themeHeader: themeHeader),
          ),
        ),
      );
    }
  }

  Future openThemeSelectorModal(HTMLElement anchorElement, {String? source}) {
    Analytics().trackEvents(
      AnalyticsEvents(
        monitoring: AnalyticsEvent('THEME_EDITOR/OPEN'),
        tagManager: AnalyticsEvent(
          'napkin_theme_editor_open',
          GoogleTagManager.buildPayload(eventParams: {'source': source, 'version': AnalyticsEvent.themeEditorAnalyticsVersion}),
        ),
      ),
    );

    return Dialog.positioned<bool>(
      [root],
      onSuccess: true,
      onCancel: false,
      closeButton: false,
      canClose: (_) => true,
      onClose: (_) {},
      pointy: Pointy(edge: PointyPosition.top, attachment: PointyPosition.right),
      containerClasses: [ThemeSelector.containerClass],
      anchorElement: anchorElement,
      offsetFromHTMLAnchorElementCenter: Vector2(100, 18),
    );
  }

  Future openThemeEditorModal({bool openThemeSelector = true, List<String>? colors, String? newThemeSource}) async {
    await _openThemeEditor(null, colors: colors, newThemeSource: newThemeSource);
  }

  // Apply theme helpers

  Iterable<ReadOnlyEntity> _getThemeFrameChildren(Iterable<ReadOnlyEntity> entities) {
    final allEntities = <ReadOnlyEntity>[];
    for (final entity in entities) {
      allEntities.add(entity);
      allEntities.addAllNoDuplicate(AiComponentThemeFrame.getAiComponentThemeFrameChildren(entity));
    }
    return allEntities;
  }

  Future _applyThemeInternal(ThemeHeader theme, Iterable<ReadOnlyEntity> entities) async {
    if (entities.isEmpty) {
      _log.warning(() => '[applyThemeInternal] no entities found');
      return;
    }

    final pageGraph = ItemHelpers.tryGraph(entities.first);
    if (pageGraph == null) {
      _log.warning(() => '[applyThemeInternal] no graph found');
      return;
    }

    final previewStage = pageGraph.previewStage;
    previewStage.resetOwnerAndRelease(owner: 'ThemeSelector');

    final allEntityIds = entities.map((e) => e.id).toList();
    final stage = pageGraph.activeStage;
    if (!theme.theme.canApply(stage, allEntityIds)) {
      _log.warning(() => '[applyThemeInternal] theme cannot be applied');
      return;
    }

    _log.info(() => '[applyThemeInternal] applying theme ${theme.id} to ${entities.length} entities (${allEntityIds.toJson()})');

    final workStage = EntityStage('applyTheme', stage);
    await theme.theme.apply(workStage, allEntityIds, _host.iconDataRequester, resetExistingThemeStyles: true);
    workStage.mergeIntoParent(options: {'pushEvent': true, 'patchOrigin': PatchOrigin.themeChange.name});

    final snapshotId = pageGraph.snapshotTake();
    previewStage.resetOwnerAndMergeIntoParent(options: {'pushEvent': true, 'patchOrigin': PatchOrigin.themeChange.name});

    if (snapshotId != null) {
      final undo = Undo('setTheme')..add(pageGraph.snapshotBuildUndoCallback(snapshotId), requestSave: ItemHelpers.trySaver(entities.first, graph: pageGraph));
      pageGraph.pushUndo(undo);
    }
  }

  @override
  ThemeSelectorPrivateer get createPrivateer => ThemeSelectorPrivateer._(this);

  @override
  Context get context => _host.context;

  @override
  IconDataRequester get requester => _host.iconDataRequester;

  @override
  bool get isLightnessInverted => _host.isInvertLightness;

  @override
  bool get isNightMode => _host.isNightMode;

  @override
  Ai get ai => _host.ai;

  @override
  String? getCssValue(String key, {bool? isNightMode}) => _host.getCssValue(key, isNightMode: isNightMode);
}

class ThemeSelectorPrivateer {
  final ThemeSelector _themeSelector;
  ThemeSelectorPrivateer._(this._themeSelector);

  ThemeSelectorHost get host => _themeSelector._host;
  ThemeEditor? get themeEditor => _themeSelector._themeEditor;
  set themeEditor(ThemeEditor? value) => _themeSelector._themeEditor = value;

  List<CustomThemeHeader> get customThemes => _themeSelector._customThemes;
  List<ThemeHeader> get builtInThemes => _themeSelector._builtInThemes;
  HTMLUListElement get listCustomElement => _themeSelector._listCustomElement;

  /// returns a future that ends when the new theme editor is closed
  Future? onNewThemeClick() {
    final buttonElement = _themeSelector.root.querySelector('.button.create_style') as HTMLElement;
    return _themeSelector._onNewThemeClick(createClickEvent(target: buttonElement));
  }

  Future? clickEditTheme(CustomThemeHeader theme) => _themeSelector._openThemeEditor(theme);
  Future? clickApplyToSelection(ThemeHeader theme) => _themeSelector._applyToSelection(theme);
  Future? clickApplyToAll(ThemeHeader theme) => _themeSelector._applyToAll(theme);
  Future? clickDeactivateOthers(ThemeHeader theme) => _themeSelector._deactivateOthers(theme);
  Future? clickDeleteTheme(CustomThemeData themeData) => _themeSelector._delete(themeData, askConfirmation: false);

  // to test duplication
  Future<CustomThemeHeader<Theme>?> createDuplicatedCustomThemeHeader(CustomThemeData customThemeData, {required bool active}) =>
      _themeSelector._createDuplicatedCustomThemeHeader(customThemeData, active: active);
  Future openThemeEditor(CustomThemeHeader? themeHeader, {List<String>? colors, String? newThemeSource}) =>
      _themeSelector._openThemeEditor(themeHeader, colors: colors, newThemeSource: newThemeSource);

  OverlayMenu buildThemeSubmenu(ThemeHeader theme) => _themeSelector._buildThemeSubmenu(theme);
  void clickDiscardUnsaved() => _themeSelector._discardTriggerForABTest?.complete(false);

  // to test copy style ID
  Future<String?> copyStyleId(ThemeHeader theme) => _themeSelector._copyStyleId(theme);

  // to test preset editing
  Future<CustomThemeHeader?> createEditableThemeFromPresetFile(ThemePresetComponentBuilderFromFile preset) =>
      _themeSelector._createEditableThemeFromPresetFile(preset);

  Future? get customFuture => _themeSelector._customFuture;
  Future? get builtInFuture => _themeSelector._builtInFuture;
  Future? get waitForConfirmation => _themeSelector._waitForConfirmation;
  Future get themeLoadingFuture => Future.wait([?customFuture, ?builtInFuture]);
}

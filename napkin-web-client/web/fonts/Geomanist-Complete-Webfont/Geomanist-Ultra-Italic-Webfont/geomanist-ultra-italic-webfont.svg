<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="geomanistitalic" horiz-adv-x="569" >
<font-face units-per-em="2048" ascent="1536" descent="-512" />
<missing-glyph horiz-adv-x="395" />
<glyph unicode="&#x2e;&#x2e;&#x2e;" horiz-adv-x="1792" d="M51 178q0 110 87 190.5t212 80.5q101 0 161 -58.5t60 -148.5q0 -108 -88 -188.5t-213 -80.5q-100 0 -159.5 58.5t-59.5 146.5zM680 178q0 110 87 190.5t212 80.5q101 0 161 -58.5t60 -148.5q0 -108 -88 -188.5t-213 -80.5q-100 0 -159.5 58.5t-59.5 146.5zM1292 178 q0 110 87 190.5t212 80.5q101 0 161 -58.5t60 -148.5q0 -108 -88 -188.5t-213 -80.5q-100 0 -159.5 58.5t-59.5 146.5z" />
<glyph unicode=" "  horiz-adv-x="395" />
<glyph unicode="&#x09;" horiz-adv-x="395" />
<glyph unicode="&#xa0;" horiz-adv-x="395" />
<glyph unicode="!" horiz-adv-x="671" d="M111 178q0 110 87 190.5t212 80.5q101 0 161 -58.5t60 -148.5q0 -108 -88 -188.5t-213 -80.5q-100 0 -159.5 58.5t-59.5 146.5zM199 477l125 957h503l-194 -957h-434z" />
<glyph unicode="&#x22;" horiz-adv-x="911" d="M213 858l78 576h385l-113 -576h-350zM633 858l78 576h389l-115 -576h-352z" />
<glyph unicode="#" horiz-adv-x="1396" d="M76 328l117 313h147l57 152h-147l117 313h149l123 328h399l-123 -328h89l122 328h400l-123 -328h158l-117 -313h-160l-57 -152h159l-116 -313h-162l-125 -328h-399l125 328h-88l-125 -328h-400l125 328h-145zM739 641h88l58 152h-88z" />
<glyph unicode="$" horiz-adv-x="1181" d="M80 346l403 201q30 -69 74.5 -110.5t91.5 -41.5t47 41q0 24 -26 46.5t-67 44t-91 46.5t-100.5 59t-91.5 75t-67 102.5t-26 134.5q0 192 129 327.5t340 174.5l21 125h305l-21 -129q78 -19 142 -57.5t103.5 -85.5t63 -92t29.5 -85l-399 -209q-15 52 -52 91.5t-83 39.5 q-22 0 -35.5 -12t-13.5 -31q0 -28 35 -57t87 -53.5t113.5 -62t113.5 -80t87 -110.5t35 -149q0 -191 -127.5 -325t-343.5 -174l-23 -142h-305l23 140q-87 17 -156.5 57.5t-112 93t-67.5 104t-35 103.5z" />
<glyph unicode="%" horiz-adv-x="1830" d="M190 0l1295 1434h393l-1294 -1434h-394zM209 1042q0 171 136.5 294.5t330.5 123.5q156 0 251 -90.5t95 -228.5q0 -171 -137.5 -294.5t-331.5 -123.5q-155 0 -249.5 90.5t-94.5 228.5zM563 1075q0 -25 10.5 -40t28.5 -15q27 0 45.5 26t18.5 62q0 25 -10.5 40t-28.5 15 q-27 0 -45.5 -26t-18.5 -62zM1044 293q0 171 137.5 294.5t331.5 123.5q155 0 250 -91t95 -229q0 -171 -136.5 -294.5t-330.5 -123.5q-157 0 -252 91t-95 229zM1401 326q0 -25 10.5 -40.5t28.5 -15.5q27 0 45 26t18 62q0 25 -10.5 40.5t-28.5 15.5q-27 0 -45 -26t-18 -62z " />
<glyph unicode="&#x26;" horiz-adv-x="1421" d="M84 393q0 136 83 252t226 189q-53 79 -53 188q0 186 153.5 312t387.5 126q203 0 312.5 -96t109.5 -248q0 -237 -287 -405l67 -78q19 24 29 65h389q-15 -91 -64.5 -191.5t-115.5 -170.5l113 -131l-344 -289l-121 154q-179 -97 -383 -97q-238 0 -370 119t-132 301zM553 438 q0 -42 32 -68t74 -26q45 0 80 21l-133 161q-23 -11 -38 -34.5t-15 -53.5zM764 1030q0 -52 39 -94q76 49 76 121q0 25 -12.5 40t-35.5 15q-28 0 -47.5 -23t-19.5 -59z" />
<glyph unicode="'" horiz-adv-x="487" d="M213 858l78 576h385l-113 -576h-350z" />
<glyph unicode="(" horiz-adv-x="780" d="M55 90l185 1098q27 165 123 279.5t244 170.5t339 56l-59 -359q-88 0 -131 -37t-56 -110l-184 -1098q-11 -73 19 -110t118 -37l-59 -359q-291 0 -435 129.5t-104 376.5z" />
<glyph unicode=")" horiz-adv-x="780" d="M47 -416l59 359q89 0 132.5 37t54.5 110l184 1098q11 73 -19 110t-118 37l59 359q291 0 435 -129.5t104 -376.5l-184 -1098q-27 -165 -123.5 -279.5t-244.5 -170.5t-339 -56z" />
<glyph unicode="*" horiz-adv-x="917" d="M195 983l174 74l-142 76l170 229l123 -74l25 146h311l-25 -146l150 76l90 -256l-162 -74l133 -76l-170 -233l-122 74l-25 -144h-311l24 144l-145 -74z" />
<glyph unicode="+" horiz-adv-x="1099" d="M98 469l56 332h329l60 352h399l-59 -352h329l-55 -332h-330l-59 -354h-399l59 354h-330z" />
<glyph unicode="," horiz-adv-x="550" d="M2 -176q54 32 105.5 77t76.5 85q-65 25 -99 76.5t-34 115.5q0 110 87 190.5t212 80.5q99 0 159 -57.5t60 -158.5q0 -159 -113.5 -330t-305.5 -284z" />
<glyph unicode="-" horiz-adv-x="829" d="M145 459l60 352h688l-59 -352h-689z" />
<glyph unicode="." horiz-adv-x="550" d="M51 178q0 110 87 190.5t212 80.5q101 0 161 -58.5t60 -148.5q0 -108 -88 -188.5t-213 -80.5q-100 0 -159.5 58.5t-59.5 146.5z" />
<glyph unicode="/" horiz-adv-x="997" d="M-49 -221l909 1757h410l-910 -1757h-409z" />
<glyph unicode="0" horiz-adv-x="1232" d="M164 717q59 373 228.5 558t459.5 185t402 -185t53 -558q-60 -374 -230.5 -559t-460.5 -185t-400.5 185t-51.5 559zM666 717q-54 -334 16 -334q69 0 123 334q51 338 -17 338q-71 0 -122 -338z" />
<glyph unicode="1" horiz-adv-x="827" d="M260 1331l352 103h394l-240 -1434h-500l166 989l-133 -33z" />
<glyph unicode="2" horiz-adv-x="1187" d="M55 0l35 203q16 92 63.5 174.5t111 143t136.5 115t142.5 99.5t126.5 87t92 88.5t35 93.5q0 23 -15 37t-39 14q-74 0 -114 -123l-422 102q160 426 622 426q212 0 349 -111.5t137 -275.5q0 -86 -31 -165t-81.5 -137.5t-111 -110t-122 -91t-113 -71.5t-85.5 -60.5t-38 -50.5 h484l-66 -387h-1096z" />
<glyph unicode="3" horiz-adv-x="1159" d="M90 383l447 94q0 -43 24.5 -69.5t59.5 -26.5q44 0 73 27t29 71q0 35 -25 58.5t-69 23.5h-219l45 281l272 205h-463l66 387h979l-60 -355l-233 -184q93 -36 149 -121t56 -215q0 -170 -88.5 -305t-236.5 -208t-327 -73q-106 0 -190 27t-135.5 68.5t-86.5 98t-49.5 109 t-17.5 107.5z" />
<glyph unicode="4" horiz-adv-x="1280" d="M72 291l49 295l674 848h559l-129 -770h141l-61 -373h-142l-49 -291h-496l50 291h-596zM571 664h158l45 268z" />
<glyph unicode="5" horiz-adv-x="1165" d="M80 379l459 98q-3 -49 20.5 -70.5t58.5 -21.5q39 0 73 30t34 85q0 38 -21.5 62t-58.5 24q-58 0 -90 -56l-399 58l174 846h964l-63 -387h-539l-24 -113q62 20 137 20q186 0 308 -111.5t122 -291.5q0 -158 -91 -292t-242.5 -210t-326.5 -76q-105 0 -190.5 26.5t-139.5 68 t-91.5 96.5t-54 108t-19.5 107z" />
<glyph unicode="6" horiz-adv-x="1204" d="M123 475q0 80 10.5 164t37 182t66.5 187t106 173t149 145t200.5 97.5t255.5 36.5q236 0 383 -102l-223 -359q-90 54 -197 54q-77 0 -124.5 -42t-63.5 -106q28 15 82 30t106 15q150 0 249.5 -94.5t99.5 -267.5q0 -130 -51.5 -245t-138.5 -196t-207.5 -127.5t-254.5 -46.5 q-250 0 -367.5 132t-117.5 370zM625.5 434.5q13.5 -57.5 58.5 -57.5q39 0 64.5 40.5t25.5 96.5q0 44 -18 72t-49 28q-41 0 -74 -22q-21 -100 -7.5 -157.5z" />
<glyph unicode="7" horiz-adv-x="1118" d="M139 0l584 1053h-533l64 381h1089l-47 -285l-624 -1149h-533z" />
<glyph unicode="8" horiz-adv-x="1208" d="M100 379q0 118 55.5 219.5t145.5 165.5q-34 37 -54 94t-20 119q0 97 44 184.5t122.5 154t196.5 105.5t258 39q226 0 350.5 -105.5t124.5 -271.5q0 -98 -45 -180.5t-117 -138.5q45 -42 70.5 -110t25.5 -144q0 -236 -181 -386.5t-476 -150.5q-236 0 -368 109.5t-132 296.5z M594 436q0 -40 18 -64t50 -24q45 0 74.5 39.5t29.5 95.5q0 37 -16.5 64.5t-49.5 27.5q-49 0 -77.5 -44.5t-28.5 -94.5zM700 969q0 -33 14 -51.5t40 -18.5q37 0 60.5 35.5t23.5 81.5q0 74 -52 74q-38 0 -62 -36.5t-24 -84.5z" />
<glyph unicode="9" horiz-adv-x="1204" d="M111 76l223 358q88 -53 196 -53q79 0 127 40t62 107q-28 -15 -82.5 -30t-106.5 -15q-150 0 -249 95t-99 268q0 174 87 315t236.5 220t328.5 79q250 0 367.5 -132t117.5 -370q0 -80 -10.5 -164t-37 -182t-66.5 -187t-106 -173t-149 -145t-200.5 -97.5t-255.5 -36.5 q-235 0 -383 103zM668 920q0 -44 18 -72.5t49 -28.5q39 0 74 23q21 100 7.5 157.5t-58.5 57.5q-39 0 -64.5 -40.5t-25.5 -96.5z" />
<glyph unicode=":" horiz-adv-x="546" d="M51 178q0 110 87 190.5t212 80.5q101 0 161 -58.5t60 -148.5q0 -108 -88 -188.5t-213 -80.5q-100 0 -159.5 58.5t-59.5 146.5zM156 823q0 110 87 190.5t212 80.5q101 0 161 -58.5t60 -148.5q0 -108 -87.5 -188.5t-211.5 -80.5q-100 0 -160.5 59t-60.5 146z" />
<glyph unicode=";" horiz-adv-x="546" d="M2 -176q51 28 103.5 74.5t78.5 87.5q-65 25 -99 76.5t-34 115.5q0 110 87 190.5t212 80.5q99 0 159 -57.5t60 -158.5q0 -159 -113.5 -330t-305.5 -284zM158 823q0 110 87 190.5t212 80.5q101 0 161 -58.5t60 -148.5q0 -108 -88 -188.5t-213 -80.5q-100 0 -159.5 58.5 t-59.5 146.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1089" d="M156 498l45 274l1016 418l-66 -381l-504 -182l443 -183l-60 -362z" />
<glyph unicode="=" horiz-adv-x="1099" d="M63 254l56 330h1059l-56 -330h-1059zM133 668l55 329h1059l-55 -329h-1059z" />
<glyph unicode="&#x3e;" horiz-adv-x="1089" d="M86 82l66 383l503 182l-442 182l59 361l873 -418l-45 -274z" />
<glyph unicode="?" horiz-adv-x="1036" d="M205 1141q23 43 50 81t75.5 84t105 78t140.5 54t180 22q199 0 321.5 -97.5t122.5 -258.5q0 -65 -21 -121.5t-54.5 -98t-74.5 -78t-83.5 -68t-79 -61t-62.5 -63.5t-32 -69l-13 -68h-452l16 92q12 70 55.5 134t96.5 109t104 84t85 75.5t34 68.5q0 23 -13.5 38.5t-39.5 15.5 q-37 0 -70.5 -31.5t-54.5 -79.5zM248 178q0 110 87 190.5t212 80.5q102 0 162.5 -58.5t60.5 -148.5q0 -108 -88 -188.5t-213 -80.5q-100 0 -160.5 59t-60.5 146z" />
<glyph unicode="@" horiz-adv-x="1916" d="M131 408q0 209 87 392.5t232.5 310.5t339.5 200t404 73q182 0 331 -51t247 -140t151 -207.5t53 -254.5q0 -172 -73 -311t-200.5 -217t-285.5 -78q-73 0 -134.5 33t-88.5 84q-40 -47 -122.5 -83t-172.5 -36q-154 0 -242 94.5t-88 245.5q0 127 54 235t152.5 173t219.5 65 q49 0 98 -17.5t72 -44.5l9 50h350l-76 -449q-13 -84 47 -84q56 0 103 81.5t47 223.5q0 193 -134 307.5t-362 114.5q-187 0 -344.5 -89t-249.5 -243.5t-92 -336.5q0 -219 141.5 -348.5t378.5 -129.5q239 0 436 86l58 -233q-98 -56 -241 -88.5t-298 -32.5q-187 0 -339 51.5 t-254.5 144.5t-158 223.5t-55.5 285.5zM950 504q0 -41 17.5 -67.5t46.5 -26.5q40 0 71 45t31 96q0 40 -16 68t-45 28q-42 0 -73.5 -42.5t-31.5 -100.5z" />
<glyph unicode="A" horiz-adv-x="1523" d="M10 0l735 1434h512l256 -1434h-518l-26 219h-336l-101 -219h-522zM780 584h162l-31 295z" />
<glyph unicode="B" horiz-adv-x="1273" d="M61 0l240 1434h621q220 0 344.5 -96t124.5 -255q0 -210 -201 -333q62 -37 96.5 -108t34.5 -148q0 -149 -79 -262.5t-216 -172.5t-311 -59h-654zM625 385h90q46 0 80 33.5t34 77.5q0 35 -22.5 58.5t-58.5 23.5h-91zM711 891h90q43 0 69.5 28.5t26.5 63.5q0 28 -18 48 t-52 20h-90z" />
<glyph unicode="C" horiz-adv-x="1312" d="M125 602q0 173 69.5 330.5t188.5 274t288.5 185t360.5 68.5q279 0 459 -141l-225 -410q-93 111 -250 111q-141 0 -241.5 -100.5t-100.5 -251.5q0 -116 67 -185t174 -69q153 0 297 112l84 -411q-221 -142 -512 -142q-299 0 -479 173t-180 456z" />
<glyph unicode="D" horiz-adv-x="1390" d="M61 0l240 1434h512q321 0 507.5 -160t186.5 -440q0 -247 -120.5 -437.5t-332.5 -293.5t-481 -103h-512zM633 410h31q140 0 224.5 100.5t84.5 282.5q0 115 -54 173t-151 58h-31z" />
<glyph unicode="E" horiz-adv-x="1017" d="M61 0l240 1434h895l-68 -404h-376l-21 -119h336l-61 -379h-336l-23 -129h377l-68 -403h-895z" />
<glyph unicode="F" horiz-adv-x="987" d="M61 0l240 1434h889l-68 -404h-370l-27 -166h336l-66 -383h-336l-79 -481h-519z" />
<glyph unicode="G" horiz-adv-x="1529" d="M125 602q0 139 44.5 269t127.5 236t194 185.5t251 123.5t292 44q385 0 570 -233l-340 -312q-102 105 -246 105q-141 0 -242.5 -101.5t-101.5 -256.5q0 -116 64.5 -186t176.5 -70q111 0 185 38l8 56h-162l58 346h626l-106 -637q-310 -236 -736 -236q-156 0 -282.5 47.5 t-209.5 132t-127 199.5t-44 250z" />
<glyph unicode="H" horiz-adv-x="1466" d="M61 0l240 1434h520l-86 -516h303l86 516h521l-240 -1434h-520l86 514h-303l-86 -514h-521z" />
<glyph unicode="I" horiz-adv-x="643" d="M61 0l240 1434h520l-239 -1434h-521z" />
<glyph unicode="J" horiz-adv-x="858" d="M27 25l116 391q59 -15 109 -15q91 0 104 78l160 955h522l-174 -1035q-69 -411 -553 -411q-147 0 -284 37z" />
<glyph unicode="K" horiz-adv-x="1431" d="M61 0l240 1434h520l-86 -521l332 521h567l-469 -684l244 -750h-578l-151 590l-98 -590h-521z" />
<glyph unicode="L" horiz-adv-x="1001" d="M61 0l240 1434h520l-172 -1031h393l-67 -403h-914z" />
<glyph unicode="M" horiz-adv-x="1875" d="M61 0l240 1434h651l90 -816l361 816h651l-239 -1434h-506l114 690l-237 -581h-459l-45 581l-115 -690h-506z" />
<glyph unicode="N" horiz-adv-x="1474" d="M61 0l240 1434h526l199 -674l113 674h514l-240 -1434h-526l-199 674l-113 -674h-514z" />
<glyph unicode="O" horiz-adv-x="1617" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q160 0 289.5 -48.5t214.5 -134t130.5 -200.5t45.5 -248q0 -174 -69 -331t-189.5 -273t-294.5 -184t-375 -68q-159 0 -288.5 48.5t-214.5 134t-131 200.5t-46 250zM672 649q0 -112 53 -180t152 -68 q138 0 223.5 110.5t85.5 272.5q0 111 -53.5 179.5t-151.5 68.5q-139 0 -224 -110.5t-85 -272.5z" />
<glyph unicode="P" horiz-adv-x="1206" d="M61 0l240 1434h602q216 0 335.5 -113.5t119.5 -298.5q0 -161 -79.5 -291.5t-220 -205t-313.5 -74.5h-108l-76 -451h-500zM700 827h48q50 0 88 35t38 92q0 40 -23.5 65t-64.5 25h-51z" />
<glyph unicode="Q" horiz-adv-x="1624" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q160 0 289.5 -48.5t214.5 -134t130.5 -200.5t45.5 -248q0 -249 -148 -464l101 -105l-345 -330l-127 129q-192 -86 -409 -86q-159 0 -288.5 48.5t-214.5 134t-131 200.5t-46 250zM672 649q0 -112 53 -180t152 -68 q138 0 223.5 110.5t85.5 272.5q0 111 -53.5 179.5t-151.5 68.5q-139 0 -224 -110.5t-85 -272.5z" />
<glyph unicode="R" horiz-adv-x="1294" d="M61 0l240 1434h637q219 0 341 -112.5t122 -287.5q0 -143 -86 -257t-211 -177l227 -600h-563l-125 463l-78 -463h-504zM707 844h61q52 0 86.5 35t34.5 79q0 35 -23.5 60.5t-64.5 25.5h-62z" />
<glyph unicode="S" horiz-adv-x="1181" d="M80 346l403 201q30 -69 74.5 -110.5t91.5 -41.5t47 41q0 24 -26 46.5t-67 44t-91 46.5t-100.5 59t-91.5 75t-67 102.5t-26 134.5q0 149 80.5 267t222.5 183.5t318 65.5q94 0 175 -23.5t135 -60t94.5 -82.5t60.5 -89.5t26 -82.5l-399 -209q-15 52 -52 91.5t-83 39.5 q-22 0 -35.5 -12t-13.5 -31q0 -28 35 -57t87 -53.5t113.5 -62t113.5 -80t87 -110.5t35 -149q0 -228 -178 -372t-463 -144q-98 0 -180.5 25t-137 63.5t-95.5 90t-62.5 99t-30.5 95.5z" />
<glyph unicode="T" horiz-adv-x="1142" d="M203 1030l67 404h1084l-68 -404h-282l-173 -1030h-520l172 1030h-280z" />
<glyph unicode="U" horiz-adv-x="1445" d="M156 563l145 871h520l-147 -883q-3 -20 -4 -38q0 -45 23 -73q31 -39 95 -39q66 0 111 39t57 111l148 883h520l-145 -871q-44 -266 -245.5 -428t-516.5 -162q-158 0 -276 43.5t-187 122t-94 186.5q-14 59 -14 124q0 55 10 114z" />
<glyph unicode="V" horiz-adv-x="1593" d="M254 1434h563l86 -840l369 840h547l-760 -1434h-524z" />
<glyph unicode="W" horiz-adv-x="2166" d="M254 1434h555l2 -789l307 789h410l45 -789l264 789h555l-612 -1434h-523l-49 752l-299 -752h-522z" />
<glyph unicode="X" horiz-adv-x="1523" d="M12 0l570 719l-334 715h583l105 -351l215 351h600l-561 -715l326 -719h-549l-131 387l-250 -387h-574z" />
<glyph unicode="Y" horiz-adv-x="1449" d="M256 1434h547l76 -408l211 408h583l-586 -822l-102 -612h-520l102 612z" />
<glyph unicode="Z" horiz-adv-x="1277" d="M27 0l45 268l667 766h-524l66 400h1204l-45 -267l-672 -764h551l-68 -403h-1224z" />
<glyph unicode="[" horiz-adv-x="700" d="M-29 -416l353 2110h622l-59 -359h-162l-233 -1392h161l-59 -359h-623z" />
<glyph unicode="\" horiz-adv-x="997" d="M246 1536h409l320 -1757h-410z" />
<glyph unicode="]" horiz-adv-x="700" d="M-33 -416l60 359h161l234 1392h-162l59 359h623l-352 -2110h-623z" />
<glyph unicode="^" horiz-adv-x="1230" d="M150 643l518 791h376l252 -791h-426l-88 320l-194 -320h-438z" />
<glyph unicode="_" horiz-adv-x="1296" d="M-49 -369l53 324h1272l-53 -324h-1272z" />
<glyph unicode="`" horiz-adv-x="999" d="M446 1493h474l90 -319h-363z" />
<glyph unicode="a" horiz-adv-x="1138" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q1 5 1 10q0 28 -29 46q-34 20 -101 21q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q96 0 171.5 -17.5t125.5 -48.5t85 -74.5t48.5 -96t17.5 -112.5q1 -13 0 -26q0 -48 -10 -98l-108 -645 h-400l-8 72q-120 -99 -280 -99q-147 0 -238.5 74t-91.5 219zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5z" />
<glyph unicode="b" horiz-adv-x="1310" d="M51 0l248 1479h467l-74 -445q42 32 115.5 57t144.5 25q205 0 315.5 -125t110.5 -332q0 -192 -73 -347t-214.5 -247t-328.5 -92q-82 0 -148.5 25.5t-105.5 64.5l-10 -63h-447zM592 502q0 -70 31.5 -114t91.5 -44q83 0 135.5 69t52.5 177q0 71 -32 115.5t-89 44.5 q-84 0 -137 -69t-53 -179z" />
<glyph unicode="c" horiz-adv-x="1021" d="M111 465q0 137 58 259t155.5 208t229.5 136t277 50q96 0 184 -27.5t146 -72.5l-162 -350q-75 75 -178 75q-102 0 -171.5 -67.5t-69.5 -159.5q0 -73 47.5 -120.5t128.5 -47.5q120 0 207 80l47 -354q-167 -101 -359 -101q-239 0 -389.5 137.5t-150.5 354.5z" />
<glyph unicode="d" horiz-adv-x="1300" d="M106 434q0 192 73 346t214 245t328 91q71 0 137 -25t98 -57l74 445h467l-248 -1479h-446l10 63q-121 -90 -283 -90q-205 0 -314.5 126t-109.5 335zM580 500q0 -74 31 -115t89 -41q86 0 138.5 73.5t52.5 176.5q0 73 -32.5 114.5t-90.5 41.5q-81 0 -134.5 -72.5 t-53.5 -177.5z" />
<glyph unicode="e" horiz-adv-x="1214" d="M113 471q0 130 53.5 249.5t144 206.5t217.5 139t267 52q239 0 369 -127.5t130 -339.5q0 -49 -8.5 -102.5t-17.5 -82.5l-8 -28h-695q3 -51 45 -82.5t107 -31.5q116 0 201 77l286 -172q-30 -31 -52.5 -52.5t-80.5 -63.5t-115 -69t-144 -49t-179 -22q-125 0 -225 39 t-164 107.5t-97.5 158t-33.5 193.5zM621 672h223q1 7 1 14q-1 36 -23 62q-26 30 -74 30q-45 0 -80 -30.5t-47 -75.5z" />
<glyph unicode="f" horiz-adv-x="770" d="M127 0l125 750h-109l58 340h110q17 102 70 182t129.5 128t166 72.5t185.5 24.5q97 0 152 -14l-58 -344q-50 8 -84 8q-40 0 -68.5 -17t-31.5 -40h156l-58 -340h-151l-125 -750h-467z" />
<glyph unicode="g" horiz-adv-x="1302" d="M109 457q0 139 49.5 262.5t133 210t196 136.5t235.5 50q83 0 149 -23.5t105 -62.5l10 60h447l-158 -938q-24 -142 -85.5 -248.5t-153 -170.5t-202.5 -95t-243 -31q-120 0 -258 32t-215 74l178 318q25 -12 63.5 -25t107.5 -27t130 -14q74 0 132 24.5t65 71.5l4 27 q-110 -74 -260 -74q-193 0 -311.5 119.5t-118.5 323.5zM580 506q0 -72 33 -113t94 -41q82 0 133 72.5t51 171.5q0 71 -32 112.5t-87 41.5q-83 0 -137.5 -71t-54.5 -173z" />
<glyph unicode="h" horiz-adv-x="1196" d="M51 0l248 1479h467l-82 -482q48 46 123.5 82.5t155.5 36.5q179 0 255 -112q53 -79 54 -198q0 -51 -10 -110l-117 -696h-467l108 643q2 13 2 24q0 29 -13 47q-18 25 -54 25q-38 0 -63 -28t-31 -68l-109 -643h-467z" />
<glyph unicode="i" d="M51 0l182 1090h467l-182 -1090h-467zM252 1313q0 110 84 187t213 77q102 0 160.5 -55.5t58.5 -143.5q0 -109 -84.5 -185.5t-212.5 -76.5q-102 0 -160.5 55.5t-58.5 141.5z" />
<glyph unicode="j" d="M-162 -377l56 328q28 -4 59 -4q40 0 68 21t34 59l178 1063h467l-180 -1076q-24 -146 -100.5 -238t-188.5 -130.5t-266 -38.5q-27 0 -70 5.5t-57 10.5zM252 1313q0 110 84 187t213 77q101 0 159 -55.5t58 -143.5q0 -109 -84 -185.5t-211 -76.5q-102 0 -160.5 55.5 t-58.5 141.5z" />
<glyph unicode="k" horiz-adv-x="1245" d="M51 0l248 1479h467l-127 -762l240 373h512l-359 -521l195 -569h-514l-125 420l-70 -420h-467z" />
<glyph unicode="l" d="M51 0l248 1479h467l-248 -1479h-467z" />
<glyph unicode="m" horiz-adv-x="1759" d="M51 0l182 1090h447l-14 -84q53 46 129.5 78t148.5 32q171 0 240 -110q141 110 303 110q78 0 141 -22t103.5 -60.5t66.5 -91.5q28 -53 32 -115q2 -21 1 -42q0 -43 -6 -89l-117 -696h-467l111 666q6 35 -9.5 54t-44.5 19q-27 0 -48 -19.5t-27 -53.5l-111 -666h-461l111 666 q6 35 -10 54t-45 19q-26 0 -49 -19.5t-29 -53.5l-111 -666h-467z" />
<glyph unicode="n" horiz-adv-x="1187" d="M51 0l182 1090h447l-14 -91q49 48 125 82.5t161 34.5q178 0 256 -113q55 -80 55 -199q0 -50 -10 -108l-116 -696h-467l108 643q2 14 2 27q0 28 -11 44q-16 24 -52 25q-37 0 -60 -27.5t-30 -68.5l-109 -643h-467z" />
<glyph unicode="o" horiz-adv-x="1286" d="M109 463q0 136 55.5 257.5t151 208.5t229.5 138t284 51q242 0 385.5 -136t143.5 -351q0 -181 -94.5 -332t-260 -238.5t-366.5 -87.5q-242 0 -385 137.5t-143 352.5zM584 498q0 -71 29.5 -113.5t86.5 -42.5q82 0 133.5 73t51.5 179q0 72 -30.5 114t-86.5 42 q-82 0 -133 -72t-51 -180z" />
<glyph unicode="p" horiz-adv-x="1300" d="M-12 -375l245 1465h447l-12 -64q53 40 127.5 65t156.5 25q205 0 314.5 -126t109.5 -335q0 -192 -73 -346t-214.5 -245t-328.5 -91q-70 0 -136 25t-98 57l-71 -430h-467zM590 496q0 -72 33 -114t90 -42q81 0 135.5 72.5t54.5 177.5q0 74 -31.5 114.5t-89.5 40.5 q-86 0 -139 -73.5t-53 -175.5z" />
<glyph unicode="q" horiz-adv-x="1300" d="M106 430q0 192 73.5 347t215 247t328.5 92q82 0 147 -25t105 -65l12 64h447l-246 -1465h-467l72 430q-42 -32 -117 -57t-146 -25q-205 0 -314.5 124.5t-109.5 332.5zM582 500q0 -72 30.5 -116t87.5 -44q84 0 137.5 69t53.5 179q0 70 -31.5 113.5t-91.5 43.5 q-83 0 -134.5 -68.5t-51.5 -176.5z" />
<glyph unicode="r" horiz-adv-x="817" d="M51 0l182 1090h457l-20 -119q45 54 125.5 99.5t169.5 45.5l-70 -422q-43 15 -80 15q-77 0 -136.5 -54t-74.5 -147l-86 -508h-467z" />
<glyph unicode="s" horiz-adv-x="1003" d="M55 160l232 264q46 -38 124.5 -71.5t127.5 -33.5t49 25q0 12 -14 20t-50 19q-55 17 -92 31t-89 42.5t-83.5 60t-54.5 82t-23 110.5q0 120 70.5 214t189.5 144.5t263 50.5q149 0 252 -50t165 -122l-262 -250q-34 35 -82 58.5t-94 23.5q-51 0 -51 -30q0 -14 21 -25t75 -27 q48 -15 82.5 -28t82 -39t77.5 -55.5t51.5 -77t21.5 -103.5q0 -201 -135 -310.5t-381 -109.5q-146 0 -277 57.5t-196 129.5z" />
<glyph unicode="t" horiz-adv-x="825" d="M170 750l57 340h111l39 227h454l-38 -227h170l-58 -340h-170l-53 -322q-7 -39 20.5 -55.5t69.5 -16.5l76 6l-62 -358q-76 -20 -182 -20q-226 0 -326 108q-74 79 -74 217q0 50 9 107l55 334h-98z" />
<glyph unicode="u" horiz-adv-x="1187" d="M115 377l118 713h467l-108 -644q-2 -14 -2 -27q0 -29 11 -45q16 -24 52 -24q37 0 60 27.5t30 68.5l109 644h467l-182 -1090h-447l15 84q-43 -43 -119 -77t-152 -34q-186 0 -268 106q-59 76 -60 197q0 47 9 101z" />
<glyph unicode="v" horiz-adv-x="1275" d="M197 1090h487l35 -553l219 553h500l-537 -1090h-520z" />
<glyph unicode="w" horiz-adv-x="1789" d="M199 1090h508l-21 -537l184 537h414l4 -541l162 541h508l-473 -1090h-502l-22 401l-152 -401h-504z" />
<glyph unicode="x" horiz-adv-x="1228" d="M-4 0l432 553l-246 537h502l61 -244l144 244h522l-428 -537l250 -553h-481l-84 256l-170 -256h-502z" />
<glyph unicode="y" horiz-adv-x="1277" d="M197 1090h489l41 -515l223 515h496l-793 -1457h-440l217 422z" />
<glyph unicode="z" horiz-adv-x="999" d="M29 0l41 240l430 499h-342l59 351h924l-41 -240l-436 -498h356l-59 -352h-932z" />
<glyph unicode="{" horiz-adv-x="870" d="M115 442l65 396q71 0 115 33.5t61 132.5l31 184q27 164 129 279.5t255 171t345 55.5l-59 -359q-96 0 -145 -37.5t-62 -109.5l-47 -277q-17 -101 -86 -172.5t-152 -99.5q74 -28 119.5 -99t28.5 -171l-47 -281q-11 -71 25 -108t132 -37l-59 -359q-293 0 -447 130.5 t-114 375.5l30 186q17 99 -15 132.5t-103 33.5z" />
<glyph unicode="|" horiz-adv-x="645" d="M8 -537l385 2302h447l-385 -2302h-447z" />
<glyph unicode="}" horiz-adv-x="870" d="M-33 -416l60 359q183 0 206 145l48 281q17 100 86 171t151 99q-74 28 -118 99.5t-27 172.5l45 277q12 72 -25 109.5t-133 37.5l59 359q293 0 447.5 -130.5t114.5 -375.5l-31 -184q-10 -43 -5 -73t13 -48t27.5 -28t38 -13.5t45.5 -3.5l-66 -396q-71 0 -115 -33.5 t-61 -132.5l-31 -186q-27 -164 -129 -279.5t-255 -171t-345 -55.5z" />
<glyph unicode="~" horiz-adv-x="1159" d="M90 393q34 206 155 328.5t292 122.5q62 0 150 -36t130 -36q30 0 53 19t29 55h377q-34 -206 -155 -328.5t-292 -122.5q-62 0 -150 36t-130 36q-30 0 -53 -19t-29 -55h-377z" />
<glyph unicode="&#xa1;" horiz-adv-x="671" d="M33 -305l194 956h435l-125 -956h-504zM227 887q0 107 88 187.5t211 80.5q102 0 163 -59t61 -146q0 -110 -88 -190t-216 -80q-99 0 -159 58.5t-60 148.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="1021" d="M133 608q0 240 164.5 418t413.5 223l30 185h303l-32 -193q103 -27 172 -80l-162 -350q-76 76 -178 76t-172 -68t-70 -160q0 -73 47.5 -120t128.5 -47q122 0 207 79l47 -354q-98 -60 -205 -82l-22 -135h-303l20 133q-178 40 -283.5 167.5t-105.5 307.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1230" d="M41 0l61 371h103l31 184h-95l54 315h94l22 134q35 204 193 330t399 126q91 0 166 -24t121.5 -60t80 -83.5t49 -89.5t21.5 -83l-419 -141q-11 90 -70 90q-27 0 -47.5 -20t-26.5 -56l-20 -123h291l-54 -315h-290l-31 -184h577l-61 -371h-1149z" />
<glyph unicode="&#xa5;" horiz-adv-x="1449" d="M248 276l45 275h264l12 70h-264l47 274h111l-207 539h547l76 -408l211 408h583l-387 -539h117l-47 -274h-266l-13 -70h266l-45 -275h-268l-45 -276h-520l45 276h-262z" />
<glyph unicode="&#xa7;" horiz-adv-x="1130" d="M14 -113l408 164q16 -60 45.5 -101.5t71.5 -41.5q21 0 37 13.5t16 39.5q0 38 -68 84l-211 141q-186 125 -186 301q0 95 51.5 174t153.5 132q-47 46 -74 111.5t-27 135.5q0 198 159.5 329.5t412.5 131.5q96 0 178 -27t134 -67t90.5 -91t57 -95t23.5 -82l-407 -164 q-38 143 -115 143q-22 0 -37.5 -13.5t-15.5 -39.5q0 -40 65 -84l211 -141q189 -127 189 -301q0 -95 -52 -174t-155 -132q47 -46 73.5 -111t26.5 -136q0 -198 -159 -329.5t-412 -131.5q-96 0 -177.5 27t-134 67t-91 91t-57 95t-24.5 82zM565 539q0 -16 21 -37l110 -60 q19 3 31 16t12 29q0 21 -20 37l-111 60q-19 -3 -31 -16t-12 -29z" />
<glyph unicode="&#xa8;" horiz-adv-x="999" d="M305 1313q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107zM754 1313q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107z" />
<glyph unicode="&#xa9;" horiz-adv-x="1628" d="M154 608q0 175 71 333.5t191 273t286 182t349 67.5q146 0 270 -48.5t209.5 -134t133.5 -204t48 -254.5q0 -230 -121 -426.5t-327.5 -311t-448.5 -114.5q-147 0 -271.5 49t-209.5 134.5t-132.5 202.5t-47.5 251zM426 637q0 -191 113 -309.5t307 -118.5q167 0 304 78.5 t214.5 214t77.5 299.5q0 189 -114 307.5t-308 118.5q-167 0 -303.5 -78t-213.5 -213t-77 -299zM545 662q0 123 62.5 227t169 164.5t229.5 60.5q201 0 286 -168l-237 -155q-27 61 -86 61q-52 0 -89.5 -45.5t-37.5 -114.5q0 -47 22.5 -75.5t59.5 -28.5q61 0 106 61l187 -157 q-142 -166 -343 -166q-148 0 -238.5 95.5t-90.5 240.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="864" d="M176 831q0 94 51 162t132 99.5t180 31.5q73 0 133 -26l4 24q5 27 -16.5 44.5t-67.5 17.5q-102 0 -213 -51l-88 221q77 50 186 80t215 30q98 0 169 -28t106 -76t48.5 -111.5t-1.5 -134.5l-80 -479h-299l-4 53q-90 -74 -209 -74q-109 0 -177.5 55t-68.5 162zM522 897 q0 -41 60 -41q26 0 46.5 13.5t20.5 39.5q0 19 -15.5 30t-37.5 11q-30 0 -52 -14t-22 -39z" />
<glyph unicode="&#xab;" horiz-adv-x="1210" d="M172 606l293 387h387l-291 -387l160 -387h-387zM629 606l293 387h387l-291 -387l160 -387h-387z" />
<glyph unicode="&#xad;" horiz-adv-x="829" d="M145 459l60 352h688l-59 -352h-689z" />
<glyph unicode="&#xae;" horiz-adv-x="944" d="M203 1024q0 134 66 248t182.5 181t257.5 67q181 0 288 -102t107 -261q0 -134 -65 -248t-183.5 -182t-265.5 -68q-176 0 -281.5 102.5t-105.5 262.5zM338 1040q0 -115 70 -187.5t192 -72.5q163 0 266 104t103 259q0 115 -71.5 184.5t-199.5 69.5q-154 0 -257 -102.5 t-103 -254.5zM432 874l76 443h209q66 0 104.5 -34t38.5 -89q0 -58 -26.5 -97t-67.5 -55l53 -168h-149l-41 142h-29l-25 -142h-143zM618 1126h48q18 0 30.5 11.5t12.5 31.5q0 13 -8.5 22t-22.5 9h-47z" />
<glyph unicode="&#xaf;" horiz-adv-x="919" d="M322 1186l49 299h673l-49 -299h-673z" />
<glyph unicode="&#xb0;" horiz-adv-x="833" d="M205 1034q0 109 59 203t163.5 150.5t229.5 56.5q152 0 242 -88.5t90 -222.5q0 -109 -59 -203t-163.5 -150.5t-229.5 -56.5q-152 0 -242 88.5t-90 222.5zM539 1067q0 -61 45 -61q33 0 52 28.5t19 65.5q0 61 -45 61q-33 0 -52 -28.5t-19 -65.5z" />
<glyph unicode="&#xb2;" horiz-adv-x="788" d="M143 614l21 111q9 50 38 96.5t67.5 81t83 66t87.5 56.5t78 48t56.5 45.5t21.5 42.5q0 13 -8.5 22t-24.5 9q-55 0 -84 -82l-256 76q13 32 32.5 64t55 69.5t79 65t107.5 46.5t138 19q133 0 226 -62.5t93 -164.5q0 -55 -24 -103.5t-60 -81.5t-78.5 -63t-79 -49.5t-62.5 -39 t-29 -34.5h280l-39 -238h-719z" />
<glyph unicode="&#xb3;" horiz-adv-x="778" d="M172 860l256 47q4 -24 22.5 -37.5t45.5 -13.5t46 16t19 39q0 17 -14 31t-43 14h-121l25 150l116 90h-254l39 238h647l-32 -191l-166 -127q67 -21 104 -75t37 -123q0 -102 -58 -176.5t-149 -109t-203 -34.5q-88 0 -152.5 24.5t-99 65t-50 83.5t-15.5 89z" />
<glyph unicode="&#xb4;" horiz-adv-x="999" d="M383 1174l197 319h483l-297 -319h-383z" />
<glyph unicode="&#xb6;" horiz-adv-x="1216" d="M201 981q0 126 67.5 230.5t181.5 163.5t250 59h320l-254 -1526h-371l123 735q-146 10 -231.5 103.5t-85.5 234.5zM793 -92l254 1526h372l-254 -1526h-372z" />
<glyph unicode="&#xb7;" horiz-adv-x="548" d="M123 608q0 110 87 190.5t212 80.5q101 0 161 -58.5t60 -148.5q0 -108 -88 -188.5t-213 -80.5q-100 0 -159.5 58.5t-59.5 146.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="919" d="M131 -449l84 197q32 -14 72 -14q26 0 42.5 12t16.5 31q0 15 -14.5 25t-40.5 10q-16 0 -31.5 -3t-23.5 -5l-7 -3l86 262h269l-45 -131q61 -12 96 -54.5t35 -102.5q0 -119 -94 -193t-252 -74q-95 0 -193 43z" />
<glyph unicode="&#xb9;" horiz-adv-x="657" d="M289 1364l233 70h287l-137 -820h-355l84 506l-82 -24z" />
<glyph unicode="&#xba;" horiz-adv-x="997" d="M203 979q0 134 72 245.5t196.5 175.5t271.5 64q181 0 290.5 -103t109.5 -263q0 -133 -72 -244.5t-197 -175.5t-274 -64q-179 0 -288 103t-109 262zM553 1008q0 -53 26 -87t68 -34q61 0 103.5 52.5t42.5 129.5q0 55 -26 89t-69 34q-60 0 -102.5 -53t-42.5 -131z" />
<glyph unicode="&#xbb;" horiz-adv-x="1210" d="M106 219l291 387l-161 387h387l163 -387l-292 -387h-388zM563 219l291 387l-162 387h387l164 -387l-293 -387h-387z" />
<glyph unicode="&#xbc;" horiz-adv-x="1816" d="M258 1364l234 70h286l-137 -820h-354l84 506l-82 -24zM268 -84l1022 1602h408l-1024 -1602h-406zM997 127l37 221l422 471h373l-76 -454h88l-39 -238h-88l-20 -127h-344l20 127h-373zM1294 365h115l27 159z" />
<glyph unicode="&#xbd;" horiz-adv-x="1845" d="M248 -84l1022 1602h407l-1024 -1602h-405zM258 1364l234 70h286l-137 -820h-354l84 506l-82 -24zM1063 0l20 111q11 58 48 111t84.5 89.5t101 72t97.5 60.5t73 52t29 51q0 13 -8.5 22t-24.5 9q-55 0 -84 -82l-256 75q13 32 32.5 64t55 69.5t78.5 65.5t107 47t138 19 q133 0 226.5 -63t93.5 -165q0 -65 -33 -120.5t-81.5 -92t-96.5 -65t-84 -52t-39 -40.5h281l-39 -238h-719z" />
<glyph unicode="&#xbe;" horiz-adv-x="1892" d="M172 860l256 47q4 -24 22.5 -37.5t45.5 -13.5t46 16t19 39q0 17 -14 31t-43 14h-121l25 150l116 90h-254l39 238h647l-32 -191l-166 -127q67 -21 104 -75t37 -123q0 -102 -58 -176.5t-149 -109t-203 -34.5q-88 0 -152.5 24.5t-99 65t-50 83.5t-15.5 89zM346 -84 l1022 1602h408l-1024 -1602h-406zM1075 127l37 221l424 471h373l-76 -454h88l-41 -238h-88l-20 -127h-345l21 127h-373zM1374 365h115l27 159z" />
<glyph unicode="&#xbf;" horiz-adv-x="1036" d="M23 29q0 65 21 121.5t54.5 98t74.5 78t83.5 68t79 61t62.5 63.5t32 69l12 67h453l-16 -92q-12 -70 -55.5 -134t-96.5 -109t-104 -84t-85 -75.5t-34 -68.5q0 -23 13.5 -38t39.5 -15q37 0 70.5 31.5t54.5 79.5l336 -158q-172 -320 -551 -320q-199 0 -321.5 98t-122.5 259z M453 891q0 108 87.5 188t213.5 80q100 0 160.5 -59t60.5 -146q0 -110 -87 -190t-212 -80q-102 0 -162.5 58.5t-60.5 148.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1523" d="M10 0l735 1434h512l256 -1434h-518l-26 219h-336l-101 -219h-522zM690 1837h473l90 -319h-362zM780 584h162l-31 295z" />
<glyph unicode="&#xc1;" horiz-adv-x="1523" d="M10 0l735 1434h512l256 -1434h-518l-26 219h-336l-101 -219h-522zM770 1518l197 319h483l-297 -319h-383zM780 584h162l-31 295z" />
<glyph unicode="&#xc2;" horiz-adv-x="1523" d="M10 0l735 1434h512l256 -1434h-518l-26 219h-336l-101 -219h-522zM575 1518l271 319h446l164 -319h-391l-35 79l-63 -79h-392zM780 584h162l-31 295z" />
<glyph unicode="&#xc3;" horiz-adv-x="1523" d="M10 0l735 1434h512l256 -1434h-518l-26 219h-336l-101 -219h-522zM578 1505q27 161 116.5 255.5t223.5 94.5q52 0 122.5 -29.5t96.5 -29.5q24 0 41.5 16.5t21.5 45.5h309q-27 -161 -116.5 -256t-223.5 -95q-52 0 -122.5 30t-96.5 30q-24 0 -41.5 -16.5t-21.5 -45.5h-309z M780 584h162l-31 295z" />
<glyph unicode="&#xc4;" horiz-adv-x="1523" d="M10 0l735 1434h512l256 -1434h-518l-26 219h-336l-101 -219h-522zM627 1657q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107zM780 584h162l-31 295zM1075 1657q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5 q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107z" />
<glyph unicode="&#xc5;" horiz-adv-x="1523" d="M10 0l735 1434h512l256 -1434h-518l-26 219h-336l-101 -219h-522zM780 584h162l-31 295zM788 1698q0 109 91.5 182.5t212.5 73.5q99 0 163 -55t64 -142q0 -110 -90 -183t-213 -73q-98 0 -163 55t-65 142zM987 1716q0 -26 14.5 -42.5t40.5 -16.5q34 0 58 24t24 58 q0 25 -15.5 42t-41.5 17q-32 0 -56 -24t-24 -58z" />
<glyph unicode="&#xc6;" horiz-adv-x="1880" d="M-27 0l1022 1434h1063l-67 -404h-377l-21 -119h336l-61 -379h-336l-23 -129h377l-67 -403h-895l37 219h-312l-141 -219h-535zM866 584h156l53 321z" />
<glyph unicode="&#xc7;" horiz-adv-x="1312" d="M125 602q0 173 69.5 330.5t188.5 274t288.5 185t360.5 68.5q279 0 459 -141l-225 -410q-93 111 -250 111q-141 0 -241.5 -100.5t-100.5 -251.5q0 -116 67 -185t174 -69q153 0 297 112l84 -411q-210 -134 -475 -142l-14 -41q61 -12 96 -54.5t35 -102.5q0 -119 -94 -193 t-252 -74q-95 0 -193 43l84 197q32 -14 72 -14q26 0 42.5 12t16.5 31q0 15 -14.5 25t-40.5 10q-16 0 -31 -3t-22 -5l-8 -3l65 201q-205 57 -321.5 215.5t-116.5 384.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1017" d="M61 0l240 1434h895l-68 -404h-376l-21 -119h336l-61 -379h-336l-23 -129h377l-68 -403h-895zM457 1837h473l90 -319h-363z" />
<glyph unicode="&#xc9;" horiz-adv-x="1017" d="M61 0l240 1434h895l-68 -404h-376l-21 -119h336l-61 -379h-336l-23 -129h377l-68 -403h-895zM524 1487l197 319h483l-297 -319h-383z" />
<glyph unicode="&#xca;" horiz-adv-x="1017" d="M61 0l240 1434h895l-68 -404h-376l-21 -119h336l-61 -379h-336l-23 -129h377l-68 -403h-895zM322 1518l270 319h446l164 -319h-391l-35 79l-63 -79h-391z" />
<glyph unicode="&#xcb;" horiz-adv-x="1017" d="M61 0l240 1434h895l-68 -404h-376l-21 -119h336l-61 -379h-336l-23 -129h377l-68 -403h-895zM373 1657q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107zM821 1657q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5 q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107z" />
<glyph unicode="&#xcc;" horiz-adv-x="643" d="M61 0l240 1434h520l-239 -1434h-521zM279 1837h473l90 -319h-363z" />
<glyph unicode="&#xcd;" horiz-adv-x="643" d="M61 0l240 1434h520l-239 -1434h-521zM338 1518l197 319h483l-297 -319h-383z" />
<glyph unicode="&#xce;" horiz-adv-x="643" d="M61 0l240 1434h520l-239 -1434h-521zM135 1518l271 319h446l164 -319h-391l-35 79l-64 -79h-391z" />
<glyph unicode="&#xcf;" horiz-adv-x="643" d="M61 0l240 1434h520l-239 -1434h-521zM193 1657q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61.5 -143t-162.5 -58q-79 0 -121 41t-42 107zM641 1657q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107z" />
<glyph unicode="&#xd0;" horiz-adv-x="1478" d="M150 0l90 539h-88l59 358h88l90 537h512q321 0 507.5 -160t186.5 -440q0 -247 -120 -437.5t-332 -293.5t-481 -103h-512zM721 410h31q140 0 224.5 100.5t84.5 282.5q0 114 -54.5 172.5t-150.5 58.5h-31l-22 -127h125l-60 -358h-125z" />
<glyph unicode="&#xd1;" horiz-adv-x="1474" d="M61 0l240 1434h526l199 -674l113 674h514l-240 -1434h-526l-199 674l-113 -674h-514zM580 1483q27 161 116.5 255.5t223.5 94.5q52 0 122.5 -29.5t96.5 -29.5q24 0 41.5 16t21.5 45h309q-27 -161 -116.5 -255.5t-223.5 -94.5q-52 0 -122.5 29.5t-96.5 29.5 q-24 0 -41.5 -16t-21.5 -45h-309z" />
<glyph unicode="&#xd2;" horiz-adv-x="1617" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q160 0 289.5 -48.5t214.5 -134t130.5 -200.5t45.5 -248q0 -174 -69 -331t-189.5 -273t-294.5 -184t-375 -68q-159 0 -288.5 48.5t-214.5 134t-131 200.5t-46 250zM672 649q0 -112 53 -180t152 -68 q138 0 223.5 110.5t85.5 272.5q0 111 -53.5 179.5t-151.5 68.5q-139 0 -224 -110.5t-85 -272.5zM743 1837h474l90 -319h-363z" />
<glyph unicode="&#xd3;" horiz-adv-x="1617" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q160 0 289.5 -48.5t214.5 -134t130.5 -200.5t45.5 -248q0 -174 -69 -331t-189.5 -273t-294.5 -184t-375 -68q-159 0 -288.5 48.5t-214.5 134t-131 200.5t-46 250zM672 649q0 -112 53 -180t152 -68 q138 0 223.5 110.5t85.5 272.5q0 111 -53.5 179.5t-151.5 68.5q-139 0 -224 -110.5t-85 -272.5zM852 1518l197 319h483l-297 -319h-383z" />
<glyph unicode="&#xd4;" horiz-adv-x="1617" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q160 0 289.5 -48.5t214.5 -134t130.5 -200.5t45.5 -248q0 -174 -69 -331t-189.5 -273t-294.5 -184t-375 -68q-159 0 -288.5 48.5t-214.5 134t-131 200.5t-46 250zM635 1518l270 319h447l164 -319h-392l-34 79 l-64 -79h-391zM672 649q0 -112 53 -180t152 -68q138 0 223.5 110.5t85.5 272.5q0 111 -53.5 179.5t-151.5 68.5q-139 0 -224 -110.5t-85 -272.5z" />
<glyph unicode="&#xd5;" horiz-adv-x="1617" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q160 0 289.5 -48.5t214.5 -134t130.5 -200.5t45.5 -248q0 -174 -69 -331t-189.5 -273t-294.5 -184t-375 -68q-159 0 -288.5 48.5t-214.5 134t-131 200.5t-46 250zM623 1505q27 161 116.5 255.5t223.5 94.5 q52 0 122.5 -29.5t96.5 -29.5q24 0 41.5 16.5t21.5 45.5h309q-27 -161 -116.5 -256t-223.5 -95q-52 0 -122.5 30t-96.5 30q-24 0 -41.5 -16.5t-21.5 -45.5h-309zM672 649q0 -112 53 -180t152 -68q138 0 223.5 110.5t85.5 272.5q0 111 -53.5 179.5t-151.5 68.5 q-139 0 -224 -110.5t-85 -272.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1617" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q160 0 289.5 -48.5t214.5 -134t130.5 -200.5t45.5 -248q0 -174 -69 -331t-189.5 -273t-294.5 -184t-375 -68q-159 0 -288.5 48.5t-214.5 134t-131 200.5t-46 250zM672 649q0 -112 53 -180t152 -68 q138 0 223.5 110.5t85.5 272.5q0 111 -53.5 179.5t-151.5 68.5q-139 0 -224 -110.5t-85 -272.5zM672 1640q0 83 62 143t161 60q79 0 121.5 -42t42.5 -107q0 -85 -61 -143t-162 -58q-79 0 -121.5 40.5t-42.5 106.5zM1120 1640q0 83 62 143t161 60q79 0 121.5 -42t42.5 -107 q0 -85 -61 -143t-162 -58q-79 0 -121.5 40.5t-42.5 106.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1110" d="M86 354l326 281l-232 280l299 252l226 -278l315 278l229 -262l-325 -280l219 -271l-301 -252l-211 269l-318 -279z" />
<glyph unicode="&#xd8;" horiz-adv-x="1617" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q192 0 340 -71l120 122l207 -194l-106 -107q119 -162 119 -381q0 -174 -69 -331t-189.5 -273t-294.5 -184t-375 -68q-189 0 -342 70l-121 -121l-205 195l105 106q-117 163 -117 383zM672 657l362 369q-24 6 -53 6 q-137 0 -222 -108.5t-87 -266.5zM821 408q28 -7 56 -7q135 0 220.5 107t88.5 266z" />
<glyph unicode="&#xd9;" horiz-adv-x="1445" d="M156 563l145 871h520l-147 -883q-12 -72 19 -111t95 -39q66 0 111 39t57 111l148 883h520l-145 -871q-44 -266 -245.5 -428t-516.5 -162q-158 0 -276 43.5t-187 122t-94 186.5t-4 238zM657 1837h473l91 -319h-363z" />
<glyph unicode="&#xda;" horiz-adv-x="1445" d="M156 563l145 871h520l-147 -883q-12 -72 19 -111t95 -39q66 0 111 39t57 111l148 883h520l-145 -871q-44 -266 -245.5 -428t-516.5 -162q-158 0 -276 43.5t-187 122t-94 186.5t-4 238zM733 1518l197 319h483l-297 -319h-383z" />
<glyph unicode="&#xdb;" horiz-adv-x="1445" d="M156 563l145 871h520l-147 -883q-12 -72 19 -111t95 -39q66 0 111 39t57 111l148 883h520l-145 -871q-44 -266 -245.5 -428t-516.5 -162q-158 0 -276 43.5t-187 122t-94 186.5t-4 238zM539 1518l270 319h446l164 -319h-391l-35 79l-63 -79h-391z" />
<glyph unicode="&#xdc;" horiz-adv-x="1445" d="M156 563l145 871h520l-147 -883q-12 -72 19 -111t95 -39q66 0 111 39t57 111l148 883h520l-145 -871q-44 -266 -245.5 -428t-516.5 -162q-158 0 -276 43.5t-187 122t-94 186.5t-4 238zM594 1640q0 83 62 143t161 60q79 0 121.5 -42t42.5 -107q0 -85 -61 -143t-162 -58 q-79 0 -121.5 40.5t-42.5 106.5zM1042 1640q0 83 62.5 143t161.5 60q79 0 121.5 -42t42.5 -107q0 -85 -61.5 -143t-162.5 -58q-79 0 -121.5 40.5t-42.5 106.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1449" d="M256 1434h547l76 -408l211 408h583l-586 -822l-102 -612h-520l102 612zM750 1487l196 319h484l-297 -319h-383z" />
<glyph unicode="&#xde;" horiz-adv-x="1206" d="M61 0l240 1434h520l-37 -222h82q214 0 334.5 -116t120.5 -295q0 -161 -79.5 -291t-219.5 -203.5t-313 -73.5h-86l-39 -233h-523zM688 625h35q42 0 76.5 33.5t34.5 82.5q0 35 -22 58.5t-52 23.5h-39z" />
<glyph unicode="&#xdf;" horiz-adv-x="1218" d="M51 0l185 1104q14 85 53 153t93 112t124 73.5t142.5 42t152.5 12.5q225 0 356 -91.5t131 -240.5q0 -183 -162 -278q69 -47 113.5 -127.5t44.5 -186.5q0 -120 -43.5 -222t-129 -181.5t-225 -124.5t-319.5 -45l58 342q80 0 133 59.5t53 137.5q0 59 -34 101t-95 42l53 326 q54 0 80.5 24.5t26.5 61.5q0 24 -17 39.5t-47 15.5q-35 0 -55 -21t-25 -49l-180 -1079h-467z" />
<glyph unicode="&#xe0;" horiz-adv-x="1138" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q96 0 171.5 -17.5t125.5 -48.5t85 -74.5t48.5 -96t17 -112.5t-9.5 -124l-108 -645h-400l-8 72q-120 -99 -280 -99 q-147 0 -238.5 74t-91.5 219zM453 1493h473l90 -319h-363zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="1138" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q96 0 171.5 -17.5t125.5 -48.5t85 -74.5t48.5 -96t17 -112.5t-9.5 -124l-108 -645h-400l-8 72q-120 -99 -280 -99 q-147 0 -238.5 74t-91.5 219zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5zM535 1174l196 319h483l-296 -319h-383z" />
<glyph unicode="&#xe2;" horiz-adv-x="1138" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q96 0 171.5 -17.5t125.5 -48.5t85 -74.5t48.5 -96t17 -112.5t-9.5 -124l-108 -645h-400l-8 72q-120 -99 -280 -99 q-147 0 -238.5 74t-91.5 219zM340 1174l270 319h447l164 -319h-392l-34 79l-64 -79h-391zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5z" />
<glyph unicode="&#xe3;" horiz-adv-x="1138" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q96 0 171.5 -17.5t125.5 -48.5t85 -74.5t48.5 -96t17 -112.5t-9.5 -124l-108 -645h-400l-8 72q-120 -99 -280 -99 q-147 0 -238.5 74t-91.5 219zM330 1161q27 161 116.5 255.5t223.5 94.5q52 0 122.5 -29.5t96.5 -29.5q24 0 41.5 16t21.5 45h310q-27 -161 -116.5 -255.5t-223.5 -94.5q-52 0 -123 30t-97 30q-24 0 -41.5 -16.5t-21.5 -45.5h-309zM535 352q0 -27 23 -43t58 -16q39 0 70 21 t31 55q0 27 -22.5 43t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="1138" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q96 0 171.5 -17.5t125.5 -48.5t85 -74.5t48.5 -96t17 -112.5t-9.5 -124l-108 -645h-400l-8 72q-120 -99 -280 -99 q-147 0 -238.5 74t-91.5 219zM385 1313q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5zM834 1313q0 83 62 143 t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61.5 -143t-162.5 -58q-79 0 -121 41t-42 107z" />
<glyph unicode="&#xe5;" horiz-adv-x="1138" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q96 0 171.5 -17.5t125.5 -48.5t85 -74.5t48.5 -96t17 -112.5t-9.5 -124l-108 -645h-400l-8 72q-120 -99 -280 -99 q-147 0 -238.5 74t-91.5 219zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5zM561 1354q0 109 91 182.5t212 73.5q99 0 163.5 -55t64.5 -142q0 -110 -90.5 -183t-213.5 -73q-98 0 -162.5 55t-64.5 142zM760 1372 q0 -26 14.5 -42.5t40.5 -16.5q34 0 58 24t24 58q0 25 -15.5 42t-41.5 17q-32 0 -56 -24t-24 -58z" />
<glyph unicode="&#xe6;" horiz-adv-x="1818" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q107 0 188.5 -26.5t126.5 -71.5q63 44 156 71t182 27q227 0 354 -128t127 -339q0 -45 -8.5 -98.5t-17.5 -84.5l-8 -30 h-695q3 -50 46.5 -81t107.5 -31q59 0 112.5 21.5t86.5 53.5l286 -172q-18 -26 -60.5 -64t-112 -83.5t-171 -77t-209.5 -31.5q-282 0 -413 181q-162 -181 -418 -181q-96 0 -172.5 30t-126 98t-49.5 165zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43 t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5zM1225 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1021" d="M111 465q0 137 58 259t155.5 208t229.5 136t277 50q96 0 184 -27.5t146 -72.5l-162 -350q-75 75 -178 75q-102 0 -171.5 -67.5t-69.5 -159.5q0 -73 47.5 -120.5t128.5 -47.5q120 0 207 80l47 -354q-153 -91 -318 -99l-14 -43q61 -12 96 -54.5t35 -102.5q0 -119 -94 -193 t-252 -74q-95 0 -193 43l84 197q32 -14 72 -14q26 0 42.5 12t16.5 31q0 15 -14.5 25t-40.5 10q-16 0 -31 -3t-23 -5l-7 -3l67 207q-152 53 -238.5 173.5t-86.5 283.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1214" d="M113 471q0 130 53.5 249.5t144 206.5t217.5 139t267 52q239 0 369 -127.5t130 -339.5q0 -49 -8.5 -102.5t-17.5 -82.5l-8 -28h-695q3 -51 45 -82.5t107 -31.5q116 0 201 77l286 -172q-30 -31 -52.5 -52.5t-80.5 -63.5t-115 -69t-144 -49t-179 -22q-125 0 -225 39 t-164 107.5t-97.5 158t-33.5 193.5zM508 1493h473l90 -319h-362zM621 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="1214" d="M113 471q0 130 53.5 249.5t144 206.5t217.5 139t267 52q239 0 369 -127.5t130 -339.5q0 -49 -8.5 -102.5t-17.5 -82.5l-8 -28h-695q3 -51 45 -82.5t107 -31.5q116 0 201 77l286 -172q-30 -31 -52.5 -52.5t-80.5 -63.5t-115 -69t-144 -49t-179 -22q-125 0 -225 39 t-164 107.5t-97.5 158t-33.5 193.5zM561 1174l197 319h483l-297 -319h-383zM621 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5z" />
<glyph unicode="&#xea;" horiz-adv-x="1214" d="M113 471q0 130 53.5 249.5t144 206.5t217.5 139t267 52q239 0 369 -127.5t130 -339.5q0 -49 -8.5 -102.5t-17.5 -82.5l-8 -28h-695q3 -51 45 -82.5t107 -31.5q116 0 201 77l286 -172q-30 -31 -52.5 -52.5t-80.5 -63.5t-115 -69t-144 -49t-179 -22q-125 0 -225 39 t-164 107.5t-97.5 158t-33.5 193.5zM377 1174l270 319h447l163 -319h-391l-35 79l-63 -79h-391zM621 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="1214" d="M113 471q0 130 53.5 249.5t144 206.5t217.5 139t267 52q239 0 369 -127.5t130 -339.5q0 -49 -8.5 -102.5t-17.5 -82.5l-8 -28h-695q3 -51 45 -82.5t107 -31.5q116 0 201 77l286 -172q-30 -31 -52.5 -52.5t-80.5 -63.5t-115 -69t-144 -49t-179 -22q-125 0 -225 39 t-164 107.5t-97.5 158t-33.5 193.5zM434 1313q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107zM621 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5zM883 1313q0 83 62 143t161 60q78 0 121 -42.5 t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107z" />
<glyph unicode="&#xec;" d="M51 0l182 1090h467l-182 -1090h-467zM158 1493h473l90 -319h-363z" />
<glyph unicode="&#xed;" d="M51 0l182 1090h467l-182 -1090h-467zM225 1174l197 319h483l-297 -319h-383z" />
<glyph unicode="&#xee;" d="M41 1174l270 319h447l164 -319h-392l-34 79l-64 -79h-391zM51 0l182 1090h467l-182 -1090h-467z" />
<glyph unicode="&#xef;" d="M51 0l182 1090h467l-182 -1090h-467zM92 1313q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107zM541 1313q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107z" />
<glyph unicode="&#xf0;" horiz-adv-x="1253" d="M104 438q0 104 34 194t92.5 155.5t137.5 114.5t169 76t186 34q-14 39 -35 69l-346 -110l8 248l152 45q-29 31 -119 86l270 209q167 -64 283 -146l336 103l-12 -256l-123 -41q192 -243 192 -535q0 -104 -26.5 -204t-84 -192t-139.5 -162t-201 -111.5t-260 -41.5 q-121 0 -219.5 36.5t-162.5 99.5t-98 147.5t-34 181.5zM575 473q0 -61 26.5 -97t80.5 -36q76 0 119 62t43 149q0 59 -26.5 95t-78.5 36q-77 0 -120.5 -61.5t-43.5 -147.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1183" d="M51 0l182 1090h447l-14 -91q49 48 125 82.5t161 34.5q177 0 255.5 -113t45.5 -307l-116 -696h-467l108 643q7 47 -9 71.5t-52 24.5q-37 0 -60 -27.5t-30 -68.5l-109 -643h-467zM369 1161q27 161 116.5 255.5t223.5 94.5q52 0 122.5 -29.5t96.5 -29.5q24 0 41.5 16 t21.5 45h309q-27 -161 -116 -255.5t-223 -94.5q-52 0 -123 30t-97 30q-24 0 -41.5 -16.5t-21.5 -45.5h-309z" />
<glyph unicode="&#xf2;" horiz-adv-x="1302" d="M109 463q0 136 55.5 257.5t151 208.5t229.5 138t284 51q242 0 385.5 -136t143.5 -351q0 -181 -94.5 -332t-260 -238.5t-366.5 -87.5q-242 0 -385 137.5t-143 352.5zM524 1493h473l90 -319h-362zM584 498q0 -71 29.5 -113.5t86.5 -42.5q82 0 133.5 73t51.5 179 q0 72 -30.5 114t-86.5 42q-82 0 -133 -72t-51 -180z" />
<glyph unicode="&#xf3;" horiz-adv-x="1302" d="M109 463q0 136 55.5 257.5t151 208.5t229.5 138t284 51q242 0 385.5 -136t143.5 -351q0 -181 -94.5 -332t-260 -238.5t-366.5 -87.5q-242 0 -385 137.5t-143 352.5zM584 498q0 -71 29.5 -113.5t86.5 -42.5q82 0 133.5 73t51.5 179q0 72 -30.5 114t-86.5 42 q-82 0 -133 -72t-51 -180zM590 1174l196 319h484l-297 -319h-383z" />
<glyph unicode="&#xf4;" horiz-adv-x="1302" d="M109 463q0 136 55.5 257.5t151 208.5t229.5 138t284 51q242 0 385.5 -136t143.5 -351q0 -181 -94.5 -332t-260 -238.5t-366.5 -87.5q-242 0 -385 137.5t-143 352.5zM414 1174l270 319h446l164 -319h-391l-35 79l-63 -79h-391zM584 498q0 -71 29.5 -113.5t86.5 -42.5 q82 0 133.5 73t51.5 179q0 72 -30.5 114t-86.5 42q-82 0 -133 -72t-51 -180z" />
<glyph unicode="&#xf5;" horiz-adv-x="1302" d="M109 463q0 136 55.5 257.5t151 208.5t229.5 138t284 51q242 0 385.5 -136t143.5 -351q0 -181 -94.5 -332t-260 -238.5t-366.5 -87.5q-242 0 -385 137.5t-143 352.5zM410 1161q27 161 116.5 255.5t223.5 94.5q52 0 122.5 -29.5t96.5 -29.5q24 0 41.5 16t21.5 45h309 q-27 -161 -116.5 -255.5t-223.5 -94.5q-52 0 -122.5 30t-96.5 30q-24 0 -41.5 -16.5t-21.5 -45.5h-309zM584 498q0 -71 29.5 -113.5t86.5 -42.5q82 0 133.5 73t51.5 179q0 72 -30.5 114t-86.5 42q-82 0 -133 -72t-51 -180z" />
<glyph unicode="&#xf6;" horiz-adv-x="1302" d="M109 463q0 136 55.5 257.5t151 208.5t229.5 138t284 51q242 0 385.5 -136t143.5 -351q0 -181 -94.5 -332t-260 -238.5t-366.5 -87.5q-242 0 -385 137.5t-143 352.5zM463 1313q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41 t-42.5 107zM584 498q0 -71 29.5 -113.5t86.5 -42.5q82 0 133.5 73t51.5 179q0 72 -30.5 114t-86.5 42q-82 0 -133 -72t-51 -180zM911 1313q0 83 62.5 143t161.5 60q78 0 120.5 -42.5t42.5 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107z" />
<glyph unicode="&#xf7;" horiz-adv-x="1099" d="M98 469l56 332h1058l-55 -332h-1059zM383 203q0 91 72.5 159t165.5 68q73 0 121.5 -50.5t48.5 -123.5q0 -91 -72.5 -159t-165.5 -68q-73 0 -121.5 50.5t-48.5 123.5zM518 1014q0 91 73 159t165 68q74 0 122 -50.5t48 -123.5q0 -91 -72.5 -159t-165.5 -68 q-73 0 -121.5 50.5t-48.5 123.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1286" d="M86 104l98 86q-75 116 -75 273q0 136 55.5 257.5t151 208.5t229.5 138t284 51q188 0 322 -88l102 88l121 -139l-92 -80q76 -118 76 -268q0 -181 -94.5 -332t-260 -238.5t-366.5 -87.5q-188 0 -322 86l-108 -94zM586 539l229 200q-26 11 -47 11q-74 0 -123.5 -59 t-58.5 -152zM651 352q23 -10 49 -10q72 0 122 59t61 150z" />
<glyph unicode="&#xf9;" horiz-adv-x="1185" d="M115 377l118 713h467l-108 -644q-7 -47 9 -71.5t52 -24.5q37 0 60 27.5t30 68.5l109 644h467l-182 -1090h-447l15 84q-43 -43 -119 -77t-152 -34q-186 0 -268.5 106t-50.5 298zM489 1493h474l90 -319h-363z" />
<glyph unicode="&#xfa;" horiz-adv-x="1185" d="M115 377l118 713h467l-108 -644q-7 -47 9 -71.5t52 -24.5q37 0 60 27.5t30 68.5l109 644h467l-182 -1090h-447l15 84q-43 -43 -119 -77t-152 -34q-186 0 -268.5 106t-50.5 298zM541 1174l196 319h484l-297 -319h-383z" />
<glyph unicode="&#xfb;" horiz-adv-x="1185" d="M115 377l118 713h467l-108 -644q-7 -47 9 -71.5t52 -24.5q37 0 60 27.5t30 68.5l109 644h467l-182 -1090h-447l15 84q-43 -43 -119 -77t-152 -34q-186 0 -268.5 106t-50.5 298zM352 1174l271 319h446l164 -319h-391l-35 79l-64 -79h-391z" />
<glyph unicode="&#xfc;" horiz-adv-x="1185" d="M115 377l118 713h467l-108 -644q-7 -47 9 -71.5t52 -24.5q37 0 60 27.5t30 68.5l109 644h467l-182 -1090h-447l15 84q-43 -43 -119 -77t-152 -34q-186 0 -268.5 106t-50.5 298zM401 1313q0 83 62.5 143t161.5 60q78 0 120.5 -42.5t42.5 -107.5q0 -85 -61 -143t-162 -58 q-79 0 -121.5 41t-42.5 107zM850 1313q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107z" />
<glyph unicode="&#xfd;" horiz-adv-x="1277" d="M197 1090h489l41 -515l223 515h496l-793 -1457h-440l217 422zM616 1174l197 319h483l-297 -319h-383z" />
<glyph unicode="&#xfe;" horiz-adv-x="1300" d="M-12 -375l309 1854h467l-72 -437q114 74 260 74q205 0 314.5 -126t109.5 -335q0 -192 -73 -346t-214.5 -245t-328.5 -91q-70 0 -136 25t-98 57l-71 -430h-467zM590 496q0 -72 33 -114t90 -42q81 0 135.5 72.5t54.5 177.5q0 74 -31.5 114.5t-89.5 40.5q-86 0 -139 -73.5 t-53 -175.5z" />
<glyph unicode="&#xff;" horiz-adv-x="1277" d="M197 1090h489l41 -515l223 515h496l-793 -1457h-440l217 422zM453 1313q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61.5 -143t-162.5 -58q-79 0 -121 41t-42 107zM901 1313q0 83 62 143t161 60q78 0 121 -42.5t43 -107.5q0 -85 -61 -143t-162 -58 q-79 0 -121.5 41t-42.5 107z" />
<glyph unicode="&#x100;" horiz-adv-x="1523" d="M10 0l735 1434h512l256 -1434h-518l-26 219h-336l-101 -219h-522zM682 1530l49 299h674l-49 -299h-674zM780 584h162l-31 295z" />
<glyph unicode="&#x101;" horiz-adv-x="1138" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q96 0 171.5 -17.5t125.5 -48.5t85 -74.5t48.5 -96t17 -112.5t-9.5 -124l-108 -645h-400l-8 72q-120 -99 -280 -99 q-147 0 -238.5 74t-91.5 219zM459 1186l49 299h674l-49 -299h-674zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5z" />
<glyph unicode="&#x102;" horiz-adv-x="1523" d="M10 0l735 1434h512l256 -1434h-518l-26 219h-336l-101 -219h-522zM680 1851h346q-16 -92 27 -92q45 0 61 92h348q-27 -158 -147 -253t-305 -95q-183 0 -270 95t-60 253zM780 584h162l-31 295z" />
<glyph unicode="&#x103;" horiz-adv-x="1138" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q96 0 171.5 -17.5t125.5 -48.5t85 -74.5t48.5 -96t17 -112.5t-9.5 -124l-108 -645h-400l-8 72q-120 -99 -280 -99 q-147 0 -238.5 74t-91.5 219zM451 1507h346q-16 -92 26 -92q46 0 62 92h348q-27 -158 -147.5 -253t-305.5 -95q-183 0 -269.5 95t-59.5 253zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5z" />
<glyph unicode="&#x104;" horiz-adv-x="1523" d="M10 0l735 1434h512l256 -1434q-36 -14 -65 -42t-29 -60q0 -25 22 -37.5t54 -12.5l-45 -266q-106 -31 -186 -31q-258 0 -258 179q0 64 41 123t94 94.5t108 52.5h-254l-26 219h-336l-101 -219h-522zM780 584h162l-31 295z" />
<glyph unicode="&#x105;" horiz-adv-x="1138" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q96 0 171.5 -17.5t125.5 -48.5t85 -74.5t48.5 -96t17 -112.5t-9.5 -124l-108 -645q-36 -14 -65.5 -42t-29.5 -60 q0 -25 22 -37.5t54 -12.5l-45 -266q-106 -31 -186 -31q-258 0 -258 179q0 64 41 123t94 94.5t108 52.5h-135l-8 72q-120 -99 -280 -99q-147 0 -238.5 74t-91.5 219zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5z " />
<glyph unicode="&#x106;" horiz-adv-x="1312" d="M125 602q0 173 69.5 330.5t188.5 274t288.5 185t360.5 68.5q279 0 459 -141l-225 -410q-93 111 -250 111q-141 0 -241.5 -100.5t-100.5 -251.5q0 -116 67 -185t174 -69q153 0 297 112l84 -411q-221 -142 -512 -142q-299 0 -479 173t-180 456zM797 1518l196 319h484 l-297 -319h-383z" />
<glyph unicode="&#x107;" horiz-adv-x="1021" d="M111 465q0 137 58 259t155.5 208t229.5 136t277 50q96 0 184 -27.5t146 -72.5l-162 -350q-75 75 -178 75q-102 0 -171.5 -67.5t-69.5 -159.5q0 -73 47.5 -120.5t128.5 -47.5q120 0 207 80l47 -354q-167 -101 -359 -101q-239 0 -389.5 137.5t-150.5 354.5zM580 1174 l196 319h484l-297 -319h-383z" />
<glyph unicode="&#x108;" horiz-adv-x="1312" d="M125 602q0 173 69.5 330.5t188.5 274t288.5 185t360.5 68.5q279 0 459 -141l-225 -410q-93 111 -250 111q-141 0 -241.5 -100.5t-100.5 -251.5q0 -116 67 -185t174 -69q153 0 297 112l84 -411q-221 -142 -512 -142q-299 0 -479 173t-180 456zM637 1518l270 319h447 l164 -319h-392l-34 79l-64 -79h-391z" />
<glyph unicode="&#x109;" horiz-adv-x="1021" d="M111 465q0 137 58 259t155.5 208t229.5 136t277 50q96 0 184 -27.5t146 -72.5l-162 -350q-75 75 -178 75q-102 0 -171.5 -67.5t-69.5 -159.5q0 -73 47.5 -120.5t128.5 -47.5q120 0 207 80l47 -354q-167 -101 -359 -101q-239 0 -389.5 137.5t-150.5 354.5zM381 1174 l270 319h447l164 -319h-392l-34 79l-64 -79h-391z" />
<glyph unicode="&#x10a;" horiz-adv-x="1312" d="M125 602q0 173 69.5 330.5t188.5 274t288.5 185t360.5 68.5q279 0 459 -141l-225 -410q-93 111 -250 111q-141 0 -241.5 -100.5t-100.5 -251.5q0 -116 67 -185t174 -69q153 0 297 112l84 -411q-221 -142 -512 -142q-299 0 -479 173t-180 456zM860 1681q0 105 74 172.5 t194 67.5q94 0 145.5 -47t51.5 -127q0 -105 -73 -173.5t-193 -68.5q-93 0 -146 49t-53 127z" />
<glyph unicode="&#x10b;" horiz-adv-x="1021" d="M111 465q0 137 58 259t155.5 208t229.5 136t277 50q96 0 184 -27.5t146 -72.5l-162 -350q-75 75 -178 75q-102 0 -171.5 -67.5t-69.5 -159.5q0 -73 47.5 -120.5t128.5 -47.5q120 0 207 80l47 -354q-167 -101 -359 -101q-239 0 -389.5 137.5t-150.5 354.5zM647 1337 q0 105 74 172.5t194 67.5q94 0 145.5 -47t51.5 -127q0 -105 -73 -173.5t-193 -68.5q-93 0 -146 49t-53 127z" />
<glyph unicode="&#x10c;" horiz-adv-x="1312" d="M125 602q0 173 69.5 330.5t188.5 274t288.5 185t360.5 68.5q279 0 459 -141l-225 -410q-93 111 -250 111q-141 0 -241.5 -100.5t-100.5 -251.5q0 -116 67 -185t174 -69q153 0 297 112l84 -411q-221 -142 -512 -142q-299 0 -479 173t-180 456zM684 1837h391l35 -80l64 80 h391l-271 -319h-446z" />
<glyph unicode="&#x10d;" horiz-adv-x="1021" d="M111 465q0 137 58 259t155.5 208t229.5 136t277 50q96 0 184 -27.5t146 -72.5l-162 -350q-75 75 -178 75q-102 0 -171.5 -67.5t-69.5 -159.5q0 -73 47.5 -120.5t128.5 -47.5q120 0 207 80l47 -354q-167 -101 -359 -101q-239 0 -389.5 137.5t-150.5 354.5zM449 1493h391 l34 -80l64 80h391l-270 -319h-447z" />
<glyph unicode="&#x10e;" horiz-adv-x="1390" d="M61 0l240 1434h512q321 0 507.5 -160t186.5 -440q0 -247 -120.5 -437.5t-332.5 -293.5t-481 -103h-512zM467 1837h391l35 -80l63 80h392l-271 -319h-446zM633 410h31q140 0 224.5 100.5t84.5 282.5q0 115 -54 173t-151 58h-31z" />
<glyph unicode="&#x10f;" horiz-adv-x="1302" d="M106 434q0 192 73 346t214 245t328 91q71 0 137 -25t98 -57l74 445h467l-248 -1479h-446l10 63q-121 -90 -283 -90q-205 0 -314.5 126t-109.5 335zM580 500q0 -74 31 -115t89 -41q86 0 138.5 73.5t52.5 176.5q0 73 -32.5 114.5t-90.5 41.5q-81 0 -134.5 -72.5 t-53.5 -177.5zM1501 1122l74 357h319l-161 -357h-232z" />
<glyph unicode="&#x110;" horiz-adv-x="1478" d="M150 0l90 539h-88l59 358h88l90 537h512q321 0 507.5 -160t186.5 -440q0 -247 -120 -437.5t-332 -293.5t-481 -103h-512zM721 410h31q140 0 224.5 100.5t84.5 282.5q0 114 -54.5 172.5t-150.5 58.5h-31l-22 -127h125l-60 -358h-125z" />
<glyph unicode="&#x111;" horiz-adv-x="1349" d="M106 434q0 192 73 346t214 245t328 91q71 0 137 -25t98 -57l21 127h-203l43 252h203l10 66h467l-10 -66h74l-43 -252h-74l-195 -1161h-446l10 63q-118 -90 -283 -90q-205 0 -314.5 126t-109.5 335zM580 500q0 -74 31 -115t89 -41q86 0 138.5 73.5t52.5 176.5 q0 73 -32.5 114.5t-90.5 41.5q-81 0 -134.5 -72.5t-53.5 -177.5z" />
<glyph unicode="&#x112;" horiz-adv-x="1017" d="M61 0l240 1434h895l-68 -404h-376l-21 -119h336l-61 -379h-336l-23 -129h377l-68 -403h-895zM428 1530l49 299h674l-49 -299h-674z" />
<glyph unicode="&#x113;" horiz-adv-x="1214" d="M113 471q0 130 53.5 249.5t144 206.5t217.5 139t267 52q239 0 369 -127.5t130 -339.5q0 -49 -8.5 -102.5t-17.5 -82.5l-8 -28h-695q3 -51 45 -82.5t107 -31.5q116 0 201 77l286 -172q-30 -31 -52.5 -52.5t-80.5 -63.5t-115 -69t-144 -49t-179 -22q-125 0 -225 39 t-164 107.5t-97.5 158t-33.5 193.5zM483 1186l49 299h674l-49 -299h-674zM621 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5z" />
<glyph unicode="&#x114;" horiz-adv-x="1017" d="M61 0l240 1434h895l-68 -404h-376l-21 -119h336l-61 -379h-336l-23 -129h377l-68 -403h-895zM426 1851h346q-16 -92 27 -92q45 0 61 92h348q-27 -158 -147 -253t-305 -95q-183 0 -270 95t-60 253z" />
<glyph unicode="&#x115;" horiz-adv-x="1214" d="M113 471q0 130 53.5 249.5t144 206.5t217.5 139t267 52q239 0 369 -127.5t130 -339.5q0 -49 -8.5 -102.5t-17.5 -82.5l-8 -28h-695q3 -51 45 -82.5t107 -31.5q116 0 201 77l286 -172q-30 -31 -52.5 -52.5t-80.5 -63.5t-115 -69t-144 -49t-179 -22q-125 0 -225 39 t-164 107.5t-97.5 158t-33.5 193.5zM487 1507h347q-16 -92 26 -92q46 0 62 92h348q-27 -158 -147.5 -253t-305.5 -95q-183 0 -270 95t-60 253zM621 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5z" />
<glyph unicode="&#x116;" horiz-adv-x="1017" d="M61 0l240 1434h895l-68 -404h-376l-21 -119h336l-61 -379h-336l-23 -129h377l-68 -403h-895zM561 1681q0 105 74 172.5t194 67.5q94 0 145.5 -47t51.5 -127q0 -105 -73 -173.5t-193 -68.5q-93 0 -146 49t-53 127z" />
<glyph unicode="&#x117;" horiz-adv-x="1214" d="M113 471q0 130 53.5 249.5t144 206.5t217.5 139t267 52q239 0 369 -127.5t130 -339.5q0 -49 -8.5 -102.5t-17.5 -82.5l-8 -28h-695q3 -51 45 -82.5t107 -31.5q116 0 201 77l286 -172q-30 -31 -52.5 -52.5t-80.5 -63.5t-115 -69t-144 -49t-179 -22q-125 0 -225 39 t-164 107.5t-97.5 158t-33.5 193.5zM621 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5zM631 1337q0 105 74 172.5t194 67.5q94 0 145.5 -47t51.5 -127q0 -105 -73.5 -173.5t-193.5 -68.5q-93 0 -145.5 48.5t-52.5 127.5z" />
<glyph unicode="&#x118;" horiz-adv-x="1017" d="M61 0l240 1434h895l-68 -404h-376l-21 -119h336l-61 -379h-336l-23 -129h377l-68 -403q-41 -16 -70.5 -43t-29.5 -59q0 -25 22 -37.5t54 -12.5l-45 -266q-106 -31 -187 -31q-258 0 -258 179q0 49 23.5 95t61 80t78.5 58.5t81 36.5h-625z" />
<glyph unicode="&#x119;" horiz-adv-x="1214" d="M113 471q0 130 53.5 249.5t144 206.5t217.5 139t267 52q239 0 369 -127.5t130 -339.5q0 -49 -8.5 -102.5t-17.5 -82.5l-8 -28h-695q3 -51 45 -82.5t107 -31.5q116 0 201 77l286 -172q-33 -33 -53.5 -51.5t-72 -58.5t-114.5 -70t-135 -49q-36 -16 -63 -43t-27 -59 q0 -25 22 -37.5t54 -12.5l-45 -266q-106 -31 -186 -31q-258 0 -258 179q0 81 58 148.5t138 103.5q-196 31 -302.5 165t-106.5 324zM621 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5z" />
<glyph unicode="&#x11a;" horiz-adv-x="1017" d="M61 0l240 1434h895l-68 -404h-376l-21 -119h336l-61 -379h-336l-23 -129h377l-68 -403h-895zM375 1837h391l35 -80l63 80h391l-270 -319h-446z" />
<glyph unicode="&#x11b;" horiz-adv-x="1214" d="M113 471q0 130 53.5 249.5t144 206.5t217.5 139t267 52q239 0 369 -127.5t130 -339.5q0 -49 -8.5 -102.5t-17.5 -82.5l-8 -28h-695q3 -51 45 -82.5t107 -31.5q116 0 201 77l286 -172q-30 -31 -52.5 -52.5t-80.5 -63.5t-115 -69t-144 -49t-179 -22q-125 0 -225 39 t-164 107.5t-97.5 158t-33.5 193.5zM434 1493h391l35 -80l64 80h391l-271 -319h-446zM621 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5z" />
<glyph unicode="&#x11c;" horiz-adv-x="1529" d="M125 602q0 139 44.5 269t127.5 236t194 185.5t251 123.5t292 44q385 0 570 -233l-340 -312q-102 105 -246 105q-141 0 -242.5 -101.5t-101.5 -256.5q0 -116 64.5 -186t176.5 -70q111 0 185 38l8 56h-162l58 346h626l-106 -637q-310 -236 -736 -236q-156 0 -282.5 47.5 t-209.5 132t-127 199.5t-44 250zM639 1518l270 319h447l164 -319h-392l-34 79l-64 -79h-391z" />
<glyph unicode="&#x11d;" horiz-adv-x="1302" d="M109 457q0 139 49.5 262.5t133 210t196 136.5t235.5 50q83 0 149 -23.5t105 -62.5l10 60h447l-158 -938q-24 -142 -85.5 -248.5t-153 -170.5t-202.5 -95t-243 -31q-120 0 -258 32t-215 74l178 318q25 -12 63.5 -25t107.5 -27t130 -14q74 0 132 24.5t65 71.5l4 27 q-110 -74 -260 -74q-193 0 -311.5 119.5t-118.5 323.5zM408 1174l270 319h446l164 -319h-391l-35 79l-63 -79h-391zM580 506q0 -72 33 -113t94 -41q82 0 133 72.5t51 171.5q0 71 -32 112.5t-87 41.5q-83 0 -137.5 -71t-54.5 -173z" />
<glyph unicode="&#x11e;" horiz-adv-x="1529" d="M125 602q0 139 44.5 269t127.5 236t194 185.5t251 123.5t292 44q385 0 570 -233l-340 -312q-102 105 -246 105q-141 0 -242.5 -101.5t-101.5 -256.5q0 -116 64.5 -186t176.5 -70q111 0 185 38l8 56h-162l58 346h626l-106 -637q-310 -236 -736 -236q-156 0 -282.5 47.5 t-209.5 132t-127 199.5t-44 250zM745 1851h347q-16 -92 26 -92q46 0 62 92h348q-27 -158 -147.5 -253t-305.5 -95q-183 0 -270 95t-60 253z" />
<glyph unicode="&#x11f;" horiz-adv-x="1302" d="M109 457q0 139 49.5 262.5t133 210t196 136.5t235.5 50q83 0 149 -23.5t105 -62.5l10 60h447l-158 -938q-24 -142 -85.5 -248.5t-153 -170.5t-202.5 -95t-243 -31q-120 0 -258 32t-215 74l178 318q25 -12 63.5 -25t107.5 -27t130 -14q74 0 132 24.5t65 71.5l4 27 q-110 -74 -260 -74q-193 0 -311.5 119.5t-118.5 323.5zM512 1507h346q-16 -92 27 -92q45 0 61 92h348q-27 -158 -147 -253t-305 -95q-183 0 -270 95t-60 253zM580 506q0 -72 33 -113t94 -41q82 0 133 72.5t51 171.5q0 71 -32 112.5t-87 41.5q-83 0 -137.5 -71t-54.5 -173z " />
<glyph unicode="&#x120;" horiz-adv-x="1529" d="M125 602q0 139 44.5 269t127.5 236t194 185.5t251 123.5t292 44q385 0 570 -233l-340 -312q-102 105 -246 105q-141 0 -242.5 -101.5t-101.5 -256.5q0 -116 64.5 -186t176.5 -70q111 0 185 38l8 56h-162l58 346h626l-106 -637q-310 -236 -736 -236q-156 0 -282.5 47.5 t-209.5 132t-127 199.5t-44 250zM858 1681q0 105 74 172.5t194 67.5q94 0 145.5 -47t51.5 -127q0 -105 -73 -173.5t-193 -68.5q-93 0 -146 49t-53 127z" />
<glyph unicode="&#x121;" horiz-adv-x="1302" d="M109 457q0 139 49.5 262.5t133 210t196 136.5t235.5 50q83 0 149 -23.5t105 -62.5l10 60h447l-158 -938q-24 -142 -85.5 -248.5t-153 -170.5t-202.5 -95t-243 -31q-120 0 -258 32t-215 74l178 318q25 -12 63.5 -25t107.5 -27t130 -14q74 0 132 24.5t65 71.5l4 27 q-110 -74 -260 -74q-193 0 -311.5 119.5t-118.5 323.5zM580 506q0 -72 33 -113t94 -41q82 0 133 72.5t51 171.5q0 71 -32 112.5t-87 41.5q-83 0 -137.5 -71t-54.5 -173zM647 1337q0 105 74 172.5t194 67.5q94 0 145.5 -47t51.5 -127q0 -105 -73 -173.5t-193 -68.5 q-93 0 -146 49t-53 127z" />
<glyph unicode="&#x122;" horiz-adv-x="1529" d="M125 602q0 139 44.5 269t127.5 236t194 185.5t251 123.5t292 44q385 0 570 -233l-340 -312q-102 105 -246 105q-141 0 -242.5 -101.5t-101.5 -256.5q0 -116 64.5 -186t176.5 -70q111 0 185 38l8 56h-162l58 346h626l-106 -637q-310 -236 -736 -236q-156 0 -282.5 47.5 t-209.5 132t-127 199.5t-44 250zM561 -573q97 62 135 127q-57 15 -87.5 58t-30.5 99q0 93 72 159.5t169 66.5q77 0 126.5 -45.5t49.5 -131.5q0 -135 -90.5 -264.5t-232.5 -218.5z" />
<glyph unicode="&#x123;" horiz-adv-x="1302" d="M109 457q0 139 49.5 262.5t133 210t196 136.5t235.5 50q83 0 149 -23.5t105 -62.5l10 60h447l-158 -938q-24 -142 -85.5 -248.5t-153 -170.5t-202.5 -95t-243 -31q-120 0 -258 32t-215 74l178 318q25 -12 63.5 -25t107.5 -27t130 -14q74 0 132 24.5t65 71.5l4 27 q-110 -74 -260 -74q-193 0 -311.5 119.5t-118.5 323.5zM580 506q0 -72 33 -113t94 -41q82 0 133 72.5t51 171.5q0 71 -32 112.5t-87 41.5q-83 0 -137.5 -71t-54.5 -173zM670 1341q0 136 90.5 265.5t232.5 218.5l111 -150q-97 -62 -135 -127q57 -15 87.5 -58t30.5 -99 q0 -93 -72 -159.5t-169 -66.5q-77 0 -126.5 45t-49.5 131z" />
<glyph unicode="&#x124;" horiz-adv-x="1466" d="M61 0l240 1434h520l-86 -516h303l86 516h521l-240 -1434h-520l86 514h-303l-86 -514h-521zM551 1518l270 319h447l164 -319h-392l-34 79l-64 -79h-391z" />
<glyph unicode="&#x125;" horiz-adv-x="1196" d="M51 0l248 1479h467l-82 -482q48 46 123.5 82.5t155.5 36.5q179 0 255.5 -112.5t43.5 -307.5l-117 -696h-467l108 643q7 46 -11 71t-54 25q-38 0 -63 -28t-31 -68l-109 -643h-467zM434 1518l271 319h446l164 -319h-391l-35 79l-64 -79h-391z" />
<glyph unicode="&#x126;" horiz-adv-x="1466" d="M61 0l240 1434h520l-86 -516h303l86 516h521l-240 -1434h-520l86 514h-303l-86 -514h-521zM657 1530l50 299h673l-49 -299h-674z" />
<glyph unicode="&#x127;" horiz-adv-x="1249" d="M104 0l195 1161h-70l41 252h70l12 66h467l-12 -66h246l-41 -252h-248l-27 -164q48 46 123.5 82.5t155.5 36.5q179 0 255.5 -112.5t43.5 -307.5l-117 -696h-467l109 643q7 46 -11.5 71t-54.5 25q-38 0 -63 -28t-31 -68l-109 -643h-467z" />
<glyph unicode="&#x128;" horiz-adv-x="643" d="M61 0l240 1434h520l-239 -1434h-521zM137 1505q27 161 116.5 255.5t223.5 94.5q52 0 122.5 -29.5t96.5 -29.5q24 0 42 16.5t22 45.5h309q-27 -161 -116.5 -256t-223.5 -95q-52 0 -122.5 30t-96.5 30q-24 0 -42 -16.5t-22 -45.5h-309z" />
<glyph unicode="&#x129;" d="M43 1161q27 161 116.5 255.5t223.5 94.5q52 0 122.5 -29.5t96.5 -29.5q24 0 42 16.5t22 44.5h309q-27 -161 -116.5 -255.5t-223.5 -94.5q-52 0 -122.5 30t-96.5 30q-24 0 -42 -16.5t-22 -45.5h-309zM51 0l182 1090h467l-182 -1090h-467z" />
<glyph unicode="&#x12a;" horiz-adv-x="643" d="M61 0l240 1434h520l-239 -1434h-521zM242 1530l49 299h674l-50 -299h-673z" />
<glyph unicode="&#x12b;" d="M51 0l182 1090h467l-182 -1090h-467zM147 1186l50 299h673l-49 -299h-674z" />
<glyph unicode="&#x12c;" horiz-adv-x="643" d="M61 0l240 1434h520l-239 -1434h-521zM240 1851h346q-16 -92 26 -92q46 0 62 92h348q-27 -158 -147.5 -253t-305.5 -95q-183 0 -269.5 95t-59.5 253z" />
<glyph unicode="&#x12d;" d="M51 0l182 1090h467l-182 -1090h-467zM145 1507h347q-16 -92 26 -92q46 0 62 92h348q-27 -158 -147.5 -253t-305.5 -95q-183 0 -270 95t-60 253z" />
<glyph unicode="&#x12e;" horiz-adv-x="643" d="M61 0l240 1434h520l-239 -1434q-39 -14 -70 -42t-31 -60q0 -25 22 -37.5t54 -12.5l-45 -266q-106 -31 -186 -31q-258 0 -258 179q0 64 41 123t94 94.5t108 52.5h-250z" />
<glyph unicode="&#x12f;" d="M4 -270q0 49 23.5 95t61 80t78.5 58.5t81 36.5h-197l182 1090h467l-182 -1090q-41 -15 -70.5 -42t-29.5 -60q0 -25 22 -37.5t54 -12.5l-45 -266q-106 -31 -187 -31q-258 0 -258 179zM252 1309q0 110 83 185t212 75q102 0 160.5 -54t58.5 -141q0 -109 -83 -183.5 t-212 -74.5q-102 0 -160.5 54t-58.5 139z" />
<glyph unicode="&#x130;" horiz-adv-x="643" d="M61 0l240 1434h520l-239 -1434h-521zM379 1681q0 105 74 172.5t194 67.5q94 0 145.5 -47t51.5 -127q0 -105 -73 -173.5t-193 -68.5q-93 0 -146 49t-53 127z" />
<glyph unicode="&#x131;" d="M51 0l182 1090h467l-182 -1090h-467z" />
<glyph unicode="&#x132;" horiz-adv-x="1501" d="M61 0l240 1434h520l-239 -1434h-521zM670 25l116 391q59 -15 109 -15q91 0 104 78l160 955h522l-174 -1035q-69 -411 -553 -411q-147 0 -284 37z" />
<glyph unicode="&#x133;" horiz-adv-x="1138" d="M51 0l182 1090h467l-182 -1090h-467zM252 1313q0 110 84 187t213 77q102 0 160.5 -55.5t58.5 -143.5q0 -109 -84.5 -185.5t-212.5 -76.5q-102 0 -160.5 55.5t-58.5 141.5zM408 -377l55 328q28 -4 59 -4q40 0 68.5 21t34.5 59l178 1063h467l-180 -1076 q-24 -146 -100.5 -238t-188.5 -130.5t-266 -38.5q-27 0 -70 5.5t-57 10.5zM821 1313q0 110 84 187t213 77q101 0 159 -55.5t58 -143.5q0 -109 -84 -185.5t-211 -76.5q-102 0 -160.5 55.5t-58.5 141.5z" />
<glyph unicode="&#x134;" horiz-adv-x="858" d="M27 25l116 391q59 -15 109 -15q91 0 104 78l160 955h522l-174 -1035q-69 -411 -553 -411q-147 0 -284 37zM344 1518l270 319h447l164 -319h-391l-35 79l-64 -79h-391z" />
<glyph unicode="&#x135;" d="M-184 -377l57 340q37 -6 82 -6q39 0 67 20.5t35 59.5l176 1053h467l-180 -1076q-24 -146 -100.5 -238t-188.5 -130.5t-266 -38.5q-28 0 -82 6t-67 10zM43 1174l270 319h447l164 -319h-392l-34 79l-64 -79h-391z" />
<glyph unicode="&#x136;" horiz-adv-x="1431" d="M61 0l240 1434h520l-86 -521l332 521h567l-469 -684l244 -750h-578l-151 590l-98 -590h-521zM418 -573q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5q77 0 126.5 -45.5t49.5 -131.5q0 -135 -91 -264.5t-233 -218.5z" />
<glyph unicode="&#x137;" horiz-adv-x="1245" d="M51 0l248 1479h467l-127 -762l240 373h512l-359 -521l195 -569h-514l-125 420l-70 -420h-467zM297 -573q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5q77 0 126.5 -45.5t49.5 -131.5q0 -135 -90.5 -264.5t-232.5 -218.5z" />
<glyph unicode="&#x138;" horiz-adv-x="1245" d="M51 0l180 1090h467l-59 -373l240 373h512l-359 -521l195 -569h-514l-125 420l-70 -420h-467z" />
<glyph unicode="&#x139;" horiz-adv-x="1001" d="M61 0l240 1434h520l-172 -1031h393l-67 -403h-914zM348 1518l197 319h483l-297 -319h-383z" />
<glyph unicode="&#x13a;" d="M51 0l248 1479h467l-248 -1479h-467zM303 1518l197 319h483l-297 -319h-383z" />
<glyph unicode="&#x13b;" horiz-adv-x="1001" d="M61 0l240 1434h520l-172 -1031h393l-67 -403h-914zM217 -573q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5q77 0 126.5 -45.5t49.5 -131.5q0 -135 -90.5 -264.5t-232.5 -218.5z" />
<glyph unicode="&#x13c;" d="M18 -573q99 63 136 127q-57 15 -88 58t-31 99q0 93 72 159.5t169 66.5q77 0 127 -45.5t50 -131.5q0 -135 -91 -264.5t-233 -218.5zM51 0l248 1479h467l-248 -1479h-467z" />
<glyph unicode="&#x13d;" horiz-adv-x="1001" d="M61 0l240 1434h520l-172 -1031h393l-67 -403h-914zM831 1077l74 357h320l-162 -357h-232z" />
<glyph unicode="&#x13e;" d="M51 0l248 1479h467l-248 -1479h-467zM770 1122l74 357h319l-162 -357h-231z" />
<glyph unicode="&#x13f;" horiz-adv-x="1128" d="M61 0l240 1434h520l-172 -1031h393l-67 -403h-914zM758 723q0 110 87 190t212 80q101 0 161 -58.5t60 -148.5q0 -108 -87.5 -188t-213.5 -80q-100 0 -159.5 58.5t-59.5 146.5z" />
<glyph unicode="&#x140;" horiz-adv-x="1118" d="M51 0l248 1479h467l-248 -1479h-467zM715 741q0 110 87 190.5t212 80.5q101 0 161 -58.5t60 -148.5q0 -108 -87.5 -188t-213.5 -80q-100 0 -159.5 58.5t-59.5 145.5z" />
<glyph unicode="&#x141;" horiz-adv-x="1077" d="M74 303l63 385l127 68l113 678h520l-68 -414l187 90l-68 -397l-186 -88l-37 -222h393l-67 -403h-914l62 367z" />
<glyph unicode="&#x142;" horiz-adv-x="710" d="M80 336l67 405l105 52l115 686h467l-72 -430l104 51l-67 -414l-107 -51l-104 -635h-467l63 385z" />
<glyph unicode="&#x143;" horiz-adv-x="1474" d="M61 0l240 1434h526l199 -674l113 674h514l-240 -1434h-526l-199 674l-113 -674h-514zM713 1518l196 319h484l-297 -319h-383z" />
<glyph unicode="&#x144;" horiz-adv-x="1187" d="M51 0l182 1090h447l-14 -91q49 48 125 82.5t161 34.5q177 0 255.5 -113t45.5 -307l-116 -696h-467l108 643q7 47 -9 71.5t-52 24.5q-37 0 -60 -27.5t-30 -68.5l-109 -643h-467zM477 1174l197 319h483l-297 -319h-383z" />
<glyph unicode="&#x145;" horiz-adv-x="1474" d="M61 0l240 1434h526l199 -674l113 674h514l-240 -1434h-526l-199 674l-113 -674h-514zM467 -573q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5q77 0 126.5 -45.5t49.5 -131.5q0 -135 -90.5 -264.5t-232.5 -218.5z" />
<glyph unicode="&#x146;" horiz-adv-x="1187" d="M51 0l182 1090h447l-14 -91q49 48 125 82.5t161 34.5q177 0 255.5 -113t45.5 -307l-116 -696h-467l108 643q7 47 -9 71.5t-52 24.5q-37 0 -60 -27.5t-30 -68.5l-109 -643h-467zM324 -573q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5q77 0 126.5 -45.5 t49.5 -131.5q0 -135 -91 -264.5t-233 -218.5z" />
<glyph unicode="&#x147;" horiz-adv-x="1474" d="M61 0l240 1434h526l199 -674l113 674h514l-240 -1434h-526l-199 674l-113 -674h-514zM604 1837h391l35 -80l64 80h391l-271 -319h-446z" />
<glyph unicode="&#x148;" horiz-adv-x="1187" d="M51 0l182 1090h447l-14 -91q49 48 125 82.5t161 34.5q177 0 255.5 -113t45.5 -307l-116 -696h-467l108 643q7 47 -9 71.5t-52 24.5q-37 0 -60 -27.5t-30 -68.5l-109 -643h-467zM403 1493h392l34 -80l64 80h391l-270 -319h-447z" />
<glyph unicode="&#x149;" horiz-adv-x="1368" d="M37 1049q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5q77 0 126.5 -45.5t49.5 -131.5q0 -135 -91 -264.5t-233 -218.5zM231 0l183 1090h446l-14 -91q49 48 125.5 82.5t161.5 34.5q177 0 255.5 -113t45.5 -307l-117 -696h-467l108 643q7 47 -9 71.5 t-52 24.5q-37 0 -60 -27.5t-30 -68.5l-109 -643h-467z" />
<glyph unicode="&#x14a;" horiz-adv-x="1474" d="M61 0l240 1434h526l205 -641l107 641h514l-240 -1438q-69 -412 -553 -412q-148 0 -285 37l111 354q55 -14 109 -14q97 0 110 78l-217 635l-113 -674h-514z" />
<glyph unicode="&#x14b;" horiz-adv-x="1187" d="M51 0l182 1090h447l-14 -91q49 48 125 82.5t161 34.5q177 0 255.5 -113t45.5 -307l-114 -682q-24 -146 -100.5 -238t-188.5 -130.5t-266 -38.5q-28 0 -81.5 5.5t-68.5 10.5l53 322q39 -6 84 -6q40 0 67.5 20.5t33.5 58.5l106 625q7 47 -9 71.5t-52 24.5q-37 0 -60 -27.5 t-30 -68.5l-109 -643h-467z" />
<glyph unicode="&#x14c;" horiz-adv-x="1617" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q160 0 289.5 -48.5t214.5 -134t130.5 -200.5t45.5 -248q0 -174 -69 -331t-189.5 -273t-294.5 -184t-375 -68q-159 0 -288.5 48.5t-214.5 134t-131 200.5t-46 250zM672 649q0 -112 53 -180t152 -68 q138 0 223.5 110.5t85.5 272.5q0 111 -53.5 179.5t-151.5 68.5q-139 0 -224 -110.5t-85 -272.5zM725 1530l49 299h674l-49 -299h-674z" />
<glyph unicode="&#x14d;" horiz-adv-x="1302" d="M109 463q0 136 55.5 257.5t151 208.5t229.5 138t284 51q242 0 385.5 -136t143.5 -351q0 -181 -94.5 -332t-260 -238.5t-366.5 -87.5q-242 0 -385 137.5t-143 352.5zM512 1186l49 299h674l-49 -299h-674zM584 498q0 -71 29.5 -113.5t86.5 -42.5q82 0 133.5 73t51.5 179 q0 72 -30.5 114t-86.5 42q-82 0 -133 -72t-51 -180z" />
<glyph unicode="&#x14e;" horiz-adv-x="1617" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q160 0 289.5 -48.5t214.5 -134t130.5 -200.5t45.5 -248q0 -174 -69 -331t-189.5 -273t-294.5 -184t-375 -68q-159 0 -288.5 48.5t-214.5 134t-131 200.5t-46 250zM672 649q0 -112 53 -180t152 -68 q138 0 223.5 110.5t85.5 272.5q0 111 -53.5 179.5t-151.5 68.5q-139 0 -224 -110.5t-85 -272.5zM739 1851h346q-16 -92 27 -92q46 0 62 92h348q-27 -158 -147.5 -253t-305.5 -95q-183 0 -270 95t-60 253z" />
<glyph unicode="&#x14f;" horiz-adv-x="1302" d="M109 463q0 136 55.5 257.5t151 208.5t229.5 138t284 51q242 0 385.5 -136t143.5 -351q0 -181 -94.5 -332t-260 -238.5t-366.5 -87.5q-242 0 -385 137.5t-143 352.5zM516 1507h346q-16 -92 27 -92q45 0 61 92h348q-27 -158 -147 -253t-305 -95q-183 0 -270 95t-60 253z M584 498q0 -71 29.5 -113.5t86.5 -42.5q82 0 133.5 73t51.5 179q0 72 -30.5 114t-86.5 42q-82 0 -133 -72t-51 -180z" />
<glyph unicode="&#x150;" horiz-adv-x="1617" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q160 0 289.5 -48.5t214.5 -134t130.5 -200.5t45.5 -248q0 -174 -69 -331t-189.5 -273t-294.5 -184t-375 -68q-159 0 -288.5 48.5t-214.5 134t-131 200.5t-46 250zM641 1518l195 319h387l-232 -319h-350zM672 649 q0 -112 53 -180t152 -68q138 0 223.5 110.5t85.5 272.5q0 111 -53.5 179.5t-151.5 68.5q-139 0 -224 -110.5t-85 -272.5zM1075 1518l195 319h387l-232 -319h-350z" />
<glyph unicode="&#x151;" horiz-adv-x="1302" d="M109 463q0 136 55.5 257.5t151 208.5t229.5 138t284 51q242 0 385.5 -136t143.5 -351q0 -181 -94.5 -332t-260 -238.5t-366.5 -87.5q-242 0 -385 137.5t-143 352.5zM418 1174l194 319h387l-231 -319h-350zM584 498q0 -71 29.5 -113.5t86.5 -42.5q82 0 133.5 73t51.5 179 q0 72 -30.5 114t-86.5 42q-82 0 -133 -72t-51 -180zM852 1174l195 319h387l-232 -319h-350z" />
<glyph unicode="&#x152;" horiz-adv-x="2056" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q111 0 225 -26h956l-67 -404h-469q18 -68 20 -119h387l-63 -379h-385q-21 -58 -64 -129h469l-67 -403h-957q-125 -27 -233 -27q-159 0 -288.5 48.5t-214.5 134t-131 200.5t-46 250zM672 649q0 -112 53 -180t152 -68 q138 0 223.5 110.5t85.5 272.5q0 111 -53.5 179.5t-151.5 68.5q-139 0 -224 -110.5t-85 -272.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1994" d="M109 463q0 136 55.5 257.5t151 208.5t229.5 138t284 51q220 0 365 -119q167 119 381 119q239 0 369.5 -127.5t130.5 -339.5q0 -49 -8.5 -102.5t-17.5 -82.5l-9 -28h-694q3 -51 44.5 -82.5t106.5 -31.5q116 0 201 77l287 -172q-248 -256 -572 -256q-213 0 -348 125 q-186 -125 -428 -125t-385 137.5t-143 352.5zM584 498q0 -71 29.5 -113.5t86.5 -42.5q82 0 133.5 73t51.5 179q0 72 -30.5 114t-86.5 42q-82 0 -133 -72t-51 -180zM1401 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5z" />
<glyph unicode="&#x154;" horiz-adv-x="1294" d="M61 0l240 1434h637q219 0 341 -112.5t122 -287.5q0 -143 -86 -257t-211 -177l227 -600h-563l-125 463l-78 -463h-504zM588 1518l196 319h484l-297 -319h-383zM707 844h61q52 0 86.5 35t34.5 79q0 35 -23.5 60.5t-64.5 25.5h-62z" />
<glyph unicode="&#x155;" horiz-adv-x="817" d="M51 0l182 1090h457l-20 -119q45 54 125.5 99.5t169.5 45.5l-70 -422q-43 15 -80 15q-77 0 -136.5 -54t-74.5 -147l-86 -508h-467zM338 1174l197 319h483l-297 -319h-383z" />
<glyph unicode="&#x156;" horiz-adv-x="1294" d="M61 0l240 1434h637q219 0 341 -112.5t122 -287.5q0 -143 -86 -257t-211 -177l227 -600h-563l-125 463l-78 -463h-504zM373 -573q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5q77 0 126.5 -45.5t49.5 -131.5q0 -135 -91 -264.5t-233 -218.5zM707 844h61 q52 0 86.5 35t34.5 79q0 35 -23.5 60.5t-64.5 25.5h-62z" />
<glyph unicode="&#x157;" horiz-adv-x="817" d="M51 0l182 1090h457l-20 -119q45 54 125.5 99.5t169.5 45.5l-70 -422q-43 15 -80 15q-77 0 -136.5 -54t-74.5 -147l-86 -508h-467zM51 -573q97 62 135 127q-57 15 -87.5 58t-30.5 99q0 93 72 159.5t169 66.5q77 0 126.5 -45.5t49.5 -131.5q0 -135 -90.5 -264.5 t-232.5 -218.5z" />
<glyph unicode="&#x158;" horiz-adv-x="1294" d="M61 0l240 1434h637q219 0 341 -112.5t122 -287.5q0 -143 -86 -257t-211 -177l227 -600h-563l-125 463l-78 -463h-504zM420 1837h391l35 -80l63 80h391l-270 -319h-446zM707 844h61q52 0 86.5 35t34.5 79q0 35 -23.5 60.5t-64.5 25.5h-62z" />
<glyph unicode="&#x159;" horiz-adv-x="817" d="M51 0l182 1090h457l-20 -119q45 54 125.5 99.5t169.5 45.5l-70 -422q-43 15 -80 15q-77 0 -136.5 -54t-74.5 -147l-86 -508h-467zM219 1493h391l35 -80l64 80h391l-271 -319h-446z" />
<glyph unicode="&#x15a;" horiz-adv-x="1181" d="M80 346l403 201q30 -69 74.5 -110.5t91.5 -41.5t47 41q0 24 -26 46.5t-67 44t-91 46.5t-100.5 59t-91.5 75t-67 102.5t-26 134.5q0 149 80.5 267t222.5 183.5t318 65.5q94 0 175 -23.5t135 -60t94.5 -82.5t60.5 -89.5t26 -82.5l-399 -209q-15 52 -52 91.5t-83 39.5 q-22 0 -35.5 -12t-13.5 -31q0 -28 35 -57t87 -53.5t113.5 -62t113.5 -80t87 -110.5t35 -149q0 -228 -178 -372t-463 -144q-98 0 -180.5 25t-137 63.5t-95.5 90t-62.5 99t-30.5 95.5zM629 1518l196 319h484l-297 -319h-383z" />
<glyph unicode="&#x15b;" horiz-adv-x="1003" d="M55 160l232 264q46 -38 124.5 -71.5t127.5 -33.5t49 25q0 12 -14 20t-50 19q-55 17 -92 31t-89 42.5t-83.5 60t-54.5 82t-23 110.5q0 120 70.5 214t189.5 144.5t263 50.5q149 0 252 -50t165 -122l-262 -250q-34 35 -82 58.5t-94 23.5q-51 0 -51 -30q0 -14 21 -25t75 -27 q48 -15 82.5 -28t82 -39t77.5 -55.5t51.5 -77t21.5 -103.5q0 -201 -135 -310.5t-381 -109.5q-146 0 -277 57.5t-196 129.5zM481 1174l197 319h483l-297 -319h-383z" />
<glyph unicode="&#x15c;" horiz-adv-x="1181" d="M80 346l403 201q30 -69 74.5 -110.5t91.5 -41.5t47 41q0 24 -26 46.5t-67 44t-91 46.5t-100.5 59t-91.5 75t-67 102.5t-26 134.5q0 149 80.5 267t222.5 183.5t318 65.5q94 0 175 -23.5t135 -60t94.5 -82.5t60.5 -89.5t26 -82.5l-399 -209q-15 52 -52 91.5t-83 39.5 q-22 0 -35.5 -12t-13.5 -31q0 -28 35 -57t87 -53.5t113.5 -62t113.5 -80t87 -110.5t35 -149q0 -228 -178 -372t-463 -144q-98 0 -180.5 25t-137 63.5t-95.5 90t-62.5 99t-30.5 95.5zM424 1518l270 319h447l164 -319h-392l-34 79l-64 -79h-391z" />
<glyph unicode="&#x15d;" horiz-adv-x="1003" d="M55 160l232 264q46 -38 124.5 -71.5t127.5 -33.5t49 25q0 12 -14 20t-50 19q-55 17 -92 31t-89 42.5t-83.5 60t-54.5 82t-23 110.5q0 120 70.5 214t189.5 144.5t263 50.5q149 0 252 -50t165 -122l-262 -250q-34 35 -82 58.5t-94 23.5q-51 0 -51 -30q0 -14 21 -25t75 -27 q48 -15 82.5 -28t82 -39t77.5 -55.5t51.5 -77t21.5 -103.5q0 -201 -135 -310.5t-381 -109.5q-146 0 -277 57.5t-196 129.5zM285 1174l270 319h446l164 -319h-391l-35 79l-63 -79h-391z" />
<glyph unicode="&#x15e;" horiz-adv-x="1181" d="M80 346l403 201q30 -69 74.5 -110.5t91.5 -41.5t47 41q0 24 -26 46.5t-67 44t-91 46.5t-100.5 59t-91.5 75t-67 102.5t-26 134.5q0 149 80.5 267t222.5 183.5t318 65.5q94 0 175 -23.5t135 -60t94.5 -82.5t60.5 -89.5t26 -82.5l-399 -209q-15 52 -52 91.5t-83 39.5 q-22 0 -35.5 -12t-13.5 -31q0 -28 35 -57t87 -53.5t113.5 -62t113.5 -80t87 -110.5t35 -149q0 -207 -148 -345.5t-393 -163.5l-14 -48q61 -12 96 -54.5t35 -102.5q0 -119 -94 -193t-252 -74q-95 0 -193 43l84 197q32 -14 72 -14q26 0 42.5 12t16.5 31q0 15 -14.5 25 t-40.5 10q-16 0 -31.5 -3t-22.5 -5l-8 -3l62 193q-80 20 -144.5 60.5t-104 91t-63 100.5t-32.5 100z" />
<glyph unicode="&#x15f;" horiz-adv-x="1003" d="M55 160l232 264q46 -38 124.5 -71.5t127.5 -33.5t49 25q0 12 -14 20t-50 19q-55 17 -92 31t-89 42.5t-83.5 60t-54.5 82t-23 110.5q0 120 70.5 214t189.5 144.5t263 50.5q149 0 252 -50t165 -122l-262 -250q-34 35 -82 58.5t-94 23.5q-51 0 -51 -30q0 -14 21 -25t75 -27 q48 -15 82.5 -28t82 -39t77.5 -55.5t51.5 -77t21.5 -103.5q0 -184 -113 -291.5t-323 -124.5l-14 -45q61 -12 96 -54.5t35 -102.5q0 -119 -94 -193t-252 -74q-95 0 -193 43l84 197q32 -14 72 -14q26 0 42.5 12t16.5 31q0 15 -14.5 25t-40.5 10q-16 0 -31 -3t-23 -5l-7 -3 l63 197q-94 23 -172 68.5t-121 93.5z" />
<glyph unicode="&#x160;" horiz-adv-x="1181" d="M80 346l403 201q30 -69 74.5 -110.5t91.5 -41.5t47 41q0 24 -26 46.5t-67 44t-91 46.5t-100.5 59t-91.5 75t-67 102.5t-26 134.5q0 149 80.5 267t222.5 183.5t318 65.5q94 0 175 -23.5t135 -60t94.5 -82.5t60.5 -89.5t26 -82.5l-399 -209q-15 52 -52 91.5t-83 39.5 q-22 0 -35.5 -12t-13.5 -31q0 -28 35 -57t87 -53.5t113.5 -62t113.5 -80t87 -110.5t35 -149q0 -228 -178 -372t-463 -144q-98 0 -180.5 25t-137 63.5t-95.5 90t-62.5 99t-30.5 95.5zM479 1837h391l35 -80l64 80h391l-270 -319h-447z" />
<glyph unicode="&#x161;" horiz-adv-x="1003" d="M55 160l232 264q46 -38 124.5 -71.5t127.5 -33.5t49 25q0 12 -14 20t-50 19q-55 17 -92 31t-89 42.5t-83.5 60t-54.5 82t-23 110.5q0 120 70.5 214t189.5 144.5t263 50.5q149 0 252 -50t165 -122l-262 -250q-34 35 -82 58.5t-94 23.5q-51 0 -51 -30q0 -14 21 -25t75 -27 q48 -15 82.5 -28t82 -39t77.5 -55.5t51.5 -77t21.5 -103.5q0 -201 -135 -310.5t-381 -109.5q-146 0 -277 57.5t-196 129.5zM344 1493h391l35 -80l64 80h391l-271 -319h-446z" />
<glyph unicode="&#x162;" horiz-adv-x="1142" d="M203 1030l67 404h1084l-68 -404h-282l-173 -1030h-520l172 1030h-280zM299 -573q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5q77 0 126.5 -45.5t49.5 -131.5q0 -135 -90.5 -264.5t-232.5 -218.5z" />
<glyph unicode="&#x163;" horiz-adv-x="788" d="M152 750l57 340h110l39 227h455l-39 -227h170l-57 -340h-170l-53 -322q-7 -39 20.5 -55.5t69.5 -16.5l75 6l-61 -358q-76 -20 -182 -20q-226 0 -326.5 107.5t-64.5 324.5l55 334h-98zM168 -573q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5 q77 0 126.5 -45.5t49.5 -131.5q0 -135 -90.5 -264.5t-232.5 -218.5z" />
<glyph unicode="&#x164;" horiz-adv-x="1142" d="M203 1030l67 404h1084l-68 -404h-282l-173 -1030h-520l172 1030h-280zM442 1837h392l34 -80l64 80h391l-270 -319h-447z" />
<glyph unicode="&#x165;" horiz-adv-x="806" d="M152 750l57 340h110l39 227h455l-39 -227h170l-57 -340h-170l-53 -322q-7 -39 20.5 -55.5t69.5 -16.5l75 6l-61 -358q-76 -20 -182 -20q-226 0 -326.5 107.5t-64.5 324.5l55 334h-98zM823 1122l74 357h320l-162 -357h-232z" />
<glyph unicode="&#x166;" horiz-adv-x="1142" d="M203 1030l67 404h1084l-68 -404h-282l-29 -168h158l-50 -299h-157l-95 -563h-520l95 563h-160l49 299h160l28 168h-280z" />
<glyph unicode="&#x167;" horiz-adv-x="788" d="M96 414l45 270h99l10 66h-98l57 340h110l39 227h455l-39 -227h170l-57 -340h-170l-10 -66h118l-45 -270h-118q0 -31 27 -44.5t65 -13.5l75 6l-59 -358q-78 -20 -184 -20q-226 0 -326.5 106.5t-64.5 323.5h-99z" />
<glyph unicode="&#x168;" horiz-adv-x="1445" d="M156 563l145 871h520l-147 -883q-12 -72 19 -111t95 -39q66 0 111 39t57 111l148 883h520l-145 -871q-44 -266 -245.5 -428t-516.5 -162q-158 0 -276 43.5t-187 122t-94 186.5t-4 238zM539 1505q27 161 116.5 255.5t223.5 94.5q52 0 122.5 -29.5t96.5 -29.5 q24 0 41.5 16.5t21.5 45.5h309q-27 -161 -116.5 -256t-223.5 -95q-52 0 -122.5 30t-96.5 30q-24 0 -41.5 -16.5t-21.5 -45.5h-309z" />
<glyph unicode="&#x169;" horiz-adv-x="1185" d="M115 377l118 713h467l-108 -644q-7 -47 9 -71.5t52 -24.5q37 0 60 27.5t30 68.5l109 644h467l-182 -1090h-447l15 84q-43 -43 -119 -77t-152 -34q-186 0 -268.5 106t-50.5 298zM350 1161q27 161 116.5 255.5t223.5 94.5q52 0 122.5 -29.5t96.5 -29.5q24 0 42 16.5 t22 44.5h309q-27 -161 -116.5 -255.5t-223.5 -94.5q-52 0 -122.5 30t-96.5 30q-24 0 -42 -16.5t-22 -45.5h-309z" />
<glyph unicode="&#x16a;" horiz-adv-x="1445" d="M156 563l145 871h520l-147 -883q-12 -72 19 -111t95 -39q66 0 111 39t57 111l148 883h520l-145 -871q-44 -266 -245.5 -428t-516.5 -162q-158 0 -276 43.5t-187 122t-94 186.5t-4 238zM641 1530l49 299h674l-49 -299h-674z" />
<glyph unicode="&#x16b;" horiz-adv-x="1185" d="M115 377l118 713h467l-108 -644q-7 -47 9 -71.5t52 -24.5q37 0 60 27.5t30 68.5l109 644h467l-182 -1090h-447l15 84q-43 -43 -119 -77t-152 -34q-186 0 -268.5 106t-50.5 298zM455 1186l49 299h674l-50 -299h-673z" />
<glyph unicode="&#x16c;" horiz-adv-x="1445" d="M156 563l145 871h520l-147 -883q-12 -72 19 -111t95 -39q66 0 111 39t57 111l148 883h520l-145 -871q-44 -266 -245.5 -428t-516.5 -162q-158 0 -276 43.5t-187 122t-94 186.5t-4 238zM641 1851h346q-16 -92 27 -92q45 0 61 92h348q-27 -158 -147 -253t-305 -95 q-183 0 -270 95t-60 253z" />
<glyph unicode="&#x16d;" horiz-adv-x="1185" d="M115 377l118 713h467l-108 -644q-7 -47 9 -71.5t52 -24.5q37 0 60 27.5t30 68.5l109 644h467l-182 -1090h-447l15 84q-43 -43 -119 -77t-152 -34q-186 0 -268.5 106t-50.5 298zM453 1507h346q-16 -92 26 -92q46 0 62 92h348q-27 -158 -147.5 -253t-305.5 -95 q-183 0 -269.5 95t-59.5 253z" />
<glyph unicode="&#x16e;" horiz-adv-x="1445" d="M156 563l145 871h520l-147 -883q-12 -72 19 -111t95 -39q66 0 111 39t57 111l148 883h520l-145 -871q-44 -266 -245.5 -428t-516.5 -162q-158 0 -276 43.5t-187 122t-94 186.5t-4 238zM745 1698q0 109 91.5 182.5t212.5 73.5q99 0 163 -55t64 -142q0 -110 -90 -183 t-213 -73q-98 0 -163 55t-65 142zM944 1716q0 -26 14.5 -42.5t40.5 -16.5q34 0 58 24t24 58q0 25 -15.5 42t-41.5 17q-32 0 -56 -24t-24 -58z" />
<glyph unicode="&#x16f;" horiz-adv-x="1185" d="M115 377l118 713h467l-108 -644q-7 -47 9 -71.5t52 -24.5q37 0 60 27.5t30 68.5l109 644h467l-182 -1090h-447l15 84q-43 -43 -119 -77t-152 -34q-186 0 -268.5 106t-50.5 298zM563 1354q0 109 91 182.5t212 73.5q99 0 163.5 -55t64.5 -142q0 -110 -90 -183t-213 -73 q-98 0 -163 55t-65 142zM762 1372q0 -26 14.5 -42.5t40.5 -16.5q34 0 58 24t24 58q0 25 -15.5 42t-41.5 17q-32 0 -56 -24t-24 -58z" />
<glyph unicode="&#x170;" horiz-adv-x="1445" d="M156 563l145 871h520l-147 -883q-12 -72 19 -111t95 -39q66 0 111 39t57 111l148 883h520l-145 -871q-44 -266 -245.5 -428t-516.5 -162q-158 0 -276 43.5t-187 122t-94 186.5t-4 238zM555 1518l195 319h387l-232 -319h-350zM989 1518l195 319h387l-232 -319h-350z" />
<glyph unicode="&#x171;" horiz-adv-x="1185" d="M115 377l118 713h467l-108 -644q-7 -47 9 -71.5t52 -24.5q37 0 60 27.5t30 68.5l109 644h467l-182 -1090h-447l15 84q-43 -43 -119 -77t-152 -34q-186 0 -268.5 106t-50.5 298zM358 1174l195 319h387l-231 -319h-351zM793 1174l194 319h387l-231 -319h-350z" />
<glyph unicode="&#x172;" horiz-adv-x="1445" d="M156 563l145 871h520l-147 -883q-12 -72 19 -111t95 -39q66 0 111 39t57 111l148 883h520l-145 -871q-36 -214 -175 -362.5t-360 -200.5q-36 -16 -63 -43t-27 -59q0 -25 22 -37.5t54 -12.5l-45 -266q-106 -31 -187 -31q-258 0 -258 179q0 79 55.5 143.5t133.5 103.5 q-270 23 -392 181t-81 405z" />
<glyph unicode="&#x173;" horiz-adv-x="1187" d="M115 377l118 713h467l-108 -644q-7 -47 9 -71.5t52 -24.5q37 0 60 27.5t30 68.5l109 644h467l-182 -1090q-36 -14 -65.5 -42t-29.5 -60q0 -25 22 -37.5t54 -12.5l-45 -266q-106 -31 -186 -31q-258 0 -258 179q0 64 41 123t94 94.5t108 52.5h-182l15 84q-43 -43 -119 -77 t-152 -34q-186 0 -268.5 106t-50.5 298z" />
<glyph unicode="&#x174;" horiz-adv-x="2166" d="M254 1434h555l2 -789l307 789h410l45 -789l264 789h555l-612 -1434h-523l-49 752l-299 -752h-522zM897 1518l270 319h447l164 -319h-392l-34 79l-64 -79h-391z" />
<glyph unicode="&#x175;" horiz-adv-x="1789" d="M199 1090h508l-21 -537l184 537h414l4 -541l162 541h508l-473 -1090h-502l-22 401l-152 -401h-504zM653 1174l271 319h446l164 -319h-391l-35 79l-64 -79h-391z" />
<glyph unicode="&#x176;" horiz-adv-x="1449" d="M256 1434h547l76 -408l211 408h583l-586 -822l-102 -612h-520l102 612zM539 1518l270 319h446l164 -319h-391l-35 79l-63 -79h-391z" />
<glyph unicode="&#x177;" horiz-adv-x="1277" d="M197 1090h489l41 -515l223 515h496l-793 -1457h-440l217 422zM401 1174l271 319h446l164 -319h-391l-35 79l-63 -79h-392z" />
<glyph unicode="&#x178;" horiz-adv-x="1449" d="M256 1434h547l76 -408l211 408h583l-586 -822l-102 -612h-520l102 612zM586 1632q0 83 62 143t161 60q79 0 121.5 -42t42.5 -107q0 -85 -61 -143t-162 -58q-79 0 -121.5 40.5t-42.5 106.5zM1034 1632q0 83 62 143t161 60q79 0 121.5 -42t42.5 -107q0 -85 -61 -143 t-162 -58q-79 0 -121.5 40.5t-42.5 106.5z" />
<glyph unicode="&#x179;" horiz-adv-x="1277" d="M27 0l45 268l667 766h-524l66 400h1204l-45 -267l-672 -764h551l-68 -403h-1224zM633 1518l196 319h484l-297 -319h-383z" />
<glyph unicode="&#x17a;" horiz-adv-x="999" d="M29 0l41 240l430 499h-342l59 351h924l-41 -240l-436 -498h356l-59 -352h-932zM459 1174l196 319h484l-297 -319h-383z" />
<glyph unicode="&#x17b;" horiz-adv-x="1277" d="M27 0l45 268l667 766h-524l66 400h1204l-45 -267l-672 -764h551l-68 -403h-1224zM705 1681q0 105 74 172.5t194 67.5q93 0 144.5 -47t51.5 -127q0 -105 -73 -173.5t-193 -68.5q-93 0 -145.5 48.5t-52.5 127.5z" />
<glyph unicode="&#x17c;" horiz-adv-x="999" d="M29 0l41 240l430 499h-342l59 351h924l-41 -240l-436 -498h356l-59 -352h-932zM496 1337q0 105 74 172.5t194 67.5q94 0 145.5 -47t51.5 -127q0 -105 -73.5 -173.5t-193.5 -68.5q-93 0 -145.5 48.5t-52.5 127.5z" />
<glyph unicode="&#x17d;" horiz-adv-x="1277" d="M27 0l45 268l667 766h-524l66 400h1204l-45 -267l-672 -764h551l-68 -403h-1224zM524 1837h391l35 -80l64 80h391l-270 -319h-447z" />
<glyph unicode="&#x17e;" horiz-adv-x="999" d="M29 0l41 240l430 499h-342l59 351h924l-41 -240l-436 -498h356l-59 -352h-932zM309 1493h391l35 -80l64 80h391l-270 -319h-447z" />
<glyph unicode="&#x192;" horiz-adv-x="1034" d="M-96 -360l57 340q53 -11 86 -11q36 0 63 18.5t33 53.5l99 549h-101l15 336h143l29 155q16 86 53 153.5t86 110t113 70t128.5 38.5t137.5 11q98 0 180 -16l-59 -344q-38 10 -76 10q-42 0 -70.5 -18.5t-34.5 -53.5l-20 -116h133l-14 -336h-185l-102 -588 q-66 -383 -516 -383q-75 0 -178 21z" />
<glyph unicode="&#x1fa;" horiz-adv-x="1523" d="M10 0l738 1438v20q0 110 90 183t211 73q99 0 163 -54.5t64 -141.5q0 -40 -21 -82l258 -1436h-518l-26 219h-336l-101 -219h-522zM780 584h162l-29 305zM827 1753l181 309h483l-281 -309h-383zM944 1477q0 -25 15.5 -42.5t41.5 -17.5q32 0 56 24t24 58q0 25 -15.5 42.5 t-41.5 17.5q-32 0 -56 -24t-24 -58z" />
<glyph unicode="&#x1fb;" horiz-adv-x="1138" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q96 0 171.5 -17.5t125.5 -48.5t85 -74.5t48.5 -96t17 -112.5t-9.5 -124l-108 -645h-400l-8 72q-120 -99 -280 -99 q-147 0 -238.5 74t-91.5 219zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5zM563 1348q0 109 91 182.5t212 73.5q99 0 163.5 -55t64.5 -142q0 -110 -90 -183t-213 -73q-98 0 -163 55t-65 142zM653 1638l191 295h483 l-299 -295h-375zM762 1366q0 -26 14.5 -42.5t40.5 -16.5q34 0 58 24t24 58q0 25 -15.5 42t-41.5 17q-32 0 -56 -24t-24 -58z" />
<glyph unicode="&#x1fc;" horiz-adv-x="1880" d="M-27 0l1022 1434h1063l-67 -404h-377l-21 -119h336l-61 -379h-336l-23 -129h377l-67 -403h-895l37 219h-312l-141 -219h-535zM866 584h156l53 321zM1143 1487l196 319h484l-297 -319h-383z" />
<glyph unicode="&#x1fd;" horiz-adv-x="1818" d="M76 266q0 127 66.5 219.5t173.5 137.5t239 45q95 0 186 -41l9 41q6 36 -28 56.5t-101 20.5q-136 0 -283 -67l-113 299q118 66 258.5 103.5t280.5 37.5q107 0 188.5 -26.5t126.5 -71.5q63 44 156 71t182 27q227 0 354 -128t127 -339q0 -45 -8.5 -98.5t-17.5 -84.5l-8 -30 h-695q3 -50 46.5 -81t107.5 -31q59 0 112.5 21.5t86.5 53.5l286 -172q-18 -26 -60.5 -64t-112 -83.5t-171 -77t-209.5 -31.5q-282 0 -413 181q-162 -181 -418 -181q-96 0 -172.5 30t-126 98t-49.5 165zM535 352q0 -27 23 -43t58 -16q39 0 70 21t31 55q0 27 -22.5 43 t-57.5 16q-41 0 -71.5 -21.5t-30.5 -54.5zM860 1174l197 319h483l-297 -319h-383zM1225 672h223q5 45 -21.5 75.5t-74.5 30.5q-45 0 -80 -30.5t-47 -75.5z" />
<glyph unicode="&#x1fe;" horiz-adv-x="1617" d="M125 606q0 173 69 329.5t189.5 272t295 184t374.5 68.5q192 0 340 -71l120 122l207 -194l-106 -107q119 -162 119 -381q0 -174 -69 -331t-189.5 -273t-294.5 -184t-375 -68q-189 0 -342 70l-121 -121l-205 195l105 106q-117 163 -117 383zM672 657l362 369q-24 6 -53 6 q-137 0 -222 -108.5t-87 -266.5zM821 408q28 -7 56 -7q135 0 220.5 107t88.5 266zM852 1518l197 319h483l-297 -319h-383z" />
<glyph unicode="&#x1ff;" horiz-adv-x="1234" d="M86 104l98 86q-75 116 -75 273q0 136 55.5 257.5t151 208.5t229.5 138t284 51q188 0 322 -88l102 88l121 -139l-92 -80q76 -118 76 -268q0 -181 -94.5 -332t-260 -238.5t-366.5 -87.5q-188 0 -322 86l-108 -94zM586 539l229 200q-26 11 -47 11q-74 0 -123.5 -59 t-58.5 -152zM590 1174l196 319h484l-297 -319h-383zM651 352q23 -10 49 -10q72 0 122 59t61 150z" />
<glyph unicode="&#x218;" horiz-adv-x="1181" d="M80 346l403 201q30 -69 74.5 -110.5t91.5 -41.5t47 41q0 24 -26 46.5t-67 44t-91 46.5t-100.5 59t-91.5 75t-67 102.5t-26 134.5q0 149 80.5 267t222.5 183.5t318 65.5q94 0 175 -23.5t135 -60t94.5 -82.5t60.5 -89.5t26 -82.5l-399 -209q-15 52 -52 91.5t-83 39.5 q-22 0 -35.5 -12t-13.5 -31q0 -28 35 -57t87 -53.5t113.5 -62t113.5 -80t87 -110.5t35 -149q0 -228 -178 -372t-463 -144q-98 0 -180.5 25t-137 63.5t-95.5 90t-62.5 99t-30.5 95.5zM330 -573q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5q77 0 126.5 -45.5 t49.5 -131.5q0 -135 -91 -264.5t-233 -218.5z" />
<glyph unicode="&#x219;" horiz-adv-x="1003" d="M55 160l232 264q46 -38 124.5 -71.5t127.5 -33.5t49 25q0 12 -14 20t-50 19q-55 17 -92 31t-89 42.5t-83.5 60t-54.5 82t-23 110.5q0 120 70.5 214t189.5 144.5t263 50.5q149 0 252 -50t165 -122l-262 -250q-34 35 -82 58.5t-94 23.5q-51 0 -51 -30q0 -14 21 -25t75 -27 q48 -15 82.5 -28t82 -39t77.5 -55.5t51.5 -77t21.5 -103.5q0 -201 -135 -310.5t-381 -109.5q-146 0 -277 57.5t-196 129.5zM231 -573q99 63 136 127q-57 15 -88 58t-31 99q0 93 72 159.5t169 66.5q77 0 127 -45.5t50 -131.5q0 -135 -91 -264.5t-233 -218.5z" />
<glyph unicode="&#x21a;" horiz-adv-x="1142" d="M203 1030l67 404h1084l-68 -404h-282l-173 -1030h-520l172 1030h-280zM299 -573q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5q77 0 126.5 -45.5t49.5 -131.5q0 -135 -90.5 -264.5t-232.5 -218.5z" />
<glyph unicode="&#x21b;" horiz-adv-x="825" d="M170 750l57 340h111l39 227h454l-38 -227h170l-58 -340h-170l-53 -322q-7 -39 20.5 -55.5t69.5 -16.5l76 6l-62 -358q-76 -20 -182 -20q-226 0 -326.5 107.5t-64.5 324.5l55 334h-98zM195 -573q97 62 135 127q-57 15 -88 58t-31 99q0 93 72 159.5t170 66.5 q77 0 126.5 -45.5t49.5 -131.5q0 -135 -91 -264.5t-233 -218.5z" />
<glyph unicode="&#x237;" d="M-184 -377l57 340q37 -6 82 -6q39 0 67 20.5t35 59.5l176 1053h467l-180 -1076q-24 -146 -100.5 -238t-188.5 -130.5t-266 -38.5q-28 0 -82 6t-67 10z" />
<glyph unicode="&#x2c6;" horiz-adv-x="921" d="M217 1174l270 319h447l164 -319h-391l-35 79l-64 -79h-391z" />
<glyph unicode="&#x2c7;" horiz-adv-x="921" d="M270 1493h392l34 -80l64 80h391l-270 -319h-447z" />
<glyph unicode="&#x2d8;" horiz-adv-x="917" d="M319 1507h347q-16 -92 26 -92q46 0 62 92h348q-27 -158 -147.5 -253t-305.5 -95q-183 0 -270 95t-60 253z" />
<glyph unicode="&#x2d9;" horiz-adv-x="917" d="M455 1337q0 105 74 172.5t194 67.5q94 0 145.5 -47t51.5 -127q0 -105 -73.5 -173.5t-193.5 -68.5q-93 0 -145.5 48.5t-52.5 127.5z" />
<glyph unicode="&#x2da;" horiz-adv-x="917" d="M424 1354q0 109 91 182.5t212 73.5q99 0 163 -55t64 -142q0 -110 -90 -183t-213 -73q-98 0 -162.5 55t-64.5 142zM623 1372q0 -26 14.5 -42.5t40.5 -16.5q34 0 58 24t24 58q0 25 -16 42t-42 17q-32 0 -55.5 -24t-23.5 -58z" />
<glyph unicode="&#x2db;" horiz-adv-x="921" d="M182 -270q0 49 23.5 95t61 80t78.5 58.5t81 36.5h264q-36 -14 -65 -42t-29 -60q0 -25 22 -37.5t54 -12.5l-45 -266q-106 -31 -187 -31q-258 0 -258 179z" />
<glyph unicode="&#x2dc;" horiz-adv-x="919" d="M217 1161q27 161 116.5 255.5t223.5 94.5q52 0 122.5 -29.5t96.5 -29.5q24 0 42 16.5t22 44.5h309q-27 -161 -116.5 -255.5t-223.5 -94.5q-52 0 -122.5 30t-96.5 30q-24 0 -42 -16.5t-22 -45.5h-309z" />
<glyph unicode="&#x2dd;" horiz-adv-x="1042" d="M236 1174l194 319h387l-231 -319h-350zM670 1174l194 319h387l-231 -319h-350z" />
<glyph unicode="&#x1e80;" horiz-adv-x="2166" d="M254 1434h555l2 -789l307 789h410l45 -789l264 789h555l-612 -1434h-523l-49 752l-299 -752h-522zM948 1837h473l90 -319h-362z" />
<glyph unicode="&#x1e81;" horiz-adv-x="1789" d="M199 1090h508l-21 -537l184 537h414l4 -541l162 541h508l-473 -1090h-502l-22 401l-152 -401h-504zM743 1493h474l90 -319h-363z" />
<glyph unicode="&#x1e82;" horiz-adv-x="2166" d="M254 1434h555l2 -789l307 789h410l45 -789l264 789h555l-612 -1434h-523l-49 752l-299 -752h-522zM1126 1518l197 319h483l-297 -319h-383z" />
<glyph unicode="&#x1e83;" horiz-adv-x="1789" d="M199 1090h508l-21 -537l184 537h414l4 -541l162 541h508l-473 -1090h-502l-22 401l-152 -401h-504zM858 1174l197 319h483l-297 -319h-383z" />
<glyph unicode="&#x1e84;" horiz-adv-x="2166" d="M254 1434h555l2 -789l307 789h410l45 -789l264 789h555l-612 -1434h-523l-49 752l-299 -752h-522zM942 1638q0 83 62 143t161 60q79 0 121.5 -42t42.5 -107q0 -85 -61 -143t-162 -58q-79 0 -121.5 40.5t-42.5 106.5zM1391 1638q0 83 62 143t161 60q79 0 121.5 -42 t42.5 -107q0 -85 -61.5 -143t-162.5 -58q-79 0 -121 40.5t-42 106.5z" />
<glyph unicode="&#x1e85;" horiz-adv-x="1789" d="M199 1090h508l-21 -537l184 537h414l4 -541l162 541h508l-473 -1090h-502l-22 401l-152 -401h-504zM700 1313q0 83 62.5 143t161.5 60q78 0 120.5 -42.5t42.5 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107zM1149 1313q0 83 62 143t161 60q78 0 121 -42.5 t43 -107.5q0 -85 -61 -143t-162 -58q-79 0 -121.5 41t-42.5 107z" />
<glyph unicode="&#x1ef2;" horiz-adv-x="1449" d="M256 1434h547l76 -408l211 408h583l-586 -822l-102 -612h-520l102 612zM631 1837h473l90 -319h-363z" />
<glyph unicode="&#x1ef3;" horiz-adv-x="1275" d="M197 1090h489l41 -515l223 515h496l-793 -1457h-440l217 422zM492 1493h473l90 -319h-363z" />
<glyph unicode="&#x2000;" horiz-adv-x="1031" />
<glyph unicode="&#x2001;" horiz-adv-x="2062" />
<glyph unicode="&#x2002;" horiz-adv-x="1031" />
<glyph unicode="&#x2003;" horiz-adv-x="2062" />
<glyph unicode="&#x2004;" horiz-adv-x="687" />
<glyph unicode="&#x2005;" horiz-adv-x="515" />
<glyph unicode="&#x2006;" horiz-adv-x="343" />
<glyph unicode="&#x2007;" horiz-adv-x="343" />
<glyph unicode="&#x2008;" horiz-adv-x="257" />
<glyph unicode="&#x2009;" horiz-adv-x="412" />
<glyph unicode="&#x200a;" horiz-adv-x="114" />
<glyph unicode="&#x2010;" horiz-adv-x="829" d="M145 459l60 352h688l-59 -352h-689z" />
<glyph unicode="&#x2011;" horiz-adv-x="829" d="M145 459l60 352h688l-59 -352h-689z" />
<glyph unicode="&#x2012;" horiz-adv-x="829" d="M145 459l60 352h688l-59 -352h-689z" />
<glyph unicode="&#x2013;" horiz-adv-x="1443" d="M158 467l57 342h1284l-57 -342h-1284z" />
<glyph unicode="&#x2014;" horiz-adv-x="2017" d="M170 467l57 342h1831l-57 -342h-1831z" />
<glyph unicode="&#x2018;" horiz-adv-x="694" d="M246 918q0 159 114 330t306 284l147 -205q-50 -27 -102 -73.5t-80 -88.5q66 -25 99.5 -76t33.5 -116q0 -110 -87 -190.5t-212 -80.5q-99 0 -159 57.5t-60 158.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="694" d="M254 907q51 28 103.5 74.5t78.5 87.5q-65 25 -99 77t-34 116q0 110 87 190t212 80q99 0 159 -57t60 -158q0 -159 -114 -330.5t-306 -284.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="550" d="M2 -176q51 28 103.5 74.5t78.5 87.5q-65 25 -99 76.5t-34 115.5q0 110 87 190.5t212 80.5q99 0 159 -57.5t60 -158.5q0 -159 -113.5 -330t-305.5 -284z" />
<glyph unicode="&#x201c;" horiz-adv-x="1269" d="M246 918q0 159 114 330t306 284l147 -205q-50 -27 -102 -73.5t-80 -88.5q66 -25 99.5 -76t33.5 -116q0 -110 -87 -190.5t-212 -80.5q-99 0 -159 57.5t-60 158.5zM821 918q0 159 114 330t306 284l148 -205q-51 -27 -103 -73.5t-80 -88.5q66 -25 99.5 -76t33.5 -116 q0 -110 -87 -190.5t-212 -80.5q-99 0 -159 57.5t-60 158.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="1269" d="M254 907q51 28 103.5 74.5t78.5 87.5q-65 25 -99 77t-34 116q0 110 87 190t212 80q99 0 159 -57t60 -158q0 -159 -114 -330.5t-306 -284.5zM829 907q51 28 104 74.5t79 87.5q-65 25 -99 77t-34 116q0 110 87 190t212 80q99 0 159 -57t60 -158q0 -159 -114 -330.5 t-306 -284.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="1269" d="M74 -176q51 28 103.5 74.5t78.5 87.5q-65 25 -99 76.5t-34 115.5q0 110 87 190.5t212 80.5q99 0 159 -57.5t60 -158.5q0 -159 -114 -330t-306 -284zM649 -176q51 28 103.5 74.5t78.5 87.5q-65 25 -99 76.5t-34 115.5q0 110 87 190.5t212 80.5q99 0 159.5 -57.5 t60.5 -158.5q0 -159 -114 -330t-306 -284z" />
<glyph unicode="&#x2020;" horiz-adv-x="976" d="M141 752l62 362h241l54 320h460l-53 -320h240l-62 -362h-239l-125 -752h-461l125 752h-242z" />
<glyph unicode="&#x2021;" horiz-adv-x="974" d="M68 307l59 359h242l16 98h-242l62 362h241l52 308h460l-51 -308h240l-62 -362h-239l-17 -98h240l-59 -359h-240l-51 -307h-461l51 307h-241z" />
<glyph unicode="&#x2022;" horiz-adv-x="831" d="M164 584q0 164 123.5 283.5t292.5 119.5q133 0 216 -86.5t83 -214.5q0 -164 -123.5 -283.5t-292.5 -119.5q-134 0 -216.5 86t-82.5 215z" />
<glyph unicode="&#x202f;" horiz-adv-x="412" />
<glyph unicode="&#x2030;" horiz-adv-x="2686" d="M190 0l1295 1434h393l-1294 -1434h-394zM209 1042q0 171 136.5 294.5t330.5 123.5q156 0 251 -90.5t95 -228.5q0 -171 -137.5 -294.5t-331.5 -123.5q-155 0 -249.5 90.5t-94.5 228.5zM563 1075q0 -25 10.5 -40t28.5 -15q27 0 45.5 26t18.5 62q0 25 -10.5 40t-28.5 15 q-27 0 -45.5 -26t-18.5 -62zM1044 293q0 171 137.5 294.5t331.5 123.5q155 0 250 -91t95 -229q0 -171 -136.5 -294.5t-330.5 -123.5q-157 0 -252 91t-95 229zM1401 326q0 -25 10.5 -40.5t28.5 -15.5q27 0 45 26t18 62q0 25 -10.5 40.5t-28.5 15.5q-27 0 -45 -26t-18 -62z M1901 293q0 171 137.5 294.5t331.5 123.5q155 0 249.5 -91t94.5 -229q0 -171 -136.5 -294.5t-330.5 -123.5q-155 0 -250.5 91t-95.5 229zM2257 326q0 -25 10.5 -40.5t28.5 -15.5q27 0 45 26t18 62q0 25 -10.5 40.5t-28.5 15.5q-27 0 -45 -26t-18 -62z" />
<glyph unicode="&#x2039;" horiz-adv-x="753" d="M172 606l293 387h387l-291 -387l160 -387h-387z" />
<glyph unicode="&#x203a;" horiz-adv-x="753" d="M106 219l291 387l-161 387h387l163 -387l-292 -387h-388z" />
<glyph unicode="&#x2044;" horiz-adv-x="1130" d="M-29 -84l1022 1602h408l-1024 -1602h-406z" />
<glyph unicode="&#x205f;" horiz-adv-x="515" />
<glyph unicode="&#x20ac;" horiz-adv-x="1409" d="M115 440l41 246h94l10 55h-94l43 260h151q113 196 325 328.5t454 132.5q247 0 397 -102l-225 -340q-90 55 -205 55q-121 0 -223 -74h329l-71 -260h-389q-11 -26 -11 -55h383l-63 -246h-260q54 -71 196 -71q59 0 123 18.5t111 46.5l104 -334q-69 -54 -179.5 -90.5 t-240.5 -36.5q-267 0 -441 127.5t-210 339.5h-149z" />
<glyph unicode="&#x2116;" horiz-adv-x="2318" d="M61 0l240 1434h526l199 -674l113 674h514l-240 -1434h-526l-199 674l-113 -674h-514zM1548 324l58 335h759l-57 -335h-760zM1647 1026q0 115 62 215t171 159.5t238 59.5q157 0 251.5 -93.5t94.5 -233.5q0 -177 -138 -306t-335 -129q-157 0 -250.5 93.5t-93.5 234.5z M2005 1061q0 -64 35 -64q26 0 44.5 32.5t18.5 68.5q0 63 -35 63q-26 0 -44.5 -32t-18.5 -68z" />
<glyph unicode="&#x2122;" horiz-adv-x="1714" d="M274 1206l39 228h609l-39 -228h-172l-86 -512h-265l86 512h-172zM856 694l125 740h336l49 -365l176 365h340l-125 -740h-254l58 338l-123 -258h-234l-37 258l-57 -338h-254z" />
<glyph unicode="&#x2212;" horiz-adv-x="1099" d="M98 469l56 332h1058l-55 -332h-1059z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1085" d="M0 0v1085h1085v-1085h-1085z" />
<glyph unicode="&#xf6c3;" horiz-adv-x="727" d="M92 -573q97 62 135 127q-57 15 -87.5 58t-30.5 99q0 93 72 159.5t169 66.5q77 0 126.5 -45.5t49.5 -131.5q0 -135 -90.5 -264.5t-232.5 -218.5z" />
<glyph horiz-adv-x="829" d="M162 545l59 352h688l-59 -352h-688z" />
<glyph horiz-adv-x="1443" d="M160 555l57 342h1284l-57 -342h-1284z" />
<glyph horiz-adv-x="1966" d="M160 555l57 342h1831l-57 -342h-1831z" />
<glyph horiz-adv-x="1210" d="M188 713l293 387h387l-290 -387l159 -387h-387zM645 713l293 387h387l-291 -387l160 -387h-387z" />
<glyph horiz-adv-x="1210" d="M125 326l291 387l-162 387h387l164 -387l-293 -387h-387zM582 326l290 387l-161 387h387l164 -387l-293 -387h-387z" />
<glyph horiz-adv-x="753" d="M188 713l293 387h387l-290 -387l159 -387h-387z" />
<glyph horiz-adv-x="753" d="M125 326l291 387l-162 387h387l164 -387l-293 -387h-387z" />
<glyph horiz-adv-x="780" d="M70 166l182 1098q42 247 228.5 376t477.5 129l-59 -358q-88 0 -130.5 -37t-55.5 -110l-185 -1098q-11 -73 19.5 -110.5t118.5 -37.5l-60 -358q-291 0 -434.5 129.5t-101.5 376.5z" />
<glyph horiz-adv-x="780" d="M59 -340l60 358q88 0 131.5 37.5t54.5 110.5l184 1098q11 73 -19 110t-118 37l60 358q291 0 434.5 -129t103.5 -376l-184 -1098q-27 -165 -123.5 -279.5t-244.5 -170.5t-339 -56z" />
<glyph horiz-adv-x="700" d="M-16 -340l352 2109h622l-59 -358h-162l-233 -1393h162l-60 -358h-622z" />
<glyph horiz-adv-x="700" d="M-16 -340l59 358h162l233 1393h-162l60 358h622l-352 -2109h-622z" />
<glyph horiz-adv-x="870" d="M127 518l66 395q71 0 115 33.5t61 132.5l30 185q27 164 129 279t255 170.5t345 55.5l-59 -358q-96 0 -145 -37.5t-62 -109.5l-47 -277q-18 -102 -86 -173.5t-151 -98.5q74 -28 119 -99.5t28 -171.5l-47 -280q-11 -71 25.5 -108.5t132.5 -37.5l-60 -358q-293 0 -447 130.5 t-114 375.5l31 186q10 43 5 73t-13 48t-27.5 28t-38 13.5t-45.5 3.5z" />
<glyph horiz-adv-x="643" d="M27 -428l385 2302h446l-385 -2302h-446z" />
<glyph horiz-adv-x="952" d="M61 -340l60 358q184 0 207 146l47 280q17 100 86 171.5t151 99.5q-74 28 -118 99.5t-27 172.5l45 277q12 72 -25 109.5t-133 37.5l60 358q293 0 447 -130t114 -375l-31 -185q-10 -43 -5 -73t13 -48t27.5 -28t38 -13.5t45.5 -3.5l-66 -395q-71 0 -115 -33.5t-61 -132.5 l-30 -186q-27 -164 -129.5 -279.5t-255.5 -171t-345 -55.5z" />
<glyph horiz-adv-x="997" d="M-41 -168l909 1757h410l-909 -1757h-410z" />
<glyph horiz-adv-x="997" d="M254 1589h410l319 -1757h-410z" />
<glyph horiz-adv-x="671" d="M84 0l195 956h434l-125 -956h-504zM279 1192q0 107 88 187.5t211 80.5q101 0 162 -59t61 -146q0 -110 -88.5 -190t-214.5 -80q-99 0 -159 58.5t-60 148.5z" />
<glyph horiz-adv-x="1036" d="M74 330q0 65 21 121.5t54.5 98t74.5 78t83.5 68t79 61t62.5 63.5t32 69l13 67h452l-16 -92q-12 -70 -55.5 -134t-96.5 -109t-104 -84t-85 -75.5t-34 -68.5q0 -23 13.5 -38t39.5 -15q37 0 70.5 31.5t54.5 79.5l336 -158q-172 -320 -551 -320q-199 0 -321.5 98t-122.5 259z M504 1192q0 108 87.5 188t213.5 80q100 0 160.5 -59t60.5 -146q0 -110 -87 -190t-212 -80q-102 0 -162.5 58.5t-60.5 148.5z" />
<glyph d="M141 674q0 110 87 190t212 80q101 0 161.5 -58.5t60.5 -148.5q0 -107 -88 -187.5t-214 -80.5q-99 0 -159 59t-60 146z" />
<glyph horiz-adv-x="831" d="M180 674q0 107 57 200.5t153 148t206 54.5q134 0 216.5 -86t82.5 -215q0 -164 -123.5 -283.5t-292.5 -119.5q-134 0 -216.5 86t-82.5 215z" />
<glyph horiz-adv-x="1298" d="M195 717q59 373 228.5 558t459.5 185t401.5 -185t52.5 -558q-60 -374 -230 -559t-460 -185t-400.5 185t-51.5 559zM696 717q-54 -334 17 -334q69 0 123 334q51 338 -17 338q-72 0 -123 -338z" />
<glyph horiz-adv-x="1298" d="M143 0l66 387h319l103 621l-273 -76l-39 362l480 140h377l-175 -1047h254l-65 -387h-1047z" />
<glyph horiz-adv-x="1298" d="M109 0l34 203q16 92 63.5 174.5t111 143t136.5 115t142.5 99.5t126.5 87t92 88.5t35 93.5q0 23 -15 37t-38 14q-75 0 -115 -123l-422 102q160 426 623 426q212 0 348.5 -111.5t136.5 -275.5q0 -86 -31 -165t-81.5 -137.5t-111 -110t-122 -91t-113 -71.5t-85.5 -60.5 t-38 -50.5h484l-66 -387h-1095z" />
<glyph horiz-adv-x="1298" d="M160 383l446 94q0 -43 24.5 -69.5t59.5 -26.5q44 0 73.5 27.5t29.5 70.5q0 35 -25.5 58.5t-69.5 23.5h-219l45 281l273 205h-463l65 387h979l-59 -355l-234 -184q93 -36 149 -121t56 -215q0 -170 -88 -305t-236.5 -208t-326.5 -73q-106 0 -190 27t-135.5 68.5t-86.5 98 t-49.5 109t-17.5 107.5z" />
<glyph horiz-adv-x="1298" d="M82 291l49 295l674 848h559l-129 -770h141l-61 -373h-141l-50 -291h-495l49 291h-596zM582 664h157l45 268z" />
<glyph horiz-adv-x="1298" d="M147 379l459 98q-3 -49 21 -70.5t59 -21.5q39 0 73 30t34 85q0 38 -21.5 62t-58.5 24q-58 0 -90 -56l-400 58l174 846h965l-64 -387h-538l-25 -113q63 20 137 20q186 0 308.5 -111.5t122.5 -291.5q0 -158 -91 -292t-242.5 -210t-326.5 -76q-105 0 -191 26.5t-140 68 t-91.5 96.5t-54 108t-19.5 107z" />
<glyph horiz-adv-x="1298" d="M170 475q0 80 10.5 164t37 182t66.5 187t106 173t149 145t200.5 97.5t255.5 36.5q236 0 383 -102l-223 -359q-90 54 -197 54q-77 0 -124.5 -42t-63.5 -106q28 15 82 30t106 15q150 0 249.5 -94.5t99.5 -267.5q0 -130 -51.5 -245t-138.5 -196t-207.5 -127.5t-254.5 -46.5 q-250 0 -367.5 132t-117.5 370zM672.5 434.5q13.5 -57.5 58.5 -57.5q39 0 64.5 40.5t25.5 96.5q0 44 -18 72t-49 28q-41 0 -74 -22q-21 -100 -7.5 -157.5z" />
<glyph horiz-adv-x="1298" d="M219 0l594 1053h-543l64 381h1108l-47 -285l-643 -1149h-533z" />
<glyph horiz-adv-x="1298" d="M145 379q0 118 55.5 219.5t145.5 165.5q-34 37 -54 94t-20 119q0 97 44 184.5t122.5 154t196.5 105.5t258 39q226 0 350.5 -105.5t124.5 -271.5q0 -98 -45 -180.5t-117 -138.5q45 -42 71 -110.5t26 -143.5q0 -236 -181.5 -386.5t-476.5 -150.5q-236 0 -368 109.5 t-132 296.5zM639 436q0 -40 18 -64t50 -24q45 0 74.5 39.5t29.5 95.5q0 37 -16.5 64.5t-49.5 27.5q-49 0 -77.5 -44.5t-28.5 -94.5zM745 969q0 -33 14 -51.5t40 -18.5q37 0 60.5 35.5t23.5 81.5q0 74 -52 74q-38 0 -62 -36.5t-24 -84.5z" />
<glyph horiz-adv-x="1298" d="M158 76l223 358q37 -22 91 -37.5t106 -15.5q79 0 126.5 39.5t61.5 107.5q-28 -15 -82 -30t-106 -15q-150 0 -249.5 95t-99.5 268q0 174 87 315t236.5 220t328.5 79q250 0 367.5 -132t117.5 -370q0 -80 -10.5 -164t-37 -182t-66.5 -187t-106 -173t-149 -145t-200.5 -97.5 t-255.5 -36.5q-235 0 -383 103zM715 920q0 -44 18 -72.5t49 -28.5q40 0 74 23q21 100 7.5 157.5t-58.5 57.5q-39 0 -64.5 -40.5t-25.5 -96.5z" />
<hkern u1="&#x2d;" u2="&#xc5;" k="102" />
<hkern u1="&#x2d;" u2="&#xc4;" k="102" />
<hkern u1="&#x2d;" u2="&#xc3;" k="102" />
<hkern u1="&#x2d;" u2="&#xc2;" k="102" />
<hkern u1="&#x2d;" u2="&#xc1;" k="102" />
<hkern u1="&#x2d;" u2="&#xc0;" k="102" />
<hkern u1="&#x2d;" u2="x" k="41" />
<hkern u1="&#x2d;" u2="Z" k="61" />
<hkern u1="&#x2d;" u2="Y" k="164" />
<hkern u1="&#x2d;" u2="X" k="143" />
<hkern u1="&#x2d;" u2="V" k="143" />
<hkern u1="&#x2d;" u2="&#x37;" k="82" />
<hkern u1="&#x2d;" u2="&#x31;" k="102" />
<hkern u1="&#x2e;" u2="&#x134;" k="-41" />
<hkern u1="&#x2e;" u2="J" k="-41" />
<hkern u1="&#x2f;" u2="&#x39;" k="41" />
<hkern u1="&#x2f;" u2="&#x37;" k="-92" />
<hkern u1="&#x2f;" u2="&#x36;" k="61" />
<hkern u1="&#x2f;" u2="&#x34;" k="184" />
<hkern u1="&#x30;" u2="&#x2e;" k="61" />
<hkern u1="&#x31;" u2="&#x2044;" k="-41" />
<hkern u1="&#x31;" u2="&#x32;" k="-41" />
<hkern u1="&#x31;" u2="&#x31;" k="-31" />
<hkern u1="&#x31;" u2="&#x2e;" k="10" />
<hkern u1="&#x32;" u2="&#x2044;" k="-82" />
<hkern u1="&#x32;" u2="&#x2e;" k="-20" />
<hkern u1="&#x33;" u2="&#x2f;" k="41" />
<hkern u1="&#x34;" u2="&#x37;" k="41" />
<hkern u1="&#x36;" u2="&#x39;" k="41" />
<hkern u1="&#x36;" u2="&#x31;" k="61" />
<hkern u1="&#x36;" u2="&#x2f;" k="61" />
<hkern u1="&#x36;" u2="&#x2e;" k="61" />
<hkern u1="&#x37;" u2="&#x2044;" k="205" />
<hkern u1="&#x37;" g2="ellipsis" k="102" />
<hkern u1="&#x37;" u2="&#x2014;" k="72" />
<hkern u1="&#x37;" u2="&#x2013;" k="72" />
<hkern u1="&#x37;" u2="&#x37;" k="-61" />
<hkern u1="&#x37;" u2="&#x34;" k="123" />
<hkern u1="&#x37;" u2="&#x31;" k="-20" />
<hkern u1="&#x37;" u2="&#x2f;" k="164" />
<hkern u1="&#x37;" u2="&#x2e;" k="143" />
<hkern u1="&#x37;" u2="&#x2d;" k="72" />
<hkern u1="&#x37;" u2="&#x2c;" k="102" />
<hkern u1="&#x37;" u2="&#x27;" k="-41" />
<hkern u1="&#x37;" u2="&#x22;" k="-41" />
<hkern u1="&#x39;" u2="&#x31;" k="61" />
<hkern u1="&#x39;" u2="&#x2f;" k="61" />
<hkern u1="&#x39;" u2="&#x2e;" k="61" />
<hkern u1="A" u2="&#x2122;" k="266" />
<hkern u1="A" u2="&#x2019;" k="225" />
<hkern u1="A" u2="&#x2014;" k="102" />
<hkern u1="A" u2="&#x2013;" k="102" />
<hkern u1="A" u2="&#x152;" k="102" />
<hkern u1="A" u2="&#xfc;" k="41" />
<hkern u1="A" u2="&#xfb;" k="41" />
<hkern u1="A" u2="&#xfa;" k="41" />
<hkern u1="A" u2="&#xf9;" k="41" />
<hkern u1="A" u2="&#xf6;" k="102" />
<hkern u1="A" u2="&#xf5;" k="102" />
<hkern u1="A" u2="&#xf4;" k="102" />
<hkern u1="A" u2="&#xf3;" k="102" />
<hkern u1="A" u2="&#xf2;" k="102" />
<hkern u1="A" u2="&#xeb;" k="102" />
<hkern u1="A" u2="&#xea;" k="102" />
<hkern u1="A" u2="&#xe9;" k="102" />
<hkern u1="A" u2="&#xe8;" k="102" />
<hkern u1="A" u2="&#xe6;" k="41" />
<hkern u1="A" u2="&#xe5;" k="41" />
<hkern u1="A" u2="&#xe4;" k="41" />
<hkern u1="A" u2="&#xe3;" k="41" />
<hkern u1="A" u2="&#xe2;" k="41" />
<hkern u1="A" u2="&#xe1;" k="41" />
<hkern u1="A" u2="&#xe0;" k="41" />
<hkern u1="A" u2="&#xdc;" k="102" />
<hkern u1="A" u2="&#xdb;" k="102" />
<hkern u1="A" u2="&#xda;" k="102" />
<hkern u1="A" u2="&#xd9;" k="102" />
<hkern u1="A" u2="&#xd8;" k="102" />
<hkern u1="A" u2="&#xd6;" k="102" />
<hkern u1="A" u2="&#xd5;" k="102" />
<hkern u1="A" u2="&#xd4;" k="102" />
<hkern u1="A" u2="&#xd3;" k="102" />
<hkern u1="A" u2="&#xd2;" k="102" />
<hkern u1="A" u2="&#xc7;" k="102" />
<hkern u1="A" u2="&#xba;" k="184" />
<hkern u1="A" u2="&#xae;" k="205" />
<hkern u1="A" u2="&#xaa;" k="164" />
<hkern u1="A" u2="&#xa9;" k="102" />
<hkern u1="A" u2="y" k="246" />
<hkern u1="A" u2="v" k="205" />
<hkern u1="A" u2="s" k="41" />
<hkern u1="A" u2="q" k="102" />
<hkern u1="A" u2="o" k="102" />
<hkern u1="A" u2="g" k="102" />
<hkern u1="A" u2="e" k="102" />
<hkern u1="A" u2="d" k="102" />
<hkern u1="A" u2="c" k="102" />
<hkern u1="A" u2="Y" k="338" />
<hkern u1="A" u2="V" k="348" />
<hkern u1="A" u2="S" k="61" />
<hkern u1="A" u2="Q" k="102" />
<hkern u1="A" u2="O" k="102" />
<hkern u1="A" u2="G" k="102" />
<hkern u1="A" u2="&#x3f;" k="143" />
<hkern u1="A" u2="&#x2a;" k="205" />
<hkern u1="B" u2="&#x174;" k="82" />
<hkern u1="B" u2="&#x104;" k="61" />
<hkern u1="B" u2="&#x102;" k="61" />
<hkern u1="B" u2="&#x100;" k="61" />
<hkern u1="B" u2="&#xc5;" k="61" />
<hkern u1="B" u2="&#xc4;" k="61" />
<hkern u1="B" u2="&#xc3;" k="61" />
<hkern u1="B" u2="&#xc2;" k="61" />
<hkern u1="B" u2="&#xc1;" k="61" />
<hkern u1="B" u2="&#xc0;" k="61" />
<hkern u1="B" u2="Y" k="143" />
<hkern u1="B" u2="X" k="133" />
<hkern u1="B" u2="W" k="82" />
<hkern u1="B" u2="V" k="102" />
<hkern u1="B" u2="A" k="61" />
<hkern u1="C" u2="&#x152;" k="82" />
<hkern u1="C" u2="&#xf6;" k="41" />
<hkern u1="C" u2="&#xf5;" k="41" />
<hkern u1="C" u2="&#xf4;" k="41" />
<hkern u1="C" u2="&#xf3;" k="41" />
<hkern u1="C" u2="&#xf2;" k="41" />
<hkern u1="C" u2="&#xd8;" k="82" />
<hkern u1="C" u2="&#xd6;" k="82" />
<hkern u1="C" u2="&#xd5;" k="82" />
<hkern u1="C" u2="&#xd4;" k="82" />
<hkern u1="C" u2="&#xd3;" k="82" />
<hkern u1="C" u2="&#xd2;" k="82" />
<hkern u1="C" u2="&#xc7;" k="82" />
<hkern u1="C" u2="&#xa9;" k="82" />
<hkern u1="C" u2="y" k="82" />
<hkern u1="C" u2="o" k="41" />
<hkern u1="C" u2="e" k="41" />
<hkern u1="C" u2="d" k="41" />
<hkern u1="C" u2="Y" k="41" />
<hkern u1="C" u2="Q" k="82" />
<hkern u1="C" u2="O" k="82" />
<hkern u1="C" u2="G" k="82" />
<hkern u1="D" g2="ellipsis" k="20" />
<hkern u1="D" u2="&#x104;" k="113" />
<hkern u1="D" u2="&#x102;" k="113" />
<hkern u1="D" u2="&#x100;" k="113" />
<hkern u1="D" u2="&#xc5;" k="113" />
<hkern u1="D" u2="&#xc4;" k="113" />
<hkern u1="D" u2="&#xc3;" k="113" />
<hkern u1="D" u2="&#xc2;" k="113" />
<hkern u1="D" u2="&#xc1;" k="113" />
<hkern u1="D" u2="&#xc0;" k="113" />
<hkern u1="D" u2="Y" k="164" />
<hkern u1="D" u2="A" k="113" />
<hkern u1="D" u2="&#x2e;" k="20" />
<hkern u1="D" u2="&#x2c;" k="20" />
<hkern u1="E" u2="&#x2122;" k="-41" />
<hkern u1="F" u2="&#x2122;" k="-41" />
<hkern u1="F" g2="ellipsis" k="102" />
<hkern u1="F" u2="&#x134;" k="102" />
<hkern u1="F" u2="&#x105;" k="51" />
<hkern u1="F" u2="&#x104;" k="123" />
<hkern u1="F" u2="&#x103;" k="51" />
<hkern u1="F" u2="&#x102;" k="123" />
<hkern u1="F" u2="&#x101;" k="51" />
<hkern u1="F" u2="&#x100;" k="123" />
<hkern u1="F" u2="&#xf1;" k="41" />
<hkern u1="F" u2="&#xe6;" k="51" />
<hkern u1="F" u2="&#xe5;" k="51" />
<hkern u1="F" u2="&#xe4;" k="51" />
<hkern u1="F" u2="&#xe3;" k="51" />
<hkern u1="F" u2="&#xe2;" k="51" />
<hkern u1="F" u2="&#xe1;" k="51" />
<hkern u1="F" u2="&#xe0;" k="51" />
<hkern u1="F" u2="&#xc6;" k="174" />
<hkern u1="F" u2="&#xc5;" k="123" />
<hkern u1="F" u2="&#xc4;" k="123" />
<hkern u1="F" u2="&#xc3;" k="123" />
<hkern u1="F" u2="&#xc2;" k="123" />
<hkern u1="F" u2="&#xc1;" k="123" />
<hkern u1="F" u2="&#xc0;" k="123" />
<hkern u1="F" u2="p" k="41" />
<hkern u1="F" u2="n" k="41" />
<hkern u1="F" u2="a" k="51" />
<hkern u1="F" u2="J" k="102" />
<hkern u1="F" u2="A" k="123" />
<hkern u1="F" u2="&#x2e;" k="102" />
<hkern u1="F" u2="&#x2c;" k="102" />
<hkern u1="G" u2="y" k="41" />
<hkern u1="G" u2="Y" k="143" />
<hkern u1="G" u2="G" k="-10" />
<hkern u1="H" u2="&#x2044;" k="-41" />
<hkern u1="H" u2="&#x32;" k="-41" />
<hkern u1="H" u2="&#x31;" k="-31" />
<hkern u1="H" u2="&#x2e;" k="10" />
<hkern u1="I" u2="&#x2044;" k="-41" />
<hkern u1="I" u2="&#x32;" k="-41" />
<hkern u1="I" u2="&#x31;" k="-31" />
<hkern u1="I" u2="&#x2e;" k="10" />
<hkern u1="J" u2="&#x2e;" k="61" />
<hkern u1="K" u2="&#x2122;" k="-41" />
<hkern u1="K" u2="&#x2014;" k="164" />
<hkern u1="K" u2="&#x2013;" k="164" />
<hkern u1="K" u2="&#x152;" k="164" />
<hkern u1="K" u2="&#xfc;" k="72" />
<hkern u1="K" u2="&#xfb;" k="72" />
<hkern u1="K" u2="&#xfa;" k="72" />
<hkern u1="K" u2="&#xf9;" k="72" />
<hkern u1="K" u2="&#xf6;" k="123" />
<hkern u1="K" u2="&#xf5;" k="123" />
<hkern u1="K" u2="&#xf4;" k="123" />
<hkern u1="K" u2="&#xf3;" k="123" />
<hkern u1="K" u2="&#xf2;" k="123" />
<hkern u1="K" u2="&#xeb;" k="123" />
<hkern u1="K" u2="&#xea;" k="123" />
<hkern u1="K" u2="&#xe9;" k="123" />
<hkern u1="K" u2="&#xe8;" k="123" />
<hkern u1="K" u2="&#xe6;" k="82" />
<hkern u1="K" u2="&#xe5;" k="82" />
<hkern u1="K" u2="&#xe4;" k="82" />
<hkern u1="K" u2="&#xe3;" k="82" />
<hkern u1="K" u2="&#xe2;" k="82" />
<hkern u1="K" u2="&#xe1;" k="82" />
<hkern u1="K" u2="&#xe0;" k="82" />
<hkern u1="K" u2="&#xd8;" k="164" />
<hkern u1="K" u2="&#xd6;" k="164" />
<hkern u1="K" u2="&#xd5;" k="164" />
<hkern u1="K" u2="&#xd4;" k="164" />
<hkern u1="K" u2="&#xd3;" k="164" />
<hkern u1="K" u2="&#xd2;" k="164" />
<hkern u1="K" u2="&#xc7;" k="164" />
<hkern u1="K" u2="&#xa9;" k="164" />
<hkern u1="K" u2="y" k="154" />
<hkern u1="K" u2="o" k="123" />
<hkern u1="K" u2="e" k="123" />
<hkern u1="K" u2="S" k="102" />
<hkern u1="K" u2="Q" k="164" />
<hkern u1="K" u2="O" k="164" />
<hkern u1="K" u2="G" k="164" />
<hkern u1="L" u2="&#x2122;" k="143" />
<hkern u1="L" u2="&#x2014;" k="123" />
<hkern u1="L" u2="&#x2013;" k="123" />
<hkern u1="L" u2="&#x174;" k="184" />
<hkern u1="L" u2="&#x172;" k="31" />
<hkern u1="L" u2="&#x170;" k="31" />
<hkern u1="L" u2="&#x16e;" k="31" />
<hkern u1="L" u2="&#x16c;" k="31" />
<hkern u1="L" u2="&#x16a;" k="31" />
<hkern u1="L" u2="&#x168;" k="31" />
<hkern u1="L" u2="&#x166;" k="184" />
<hkern u1="L" u2="&#x164;" k="184" />
<hkern u1="L" u2="&#x162;" k="184" />
<hkern u1="L" u2="&#xdc;" k="31" />
<hkern u1="L" u2="&#xdb;" k="31" />
<hkern u1="L" u2="&#xda;" k="31" />
<hkern u1="L" u2="&#xd9;" k="31" />
<hkern u1="L" u2="y" k="184" />
<hkern u1="L" u2="Y" k="246" />
<hkern u1="L" u2="W" k="184" />
<hkern u1="L" u2="V" k="246" />
<hkern u1="L" u2="U" k="31" />
<hkern u1="L" u2="T" k="184" />
<hkern u1="L" u2="O" k="31" />
<hkern u1="L" u2="&#x2d;" k="123" />
<hkern u1="M" u2="&#x2044;" k="-41" />
<hkern u1="M" u2="&#x32;" k="-41" />
<hkern u1="M" u2="&#x31;" k="-31" />
<hkern u1="M" u2="&#x2e;" k="10" />
<hkern u1="N" u2="&#x2044;" k="-41" />
<hkern u1="N" u2="&#x32;" k="-41" />
<hkern u1="N" u2="&#x31;" k="-31" />
<hkern u1="N" u2="&#x2e;" k="10" />
<hkern u1="O" g2="ellipsis" k="20" />
<hkern u1="O" u2="&#x174;" k="102" />
<hkern u1="O" u2="&#x166;" k="20" />
<hkern u1="O" u2="&#x164;" k="20" />
<hkern u1="O" u2="&#x162;" k="20" />
<hkern u1="O" u2="&#x104;" k="102" />
<hkern u1="O" u2="&#x102;" k="102" />
<hkern u1="O" u2="&#x100;" k="102" />
<hkern u1="O" u2="&#xc5;" k="102" />
<hkern u1="O" u2="&#xc4;" k="102" />
<hkern u1="O" u2="&#xc3;" k="102" />
<hkern u1="O" u2="&#xc2;" k="102" />
<hkern u1="O" u2="&#xc1;" k="102" />
<hkern u1="O" u2="&#xc0;" k="102" />
<hkern u1="O" u2="z" k="-20" />
<hkern u1="O" u2="Y" k="164" />
<hkern u1="O" u2="X" k="164" />
<hkern u1="O" u2="W" k="102" />
<hkern u1="O" u2="V" k="123" />
<hkern u1="O" u2="T" k="20" />
<hkern u1="O" u2="A" k="102" />
<hkern u1="O" u2="&#x2e;" k="20" />
<hkern u1="O" u2="&#x2c;" k="20" />
<hkern u1="P" u2="&#x175;" k="-10" />
<hkern u1="P" u2="&#x173;" k="-10" />
<hkern u1="P" u2="&#x171;" k="-10" />
<hkern u1="P" u2="&#x16f;" k="-10" />
<hkern u1="P" u2="&#x16d;" k="-10" />
<hkern u1="P" u2="&#x16b;" k="-10" />
<hkern u1="P" u2="&#x169;" k="-10" />
<hkern u1="P" u2="&#x167;" k="-20" />
<hkern u1="P" u2="&#x165;" k="-20" />
<hkern u1="P" u2="&#x163;" k="-20" />
<hkern u1="P" u2="&#x104;" k="205" />
<hkern u1="P" u2="&#x102;" k="205" />
<hkern u1="P" u2="&#x100;" k="205" />
<hkern u1="P" u2="&#xfc;" k="-10" />
<hkern u1="P" u2="&#xfb;" k="-10" />
<hkern u1="P" u2="&#xfa;" k="-10" />
<hkern u1="P" u2="&#xf9;" k="-10" />
<hkern u1="P" u2="&#xc6;" k="287" />
<hkern u1="P" u2="&#xc5;" k="205" />
<hkern u1="P" u2="&#xc4;" k="205" />
<hkern u1="P" u2="&#xc3;" k="205" />
<hkern u1="P" u2="&#xc2;" k="205" />
<hkern u1="P" u2="&#xc1;" k="205" />
<hkern u1="P" u2="&#xc0;" k="205" />
<hkern u1="P" u2="y" k="-10" />
<hkern u1="P" u2="w" k="-10" />
<hkern u1="P" u2="v" k="-41" />
<hkern u1="P" u2="u" k="-10" />
<hkern u1="P" u2="t" k="-20" />
<hkern u1="P" u2="s" k="20" />
<hkern u1="P" u2="Y" k="123" />
<hkern u1="P" u2="A" k="205" />
<hkern u1="P" u2="&#x2e;" k="123" />
<hkern u1="Q" u2="&#x173;" k="-20" />
<hkern u1="Q" u2="&#x171;" k="-20" />
<hkern u1="Q" u2="&#x16f;" k="-20" />
<hkern u1="Q" u2="&#x16d;" k="-20" />
<hkern u1="Q" u2="&#x16b;" k="-20" />
<hkern u1="Q" u2="&#x169;" k="-20" />
<hkern u1="Q" u2="&#xfc;" k="-20" />
<hkern u1="Q" u2="&#xfb;" k="-20" />
<hkern u1="Q" u2="&#xfa;" k="-20" />
<hkern u1="Q" u2="&#xf9;" k="-20" />
<hkern u1="Q" u2="u" k="-20" />
<hkern u1="R" u2="&#x152;" k="41" />
<hkern u1="R" u2="&#xf6;" k="41" />
<hkern u1="R" u2="&#xf5;" k="41" />
<hkern u1="R" u2="&#xf4;" k="41" />
<hkern u1="R" u2="&#xf3;" k="41" />
<hkern u1="R" u2="&#xf2;" k="41" />
<hkern u1="R" u2="&#xeb;" k="41" />
<hkern u1="R" u2="&#xea;" k="41" />
<hkern u1="R" u2="&#xe9;" k="41" />
<hkern u1="R" u2="&#xe8;" k="41" />
<hkern u1="R" u2="&#xe6;" k="31" />
<hkern u1="R" u2="&#xe5;" k="31" />
<hkern u1="R" u2="&#xe4;" k="31" />
<hkern u1="R" u2="&#xe3;" k="31" />
<hkern u1="R" u2="&#xe2;" k="31" />
<hkern u1="R" u2="&#xe1;" k="31" />
<hkern u1="R" u2="&#xe0;" k="31" />
<hkern u1="R" u2="&#xd8;" k="41" />
<hkern u1="R" u2="&#xd6;" k="41" />
<hkern u1="R" u2="&#xd5;" k="41" />
<hkern u1="R" u2="&#xd4;" k="41" />
<hkern u1="R" u2="&#xd3;" k="41" />
<hkern u1="R" u2="&#xd2;" k="41" />
<hkern u1="R" u2="&#xc7;" k="41" />
<hkern u1="R" u2="&#xa9;" k="41" />
<hkern u1="R" u2="y" k="31" />
<hkern u1="R" u2="o" k="61" />
<hkern u1="R" u2="e" k="61" />
<hkern u1="R" u2="Y" k="143" />
<hkern u1="R" u2="V" k="102" />
<hkern u1="R" u2="Q" k="41" />
<hkern u1="R" u2="O" k="41" />
<hkern u1="R" u2="G" k="41" />
<hkern u1="S" u2="&#x104;" k="41" />
<hkern u1="S" u2="&#x102;" k="41" />
<hkern u1="S" u2="&#x100;" k="41" />
<hkern u1="S" u2="&#xc5;" k="41" />
<hkern u1="S" u2="&#xc4;" k="41" />
<hkern u1="S" u2="&#xc3;" k="41" />
<hkern u1="S" u2="&#xc2;" k="41" />
<hkern u1="S" u2="&#xc1;" k="41" />
<hkern u1="S" u2="&#xc0;" k="41" />
<hkern u1="S" u2="y" k="61" />
<hkern u1="S" u2="v" k="61" />
<hkern u1="S" u2="Y" k="82" />
<hkern u1="S" u2="V" k="61" />
<hkern u1="S" u2="A" k="41" />
<hkern u1="T" u2="&#x2122;" k="-61" />
<hkern u1="T" g2="ellipsis" k="123" />
<hkern u1="T" u2="&#x2014;" k="82" />
<hkern u1="T" u2="&#x2013;" k="82" />
<hkern u1="T" u2="&#xf6;" k="102" />
<hkern u1="T" u2="&#xf5;" k="102" />
<hkern u1="T" u2="&#xf4;" k="102" />
<hkern u1="T" u2="&#xf3;" k="102" />
<hkern u1="T" u2="&#xf2;" k="102" />
<hkern u1="T" u2="&#xeb;" k="102" />
<hkern u1="T" u2="&#xea;" k="102" />
<hkern u1="T" u2="&#xe9;" k="102" />
<hkern u1="T" u2="&#xe8;" k="102" />
<hkern u1="T" u2="&#xc5;" k="143" />
<hkern u1="T" u2="&#xc4;" k="143" />
<hkern u1="T" u2="&#xc3;" k="143" />
<hkern u1="T" u2="&#xc2;" k="143" />
<hkern u1="T" u2="&#xc1;" k="143" />
<hkern u1="T" u2="&#xc0;" k="143" />
<hkern u1="T" u2="x" k="61" />
<hkern u1="T" u2="s" k="82" />
<hkern u1="T" u2="o" k="102" />
<hkern u1="T" u2="e" k="102" />
<hkern u1="T" u2="d" k="92" />
<hkern u1="T" u2="Y" k="-31" />
<hkern u1="T" u2="V" k="-10" />
<hkern u1="T" u2="Q" k="20" />
<hkern u1="T" u2="O" k="20" />
<hkern u1="T" u2="&#x2e;" k="102" />
<hkern u1="U" u2="&#xc5;" k="102" />
<hkern u1="U" u2="&#xc4;" k="102" />
<hkern u1="U" u2="&#xc3;" k="102" />
<hkern u1="U" u2="&#xc2;" k="102" />
<hkern u1="U" u2="&#xc1;" k="102" />
<hkern u1="U" u2="&#xc0;" k="102" />
<hkern u1="U" u2="&#x2e;" k="41" />
<hkern u1="V" u2="&#x2122;" k="-61" />
<hkern u1="V" g2="ellipsis" k="225" />
<hkern u1="V" u2="&#x2014;" k="143" />
<hkern u1="V" u2="&#x2013;" k="143" />
<hkern u1="V" u2="&#x105;" k="154" />
<hkern u1="V" u2="&#x104;" k="348" />
<hkern u1="V" u2="&#x103;" k="154" />
<hkern u1="V" u2="&#x102;" k="348" />
<hkern u1="V" u2="&#x101;" k="154" />
<hkern u1="V" u2="&#x100;" k="348" />
<hkern u1="V" u2="&#xf6;" k="184" />
<hkern u1="V" u2="&#xf5;" k="184" />
<hkern u1="V" u2="&#xf4;" k="184" />
<hkern u1="V" u2="&#xf3;" k="184" />
<hkern u1="V" u2="&#xf2;" k="184" />
<hkern u1="V" u2="&#xeb;" k="184" />
<hkern u1="V" u2="&#xea;" k="184" />
<hkern u1="V" u2="&#xe9;" k="184" />
<hkern u1="V" u2="&#xe8;" k="184" />
<hkern u1="V" u2="&#xe6;" k="154" />
<hkern u1="V" u2="&#xe5;" k="154" />
<hkern u1="V" u2="&#xe4;" k="154" />
<hkern u1="V" u2="&#xe3;" k="154" />
<hkern u1="V" u2="&#xe2;" k="154" />
<hkern u1="V" u2="&#xe1;" k="154" />
<hkern u1="V" u2="&#xe0;" k="154" />
<hkern u1="V" u2="&#xc6;" k="369" />
<hkern u1="V" u2="&#xc5;" k="348" />
<hkern u1="V" u2="&#xc4;" k="348" />
<hkern u1="V" u2="&#xc3;" k="348" />
<hkern u1="V" u2="&#xc2;" k="348" />
<hkern u1="V" u2="&#xc1;" k="348" />
<hkern u1="V" u2="&#xc0;" k="348" />
<hkern u1="V" u2="&#xba;" k="-41" />
<hkern u1="V" u2="&#xae;" k="-61" />
<hkern u1="V" u2="s" k="184" />
<hkern u1="V" u2="o" k="184" />
<hkern u1="V" u2="e" k="184" />
<hkern u1="V" u2="a" k="154" />
<hkern u1="V" u2="Y" k="-41" />
<hkern u1="V" u2="S" k="113" />
<hkern u1="V" u2="O" k="102" />
<hkern u1="V" u2="A" k="348" />
<hkern u1="V" u2="&#x2e;" k="225" />
<hkern u1="V" u2="&#x2d;" k="143" />
<hkern u1="V" u2="&#x2c;" k="225" />
<hkern u1="W" u2="&#x2122;" k="-61" />
<hkern u1="W" u2="&#x2014;" k="82" />
<hkern u1="W" u2="&#x2013;" k="82" />
<hkern u1="W" u2="&#xf6;" k="143" />
<hkern u1="W" u2="&#xf5;" k="143" />
<hkern u1="W" u2="&#xf4;" k="143" />
<hkern u1="W" u2="&#xf3;" k="143" />
<hkern u1="W" u2="&#xf2;" k="143" />
<hkern u1="W" u2="&#xeb;" k="143" />
<hkern u1="W" u2="&#xea;" k="143" />
<hkern u1="W" u2="&#xe9;" k="143" />
<hkern u1="W" u2="&#xe8;" k="143" />
<hkern u1="W" u2="&#xe6;" k="102" />
<hkern u1="W" u2="&#xe5;" k="102" />
<hkern u1="W" u2="&#xe4;" k="102" />
<hkern u1="W" u2="&#xe3;" k="102" />
<hkern u1="W" u2="&#xe2;" k="102" />
<hkern u1="W" u2="&#xe1;" k="102" />
<hkern u1="W" u2="&#xe0;" k="102" />
<hkern u1="W" u2="&#xc5;" k="287" />
<hkern u1="W" u2="&#xc4;" k="287" />
<hkern u1="W" u2="&#xc3;" k="287" />
<hkern u1="W" u2="&#xc2;" k="287" />
<hkern u1="W" u2="&#xc1;" k="287" />
<hkern u1="W" u2="&#xc0;" k="287" />
<hkern u1="W" u2="&#xba;" k="-61" />
<hkern u1="W" u2="&#xae;" k="-41" />
<hkern u1="W" u2="y" k="61" />
<hkern u1="W" u2="o" k="143" />
<hkern u1="W" u2="e" k="143" />
<hkern u1="W" u2="S" k="61" />
<hkern u1="W" u2="O" k="82" />
<hkern u1="W" u2="G" k="82" />
<hkern u1="W" u2="&#x2e;" k="123" />
<hkern u1="X" u2="&#x2122;" k="-61" />
<hkern u1="X" u2="&#x2014;" k="143" />
<hkern u1="X" u2="&#x2013;" k="143" />
<hkern u1="X" u2="&#x152;" k="143" />
<hkern u1="X" u2="&#x150;" k="143" />
<hkern u1="X" u2="&#x14e;" k="143" />
<hkern u1="X" u2="&#x14c;" k="143" />
<hkern u1="X" u2="&#x122;" k="143" />
<hkern u1="X" u2="&#x120;" k="143" />
<hkern u1="X" u2="&#x11e;" k="143" />
<hkern u1="X" u2="&#x11c;" k="143" />
<hkern u1="X" u2="&#x10c;" k="143" />
<hkern u1="X" u2="&#x10a;" k="143" />
<hkern u1="X" u2="&#x108;" k="143" />
<hkern u1="X" u2="&#x106;" k="143" />
<hkern u1="X" u2="&#x105;" k="82" />
<hkern u1="X" u2="&#x103;" k="82" />
<hkern u1="X" u2="&#x101;" k="82" />
<hkern u1="X" u2="&#xeb;" k="123" />
<hkern u1="X" u2="&#xea;" k="123" />
<hkern u1="X" u2="&#xe9;" k="123" />
<hkern u1="X" u2="&#xe8;" k="123" />
<hkern u1="X" u2="&#xe6;" k="82" />
<hkern u1="X" u2="&#xe5;" k="82" />
<hkern u1="X" u2="&#xe4;" k="82" />
<hkern u1="X" u2="&#xe3;" k="82" />
<hkern u1="X" u2="&#xe2;" k="82" />
<hkern u1="X" u2="&#xe1;" k="82" />
<hkern u1="X" u2="&#xe0;" k="82" />
<hkern u1="X" u2="&#xd8;" k="143" />
<hkern u1="X" u2="&#xd6;" k="143" />
<hkern u1="X" u2="&#xd5;" k="143" />
<hkern u1="X" u2="&#xd4;" k="143" />
<hkern u1="X" u2="&#xd3;" k="143" />
<hkern u1="X" u2="&#xd2;" k="143" />
<hkern u1="X" u2="&#xc7;" k="143" />
<hkern u1="X" u2="&#xa9;" k="143" />
<hkern u1="X" u2="y" k="154" />
<hkern u1="X" u2="v" k="123" />
<hkern u1="X" u2="e" k="123" />
<hkern u1="X" u2="a" k="82" />
<hkern u1="X" u2="Q" k="143" />
<hkern u1="X" u2="O" k="164" />
<hkern u1="X" u2="G" k="143" />
<hkern u1="X" u2="C" k="143" />
<hkern u1="X" u2="&#x2d;" k="143" />
<hkern u1="Y" u2="&#x2122;" k="-61" />
<hkern u1="Y" g2="ellipsis" k="225" />
<hkern u1="Y" u2="&#x2014;" k="164" />
<hkern u1="Y" u2="&#x2013;" k="164" />
<hkern u1="Y" u2="&#x173;" k="123" />
<hkern u1="Y" u2="&#x171;" k="123" />
<hkern u1="Y" u2="&#x16f;" k="123" />
<hkern u1="Y" u2="&#x16d;" k="123" />
<hkern u1="Y" u2="&#x16b;" k="123" />
<hkern u1="Y" u2="&#x169;" k="123" />
<hkern u1="Y" u2="&#x166;" k="-20" />
<hkern u1="Y" u2="&#x164;" k="-20" />
<hkern u1="Y" u2="&#x162;" k="-20" />
<hkern u1="Y" u2="&#x152;" k="164" />
<hkern u1="Y" u2="&#x150;" k="164" />
<hkern u1="Y" u2="&#x14e;" k="164" />
<hkern u1="Y" u2="&#x14c;" k="164" />
<hkern u1="Y" u2="&#x122;" k="164" />
<hkern u1="Y" u2="&#x120;" k="164" />
<hkern u1="Y" u2="&#x11e;" k="164" />
<hkern u1="Y" u2="&#x11c;" k="164" />
<hkern u1="Y" u2="&#x10c;" k="164" />
<hkern u1="Y" u2="&#x10a;" k="164" />
<hkern u1="Y" u2="&#x108;" k="164" />
<hkern u1="Y" u2="&#x106;" k="164" />
<hkern u1="Y" u2="&#x105;" k="184" />
<hkern u1="Y" u2="&#x104;" k="338" />
<hkern u1="Y" u2="&#x103;" k="184" />
<hkern u1="Y" u2="&#x102;" k="338" />
<hkern u1="Y" u2="&#x101;" k="184" />
<hkern u1="Y" u2="&#x100;" k="338" />
<hkern u1="Y" u2="&#xfc;" k="123" />
<hkern u1="Y" u2="&#xfb;" k="123" />
<hkern u1="Y" u2="&#xfa;" k="123" />
<hkern u1="Y" u2="&#xf9;" k="123" />
<hkern u1="Y" u2="&#xf6;" k="184" />
<hkern u1="Y" u2="&#xf5;" k="184" />
<hkern u1="Y" u2="&#xf4;" k="184" />
<hkern u1="Y" u2="&#xf3;" k="184" />
<hkern u1="Y" u2="&#xf2;" k="184" />
<hkern u1="Y" u2="&#xeb;" k="184" />
<hkern u1="Y" u2="&#xea;" k="184" />
<hkern u1="Y" u2="&#xe9;" k="184" />
<hkern u1="Y" u2="&#xe8;" k="184" />
<hkern u1="Y" u2="&#xe6;" k="164" />
<hkern u1="Y" u2="&#xe5;" k="164" />
<hkern u1="Y" u2="&#xe4;" k="164" />
<hkern u1="Y" u2="&#xe3;" k="164" />
<hkern u1="Y" u2="&#xe2;" k="164" />
<hkern u1="Y" u2="&#xe1;" k="164" />
<hkern u1="Y" u2="&#xe0;" k="164" />
<hkern u1="Y" u2="&#xd8;" k="164" />
<hkern u1="Y" u2="&#xd6;" k="164" />
<hkern u1="Y" u2="&#xd5;" k="164" />
<hkern u1="Y" u2="&#xd4;" k="164" />
<hkern u1="Y" u2="&#xd3;" k="164" />
<hkern u1="Y" u2="&#xd2;" k="164" />
<hkern u1="Y" u2="&#xc7;" k="164" />
<hkern u1="Y" u2="&#xc6;" k="348" />
<hkern u1="Y" u2="&#xc5;" k="338" />
<hkern u1="Y" u2="&#xc4;" k="338" />
<hkern u1="Y" u2="&#xc3;" k="338" />
<hkern u1="Y" u2="&#xc2;" k="338" />
<hkern u1="Y" u2="&#xc1;" k="338" />
<hkern u1="Y" u2="&#xc0;" k="338" />
<hkern u1="Y" u2="&#xba;" k="-41" />
<hkern u1="Y" u2="&#xa9;" k="164" />
<hkern u1="Y" u2="u" k="123" />
<hkern u1="Y" u2="s" k="205" />
<hkern u1="Y" u2="p" k="123" />
<hkern u1="Y" u2="o" k="246" />
<hkern u1="Y" u2="e" k="246" />
<hkern u1="Y" u2="a" k="184" />
<hkern u1="Y" u2="T" k="-20" />
<hkern u1="Y" u2="S" k="143" />
<hkern u1="Y" u2="Q" k="164" />
<hkern u1="Y" u2="O" k="164" />
<hkern u1="Y" u2="G" k="164" />
<hkern u1="Y" u2="C" k="164" />
<hkern u1="Y" u2="A" k="338" />
<hkern u1="Y" u2="&#x2e;" k="225" />
<hkern u1="Y" u2="&#x2d;" k="164" />
<hkern u1="Y" u2="&#x2c;" k="225" />
<hkern u1="Z" u2="&#x2014;" k="61" />
<hkern u1="Z" u2="&#x2013;" k="61" />
<hkern u1="Z" u2="&#x152;" k="20" />
<hkern u1="Z" u2="&#x150;" k="20" />
<hkern u1="Z" u2="&#x14e;" k="20" />
<hkern u1="Z" u2="&#x14c;" k="20" />
<hkern u1="Z" u2="&#x122;" k="20" />
<hkern u1="Z" u2="&#x120;" k="20" />
<hkern u1="Z" u2="&#x11e;" k="20" />
<hkern u1="Z" u2="&#x11c;" k="20" />
<hkern u1="Z" u2="&#x10c;" k="20" />
<hkern u1="Z" u2="&#x10a;" k="20" />
<hkern u1="Z" u2="&#x108;" k="20" />
<hkern u1="Z" u2="&#x106;" k="20" />
<hkern u1="Z" u2="&#x105;" k="-10" />
<hkern u1="Z" u2="&#x103;" k="-10" />
<hkern u1="Z" u2="&#x101;" k="-10" />
<hkern u1="Z" u2="&#xe6;" k="-10" />
<hkern u1="Z" u2="&#xe5;" k="-10" />
<hkern u1="Z" u2="&#xe4;" k="-10" />
<hkern u1="Z" u2="&#xe3;" k="-10" />
<hkern u1="Z" u2="&#xe2;" k="-10" />
<hkern u1="Z" u2="&#xe1;" k="-10" />
<hkern u1="Z" u2="&#xe0;" k="-10" />
<hkern u1="Z" u2="&#xd8;" k="20" />
<hkern u1="Z" u2="&#xd6;" k="20" />
<hkern u1="Z" u2="&#xd5;" k="20" />
<hkern u1="Z" u2="&#xd4;" k="20" />
<hkern u1="Z" u2="&#xd3;" k="20" />
<hkern u1="Z" u2="&#xd2;" k="20" />
<hkern u1="Z" u2="&#xc7;" k="20" />
<hkern u1="Z" u2="&#xa9;" k="20" />
<hkern u1="Z" u2="a" k="-10" />
<hkern u1="Z" u2="Q" k="20" />
<hkern u1="Z" u2="O" k="20" />
<hkern u1="Z" u2="G" k="20" />
<hkern u1="Z" u2="C" k="20" />
<hkern u1="Z" u2="&#x2d;" k="61" />
<hkern u1="[" u2="&#x135;" k="-164" />
<hkern u1="[" u2="j" k="-164" />
<hkern u1="a" u2="y" k="61" />
<hkern u1="a" u2="v" k="61" />
<hkern u1="a" u2="&#x2a;" k="20" />
<hkern u1="b" u2="&#x175;" k="102" />
<hkern u1="b" u2="y" k="102" />
<hkern u1="b" u2="w" k="102" />
<hkern u1="b" u2="v" k="102" />
<hkern u1="b" u2="&#x3f;" k="113" />
<hkern u1="b" u2="&#x2e;" k="20" />
<hkern u1="c" u2="&#xf6;" k="61" />
<hkern u1="c" u2="&#xf5;" k="61" />
<hkern u1="c" u2="&#xf4;" k="61" />
<hkern u1="c" u2="&#xf3;" k="61" />
<hkern u1="c" u2="&#xf2;" k="61" />
<hkern u1="c" u2="&#xeb;" k="61" />
<hkern u1="c" u2="&#xea;" k="61" />
<hkern u1="c" u2="&#xe9;" k="61" />
<hkern u1="c" u2="&#xe8;" k="61" />
<hkern u1="c" u2="q" k="61" />
<hkern u1="c" u2="o" k="61" />
<hkern u1="c" u2="e" k="61" />
<hkern u1="c" u2="c" k="41" />
<hkern u1="e" u2="y" k="82" />
<hkern u1="e" u2="x" k="102" />
<hkern u1="e" u2="v" k="72" />
<hkern u1="e" u2="&#x2e;" k="-20" />
<hkern u1="e" u2="&#x2a;" k="20" />
<hkern u1="f" g2="ellipsis" k="61" />
<hkern u1="f" u2="&#x2014;" k="-20" />
<hkern u1="f" u2="&#x2013;" k="-20" />
<hkern u1="f" u2="&#xeb;" k="20" />
<hkern u1="f" u2="&#xea;" k="20" />
<hkern u1="f" u2="&#xe9;" k="20" />
<hkern u1="f" u2="&#xe8;" k="20" />
<hkern u1="f" u2="&#xae;" k="-123" />
<hkern u1="f" u2="y" k="-41" />
<hkern u1="f" u2="n" k="-10" />
<hkern u1="f" u2="e" k="20" />
<hkern u1="f" u2="&#x2e;" k="61" />
<hkern u1="f" u2="&#x2a;" k="-102" />
<hkern u1="h" u2="&#x175;" k="51" />
<hkern u1="h" u2="y" k="61" />
<hkern u1="h" u2="w" k="51" />
<hkern u1="h" u2="v" k="41" />
<hkern u1="h" u2="&#x2a;" k="20" />
<hkern u1="i" u2="&#xae;" k="-61" />
<hkern u1="j" u2="&#xae;" k="-41" />
<hkern u1="k" u2="&#x2014;" k="123" />
<hkern u1="k" u2="&#x2013;" k="123" />
<hkern u1="k" u2="&#xf6;" k="143" />
<hkern u1="k" u2="&#xf5;" k="143" />
<hkern u1="k" u2="&#xf4;" k="143" />
<hkern u1="k" u2="&#xf3;" k="143" />
<hkern u1="k" u2="&#xf2;" k="143" />
<hkern u1="k" u2="&#xeb;" k="143" />
<hkern u1="k" u2="&#xea;" k="143" />
<hkern u1="k" u2="&#xe9;" k="143" />
<hkern u1="k" u2="&#xe8;" k="143" />
<hkern u1="k" u2="&#xe6;" k="41" />
<hkern u1="k" u2="&#xe5;" k="41" />
<hkern u1="k" u2="&#xe4;" k="41" />
<hkern u1="k" u2="&#xe3;" k="41" />
<hkern u1="k" u2="&#xe2;" k="41" />
<hkern u1="k" u2="&#xe1;" k="41" />
<hkern u1="k" u2="&#xe0;" k="41" />
<hkern u1="k" u2="&#xae;" k="-61" />
<hkern u1="k" u2="s" k="51" />
<hkern u1="k" u2="o" k="143" />
<hkern u1="k" u2="g" k="143" />
<hkern u1="k" u2="e" k="143" />
<hkern u1="k" u2="c" k="143" />
<hkern u1="k" u2="&#x2e;" k="-20" />
<hkern u1="m" u2="&#x175;" k="51" />
<hkern u1="m" u2="y" k="61" />
<hkern u1="m" u2="w" k="51" />
<hkern u1="m" u2="v" k="41" />
<hkern u1="m" u2="&#x2a;" k="20" />
<hkern u1="n" u2="&#x2019;" k="102" />
<hkern u1="n" u2="&#x175;" k="51" />
<hkern u1="n" u2="y" k="82" />
<hkern u1="n" u2="w" k="51" />
<hkern u1="n" u2="v" k="61" />
<hkern u1="n" u2="&#x2a;" k="20" />
<hkern u1="o" u2="&#x175;" k="61" />
<hkern u1="o" u2="y" k="102" />
<hkern u1="o" u2="x" k="123" />
<hkern u1="o" u2="w" k="61" />
<hkern u1="o" u2="v" k="82" />
<hkern u1="o" u2="&#x2e;" k="20" />
<hkern u1="o" u2="&#x2a;" k="41" />
<hkern u1="p" u2="&#x175;" k="82" />
<hkern u1="p" u2="y" k="102" />
<hkern u1="p" u2="w" k="82" />
<hkern u1="p" u2="v" k="82" />
<hkern u1="p" u2="&#x2e;" k="20" />
<hkern u1="r" g2="ellipsis" k="82" />
<hkern u1="r" u2="&#xf6;" k="20" />
<hkern u1="r" u2="&#xf5;" k="20" />
<hkern u1="r" u2="&#xf4;" k="20" />
<hkern u1="r" u2="&#xf3;" k="20" />
<hkern u1="r" u2="&#xf2;" k="20" />
<hkern u1="r" u2="&#xeb;" k="20" />
<hkern u1="r" u2="&#xea;" k="20" />
<hkern u1="r" u2="&#xe9;" k="20" />
<hkern u1="r" u2="&#xe8;" k="20" />
<hkern u1="r" u2="&#xe6;" k="31" />
<hkern u1="r" u2="&#xe5;" k="31" />
<hkern u1="r" u2="&#xe4;" k="31" />
<hkern u1="r" u2="&#xe3;" k="31" />
<hkern u1="r" u2="&#xe2;" k="31" />
<hkern u1="r" u2="&#xe1;" k="31" />
<hkern u1="r" u2="&#xe0;" k="31" />
<hkern u1="r" u2="&#xae;" k="-143" />
<hkern u1="r" u2="z" k="-20" />
<hkern u1="r" u2="v" k="-10" />
<hkern u1="r" u2="o" k="20" />
<hkern u1="r" u2="e" k="20" />
<hkern u1="r" u2="c" k="20" />
<hkern u1="r" u2="&#x2e;" k="82" />
<hkern u1="s" u2="y" k="61" />
<hkern u1="s" u2="x" k="20" />
<hkern u1="t" u2="&#x2014;" k="61" />
<hkern u1="t" u2="&#x2013;" k="61" />
<hkern u1="t" u2="&#xae;" k="-82" />
<hkern u1="t" u2="z" k="-10" />
<hkern u1="t" u2="y" k="-10" />
<hkern u1="v" u2="&#x105;" k="41" />
<hkern u1="v" u2="&#x103;" k="41" />
<hkern u1="v" u2="&#x101;" k="41" />
<hkern u1="v" u2="&#xf6;" k="82" />
<hkern u1="v" u2="&#xf5;" k="82" />
<hkern u1="v" u2="&#xf4;" k="82" />
<hkern u1="v" u2="&#xf3;" k="82" />
<hkern u1="v" u2="&#xf2;" k="82" />
<hkern u1="v" u2="&#xeb;" k="72" />
<hkern u1="v" u2="&#xea;" k="72" />
<hkern u1="v" u2="&#xe9;" k="72" />
<hkern u1="v" u2="&#xe8;" k="72" />
<hkern u1="v" u2="&#xe6;" k="20" />
<hkern u1="v" u2="&#xe5;" k="20" />
<hkern u1="v" u2="&#xe4;" k="20" />
<hkern u1="v" u2="&#xe3;" k="20" />
<hkern u1="v" u2="&#xe2;" k="20" />
<hkern u1="v" u2="&#xe1;" k="20" />
<hkern u1="v" u2="&#xe0;" k="20" />
<hkern u1="v" u2="&#xae;" k="-82" />
<hkern u1="v" u2="y" k="-10" />
<hkern u1="v" u2="v" k="-10" />
<hkern u1="v" u2="s" k="82" />
<hkern u1="v" u2="q" k="82" />
<hkern u1="v" u2="o" k="82" />
<hkern u1="v" u2="g" k="82" />
<hkern u1="v" u2="e" k="72" />
<hkern u1="v" u2="d" k="102" />
<hkern u1="v" u2="c" k="102" />
<hkern u1="v" u2="a" k="41" />
<hkern u1="v" u2="&#x2e;" k="164" />
<hkern u1="w" g2="ellipsis" k="102" />
<hkern u1="w" u2="&#xf6;" k="61" />
<hkern u1="w" u2="&#xf5;" k="61" />
<hkern u1="w" u2="&#xf4;" k="61" />
<hkern u1="w" u2="&#xf3;" k="61" />
<hkern u1="w" u2="&#xf2;" k="61" />
<hkern u1="w" u2="&#xeb;" k="61" />
<hkern u1="w" u2="&#xea;" k="61" />
<hkern u1="w" u2="&#xe9;" k="61" />
<hkern u1="w" u2="&#xe8;" k="61" />
<hkern u1="w" u2="&#xe6;" k="31" />
<hkern u1="w" u2="&#xe5;" k="31" />
<hkern u1="w" u2="&#xe4;" k="31" />
<hkern u1="w" u2="&#xe3;" k="31" />
<hkern u1="w" u2="&#xe2;" k="31" />
<hkern u1="w" u2="&#xe1;" k="31" />
<hkern u1="w" u2="&#xe0;" k="31" />
<hkern u1="w" u2="&#xae;" k="-123" />
<hkern u1="w" u2="y" k="-10" />
<hkern u1="w" u2="s" k="61" />
<hkern u1="w" u2="q" k="82" />
<hkern u1="w" u2="o" k="61" />
<hkern u1="w" u2="g" k="61" />
<hkern u1="w" u2="e" k="61" />
<hkern u1="w" u2="d" k="41" />
<hkern u1="w" u2="c" k="61" />
<hkern u1="w" u2="&#x2e;" k="102" />
<hkern u1="x" u2="&#x2014;" k="41" />
<hkern u1="x" u2="&#x2013;" k="41" />
<hkern u1="x" u2="&#x105;" k="31" />
<hkern u1="x" u2="&#x103;" k="31" />
<hkern u1="x" u2="&#x101;" k="31" />
<hkern u1="x" u2="&#xf6;" k="123" />
<hkern u1="x" u2="&#xf5;" k="123" />
<hkern u1="x" u2="&#xf4;" k="123" />
<hkern u1="x" u2="&#xf3;" k="123" />
<hkern u1="x" u2="&#xf2;" k="123" />
<hkern u1="x" u2="&#xeb;" k="123" />
<hkern u1="x" u2="&#xea;" k="123" />
<hkern u1="x" u2="&#xe9;" k="123" />
<hkern u1="x" u2="&#xe8;" k="123" />
<hkern u1="x" u2="&#xe6;" k="31" />
<hkern u1="x" u2="&#xe5;" k="31" />
<hkern u1="x" u2="&#xe4;" k="31" />
<hkern u1="x" u2="&#xe3;" k="31" />
<hkern u1="x" u2="&#xe2;" k="31" />
<hkern u1="x" u2="&#xe1;" k="31" />
<hkern u1="x" u2="&#xe0;" k="31" />
<hkern u1="x" u2="&#xae;" k="-82" />
<hkern u1="x" u2="s" k="41" />
<hkern u1="x" u2="o" k="123" />
<hkern u1="x" u2="f" k="-41" />
<hkern u1="x" u2="e" k="123" />
<hkern u1="x" u2="c" k="123" />
<hkern u1="x" u2="a" k="31" />
<hkern u1="x" u2="&#x2d;" k="41" />
<hkern u1="y" g2="ellipsis" k="164" />
<hkern u1="y" u2="&#x105;" k="41" />
<hkern u1="y" u2="&#x103;" k="41" />
<hkern u1="y" u2="&#x101;" k="41" />
<hkern u1="y" u2="&#xf6;" k="82" />
<hkern u1="y" u2="&#xf5;" k="82" />
<hkern u1="y" u2="&#xf4;" k="82" />
<hkern u1="y" u2="&#xf3;" k="82" />
<hkern u1="y" u2="&#xf2;" k="82" />
<hkern u1="y" u2="&#xeb;" k="82" />
<hkern u1="y" u2="&#xea;" k="82" />
<hkern u1="y" u2="&#xe9;" k="82" />
<hkern u1="y" u2="&#xe8;" k="82" />
<hkern u1="y" u2="&#xe6;" k="41" />
<hkern u1="y" u2="&#xe5;" k="41" />
<hkern u1="y" u2="&#xe4;" k="41" />
<hkern u1="y" u2="&#xe3;" k="41" />
<hkern u1="y" u2="&#xe2;" k="41" />
<hkern u1="y" u2="&#xe1;" k="41" />
<hkern u1="y" u2="&#xe0;" k="41" />
<hkern u1="y" u2="&#xae;" k="-123" />
<hkern u1="y" u2="s" k="82" />
<hkern u1="y" u2="q" k="82" />
<hkern u1="y" u2="o" k="82" />
<hkern u1="y" u2="g" k="82" />
<hkern u1="y" u2="f" k="-20" />
<hkern u1="y" u2="e" k="82" />
<hkern u1="y" u2="d" k="82" />
<hkern u1="y" u2="c" k="102" />
<hkern u1="y" u2="a" k="41" />
<hkern u1="y" u2="&#x2e;" k="164" />
<hkern u1="y" u2="&#x2c;" k="164" />
<hkern u1="z" u2="&#x2014;" k="20" />
<hkern u1="z" u2="&#x2013;" k="20" />
<hkern u1="z" u2="&#xf6;" k="31" />
<hkern u1="z" u2="&#xf5;" k="31" />
<hkern u1="z" u2="&#xf4;" k="31" />
<hkern u1="z" u2="&#xf3;" k="31" />
<hkern u1="z" u2="&#xf2;" k="31" />
<hkern u1="z" u2="&#xeb;" k="31" />
<hkern u1="z" u2="&#xea;" k="31" />
<hkern u1="z" u2="&#xe9;" k="31" />
<hkern u1="z" u2="&#xe8;" k="31" />
<hkern u1="z" u2="&#xae;" k="-102" />
<hkern u1="z" u2="o" k="31" />
<hkern u1="z" u2="e" k="31" />
<hkern u1="z" u2="&#x2d;" k="20" />
<hkern u1="&#x7b;" u2="&#x135;" k="-143" />
<hkern u1="&#x7b;" u2="j" k="-143" />
<hkern u1="&#xbf;" u2="&#x174;" k="184" />
<hkern u1="&#xbf;" u2="&#x152;" k="123" />
<hkern u1="&#xbf;" u2="&#x150;" k="123" />
<hkern u1="&#xbf;" u2="&#x14e;" k="123" />
<hkern u1="&#xbf;" u2="&#x14c;" k="123" />
<hkern u1="&#xbf;" u2="&#x122;" k="123" />
<hkern u1="&#xbf;" u2="&#x120;" k="123" />
<hkern u1="&#xbf;" u2="&#x11e;" k="123" />
<hkern u1="&#xbf;" u2="&#x11c;" k="123" />
<hkern u1="&#xbf;" u2="&#x10c;" k="123" />
<hkern u1="&#xbf;" u2="&#x10a;" k="123" />
<hkern u1="&#xbf;" u2="&#x108;" k="123" />
<hkern u1="&#xbf;" u2="&#x106;" k="123" />
<hkern u1="&#xbf;" u2="&#xd8;" k="123" />
<hkern u1="&#xbf;" u2="&#xd6;" k="123" />
<hkern u1="&#xbf;" u2="&#xd5;" k="123" />
<hkern u1="&#xbf;" u2="&#xd4;" k="123" />
<hkern u1="&#xbf;" u2="&#xd3;" k="123" />
<hkern u1="&#xbf;" u2="&#xd2;" k="123" />
<hkern u1="&#xbf;" u2="&#xc7;" k="123" />
<hkern u1="&#xbf;" u2="&#xa9;" k="123" />
<hkern u1="&#xbf;" u2="W" k="184" />
<hkern u1="&#xbf;" u2="Q" k="123" />
<hkern u1="&#xbf;" u2="O" k="123" />
<hkern u1="&#xbf;" u2="H" k="61" />
<hkern u1="&#xbf;" u2="G" k="123" />
<hkern u1="&#xbf;" u2="F" k="61" />
<hkern u1="&#xbf;" u2="E" k="61" />
<hkern u1="&#xbf;" u2="D" k="61" />
<hkern u1="&#xbf;" u2="C" k="123" />
<hkern u1="&#xbf;" u2="B" k="61" />
<hkern u1="&#xc0;" u2="&#x2122;" k="266" />
<hkern u1="&#xc0;" u2="&#x201d;" k="225" />
<hkern u1="&#xc0;" u2="&#x2019;" k="225" />
<hkern u1="&#xc0;" u2="&#x2014;" k="102" />
<hkern u1="&#xc0;" u2="&#x2013;" k="102" />
<hkern u1="&#xc0;" u2="&#x175;" k="205" />
<hkern u1="&#xc0;" u2="&#x174;" k="287" />
<hkern u1="&#xc0;" u2="&#x173;" k="41" />
<hkern u1="&#xc0;" u2="&#x172;" k="102" />
<hkern u1="&#xc0;" u2="&#x171;" k="41" />
<hkern u1="&#xc0;" u2="&#x170;" k="102" />
<hkern u1="&#xc0;" u2="&#x16f;" k="41" />
<hkern u1="&#xc0;" u2="&#x16e;" k="102" />
<hkern u1="&#xc0;" u2="&#x16d;" k="41" />
<hkern u1="&#xc0;" u2="&#x16c;" k="102" />
<hkern u1="&#xc0;" u2="&#x16b;" k="41" />
<hkern u1="&#xc0;" u2="&#x16a;" k="102" />
<hkern u1="&#xc0;" u2="&#x169;" k="41" />
<hkern u1="&#xc0;" u2="&#x168;" k="102" />
<hkern u1="&#xc0;" u2="&#x166;" k="164" />
<hkern u1="&#xc0;" u2="&#x164;" k="164" />
<hkern u1="&#xc0;" u2="&#x162;" k="164" />
<hkern u1="&#xc0;" u2="&#x152;" k="102" />
<hkern u1="&#xc0;" u2="&#x150;" k="102" />
<hkern u1="&#xc0;" u2="&#x14e;" k="102" />
<hkern u1="&#xc0;" u2="&#x14c;" k="102" />
<hkern u1="&#xc0;" u2="&#x122;" k="102" />
<hkern u1="&#xc0;" u2="&#x120;" k="102" />
<hkern u1="&#xc0;" u2="&#x11e;" k="102" />
<hkern u1="&#xc0;" u2="&#x11c;" k="102" />
<hkern u1="&#xc0;" u2="&#x10c;" k="102" />
<hkern u1="&#xc0;" u2="&#x10a;" k="102" />
<hkern u1="&#xc0;" u2="&#x108;" k="102" />
<hkern u1="&#xc0;" u2="&#x106;" k="102" />
<hkern u1="&#xc0;" u2="&#x105;" k="41" />
<hkern u1="&#xc0;" u2="&#x103;" k="41" />
<hkern u1="&#xc0;" u2="&#x101;" k="41" />
<hkern u1="&#xc0;" u2="&#xfc;" k="41" />
<hkern u1="&#xc0;" u2="&#xfb;" k="41" />
<hkern u1="&#xc0;" u2="&#xfa;" k="41" />
<hkern u1="&#xc0;" u2="&#xf9;" k="41" />
<hkern u1="&#xc0;" u2="&#xf6;" k="102" />
<hkern u1="&#xc0;" u2="&#xf5;" k="102" />
<hkern u1="&#xc0;" u2="&#xf4;" k="102" />
<hkern u1="&#xc0;" u2="&#xf3;" k="102" />
<hkern u1="&#xc0;" u2="&#xf2;" k="102" />
<hkern u1="&#xc0;" u2="&#xeb;" k="102" />
<hkern u1="&#xc0;" u2="&#xea;" k="102" />
<hkern u1="&#xc0;" u2="&#xe9;" k="102" />
<hkern u1="&#xc0;" u2="&#xe8;" k="102" />
<hkern u1="&#xc0;" u2="&#xe6;" k="41" />
<hkern u1="&#xc0;" u2="&#xe5;" k="41" />
<hkern u1="&#xc0;" u2="&#xe4;" k="41" />
<hkern u1="&#xc0;" u2="&#xe3;" k="41" />
<hkern u1="&#xc0;" u2="&#xe2;" k="41" />
<hkern u1="&#xc0;" u2="&#xe1;" k="41" />
<hkern u1="&#xc0;" u2="&#xe0;" k="41" />
<hkern u1="&#xc0;" u2="&#xdc;" k="102" />
<hkern u1="&#xc0;" u2="&#xdb;" k="102" />
<hkern u1="&#xc0;" u2="&#xda;" k="102" />
<hkern u1="&#xc0;" u2="&#xd9;" k="102" />
<hkern u1="&#xc0;" u2="&#xd8;" k="102" />
<hkern u1="&#xc0;" u2="&#xd6;" k="102" />
<hkern u1="&#xc0;" u2="&#xd5;" k="102" />
<hkern u1="&#xc0;" u2="&#xd4;" k="102" />
<hkern u1="&#xc0;" u2="&#xd3;" k="102" />
<hkern u1="&#xc0;" u2="&#xd2;" k="102" />
<hkern u1="&#xc0;" u2="&#xc7;" k="102" />
<hkern u1="&#xc0;" u2="&#xba;" k="184" />
<hkern u1="&#xc0;" u2="&#xae;" k="205" />
<hkern u1="&#xc0;" u2="&#xaa;" k="164" />
<hkern u1="&#xc0;" u2="&#xa9;" k="102" />
<hkern u1="&#xc0;" u2="y" k="246" />
<hkern u1="&#xc0;" u2="w" k="205" />
<hkern u1="&#xc0;" u2="v" k="205" />
<hkern u1="&#xc0;" u2="u" k="41" />
<hkern u1="&#xc0;" u2="s" k="41" />
<hkern u1="&#xc0;" u2="q" k="102" />
<hkern u1="&#xc0;" u2="o" k="102" />
<hkern u1="&#xc0;" u2="g" k="102" />
<hkern u1="&#xc0;" u2="f" k="61" />
<hkern u1="&#xc0;" u2="e" k="102" />
<hkern u1="&#xc0;" u2="d" k="102" />
<hkern u1="&#xc0;" u2="c" k="102" />
<hkern u1="&#xc0;" u2="a" k="41" />
<hkern u1="&#xc0;" u2="Y" k="338" />
<hkern u1="&#xc0;" u2="W" k="287" />
<hkern u1="&#xc0;" u2="V" k="348" />
<hkern u1="&#xc0;" u2="U" k="102" />
<hkern u1="&#xc0;" u2="T" k="164" />
<hkern u1="&#xc0;" u2="S" k="61" />
<hkern u1="&#xc0;" u2="Q" k="102" />
<hkern u1="&#xc0;" u2="O" k="102" />
<hkern u1="&#xc0;" u2="G" k="102" />
<hkern u1="&#xc0;" u2="C" k="102" />
<hkern u1="&#xc0;" u2="&#x3f;" k="143" />
<hkern u1="&#xc0;" u2="&#x2d;" k="102" />
<hkern u1="&#xc0;" u2="&#x2a;" k="205" />
<hkern u1="&#xc1;" u2="&#x2122;" k="266" />
<hkern u1="&#xc1;" u2="&#x201d;" k="225" />
<hkern u1="&#xc1;" u2="&#x2019;" k="225" />
<hkern u1="&#xc1;" u2="&#x2014;" k="102" />
<hkern u1="&#xc1;" u2="&#x2013;" k="102" />
<hkern u1="&#xc1;" u2="&#x175;" k="205" />
<hkern u1="&#xc1;" u2="&#x174;" k="287" />
<hkern u1="&#xc1;" u2="&#x173;" k="41" />
<hkern u1="&#xc1;" u2="&#x172;" k="102" />
<hkern u1="&#xc1;" u2="&#x171;" k="41" />
<hkern u1="&#xc1;" u2="&#x170;" k="102" />
<hkern u1="&#xc1;" u2="&#x16f;" k="41" />
<hkern u1="&#xc1;" u2="&#x16e;" k="102" />
<hkern u1="&#xc1;" u2="&#x16d;" k="41" />
<hkern u1="&#xc1;" u2="&#x16c;" k="102" />
<hkern u1="&#xc1;" u2="&#x16b;" k="41" />
<hkern u1="&#xc1;" u2="&#x16a;" k="102" />
<hkern u1="&#xc1;" u2="&#x169;" k="41" />
<hkern u1="&#xc1;" u2="&#x168;" k="102" />
<hkern u1="&#xc1;" u2="&#x166;" k="164" />
<hkern u1="&#xc1;" u2="&#x164;" k="164" />
<hkern u1="&#xc1;" u2="&#x162;" k="164" />
<hkern u1="&#xc1;" u2="&#x152;" k="102" />
<hkern u1="&#xc1;" u2="&#x150;" k="102" />
<hkern u1="&#xc1;" u2="&#x14e;" k="102" />
<hkern u1="&#xc1;" u2="&#x14c;" k="102" />
<hkern u1="&#xc1;" u2="&#x122;" k="102" />
<hkern u1="&#xc1;" u2="&#x120;" k="102" />
<hkern u1="&#xc1;" u2="&#x11e;" k="102" />
<hkern u1="&#xc1;" u2="&#x11c;" k="102" />
<hkern u1="&#xc1;" u2="&#x10c;" k="102" />
<hkern u1="&#xc1;" u2="&#x10a;" k="102" />
<hkern u1="&#xc1;" u2="&#x108;" k="102" />
<hkern u1="&#xc1;" u2="&#x106;" k="102" />
<hkern u1="&#xc1;" u2="&#x105;" k="41" />
<hkern u1="&#xc1;" u2="&#x103;" k="41" />
<hkern u1="&#xc1;" u2="&#x101;" k="41" />
<hkern u1="&#xc1;" u2="&#xfc;" k="41" />
<hkern u1="&#xc1;" u2="&#xfb;" k="41" />
<hkern u1="&#xc1;" u2="&#xfa;" k="41" />
<hkern u1="&#xc1;" u2="&#xf9;" k="41" />
<hkern u1="&#xc1;" u2="&#xf6;" k="102" />
<hkern u1="&#xc1;" u2="&#xf5;" k="102" />
<hkern u1="&#xc1;" u2="&#xf4;" k="102" />
<hkern u1="&#xc1;" u2="&#xf3;" k="102" />
<hkern u1="&#xc1;" u2="&#xf2;" k="102" />
<hkern u1="&#xc1;" u2="&#xeb;" k="102" />
<hkern u1="&#xc1;" u2="&#xea;" k="102" />
<hkern u1="&#xc1;" u2="&#xe9;" k="102" />
<hkern u1="&#xc1;" u2="&#xe8;" k="102" />
<hkern u1="&#xc1;" u2="&#xe6;" k="41" />
<hkern u1="&#xc1;" u2="&#xe5;" k="41" />
<hkern u1="&#xc1;" u2="&#xe4;" k="41" />
<hkern u1="&#xc1;" u2="&#xe3;" k="41" />
<hkern u1="&#xc1;" u2="&#xe2;" k="41" />
<hkern u1="&#xc1;" u2="&#xe1;" k="41" />
<hkern u1="&#xc1;" u2="&#xe0;" k="41" />
<hkern u1="&#xc1;" u2="&#xdc;" k="102" />
<hkern u1="&#xc1;" u2="&#xdb;" k="102" />
<hkern u1="&#xc1;" u2="&#xda;" k="102" />
<hkern u1="&#xc1;" u2="&#xd9;" k="102" />
<hkern u1="&#xc1;" u2="&#xd8;" k="102" />
<hkern u1="&#xc1;" u2="&#xd6;" k="102" />
<hkern u1="&#xc1;" u2="&#xd5;" k="102" />
<hkern u1="&#xc1;" u2="&#xd4;" k="102" />
<hkern u1="&#xc1;" u2="&#xd3;" k="102" />
<hkern u1="&#xc1;" u2="&#xd2;" k="102" />
<hkern u1="&#xc1;" u2="&#xc7;" k="102" />
<hkern u1="&#xc1;" u2="&#xba;" k="184" />
<hkern u1="&#xc1;" u2="&#xae;" k="205" />
<hkern u1="&#xc1;" u2="&#xaa;" k="164" />
<hkern u1="&#xc1;" u2="&#xa9;" k="102" />
<hkern u1="&#xc1;" u2="y" k="246" />
<hkern u1="&#xc1;" u2="w" k="205" />
<hkern u1="&#xc1;" u2="v" k="205" />
<hkern u1="&#xc1;" u2="u" k="41" />
<hkern u1="&#xc1;" u2="s" k="41" />
<hkern u1="&#xc1;" u2="q" k="102" />
<hkern u1="&#xc1;" u2="o" k="102" />
<hkern u1="&#xc1;" u2="g" k="102" />
<hkern u1="&#xc1;" u2="f" k="61" />
<hkern u1="&#xc1;" u2="e" k="102" />
<hkern u1="&#xc1;" u2="d" k="102" />
<hkern u1="&#xc1;" u2="c" k="102" />
<hkern u1="&#xc1;" u2="a" k="41" />
<hkern u1="&#xc1;" u2="Y" k="338" />
<hkern u1="&#xc1;" u2="W" k="287" />
<hkern u1="&#xc1;" u2="V" k="348" />
<hkern u1="&#xc1;" u2="U" k="102" />
<hkern u1="&#xc1;" u2="T" k="164" />
<hkern u1="&#xc1;" u2="S" k="61" />
<hkern u1="&#xc1;" u2="Q" k="102" />
<hkern u1="&#xc1;" u2="O" k="102" />
<hkern u1="&#xc1;" u2="G" k="102" />
<hkern u1="&#xc1;" u2="C" k="102" />
<hkern u1="&#xc1;" u2="&#x3f;" k="143" />
<hkern u1="&#xc1;" u2="&#x2d;" k="102" />
<hkern u1="&#xc1;" u2="&#x2a;" k="205" />
<hkern u1="&#xc2;" u2="&#x2122;" k="266" />
<hkern u1="&#xc2;" u2="&#x201d;" k="225" />
<hkern u1="&#xc2;" u2="&#x2019;" k="225" />
<hkern u1="&#xc2;" u2="&#x2014;" k="102" />
<hkern u1="&#xc2;" u2="&#x2013;" k="102" />
<hkern u1="&#xc2;" u2="&#x175;" k="205" />
<hkern u1="&#xc2;" u2="&#x174;" k="287" />
<hkern u1="&#xc2;" u2="&#x173;" k="41" />
<hkern u1="&#xc2;" u2="&#x172;" k="102" />
<hkern u1="&#xc2;" u2="&#x171;" k="41" />
<hkern u1="&#xc2;" u2="&#x170;" k="102" />
<hkern u1="&#xc2;" u2="&#x16f;" k="41" />
<hkern u1="&#xc2;" u2="&#x16e;" k="102" />
<hkern u1="&#xc2;" u2="&#x16d;" k="41" />
<hkern u1="&#xc2;" u2="&#x16c;" k="102" />
<hkern u1="&#xc2;" u2="&#x16b;" k="41" />
<hkern u1="&#xc2;" u2="&#x16a;" k="102" />
<hkern u1="&#xc2;" u2="&#x169;" k="41" />
<hkern u1="&#xc2;" u2="&#x168;" k="102" />
<hkern u1="&#xc2;" u2="&#x166;" k="164" />
<hkern u1="&#xc2;" u2="&#x164;" k="164" />
<hkern u1="&#xc2;" u2="&#x162;" k="164" />
<hkern u1="&#xc2;" u2="&#x152;" k="102" />
<hkern u1="&#xc2;" u2="&#x150;" k="102" />
<hkern u1="&#xc2;" u2="&#x14e;" k="102" />
<hkern u1="&#xc2;" u2="&#x14c;" k="102" />
<hkern u1="&#xc2;" u2="&#x122;" k="102" />
<hkern u1="&#xc2;" u2="&#x120;" k="102" />
<hkern u1="&#xc2;" u2="&#x11e;" k="102" />
<hkern u1="&#xc2;" u2="&#x11c;" k="102" />
<hkern u1="&#xc2;" u2="&#x10c;" k="102" />
<hkern u1="&#xc2;" u2="&#x10a;" k="102" />
<hkern u1="&#xc2;" u2="&#x108;" k="102" />
<hkern u1="&#xc2;" u2="&#x106;" k="102" />
<hkern u1="&#xc2;" u2="&#x105;" k="41" />
<hkern u1="&#xc2;" u2="&#x103;" k="41" />
<hkern u1="&#xc2;" u2="&#x101;" k="41" />
<hkern u1="&#xc2;" u2="&#xfc;" k="41" />
<hkern u1="&#xc2;" u2="&#xfb;" k="41" />
<hkern u1="&#xc2;" u2="&#xfa;" k="41" />
<hkern u1="&#xc2;" u2="&#xf9;" k="41" />
<hkern u1="&#xc2;" u2="&#xf6;" k="102" />
<hkern u1="&#xc2;" u2="&#xf5;" k="102" />
<hkern u1="&#xc2;" u2="&#xf4;" k="102" />
<hkern u1="&#xc2;" u2="&#xf3;" k="102" />
<hkern u1="&#xc2;" u2="&#xf2;" k="102" />
<hkern u1="&#xc2;" u2="&#xeb;" k="102" />
<hkern u1="&#xc2;" u2="&#xea;" k="102" />
<hkern u1="&#xc2;" u2="&#xe9;" k="102" />
<hkern u1="&#xc2;" u2="&#xe8;" k="102" />
<hkern u1="&#xc2;" u2="&#xe6;" k="41" />
<hkern u1="&#xc2;" u2="&#xe5;" k="41" />
<hkern u1="&#xc2;" u2="&#xe4;" k="41" />
<hkern u1="&#xc2;" u2="&#xe3;" k="41" />
<hkern u1="&#xc2;" u2="&#xe2;" k="41" />
<hkern u1="&#xc2;" u2="&#xe1;" k="41" />
<hkern u1="&#xc2;" u2="&#xe0;" k="41" />
<hkern u1="&#xc2;" u2="&#xdc;" k="102" />
<hkern u1="&#xc2;" u2="&#xdb;" k="102" />
<hkern u1="&#xc2;" u2="&#xda;" k="102" />
<hkern u1="&#xc2;" u2="&#xd9;" k="102" />
<hkern u1="&#xc2;" u2="&#xd8;" k="102" />
<hkern u1="&#xc2;" u2="&#xd6;" k="102" />
<hkern u1="&#xc2;" u2="&#xd5;" k="102" />
<hkern u1="&#xc2;" u2="&#xd4;" k="102" />
<hkern u1="&#xc2;" u2="&#xd3;" k="102" />
<hkern u1="&#xc2;" u2="&#xd2;" k="102" />
<hkern u1="&#xc2;" u2="&#xc7;" k="102" />
<hkern u1="&#xc2;" u2="&#xba;" k="184" />
<hkern u1="&#xc2;" u2="&#xae;" k="205" />
<hkern u1="&#xc2;" u2="&#xaa;" k="164" />
<hkern u1="&#xc2;" u2="&#xa9;" k="102" />
<hkern u1="&#xc2;" u2="y" k="246" />
<hkern u1="&#xc2;" u2="w" k="205" />
<hkern u1="&#xc2;" u2="v" k="205" />
<hkern u1="&#xc2;" u2="u" k="41" />
<hkern u1="&#xc2;" u2="s" k="41" />
<hkern u1="&#xc2;" u2="q" k="102" />
<hkern u1="&#xc2;" u2="o" k="102" />
<hkern u1="&#xc2;" u2="g" k="102" />
<hkern u1="&#xc2;" u2="f" k="61" />
<hkern u1="&#xc2;" u2="e" k="102" />
<hkern u1="&#xc2;" u2="d" k="102" />
<hkern u1="&#xc2;" u2="c" k="102" />
<hkern u1="&#xc2;" u2="a" k="41" />
<hkern u1="&#xc2;" u2="Y" k="338" />
<hkern u1="&#xc2;" u2="W" k="287" />
<hkern u1="&#xc2;" u2="V" k="348" />
<hkern u1="&#xc2;" u2="U" k="102" />
<hkern u1="&#xc2;" u2="T" k="164" />
<hkern u1="&#xc2;" u2="S" k="61" />
<hkern u1="&#xc2;" u2="Q" k="102" />
<hkern u1="&#xc2;" u2="O" k="102" />
<hkern u1="&#xc2;" u2="G" k="102" />
<hkern u1="&#xc2;" u2="C" k="102" />
<hkern u1="&#xc2;" u2="&#x3f;" k="143" />
<hkern u1="&#xc2;" u2="&#x2d;" k="102" />
<hkern u1="&#xc2;" u2="&#x2a;" k="205" />
<hkern u1="&#xc3;" u2="&#x2122;" k="266" />
<hkern u1="&#xc3;" u2="&#x201d;" k="225" />
<hkern u1="&#xc3;" u2="&#x2019;" k="225" />
<hkern u1="&#xc3;" u2="&#x2014;" k="102" />
<hkern u1="&#xc3;" u2="&#x2013;" k="102" />
<hkern u1="&#xc3;" u2="&#x175;" k="205" />
<hkern u1="&#xc3;" u2="&#x174;" k="287" />
<hkern u1="&#xc3;" u2="&#x173;" k="41" />
<hkern u1="&#xc3;" u2="&#x172;" k="102" />
<hkern u1="&#xc3;" u2="&#x171;" k="41" />
<hkern u1="&#xc3;" u2="&#x170;" k="102" />
<hkern u1="&#xc3;" u2="&#x16f;" k="41" />
<hkern u1="&#xc3;" u2="&#x16e;" k="102" />
<hkern u1="&#xc3;" u2="&#x16d;" k="41" />
<hkern u1="&#xc3;" u2="&#x16c;" k="102" />
<hkern u1="&#xc3;" u2="&#x16b;" k="41" />
<hkern u1="&#xc3;" u2="&#x16a;" k="102" />
<hkern u1="&#xc3;" u2="&#x169;" k="41" />
<hkern u1="&#xc3;" u2="&#x168;" k="102" />
<hkern u1="&#xc3;" u2="&#x166;" k="164" />
<hkern u1="&#xc3;" u2="&#x164;" k="164" />
<hkern u1="&#xc3;" u2="&#x162;" k="164" />
<hkern u1="&#xc3;" u2="&#x152;" k="102" />
<hkern u1="&#xc3;" u2="&#x150;" k="102" />
<hkern u1="&#xc3;" u2="&#x14e;" k="102" />
<hkern u1="&#xc3;" u2="&#x14c;" k="102" />
<hkern u1="&#xc3;" u2="&#x122;" k="102" />
<hkern u1="&#xc3;" u2="&#x120;" k="102" />
<hkern u1="&#xc3;" u2="&#x11e;" k="102" />
<hkern u1="&#xc3;" u2="&#x11c;" k="102" />
<hkern u1="&#xc3;" u2="&#x10c;" k="102" />
<hkern u1="&#xc3;" u2="&#x10a;" k="102" />
<hkern u1="&#xc3;" u2="&#x108;" k="102" />
<hkern u1="&#xc3;" u2="&#x106;" k="102" />
<hkern u1="&#xc3;" u2="&#x105;" k="41" />
<hkern u1="&#xc3;" u2="&#x103;" k="41" />
<hkern u1="&#xc3;" u2="&#x101;" k="41" />
<hkern u1="&#xc3;" u2="&#xfc;" k="41" />
<hkern u1="&#xc3;" u2="&#xfb;" k="41" />
<hkern u1="&#xc3;" u2="&#xfa;" k="41" />
<hkern u1="&#xc3;" u2="&#xf9;" k="41" />
<hkern u1="&#xc3;" u2="&#xf6;" k="102" />
<hkern u1="&#xc3;" u2="&#xf5;" k="102" />
<hkern u1="&#xc3;" u2="&#xf4;" k="102" />
<hkern u1="&#xc3;" u2="&#xf3;" k="102" />
<hkern u1="&#xc3;" u2="&#xf2;" k="102" />
<hkern u1="&#xc3;" u2="&#xeb;" k="102" />
<hkern u1="&#xc3;" u2="&#xea;" k="102" />
<hkern u1="&#xc3;" u2="&#xe9;" k="102" />
<hkern u1="&#xc3;" u2="&#xe8;" k="102" />
<hkern u1="&#xc3;" u2="&#xe6;" k="41" />
<hkern u1="&#xc3;" u2="&#xe5;" k="41" />
<hkern u1="&#xc3;" u2="&#xe4;" k="41" />
<hkern u1="&#xc3;" u2="&#xe3;" k="41" />
<hkern u1="&#xc3;" u2="&#xe2;" k="41" />
<hkern u1="&#xc3;" u2="&#xe1;" k="41" />
<hkern u1="&#xc3;" u2="&#xe0;" k="41" />
<hkern u1="&#xc3;" u2="&#xdc;" k="102" />
<hkern u1="&#xc3;" u2="&#xdb;" k="102" />
<hkern u1="&#xc3;" u2="&#xda;" k="102" />
<hkern u1="&#xc3;" u2="&#xd9;" k="102" />
<hkern u1="&#xc3;" u2="&#xd8;" k="102" />
<hkern u1="&#xc3;" u2="&#xd6;" k="102" />
<hkern u1="&#xc3;" u2="&#xd5;" k="102" />
<hkern u1="&#xc3;" u2="&#xd4;" k="102" />
<hkern u1="&#xc3;" u2="&#xd3;" k="102" />
<hkern u1="&#xc3;" u2="&#xd2;" k="102" />
<hkern u1="&#xc3;" u2="&#xc7;" k="102" />
<hkern u1="&#xc3;" u2="&#xba;" k="184" />
<hkern u1="&#xc3;" u2="&#xae;" k="205" />
<hkern u1="&#xc3;" u2="&#xaa;" k="164" />
<hkern u1="&#xc3;" u2="&#xa9;" k="102" />
<hkern u1="&#xc3;" u2="y" k="246" />
<hkern u1="&#xc3;" u2="w" k="205" />
<hkern u1="&#xc3;" u2="v" k="205" />
<hkern u1="&#xc3;" u2="u" k="41" />
<hkern u1="&#xc3;" u2="s" k="41" />
<hkern u1="&#xc3;" u2="q" k="102" />
<hkern u1="&#xc3;" u2="o" k="102" />
<hkern u1="&#xc3;" u2="g" k="102" />
<hkern u1="&#xc3;" u2="f" k="61" />
<hkern u1="&#xc3;" u2="e" k="102" />
<hkern u1="&#xc3;" u2="d" k="102" />
<hkern u1="&#xc3;" u2="c" k="102" />
<hkern u1="&#xc3;" u2="a" k="41" />
<hkern u1="&#xc3;" u2="Y" k="338" />
<hkern u1="&#xc3;" u2="W" k="287" />
<hkern u1="&#xc3;" u2="V" k="348" />
<hkern u1="&#xc3;" u2="U" k="102" />
<hkern u1="&#xc3;" u2="T" k="164" />
<hkern u1="&#xc3;" u2="S" k="61" />
<hkern u1="&#xc3;" u2="Q" k="102" />
<hkern u1="&#xc3;" u2="O" k="102" />
<hkern u1="&#xc3;" u2="G" k="102" />
<hkern u1="&#xc3;" u2="C" k="102" />
<hkern u1="&#xc3;" u2="&#x3f;" k="143" />
<hkern u1="&#xc3;" u2="&#x2d;" k="102" />
<hkern u1="&#xc3;" u2="&#x2a;" k="205" />
<hkern u1="&#xc4;" u2="&#x2122;" k="266" />
<hkern u1="&#xc4;" u2="&#x201d;" k="225" />
<hkern u1="&#xc4;" u2="&#x2019;" k="225" />
<hkern u1="&#xc4;" u2="&#x2014;" k="102" />
<hkern u1="&#xc4;" u2="&#x2013;" k="102" />
<hkern u1="&#xc4;" u2="&#x175;" k="205" />
<hkern u1="&#xc4;" u2="&#x174;" k="287" />
<hkern u1="&#xc4;" u2="&#x173;" k="41" />
<hkern u1="&#xc4;" u2="&#x172;" k="102" />
<hkern u1="&#xc4;" u2="&#x171;" k="41" />
<hkern u1="&#xc4;" u2="&#x170;" k="102" />
<hkern u1="&#xc4;" u2="&#x16f;" k="41" />
<hkern u1="&#xc4;" u2="&#x16e;" k="102" />
<hkern u1="&#xc4;" u2="&#x16d;" k="41" />
<hkern u1="&#xc4;" u2="&#x16c;" k="102" />
<hkern u1="&#xc4;" u2="&#x16b;" k="41" />
<hkern u1="&#xc4;" u2="&#x16a;" k="102" />
<hkern u1="&#xc4;" u2="&#x169;" k="41" />
<hkern u1="&#xc4;" u2="&#x168;" k="102" />
<hkern u1="&#xc4;" u2="&#x166;" k="164" />
<hkern u1="&#xc4;" u2="&#x164;" k="164" />
<hkern u1="&#xc4;" u2="&#x162;" k="164" />
<hkern u1="&#xc4;" u2="&#x152;" k="102" />
<hkern u1="&#xc4;" u2="&#x150;" k="102" />
<hkern u1="&#xc4;" u2="&#x14e;" k="102" />
<hkern u1="&#xc4;" u2="&#x14c;" k="102" />
<hkern u1="&#xc4;" u2="&#x122;" k="102" />
<hkern u1="&#xc4;" u2="&#x120;" k="102" />
<hkern u1="&#xc4;" u2="&#x11e;" k="102" />
<hkern u1="&#xc4;" u2="&#x11c;" k="102" />
<hkern u1="&#xc4;" u2="&#x10c;" k="102" />
<hkern u1="&#xc4;" u2="&#x10a;" k="102" />
<hkern u1="&#xc4;" u2="&#x108;" k="102" />
<hkern u1="&#xc4;" u2="&#x106;" k="102" />
<hkern u1="&#xc4;" u2="&#x105;" k="41" />
<hkern u1="&#xc4;" u2="&#x103;" k="41" />
<hkern u1="&#xc4;" u2="&#x101;" k="41" />
<hkern u1="&#xc4;" u2="&#xfc;" k="41" />
<hkern u1="&#xc4;" u2="&#xfb;" k="41" />
<hkern u1="&#xc4;" u2="&#xfa;" k="41" />
<hkern u1="&#xc4;" u2="&#xf9;" k="41" />
<hkern u1="&#xc4;" u2="&#xf6;" k="102" />
<hkern u1="&#xc4;" u2="&#xf5;" k="102" />
<hkern u1="&#xc4;" u2="&#xf4;" k="102" />
<hkern u1="&#xc4;" u2="&#xf3;" k="102" />
<hkern u1="&#xc4;" u2="&#xf2;" k="102" />
<hkern u1="&#xc4;" u2="&#xeb;" k="102" />
<hkern u1="&#xc4;" u2="&#xea;" k="102" />
<hkern u1="&#xc4;" u2="&#xe9;" k="102" />
<hkern u1="&#xc4;" u2="&#xe8;" k="102" />
<hkern u1="&#xc4;" u2="&#xe6;" k="41" />
<hkern u1="&#xc4;" u2="&#xe5;" k="41" />
<hkern u1="&#xc4;" u2="&#xe4;" k="41" />
<hkern u1="&#xc4;" u2="&#xe3;" k="41" />
<hkern u1="&#xc4;" u2="&#xe2;" k="41" />
<hkern u1="&#xc4;" u2="&#xe1;" k="41" />
<hkern u1="&#xc4;" u2="&#xe0;" k="41" />
<hkern u1="&#xc4;" u2="&#xdc;" k="102" />
<hkern u1="&#xc4;" u2="&#xdb;" k="102" />
<hkern u1="&#xc4;" u2="&#xda;" k="102" />
<hkern u1="&#xc4;" u2="&#xd9;" k="102" />
<hkern u1="&#xc4;" u2="&#xd8;" k="102" />
<hkern u1="&#xc4;" u2="&#xd6;" k="102" />
<hkern u1="&#xc4;" u2="&#xd5;" k="102" />
<hkern u1="&#xc4;" u2="&#xd4;" k="102" />
<hkern u1="&#xc4;" u2="&#xd3;" k="102" />
<hkern u1="&#xc4;" u2="&#xd2;" k="102" />
<hkern u1="&#xc4;" u2="&#xc7;" k="102" />
<hkern u1="&#xc4;" u2="&#xba;" k="184" />
<hkern u1="&#xc4;" u2="&#xae;" k="205" />
<hkern u1="&#xc4;" u2="&#xaa;" k="164" />
<hkern u1="&#xc4;" u2="&#xa9;" k="102" />
<hkern u1="&#xc4;" u2="y" k="246" />
<hkern u1="&#xc4;" u2="w" k="205" />
<hkern u1="&#xc4;" u2="v" k="205" />
<hkern u1="&#xc4;" u2="u" k="41" />
<hkern u1="&#xc4;" u2="s" k="41" />
<hkern u1="&#xc4;" u2="q" k="102" />
<hkern u1="&#xc4;" u2="o" k="102" />
<hkern u1="&#xc4;" u2="g" k="102" />
<hkern u1="&#xc4;" u2="f" k="61" />
<hkern u1="&#xc4;" u2="e" k="102" />
<hkern u1="&#xc4;" u2="d" k="102" />
<hkern u1="&#xc4;" u2="c" k="102" />
<hkern u1="&#xc4;" u2="a" k="41" />
<hkern u1="&#xc4;" u2="Y" k="338" />
<hkern u1="&#xc4;" u2="W" k="287" />
<hkern u1="&#xc4;" u2="V" k="348" />
<hkern u1="&#xc4;" u2="U" k="102" />
<hkern u1="&#xc4;" u2="T" k="164" />
<hkern u1="&#xc4;" u2="S" k="61" />
<hkern u1="&#xc4;" u2="Q" k="102" />
<hkern u1="&#xc4;" u2="O" k="102" />
<hkern u1="&#xc4;" u2="G" k="102" />
<hkern u1="&#xc4;" u2="C" k="102" />
<hkern u1="&#xc4;" u2="&#x3f;" k="143" />
<hkern u1="&#xc4;" u2="&#x2d;" k="102" />
<hkern u1="&#xc4;" u2="&#x2a;" k="205" />
<hkern u1="&#xc5;" u2="&#x2122;" k="266" />
<hkern u1="&#xc5;" u2="&#x201d;" k="225" />
<hkern u1="&#xc5;" u2="&#x2019;" k="225" />
<hkern u1="&#xc5;" u2="&#x2014;" k="102" />
<hkern u1="&#xc5;" u2="&#x2013;" k="102" />
<hkern u1="&#xc5;" u2="&#x175;" k="205" />
<hkern u1="&#xc5;" u2="&#x174;" k="287" />
<hkern u1="&#xc5;" u2="&#x173;" k="41" />
<hkern u1="&#xc5;" u2="&#x172;" k="102" />
<hkern u1="&#xc5;" u2="&#x171;" k="41" />
<hkern u1="&#xc5;" u2="&#x170;" k="102" />
<hkern u1="&#xc5;" u2="&#x16f;" k="41" />
<hkern u1="&#xc5;" u2="&#x16e;" k="102" />
<hkern u1="&#xc5;" u2="&#x16d;" k="41" />
<hkern u1="&#xc5;" u2="&#x16c;" k="102" />
<hkern u1="&#xc5;" u2="&#x16b;" k="41" />
<hkern u1="&#xc5;" u2="&#x16a;" k="102" />
<hkern u1="&#xc5;" u2="&#x169;" k="41" />
<hkern u1="&#xc5;" u2="&#x168;" k="102" />
<hkern u1="&#xc5;" u2="&#x166;" k="164" />
<hkern u1="&#xc5;" u2="&#x164;" k="164" />
<hkern u1="&#xc5;" u2="&#x162;" k="164" />
<hkern u1="&#xc5;" u2="&#x152;" k="102" />
<hkern u1="&#xc5;" u2="&#x150;" k="102" />
<hkern u1="&#xc5;" u2="&#x14e;" k="102" />
<hkern u1="&#xc5;" u2="&#x14c;" k="102" />
<hkern u1="&#xc5;" u2="&#x122;" k="102" />
<hkern u1="&#xc5;" u2="&#x120;" k="102" />
<hkern u1="&#xc5;" u2="&#x11e;" k="102" />
<hkern u1="&#xc5;" u2="&#x11c;" k="102" />
<hkern u1="&#xc5;" u2="&#x10c;" k="102" />
<hkern u1="&#xc5;" u2="&#x10a;" k="102" />
<hkern u1="&#xc5;" u2="&#x108;" k="102" />
<hkern u1="&#xc5;" u2="&#x106;" k="102" />
<hkern u1="&#xc5;" u2="&#x105;" k="41" />
<hkern u1="&#xc5;" u2="&#x103;" k="41" />
<hkern u1="&#xc5;" u2="&#x101;" k="41" />
<hkern u1="&#xc5;" u2="&#xfc;" k="41" />
<hkern u1="&#xc5;" u2="&#xfb;" k="41" />
<hkern u1="&#xc5;" u2="&#xfa;" k="41" />
<hkern u1="&#xc5;" u2="&#xf9;" k="41" />
<hkern u1="&#xc5;" u2="&#xf6;" k="102" />
<hkern u1="&#xc5;" u2="&#xf5;" k="102" />
<hkern u1="&#xc5;" u2="&#xf4;" k="102" />
<hkern u1="&#xc5;" u2="&#xf3;" k="102" />
<hkern u1="&#xc5;" u2="&#xf2;" k="102" />
<hkern u1="&#xc5;" u2="&#xeb;" k="102" />
<hkern u1="&#xc5;" u2="&#xea;" k="102" />
<hkern u1="&#xc5;" u2="&#xe9;" k="102" />
<hkern u1="&#xc5;" u2="&#xe8;" k="102" />
<hkern u1="&#xc5;" u2="&#xe6;" k="41" />
<hkern u1="&#xc5;" u2="&#xe5;" k="41" />
<hkern u1="&#xc5;" u2="&#xe4;" k="41" />
<hkern u1="&#xc5;" u2="&#xe3;" k="41" />
<hkern u1="&#xc5;" u2="&#xe2;" k="41" />
<hkern u1="&#xc5;" u2="&#xe1;" k="41" />
<hkern u1="&#xc5;" u2="&#xe0;" k="41" />
<hkern u1="&#xc5;" u2="&#xdc;" k="102" />
<hkern u1="&#xc5;" u2="&#xdb;" k="102" />
<hkern u1="&#xc5;" u2="&#xda;" k="102" />
<hkern u1="&#xc5;" u2="&#xd9;" k="102" />
<hkern u1="&#xc5;" u2="&#xd8;" k="102" />
<hkern u1="&#xc5;" u2="&#xd6;" k="102" />
<hkern u1="&#xc5;" u2="&#xd5;" k="102" />
<hkern u1="&#xc5;" u2="&#xd4;" k="102" />
<hkern u1="&#xc5;" u2="&#xd3;" k="102" />
<hkern u1="&#xc5;" u2="&#xd2;" k="102" />
<hkern u1="&#xc5;" u2="&#xc7;" k="102" />
<hkern u1="&#xc5;" u2="&#xba;" k="184" />
<hkern u1="&#xc5;" u2="&#xae;" k="205" />
<hkern u1="&#xc5;" u2="&#xaa;" k="164" />
<hkern u1="&#xc5;" u2="&#xa9;" k="102" />
<hkern u1="&#xc5;" u2="y" k="246" />
<hkern u1="&#xc5;" u2="w" k="205" />
<hkern u1="&#xc5;" u2="v" k="205" />
<hkern u1="&#xc5;" u2="u" k="41" />
<hkern u1="&#xc5;" u2="s" k="41" />
<hkern u1="&#xc5;" u2="q" k="102" />
<hkern u1="&#xc5;" u2="o" k="102" />
<hkern u1="&#xc5;" u2="g" k="102" />
<hkern u1="&#xc5;" u2="f" k="61" />
<hkern u1="&#xc5;" u2="e" k="102" />
<hkern u1="&#xc5;" u2="d" k="102" />
<hkern u1="&#xc5;" u2="c" k="102" />
<hkern u1="&#xc5;" u2="a" k="41" />
<hkern u1="&#xc5;" u2="Y" k="338" />
<hkern u1="&#xc5;" u2="W" k="287" />
<hkern u1="&#xc5;" u2="V" k="348" />
<hkern u1="&#xc5;" u2="U" k="102" />
<hkern u1="&#xc5;" u2="T" k="164" />
<hkern u1="&#xc5;" u2="S" k="61" />
<hkern u1="&#xc5;" u2="Q" k="102" />
<hkern u1="&#xc5;" u2="O" k="102" />
<hkern u1="&#xc5;" u2="G" k="102" />
<hkern u1="&#xc5;" u2="C" k="102" />
<hkern u1="&#xc5;" u2="&#x3f;" k="143" />
<hkern u1="&#xc5;" u2="&#x2d;" k="102" />
<hkern u1="&#xc5;" u2="&#x2a;" k="205" />
<hkern u1="&#xc6;" u2="&#x2122;" k="-41" />
<hkern u1="&#xc7;" u2="&#x175;" k="61" />
<hkern u1="&#xc7;" u2="&#x167;" k="41" />
<hkern u1="&#xc7;" u2="&#x165;" k="41" />
<hkern u1="&#xc7;" u2="&#x163;" k="41" />
<hkern u1="&#xc7;" u2="&#x152;" k="82" />
<hkern u1="&#xc7;" u2="&#x150;" k="82" />
<hkern u1="&#xc7;" u2="&#x14e;" k="82" />
<hkern u1="&#xc7;" u2="&#x14c;" k="82" />
<hkern u1="&#xc7;" u2="&#x122;" k="82" />
<hkern u1="&#xc7;" u2="&#x120;" k="82" />
<hkern u1="&#xc7;" u2="&#x11e;" k="82" />
<hkern u1="&#xc7;" u2="&#x11c;" k="82" />
<hkern u1="&#xc7;" u2="&#x10c;" k="82" />
<hkern u1="&#xc7;" u2="&#x10a;" k="82" />
<hkern u1="&#xc7;" u2="&#x108;" k="82" />
<hkern u1="&#xc7;" u2="&#x106;" k="82" />
<hkern u1="&#xc7;" u2="&#xf6;" k="41" />
<hkern u1="&#xc7;" u2="&#xf5;" k="41" />
<hkern u1="&#xc7;" u2="&#xf4;" k="41" />
<hkern u1="&#xc7;" u2="&#xf3;" k="41" />
<hkern u1="&#xc7;" u2="&#xf2;" k="41" />
<hkern u1="&#xc7;" u2="&#xd8;" k="82" />
<hkern u1="&#xc7;" u2="&#xd6;" k="82" />
<hkern u1="&#xc7;" u2="&#xd5;" k="82" />
<hkern u1="&#xc7;" u2="&#xd4;" k="82" />
<hkern u1="&#xc7;" u2="&#xd3;" k="82" />
<hkern u1="&#xc7;" u2="&#xd2;" k="82" />
<hkern u1="&#xc7;" u2="&#xc7;" k="82" />
<hkern u1="&#xc7;" u2="&#xa9;" k="82" />
<hkern u1="&#xc7;" u2="y" k="82" />
<hkern u1="&#xc7;" u2="w" k="61" />
<hkern u1="&#xc7;" u2="t" k="41" />
<hkern u1="&#xc7;" u2="o" k="41" />
<hkern u1="&#xc7;" u2="e" k="41" />
<hkern u1="&#xc7;" u2="d" k="41" />
<hkern u1="&#xc7;" u2="Y" k="41" />
<hkern u1="&#xc7;" u2="Q" k="82" />
<hkern u1="&#xc7;" u2="O" k="82" />
<hkern u1="&#xc7;" u2="G" k="82" />
<hkern u1="&#xc7;" u2="C" k="82" />
<hkern u1="&#xc8;" u2="&#x2122;" k="-41" />
<hkern u1="&#xc9;" u2="&#x2122;" k="-41" />
<hkern u1="&#xca;" u2="&#x2122;" k="-41" />
<hkern u1="&#xcb;" u2="&#x2122;" k="-41" />
<hkern u1="&#xcc;" u2="&#x2044;" k="-41" />
<hkern u1="&#xcc;" u2="&#x32;" k="-41" />
<hkern u1="&#xcc;" u2="&#x31;" k="-31" />
<hkern u1="&#xcc;" u2="&#x2e;" k="10" />
<hkern u1="&#xcd;" u2="&#x2044;" k="-41" />
<hkern u1="&#xcd;" u2="&#x32;" k="-41" />
<hkern u1="&#xcd;" u2="&#x31;" k="-31" />
<hkern u1="&#xcd;" u2="&#x2e;" k="10" />
<hkern u1="&#xce;" u2="&#x2044;" k="-41" />
<hkern u1="&#xce;" u2="&#x32;" k="-41" />
<hkern u1="&#xce;" u2="&#x31;" k="-31" />
<hkern u1="&#xce;" u2="&#x2e;" k="10" />
<hkern u1="&#xcf;" u2="&#x2044;" k="-41" />
<hkern u1="&#xcf;" u2="&#x32;" k="-41" />
<hkern u1="&#xcf;" u2="&#x31;" k="-31" />
<hkern u1="&#xcf;" u2="&#x2e;" k="10" />
<hkern u1="&#xd1;" u2="&#x2044;" k="-41" />
<hkern u1="&#xd1;" u2="&#x32;" k="-41" />
<hkern u1="&#xd1;" u2="&#x31;" k="-31" />
<hkern u1="&#xd1;" u2="&#x2e;" k="10" />
<hkern u1="&#xd9;" u2="&#x104;" k="102" />
<hkern u1="&#xd9;" u2="&#x102;" k="102" />
<hkern u1="&#xd9;" u2="&#x100;" k="102" />
<hkern u1="&#xd9;" u2="&#xc5;" k="102" />
<hkern u1="&#xd9;" u2="&#xc4;" k="102" />
<hkern u1="&#xd9;" u2="&#xc3;" k="102" />
<hkern u1="&#xd9;" u2="&#xc2;" k="102" />
<hkern u1="&#xd9;" u2="&#xc1;" k="102" />
<hkern u1="&#xd9;" u2="&#xc0;" k="102" />
<hkern u1="&#xd9;" u2="A" k="102" />
<hkern u1="&#xd9;" u2="&#x2e;" k="41" />
<hkern u1="&#xda;" u2="&#x104;" k="102" />
<hkern u1="&#xda;" u2="&#x102;" k="102" />
<hkern u1="&#xda;" u2="&#x100;" k="102" />
<hkern u1="&#xda;" u2="&#xc5;" k="102" />
<hkern u1="&#xda;" u2="&#xc4;" k="102" />
<hkern u1="&#xda;" u2="&#xc3;" k="102" />
<hkern u1="&#xda;" u2="&#xc2;" k="102" />
<hkern u1="&#xda;" u2="&#xc1;" k="102" />
<hkern u1="&#xda;" u2="&#xc0;" k="102" />
<hkern u1="&#xda;" u2="A" k="102" />
<hkern u1="&#xda;" u2="&#x2e;" k="41" />
<hkern u1="&#xdb;" u2="&#x104;" k="102" />
<hkern u1="&#xdb;" u2="&#x102;" k="102" />
<hkern u1="&#xdb;" u2="&#x100;" k="102" />
<hkern u1="&#xdb;" u2="&#xc5;" k="102" />
<hkern u1="&#xdb;" u2="&#xc4;" k="102" />
<hkern u1="&#xdb;" u2="&#xc3;" k="102" />
<hkern u1="&#xdb;" u2="&#xc2;" k="102" />
<hkern u1="&#xdb;" u2="&#xc1;" k="102" />
<hkern u1="&#xdb;" u2="&#xc0;" k="102" />
<hkern u1="&#xdb;" u2="A" k="102" />
<hkern u1="&#xdb;" u2="&#x2e;" k="41" />
<hkern u1="&#xdc;" u2="&#x104;" k="102" />
<hkern u1="&#xdc;" u2="&#x102;" k="102" />
<hkern u1="&#xdc;" u2="&#x100;" k="102" />
<hkern u1="&#xdc;" u2="&#xc5;" k="102" />
<hkern u1="&#xdc;" u2="&#xc4;" k="102" />
<hkern u1="&#xdc;" u2="&#xc3;" k="102" />
<hkern u1="&#xdc;" u2="&#xc2;" k="102" />
<hkern u1="&#xdc;" u2="&#xc1;" k="102" />
<hkern u1="&#xdc;" u2="&#xc0;" k="102" />
<hkern u1="&#xdc;" u2="A" k="102" />
<hkern u1="&#xdc;" u2="&#x2e;" k="41" />
<hkern u1="&#xe0;" u2="&#x175;" k="51" />
<hkern u1="&#xe0;" u2="y" k="82" />
<hkern u1="&#xe0;" u2="w" k="51" />
<hkern u1="&#xe0;" u2="v" k="41" />
<hkern u1="&#xe0;" u2="&#x2a;" k="20" />
<hkern u1="&#xe1;" u2="&#x175;" k="51" />
<hkern u1="&#xe1;" u2="y" k="82" />
<hkern u1="&#xe1;" u2="w" k="51" />
<hkern u1="&#xe1;" u2="v" k="41" />
<hkern u1="&#xe1;" u2="&#x2a;" k="20" />
<hkern u1="&#xe2;" u2="&#x175;" k="51" />
<hkern u1="&#xe2;" u2="y" k="82" />
<hkern u1="&#xe2;" u2="w" k="51" />
<hkern u1="&#xe2;" u2="v" k="41" />
<hkern u1="&#xe2;" u2="&#x2a;" k="20" />
<hkern u1="&#xe3;" u2="&#x175;" k="51" />
<hkern u1="&#xe3;" u2="y" k="82" />
<hkern u1="&#xe3;" u2="w" k="51" />
<hkern u1="&#xe3;" u2="v" k="41" />
<hkern u1="&#xe3;" u2="&#x2a;" k="20" />
<hkern u1="&#xe4;" u2="&#x175;" k="51" />
<hkern u1="&#xe4;" u2="y" k="82" />
<hkern u1="&#xe4;" u2="w" k="51" />
<hkern u1="&#xe4;" u2="v" k="41" />
<hkern u1="&#xe4;" u2="&#x2a;" k="20" />
<hkern u1="&#xe5;" u2="&#x175;" k="51" />
<hkern u1="&#xe5;" u2="y" k="82" />
<hkern u1="&#xe5;" u2="w" k="51" />
<hkern u1="&#xe5;" u2="v" k="41" />
<hkern u1="&#xe5;" u2="&#x2a;" k="20" />
<hkern u1="&#xe6;" u2="&#x175;" k="61" />
<hkern u1="&#xe6;" u2="y" k="82" />
<hkern u1="&#xe6;" u2="x" k="82" />
<hkern u1="&#xe6;" u2="w" k="61" />
<hkern u1="&#xe6;" u2="v" k="61" />
<hkern u1="&#xe6;" u2="&#x2e;" k="-20" />
<hkern u1="&#xe6;" u2="&#x2a;" k="20" />
<hkern u1="&#xe7;" u2="&#xf6;" k="61" />
<hkern u1="&#xe7;" u2="&#xf5;" k="61" />
<hkern u1="&#xe7;" u2="&#xf4;" k="61" />
<hkern u1="&#xe7;" u2="&#xf3;" k="61" />
<hkern u1="&#xe7;" u2="&#xf2;" k="61" />
<hkern u1="&#xe7;" u2="&#xeb;" k="61" />
<hkern u1="&#xe7;" u2="&#xea;" k="61" />
<hkern u1="&#xe7;" u2="&#xe9;" k="61" />
<hkern u1="&#xe7;" u2="&#xe8;" k="61" />
<hkern u1="&#xe7;" u2="q" k="61" />
<hkern u1="&#xe7;" u2="o" k="61" />
<hkern u1="&#xe7;" u2="e" k="61" />
<hkern u1="&#xe7;" u2="c" k="41" />
<hkern u1="&#xe8;" u2="&#x175;" k="61" />
<hkern u1="&#xe8;" u2="y" k="82" />
<hkern u1="&#xe8;" u2="x" k="82" />
<hkern u1="&#xe8;" u2="w" k="61" />
<hkern u1="&#xe8;" u2="v" k="61" />
<hkern u1="&#xe8;" u2="&#x2e;" k="-20" />
<hkern u1="&#xe8;" u2="&#x2a;" k="20" />
<hkern u1="&#xe9;" u2="&#x175;" k="61" />
<hkern u1="&#xe9;" u2="y" k="82" />
<hkern u1="&#xe9;" u2="x" k="82" />
<hkern u1="&#xe9;" u2="w" k="61" />
<hkern u1="&#xe9;" u2="v" k="61" />
<hkern u1="&#xe9;" u2="&#x2e;" k="-20" />
<hkern u1="&#xe9;" u2="&#x2a;" k="20" />
<hkern u1="&#xea;" u2="&#x175;" k="61" />
<hkern u1="&#xea;" u2="y" k="82" />
<hkern u1="&#xea;" u2="x" k="82" />
<hkern u1="&#xea;" u2="w" k="61" />
<hkern u1="&#xea;" u2="v" k="61" />
<hkern u1="&#xea;" u2="&#x2e;" k="-20" />
<hkern u1="&#xea;" u2="&#x2a;" k="20" />
<hkern u1="&#xeb;" u2="&#x175;" k="61" />
<hkern u1="&#xeb;" u2="y" k="82" />
<hkern u1="&#xeb;" u2="x" k="82" />
<hkern u1="&#xeb;" u2="w" k="61" />
<hkern u1="&#xeb;" u2="v" k="61" />
<hkern u1="&#xeb;" u2="&#x2e;" k="-20" />
<hkern u1="&#xeb;" u2="&#x2a;" k="20" />
<hkern u1="&#xec;" u2="&#xae;" k="-61" />
<hkern u1="&#xed;" u2="&#xae;" k="-61" />
<hkern u1="&#xee;" u2="&#xae;" k="-61" />
<hkern u1="&#xef;" u2="&#xae;" k="-61" />
<hkern u1="&#xf1;" u2="&#x175;" k="51" />
<hkern u1="&#xf1;" u2="y" k="82" />
<hkern u1="&#xf1;" u2="w" k="51" />
<hkern u1="&#xf1;" u2="v" k="41" />
<hkern u1="&#xf1;" u2="&#x2a;" k="20" />
<hkern u1="&#xf2;" u2="&#x175;" k="61" />
<hkern u1="&#xf2;" u2="y" k="102" />
<hkern u1="&#xf2;" u2="x" k="123" />
<hkern u1="&#xf2;" u2="w" k="61" />
<hkern u1="&#xf2;" u2="v" k="82" />
<hkern u1="&#xf2;" u2="&#x2e;" k="20" />
<hkern u1="&#xf2;" u2="&#x2a;" k="41" />
<hkern u1="&#xf3;" u2="&#x175;" k="61" />
<hkern u1="&#xf3;" u2="y" k="102" />
<hkern u1="&#xf3;" u2="x" k="123" />
<hkern u1="&#xf3;" u2="w" k="61" />
<hkern u1="&#xf3;" u2="v" k="82" />
<hkern u1="&#xf3;" u2="&#x2e;" k="20" />
<hkern u1="&#xf3;" u2="&#x2a;" k="41" />
<hkern u1="&#xf4;" u2="&#x175;" k="61" />
<hkern u1="&#xf4;" u2="y" k="102" />
<hkern u1="&#xf4;" u2="x" k="123" />
<hkern u1="&#xf4;" u2="w" k="61" />
<hkern u1="&#xf4;" u2="v" k="82" />
<hkern u1="&#xf4;" u2="&#x2e;" k="20" />
<hkern u1="&#xf4;" u2="&#x2a;" k="41" />
<hkern u1="&#xf5;" u2="&#x175;" k="61" />
<hkern u1="&#xf5;" u2="y" k="102" />
<hkern u1="&#xf5;" u2="x" k="123" />
<hkern u1="&#xf5;" u2="w" k="61" />
<hkern u1="&#xf5;" u2="v" k="82" />
<hkern u1="&#xf5;" u2="&#x2e;" k="20" />
<hkern u1="&#xf5;" u2="&#x2a;" k="41" />
<hkern u1="&#xf6;" u2="&#x175;" k="61" />
<hkern u1="&#xf6;" u2="y" k="102" />
<hkern u1="&#xf6;" u2="x" k="123" />
<hkern u1="&#xf6;" u2="w" k="61" />
<hkern u1="&#xf6;" u2="v" k="82" />
<hkern u1="&#xf6;" u2="&#x2e;" k="20" />
<hkern u1="&#xf6;" u2="&#x2a;" k="41" />
<hkern u1="&#x100;" u2="&#x2122;" k="266" />
<hkern u1="&#x100;" u2="&#x2019;" k="225" />
<hkern u1="&#x100;" u2="&#x2014;" k="102" />
<hkern u1="&#x100;" u2="&#x2013;" k="102" />
<hkern u1="&#x100;" u2="&#x152;" k="102" />
<hkern u1="&#x100;" u2="&#xfc;" k="41" />
<hkern u1="&#x100;" u2="&#xfb;" k="41" />
<hkern u1="&#x100;" u2="&#xfa;" k="41" />
<hkern u1="&#x100;" u2="&#xf9;" k="41" />
<hkern u1="&#x100;" u2="&#xf6;" k="102" />
<hkern u1="&#x100;" u2="&#xf5;" k="102" />
<hkern u1="&#x100;" u2="&#xf4;" k="102" />
<hkern u1="&#x100;" u2="&#xf3;" k="102" />
<hkern u1="&#x100;" u2="&#xf2;" k="102" />
<hkern u1="&#x100;" u2="&#xeb;" k="102" />
<hkern u1="&#x100;" u2="&#xea;" k="102" />
<hkern u1="&#x100;" u2="&#xe9;" k="102" />
<hkern u1="&#x100;" u2="&#xe8;" k="102" />
<hkern u1="&#x100;" u2="&#xe6;" k="41" />
<hkern u1="&#x100;" u2="&#xe5;" k="41" />
<hkern u1="&#x100;" u2="&#xe4;" k="41" />
<hkern u1="&#x100;" u2="&#xe3;" k="41" />
<hkern u1="&#x100;" u2="&#xe2;" k="41" />
<hkern u1="&#x100;" u2="&#xe1;" k="41" />
<hkern u1="&#x100;" u2="&#xe0;" k="41" />
<hkern u1="&#x100;" u2="&#xdc;" k="102" />
<hkern u1="&#x100;" u2="&#xdb;" k="102" />
<hkern u1="&#x100;" u2="&#xda;" k="102" />
<hkern u1="&#x100;" u2="&#xd9;" k="102" />
<hkern u1="&#x100;" u2="&#xd8;" k="102" />
<hkern u1="&#x100;" u2="&#xd6;" k="102" />
<hkern u1="&#x100;" u2="&#xd5;" k="102" />
<hkern u1="&#x100;" u2="&#xd4;" k="102" />
<hkern u1="&#x100;" u2="&#xd3;" k="102" />
<hkern u1="&#x100;" u2="&#xd2;" k="102" />
<hkern u1="&#x100;" u2="&#xc7;" k="102" />
<hkern u1="&#x100;" u2="&#xba;" k="184" />
<hkern u1="&#x100;" u2="&#xae;" k="205" />
<hkern u1="&#x100;" u2="&#xaa;" k="164" />
<hkern u1="&#x100;" u2="&#xa9;" k="102" />
<hkern u1="&#x100;" u2="y" k="246" />
<hkern u1="&#x100;" u2="v" k="205" />
<hkern u1="&#x100;" u2="s" k="41" />
<hkern u1="&#x100;" u2="q" k="102" />
<hkern u1="&#x100;" u2="o" k="102" />
<hkern u1="&#x100;" u2="g" k="102" />
<hkern u1="&#x100;" u2="e" k="102" />
<hkern u1="&#x100;" u2="d" k="102" />
<hkern u1="&#x100;" u2="c" k="102" />
<hkern u1="&#x100;" u2="Y" k="338" />
<hkern u1="&#x100;" u2="V" k="348" />
<hkern u1="&#x100;" u2="S" k="61" />
<hkern u1="&#x100;" u2="Q" k="102" />
<hkern u1="&#x100;" u2="O" k="102" />
<hkern u1="&#x100;" u2="G" k="102" />
<hkern u1="&#x100;" u2="&#x3f;" k="143" />
<hkern u1="&#x100;" u2="&#x2a;" k="205" />
<hkern u1="&#x101;" u2="y" k="61" />
<hkern u1="&#x101;" u2="v" k="61" />
<hkern u1="&#x101;" u2="&#x2a;" k="20" />
<hkern u1="&#x102;" u2="&#x2122;" k="266" />
<hkern u1="&#x102;" u2="&#x2019;" k="225" />
<hkern u1="&#x102;" u2="&#x2014;" k="102" />
<hkern u1="&#x102;" u2="&#x2013;" k="102" />
<hkern u1="&#x102;" u2="&#x152;" k="102" />
<hkern u1="&#x102;" u2="&#xfc;" k="41" />
<hkern u1="&#x102;" u2="&#xfb;" k="41" />
<hkern u1="&#x102;" u2="&#xfa;" k="41" />
<hkern u1="&#x102;" u2="&#xf9;" k="41" />
<hkern u1="&#x102;" u2="&#xf6;" k="102" />
<hkern u1="&#x102;" u2="&#xf5;" k="102" />
<hkern u1="&#x102;" u2="&#xf4;" k="102" />
<hkern u1="&#x102;" u2="&#xf3;" k="102" />
<hkern u1="&#x102;" u2="&#xf2;" k="102" />
<hkern u1="&#x102;" u2="&#xeb;" k="102" />
<hkern u1="&#x102;" u2="&#xea;" k="102" />
<hkern u1="&#x102;" u2="&#xe9;" k="102" />
<hkern u1="&#x102;" u2="&#xe8;" k="102" />
<hkern u1="&#x102;" u2="&#xe6;" k="41" />
<hkern u1="&#x102;" u2="&#xe5;" k="41" />
<hkern u1="&#x102;" u2="&#xe4;" k="41" />
<hkern u1="&#x102;" u2="&#xe3;" k="41" />
<hkern u1="&#x102;" u2="&#xe2;" k="41" />
<hkern u1="&#x102;" u2="&#xe1;" k="41" />
<hkern u1="&#x102;" u2="&#xe0;" k="41" />
<hkern u1="&#x102;" u2="&#xdc;" k="102" />
<hkern u1="&#x102;" u2="&#xdb;" k="102" />
<hkern u1="&#x102;" u2="&#xda;" k="102" />
<hkern u1="&#x102;" u2="&#xd9;" k="102" />
<hkern u1="&#x102;" u2="&#xd8;" k="102" />
<hkern u1="&#x102;" u2="&#xd6;" k="102" />
<hkern u1="&#x102;" u2="&#xd5;" k="102" />
<hkern u1="&#x102;" u2="&#xd4;" k="102" />
<hkern u1="&#x102;" u2="&#xd3;" k="102" />
<hkern u1="&#x102;" u2="&#xd2;" k="102" />
<hkern u1="&#x102;" u2="&#xc7;" k="102" />
<hkern u1="&#x102;" u2="&#xba;" k="184" />
<hkern u1="&#x102;" u2="&#xae;" k="205" />
<hkern u1="&#x102;" u2="&#xaa;" k="164" />
<hkern u1="&#x102;" u2="&#xa9;" k="102" />
<hkern u1="&#x102;" u2="y" k="246" />
<hkern u1="&#x102;" u2="v" k="205" />
<hkern u1="&#x102;" u2="s" k="41" />
<hkern u1="&#x102;" u2="q" k="102" />
<hkern u1="&#x102;" u2="o" k="102" />
<hkern u1="&#x102;" u2="g" k="102" />
<hkern u1="&#x102;" u2="e" k="102" />
<hkern u1="&#x102;" u2="d" k="102" />
<hkern u1="&#x102;" u2="c" k="102" />
<hkern u1="&#x102;" u2="Y" k="338" />
<hkern u1="&#x102;" u2="V" k="348" />
<hkern u1="&#x102;" u2="S" k="61" />
<hkern u1="&#x102;" u2="Q" k="102" />
<hkern u1="&#x102;" u2="O" k="102" />
<hkern u1="&#x102;" u2="G" k="102" />
<hkern u1="&#x102;" u2="&#x3f;" k="143" />
<hkern u1="&#x102;" u2="&#x2a;" k="205" />
<hkern u1="&#x103;" u2="y" k="61" />
<hkern u1="&#x103;" u2="v" k="61" />
<hkern u1="&#x103;" u2="&#x2a;" k="20" />
<hkern u1="&#x104;" u2="&#x2122;" k="266" />
<hkern u1="&#x104;" u2="&#x2019;" k="225" />
<hkern u1="&#x104;" u2="&#x2014;" k="102" />
<hkern u1="&#x104;" u2="&#x2013;" k="102" />
<hkern u1="&#x104;" u2="&#x152;" k="102" />
<hkern u1="&#x104;" u2="&#xfc;" k="41" />
<hkern u1="&#x104;" u2="&#xfb;" k="41" />
<hkern u1="&#x104;" u2="&#xfa;" k="41" />
<hkern u1="&#x104;" u2="&#xf9;" k="41" />
<hkern u1="&#x104;" u2="&#xf6;" k="102" />
<hkern u1="&#x104;" u2="&#xf5;" k="102" />
<hkern u1="&#x104;" u2="&#xf4;" k="102" />
<hkern u1="&#x104;" u2="&#xf3;" k="102" />
<hkern u1="&#x104;" u2="&#xf2;" k="102" />
<hkern u1="&#x104;" u2="&#xeb;" k="102" />
<hkern u1="&#x104;" u2="&#xea;" k="102" />
<hkern u1="&#x104;" u2="&#xe9;" k="102" />
<hkern u1="&#x104;" u2="&#xe8;" k="102" />
<hkern u1="&#x104;" u2="&#xe6;" k="41" />
<hkern u1="&#x104;" u2="&#xe5;" k="41" />
<hkern u1="&#x104;" u2="&#xe4;" k="41" />
<hkern u1="&#x104;" u2="&#xe3;" k="41" />
<hkern u1="&#x104;" u2="&#xe2;" k="41" />
<hkern u1="&#x104;" u2="&#xe1;" k="41" />
<hkern u1="&#x104;" u2="&#xe0;" k="41" />
<hkern u1="&#x104;" u2="&#xdc;" k="102" />
<hkern u1="&#x104;" u2="&#xdb;" k="102" />
<hkern u1="&#x104;" u2="&#xda;" k="102" />
<hkern u1="&#x104;" u2="&#xd9;" k="102" />
<hkern u1="&#x104;" u2="&#xd8;" k="102" />
<hkern u1="&#x104;" u2="&#xd6;" k="102" />
<hkern u1="&#x104;" u2="&#xd5;" k="102" />
<hkern u1="&#x104;" u2="&#xd4;" k="102" />
<hkern u1="&#x104;" u2="&#xd3;" k="102" />
<hkern u1="&#x104;" u2="&#xd2;" k="102" />
<hkern u1="&#x104;" u2="&#xc7;" k="102" />
<hkern u1="&#x104;" u2="&#xba;" k="184" />
<hkern u1="&#x104;" u2="&#xae;" k="205" />
<hkern u1="&#x104;" u2="&#xaa;" k="164" />
<hkern u1="&#x104;" u2="&#xa9;" k="102" />
<hkern u1="&#x104;" u2="y" k="246" />
<hkern u1="&#x104;" u2="v" k="205" />
<hkern u1="&#x104;" u2="s" k="41" />
<hkern u1="&#x104;" u2="q" k="102" />
<hkern u1="&#x104;" u2="o" k="102" />
<hkern u1="&#x104;" u2="g" k="102" />
<hkern u1="&#x104;" u2="e" k="102" />
<hkern u1="&#x104;" u2="d" k="102" />
<hkern u1="&#x104;" u2="c" k="102" />
<hkern u1="&#x104;" u2="Y" k="338" />
<hkern u1="&#x104;" u2="V" k="348" />
<hkern u1="&#x104;" u2="S" k="61" />
<hkern u1="&#x104;" u2="Q" k="102" />
<hkern u1="&#x104;" u2="O" k="102" />
<hkern u1="&#x104;" u2="G" k="102" />
<hkern u1="&#x104;" u2="&#x3f;" k="143" />
<hkern u1="&#x104;" u2="&#x2a;" k="205" />
<hkern u1="&#x105;" u2="y" k="61" />
<hkern u1="&#x105;" u2="v" k="61" />
<hkern u1="&#x105;" u2="&#x2a;" k="20" />
<hkern u1="&#x106;" u2="&#x152;" k="82" />
<hkern u1="&#x106;" u2="&#xf6;" k="41" />
<hkern u1="&#x106;" u2="&#xf5;" k="41" />
<hkern u1="&#x106;" u2="&#xf4;" k="41" />
<hkern u1="&#x106;" u2="&#xf3;" k="41" />
<hkern u1="&#x106;" u2="&#xf2;" k="41" />
<hkern u1="&#x106;" u2="&#xd8;" k="82" />
<hkern u1="&#x106;" u2="&#xd6;" k="82" />
<hkern u1="&#x106;" u2="&#xd5;" k="82" />
<hkern u1="&#x106;" u2="&#xd4;" k="82" />
<hkern u1="&#x106;" u2="&#xd3;" k="82" />
<hkern u1="&#x106;" u2="&#xd2;" k="82" />
<hkern u1="&#x106;" u2="&#xc7;" k="82" />
<hkern u1="&#x106;" u2="&#xa9;" k="82" />
<hkern u1="&#x106;" u2="y" k="82" />
<hkern u1="&#x106;" u2="o" k="41" />
<hkern u1="&#x106;" u2="e" k="41" />
<hkern u1="&#x106;" u2="d" k="41" />
<hkern u1="&#x106;" u2="Y" k="41" />
<hkern u1="&#x106;" u2="Q" k="82" />
<hkern u1="&#x106;" u2="O" k="82" />
<hkern u1="&#x106;" u2="G" k="82" />
<hkern u1="&#x107;" u2="&#xf6;" k="61" />
<hkern u1="&#x107;" u2="&#xf5;" k="61" />
<hkern u1="&#x107;" u2="&#xf4;" k="61" />
<hkern u1="&#x107;" u2="&#xf3;" k="61" />
<hkern u1="&#x107;" u2="&#xf2;" k="61" />
<hkern u1="&#x107;" u2="&#xeb;" k="61" />
<hkern u1="&#x107;" u2="&#xea;" k="61" />
<hkern u1="&#x107;" u2="&#xe9;" k="61" />
<hkern u1="&#x107;" u2="&#xe8;" k="61" />
<hkern u1="&#x107;" u2="q" k="61" />
<hkern u1="&#x107;" u2="o" k="61" />
<hkern u1="&#x107;" u2="e" k="61" />
<hkern u1="&#x107;" u2="c" k="41" />
<hkern u1="&#x108;" u2="&#x152;" k="82" />
<hkern u1="&#x108;" u2="&#xf6;" k="41" />
<hkern u1="&#x108;" u2="&#xf5;" k="41" />
<hkern u1="&#x108;" u2="&#xf4;" k="41" />
<hkern u1="&#x108;" u2="&#xf3;" k="41" />
<hkern u1="&#x108;" u2="&#xf2;" k="41" />
<hkern u1="&#x108;" u2="&#xd8;" k="82" />
<hkern u1="&#x108;" u2="&#xd6;" k="82" />
<hkern u1="&#x108;" u2="&#xd5;" k="82" />
<hkern u1="&#x108;" u2="&#xd4;" k="82" />
<hkern u1="&#x108;" u2="&#xd3;" k="82" />
<hkern u1="&#x108;" u2="&#xd2;" k="82" />
<hkern u1="&#x108;" u2="&#xc7;" k="82" />
<hkern u1="&#x108;" u2="&#xa9;" k="82" />
<hkern u1="&#x108;" u2="y" k="82" />
<hkern u1="&#x108;" u2="o" k="41" />
<hkern u1="&#x108;" u2="e" k="41" />
<hkern u1="&#x108;" u2="d" k="41" />
<hkern u1="&#x108;" u2="Y" k="41" />
<hkern u1="&#x108;" u2="Q" k="82" />
<hkern u1="&#x108;" u2="O" k="82" />
<hkern u1="&#x108;" u2="G" k="82" />
<hkern u1="&#x109;" u2="&#xf6;" k="61" />
<hkern u1="&#x109;" u2="&#xf5;" k="61" />
<hkern u1="&#x109;" u2="&#xf4;" k="61" />
<hkern u1="&#x109;" u2="&#xf3;" k="61" />
<hkern u1="&#x109;" u2="&#xf2;" k="61" />
<hkern u1="&#x109;" u2="&#xeb;" k="61" />
<hkern u1="&#x109;" u2="&#xea;" k="61" />
<hkern u1="&#x109;" u2="&#xe9;" k="61" />
<hkern u1="&#x109;" u2="&#xe8;" k="61" />
<hkern u1="&#x109;" u2="q" k="61" />
<hkern u1="&#x109;" u2="o" k="61" />
<hkern u1="&#x109;" u2="e" k="61" />
<hkern u1="&#x109;" u2="c" k="41" />
<hkern u1="&#x10a;" u2="&#x152;" k="82" />
<hkern u1="&#x10a;" u2="&#xf6;" k="41" />
<hkern u1="&#x10a;" u2="&#xf5;" k="41" />
<hkern u1="&#x10a;" u2="&#xf4;" k="41" />
<hkern u1="&#x10a;" u2="&#xf3;" k="41" />
<hkern u1="&#x10a;" u2="&#xf2;" k="41" />
<hkern u1="&#x10a;" u2="&#xd8;" k="82" />
<hkern u1="&#x10a;" u2="&#xd6;" k="82" />
<hkern u1="&#x10a;" u2="&#xd5;" k="82" />
<hkern u1="&#x10a;" u2="&#xd4;" k="82" />
<hkern u1="&#x10a;" u2="&#xd3;" k="82" />
<hkern u1="&#x10a;" u2="&#xd2;" k="82" />
<hkern u1="&#x10a;" u2="&#xc7;" k="82" />
<hkern u1="&#x10a;" u2="&#xa9;" k="82" />
<hkern u1="&#x10a;" u2="y" k="82" />
<hkern u1="&#x10a;" u2="o" k="41" />
<hkern u1="&#x10a;" u2="e" k="41" />
<hkern u1="&#x10a;" u2="d" k="41" />
<hkern u1="&#x10a;" u2="Y" k="41" />
<hkern u1="&#x10a;" u2="Q" k="82" />
<hkern u1="&#x10a;" u2="O" k="82" />
<hkern u1="&#x10a;" u2="G" k="82" />
<hkern u1="&#x10b;" u2="&#xf6;" k="61" />
<hkern u1="&#x10b;" u2="&#xf5;" k="61" />
<hkern u1="&#x10b;" u2="&#xf4;" k="61" />
<hkern u1="&#x10b;" u2="&#xf3;" k="61" />
<hkern u1="&#x10b;" u2="&#xf2;" k="61" />
<hkern u1="&#x10b;" u2="&#xeb;" k="61" />
<hkern u1="&#x10b;" u2="&#xea;" k="61" />
<hkern u1="&#x10b;" u2="&#xe9;" k="61" />
<hkern u1="&#x10b;" u2="&#xe8;" k="61" />
<hkern u1="&#x10b;" u2="q" k="61" />
<hkern u1="&#x10b;" u2="o" k="61" />
<hkern u1="&#x10b;" u2="e" k="61" />
<hkern u1="&#x10b;" u2="c" k="41" />
<hkern u1="&#x10c;" u2="&#x152;" k="82" />
<hkern u1="&#x10c;" u2="&#xf6;" k="41" />
<hkern u1="&#x10c;" u2="&#xf5;" k="41" />
<hkern u1="&#x10c;" u2="&#xf4;" k="41" />
<hkern u1="&#x10c;" u2="&#xf3;" k="41" />
<hkern u1="&#x10c;" u2="&#xf2;" k="41" />
<hkern u1="&#x10c;" u2="&#xd8;" k="82" />
<hkern u1="&#x10c;" u2="&#xd6;" k="82" />
<hkern u1="&#x10c;" u2="&#xd5;" k="82" />
<hkern u1="&#x10c;" u2="&#xd4;" k="82" />
<hkern u1="&#x10c;" u2="&#xd3;" k="82" />
<hkern u1="&#x10c;" u2="&#xd2;" k="82" />
<hkern u1="&#x10c;" u2="&#xc7;" k="82" />
<hkern u1="&#x10c;" u2="&#xa9;" k="82" />
<hkern u1="&#x10c;" u2="y" k="82" />
<hkern u1="&#x10c;" u2="o" k="41" />
<hkern u1="&#x10c;" u2="e" k="41" />
<hkern u1="&#x10c;" u2="d" k="41" />
<hkern u1="&#x10c;" u2="Y" k="41" />
<hkern u1="&#x10c;" u2="Q" k="82" />
<hkern u1="&#x10c;" u2="O" k="82" />
<hkern u1="&#x10c;" u2="G" k="82" />
<hkern u1="&#x10d;" u2="&#xf6;" k="61" />
<hkern u1="&#x10d;" u2="&#xf5;" k="61" />
<hkern u1="&#x10d;" u2="&#xf4;" k="61" />
<hkern u1="&#x10d;" u2="&#xf3;" k="61" />
<hkern u1="&#x10d;" u2="&#xf2;" k="61" />
<hkern u1="&#x10d;" u2="&#xeb;" k="61" />
<hkern u1="&#x10d;" u2="&#xea;" k="61" />
<hkern u1="&#x10d;" u2="&#xe9;" k="61" />
<hkern u1="&#x10d;" u2="&#xe8;" k="61" />
<hkern u1="&#x10d;" u2="q" k="61" />
<hkern u1="&#x10d;" u2="o" k="61" />
<hkern u1="&#x10d;" u2="e" k="61" />
<hkern u1="&#x10d;" u2="c" k="41" />
<hkern u1="&#x112;" u2="&#x2122;" k="-41" />
<hkern u1="&#x113;" u2="y" k="82" />
<hkern u1="&#x113;" u2="x" k="102" />
<hkern u1="&#x113;" u2="v" k="72" />
<hkern u1="&#x113;" u2="&#x2e;" k="-20" />
<hkern u1="&#x113;" u2="&#x2a;" k="20" />
<hkern u1="&#x114;" u2="&#x2122;" k="-41" />
<hkern u1="&#x115;" u2="y" k="82" />
<hkern u1="&#x115;" u2="x" k="102" />
<hkern u1="&#x115;" u2="v" k="72" />
<hkern u1="&#x115;" u2="&#x2e;" k="-20" />
<hkern u1="&#x115;" u2="&#x2a;" k="20" />
<hkern u1="&#x116;" u2="&#x2122;" k="-41" />
<hkern u1="&#x117;" u2="y" k="82" />
<hkern u1="&#x117;" u2="x" k="102" />
<hkern u1="&#x117;" u2="v" k="72" />
<hkern u1="&#x117;" u2="&#x2e;" k="-20" />
<hkern u1="&#x117;" u2="&#x2a;" k="20" />
<hkern u1="&#x118;" u2="&#x2122;" k="-41" />
<hkern u1="&#x119;" u2="y" k="82" />
<hkern u1="&#x119;" u2="x" k="102" />
<hkern u1="&#x119;" u2="v" k="72" />
<hkern u1="&#x119;" u2="&#x2e;" k="-20" />
<hkern u1="&#x119;" u2="&#x2a;" k="20" />
<hkern u1="&#x11a;" u2="&#x2122;" k="-41" />
<hkern u1="&#x11b;" u2="y" k="82" />
<hkern u1="&#x11b;" u2="x" k="102" />
<hkern u1="&#x11b;" u2="v" k="72" />
<hkern u1="&#x11b;" u2="&#x2e;" k="-20" />
<hkern u1="&#x11b;" u2="&#x2a;" k="20" />
<hkern u1="&#x11c;" u2="y" k="41" />
<hkern u1="&#x11c;" u2="Y" k="143" />
<hkern u1="&#x11c;" u2="G" k="-10" />
<hkern u1="&#x11e;" u2="y" k="41" />
<hkern u1="&#x11e;" u2="Y" k="143" />
<hkern u1="&#x11e;" u2="G" k="-10" />
<hkern u1="&#x120;" u2="y" k="41" />
<hkern u1="&#x120;" u2="Y" k="143" />
<hkern u1="&#x120;" u2="G" k="-10" />
<hkern u1="&#x122;" u2="y" k="41" />
<hkern u1="&#x122;" u2="Y" k="143" />
<hkern u1="&#x122;" u2="G" k="-10" />
<hkern u1="&#x124;" u2="&#x2044;" k="-41" />
<hkern u1="&#x124;" u2="&#x32;" k="-41" />
<hkern u1="&#x124;" u2="&#x31;" k="-31" />
<hkern u1="&#x124;" u2="&#x2e;" k="10" />
<hkern u1="&#x125;" u2="y" k="61" />
<hkern u1="&#x125;" u2="v" k="61" />
<hkern u1="&#x125;" u2="&#x2a;" k="20" />
<hkern u1="&#x127;" u2="y" k="61" />
<hkern u1="&#x127;" u2="v" k="61" />
<hkern u1="&#x127;" u2="&#x2a;" k="20" />
<hkern u1="&#x128;" u2="&#x2044;" k="-41" />
<hkern u1="&#x128;" u2="&#x32;" k="-41" />
<hkern u1="&#x128;" u2="&#x31;" k="-31" />
<hkern u1="&#x128;" u2="&#x2e;" k="10" />
<hkern u1="&#x129;" u2="&#xae;" k="-61" />
<hkern u1="&#x12b;" u2="&#xae;" k="-61" />
<hkern u1="&#x12c;" u2="&#x2044;" k="-41" />
<hkern u1="&#x12c;" u2="&#x32;" k="-41" />
<hkern u1="&#x12c;" u2="&#x31;" k="-31" />
<hkern u1="&#x12c;" u2="&#x2e;" k="10" />
<hkern u1="&#x12d;" u2="&#xae;" k="-61" />
<hkern u1="&#x12e;" u2="&#x2044;" k="-41" />
<hkern u1="&#x12e;" u2="&#x32;" k="-41" />
<hkern u1="&#x12e;" u2="&#x31;" k="-31" />
<hkern u1="&#x12e;" u2="&#x2e;" k="10" />
<hkern u1="&#x12f;" u2="&#xae;" k="-61" />
<hkern u1="&#x130;" u2="&#x2044;" k="-41" />
<hkern u1="&#x130;" u2="&#x32;" k="-41" />
<hkern u1="&#x130;" u2="&#x31;" k="-31" />
<hkern u1="&#x130;" u2="&#x2e;" k="10" />
<hkern u1="&#x132;" u2="&#x2e;" k="61" />
<hkern u1="&#x133;" u2="&#xae;" k="-41" />
<hkern u1="&#x134;" u2="&#x2e;" k="61" />
<hkern u1="&#x135;" u2="&#xae;" k="-41" />
<hkern u1="&#x136;" u2="&#x2122;" k="-41" />
<hkern u1="&#x136;" u2="&#x2014;" k="164" />
<hkern u1="&#x136;" u2="&#x2013;" k="164" />
<hkern u1="&#x136;" u2="&#x152;" k="164" />
<hkern u1="&#x136;" u2="&#xfc;" k="72" />
<hkern u1="&#x136;" u2="&#xfb;" k="72" />
<hkern u1="&#x136;" u2="&#xfa;" k="72" />
<hkern u1="&#x136;" u2="&#xf9;" k="72" />
<hkern u1="&#x136;" u2="&#xf6;" k="123" />
<hkern u1="&#x136;" u2="&#xf5;" k="123" />
<hkern u1="&#x136;" u2="&#xf4;" k="123" />
<hkern u1="&#x136;" u2="&#xf3;" k="123" />
<hkern u1="&#x136;" u2="&#xf2;" k="123" />
<hkern u1="&#x136;" u2="&#xeb;" k="123" />
<hkern u1="&#x136;" u2="&#xea;" k="123" />
<hkern u1="&#x136;" u2="&#xe9;" k="123" />
<hkern u1="&#x136;" u2="&#xe8;" k="123" />
<hkern u1="&#x136;" u2="&#xe6;" k="82" />
<hkern u1="&#x136;" u2="&#xe5;" k="82" />
<hkern u1="&#x136;" u2="&#xe4;" k="82" />
<hkern u1="&#x136;" u2="&#xe3;" k="82" />
<hkern u1="&#x136;" u2="&#xe2;" k="82" />
<hkern u1="&#x136;" u2="&#xe1;" k="82" />
<hkern u1="&#x136;" u2="&#xe0;" k="82" />
<hkern u1="&#x136;" u2="&#xd8;" k="164" />
<hkern u1="&#x136;" u2="&#xd6;" k="164" />
<hkern u1="&#x136;" u2="&#xd5;" k="164" />
<hkern u1="&#x136;" u2="&#xd4;" k="164" />
<hkern u1="&#x136;" u2="&#xd3;" k="164" />
<hkern u1="&#x136;" u2="&#xd2;" k="164" />
<hkern u1="&#x136;" u2="&#xc7;" k="164" />
<hkern u1="&#x136;" u2="&#xa9;" k="164" />
<hkern u1="&#x136;" u2="y" k="154" />
<hkern u1="&#x136;" u2="o" k="123" />
<hkern u1="&#x136;" u2="e" k="123" />
<hkern u1="&#x136;" u2="S" k="102" />
<hkern u1="&#x136;" u2="Q" k="164" />
<hkern u1="&#x136;" u2="O" k="164" />
<hkern u1="&#x136;" u2="G" k="164" />
<hkern u1="&#x137;" u2="&#x2014;" k="123" />
<hkern u1="&#x137;" u2="&#x2013;" k="123" />
<hkern u1="&#x137;" u2="&#xf6;" k="143" />
<hkern u1="&#x137;" u2="&#xf5;" k="143" />
<hkern u1="&#x137;" u2="&#xf4;" k="143" />
<hkern u1="&#x137;" u2="&#xf3;" k="143" />
<hkern u1="&#x137;" u2="&#xf2;" k="143" />
<hkern u1="&#x137;" u2="&#xeb;" k="143" />
<hkern u1="&#x137;" u2="&#xea;" k="143" />
<hkern u1="&#x137;" u2="&#xe9;" k="143" />
<hkern u1="&#x137;" u2="&#xe8;" k="143" />
<hkern u1="&#x137;" u2="&#xe6;" k="41" />
<hkern u1="&#x137;" u2="&#xe5;" k="41" />
<hkern u1="&#x137;" u2="&#xe4;" k="41" />
<hkern u1="&#x137;" u2="&#xe3;" k="41" />
<hkern u1="&#x137;" u2="&#xe2;" k="41" />
<hkern u1="&#x137;" u2="&#xe1;" k="41" />
<hkern u1="&#x137;" u2="&#xe0;" k="41" />
<hkern u1="&#x137;" u2="&#xae;" k="-61" />
<hkern u1="&#x137;" u2="s" k="51" />
<hkern u1="&#x137;" u2="o" k="143" />
<hkern u1="&#x137;" u2="g" k="143" />
<hkern u1="&#x137;" u2="e" k="143" />
<hkern u1="&#x137;" u2="c" k="143" />
<hkern u1="&#x137;" u2="&#x2e;" k="-20" />
<hkern u1="&#x143;" u2="&#x2044;" k="-41" />
<hkern u1="&#x143;" u2="&#x32;" k="-41" />
<hkern u1="&#x143;" u2="&#x31;" k="-31" />
<hkern u1="&#x143;" u2="&#x2e;" k="10" />
<hkern u1="&#x144;" u2="y" k="61" />
<hkern u1="&#x144;" u2="v" k="61" />
<hkern u1="&#x144;" u2="&#x2a;" k="20" />
<hkern u1="&#x145;" u2="&#x2044;" k="-41" />
<hkern u1="&#x145;" u2="&#x32;" k="-41" />
<hkern u1="&#x145;" u2="&#x31;" k="-31" />
<hkern u1="&#x145;" u2="&#x2e;" k="10" />
<hkern u1="&#x146;" u2="y" k="61" />
<hkern u1="&#x146;" u2="v" k="61" />
<hkern u1="&#x146;" u2="&#x2a;" k="20" />
<hkern u1="&#x147;" u2="&#x2044;" k="-41" />
<hkern u1="&#x147;" u2="&#x32;" k="-41" />
<hkern u1="&#x147;" u2="&#x31;" k="-31" />
<hkern u1="&#x147;" u2="&#x2e;" k="10" />
<hkern u1="&#x148;" u2="y" k="61" />
<hkern u1="&#x148;" u2="v" k="61" />
<hkern u1="&#x148;" u2="&#x2a;" k="20" />
<hkern u1="&#x149;" u2="y" k="61" />
<hkern u1="&#x149;" u2="v" k="61" />
<hkern u1="&#x149;" u2="&#x2a;" k="20" />
<hkern u1="&#x14a;" u2="&#x2044;" k="-41" />
<hkern u1="&#x14a;" u2="&#x32;" k="-41" />
<hkern u1="&#x14a;" u2="&#x31;" k="-31" />
<hkern u1="&#x14a;" u2="&#x2e;" k="10" />
<hkern u1="&#x14b;" u2="y" k="61" />
<hkern u1="&#x14b;" u2="v" k="61" />
<hkern u1="&#x14b;" u2="&#x2a;" k="20" />
<hkern u1="&#x152;" u2="&#x2122;" k="-41" />
<hkern u1="&#x153;" u2="&#x175;" k="61" />
<hkern u1="&#x153;" u2="y" k="82" />
<hkern u1="&#x153;" u2="x" k="102" />
<hkern u1="&#x153;" u2="w" k="61" />
<hkern u1="&#x153;" u2="v" k="72" />
<hkern u1="&#x153;" u2="&#x2e;" k="-20" />
<hkern u1="&#x153;" u2="&#x2a;" k="20" />
<hkern u1="&#x154;" u2="&#x152;" k="41" />
<hkern u1="&#x154;" u2="&#xf6;" k="41" />
<hkern u1="&#x154;" u2="&#xf5;" k="41" />
<hkern u1="&#x154;" u2="&#xf4;" k="41" />
<hkern u1="&#x154;" u2="&#xf3;" k="41" />
<hkern u1="&#x154;" u2="&#xf2;" k="41" />
<hkern u1="&#x154;" u2="&#xeb;" k="41" />
<hkern u1="&#x154;" u2="&#xea;" k="41" />
<hkern u1="&#x154;" u2="&#xe9;" k="41" />
<hkern u1="&#x154;" u2="&#xe8;" k="41" />
<hkern u1="&#x154;" u2="&#xe6;" k="31" />
<hkern u1="&#x154;" u2="&#xe5;" k="31" />
<hkern u1="&#x154;" u2="&#xe4;" k="31" />
<hkern u1="&#x154;" u2="&#xe3;" k="31" />
<hkern u1="&#x154;" u2="&#xe2;" k="31" />
<hkern u1="&#x154;" u2="&#xe1;" k="31" />
<hkern u1="&#x154;" u2="&#xe0;" k="31" />
<hkern u1="&#x154;" u2="&#xd8;" k="41" />
<hkern u1="&#x154;" u2="&#xd6;" k="41" />
<hkern u1="&#x154;" u2="&#xd5;" k="41" />
<hkern u1="&#x154;" u2="&#xd4;" k="41" />
<hkern u1="&#x154;" u2="&#xd3;" k="41" />
<hkern u1="&#x154;" u2="&#xd2;" k="41" />
<hkern u1="&#x154;" u2="&#xc7;" k="41" />
<hkern u1="&#x154;" u2="&#xa9;" k="41" />
<hkern u1="&#x154;" u2="y" k="31" />
<hkern u1="&#x154;" u2="o" k="61" />
<hkern u1="&#x154;" u2="e" k="61" />
<hkern u1="&#x154;" u2="Y" k="143" />
<hkern u1="&#x154;" u2="V" k="102" />
<hkern u1="&#x154;" u2="Q" k="41" />
<hkern u1="&#x154;" u2="O" k="41" />
<hkern u1="&#x154;" u2="G" k="41" />
<hkern u1="&#x155;" g2="ellipsis" k="82" />
<hkern u1="&#x155;" u2="&#xf6;" k="20" />
<hkern u1="&#x155;" u2="&#xf5;" k="20" />
<hkern u1="&#x155;" u2="&#xf4;" k="20" />
<hkern u1="&#x155;" u2="&#xf3;" k="20" />
<hkern u1="&#x155;" u2="&#xf2;" k="20" />
<hkern u1="&#x155;" u2="&#xeb;" k="20" />
<hkern u1="&#x155;" u2="&#xea;" k="20" />
<hkern u1="&#x155;" u2="&#xe9;" k="20" />
<hkern u1="&#x155;" u2="&#xe8;" k="20" />
<hkern u1="&#x155;" u2="&#xe6;" k="31" />
<hkern u1="&#x155;" u2="&#xe5;" k="31" />
<hkern u1="&#x155;" u2="&#xe4;" k="31" />
<hkern u1="&#x155;" u2="&#xe3;" k="31" />
<hkern u1="&#x155;" u2="&#xe2;" k="31" />
<hkern u1="&#x155;" u2="&#xe1;" k="31" />
<hkern u1="&#x155;" u2="&#xe0;" k="31" />
<hkern u1="&#x155;" u2="&#xae;" k="-143" />
<hkern u1="&#x155;" u2="z" k="-20" />
<hkern u1="&#x155;" u2="v" k="-10" />
<hkern u1="&#x155;" u2="o" k="20" />
<hkern u1="&#x155;" u2="e" k="20" />
<hkern u1="&#x155;" u2="c" k="20" />
<hkern u1="&#x155;" u2="&#x2e;" k="82" />
<hkern u1="&#x156;" u2="&#x152;" k="41" />
<hkern u1="&#x156;" u2="&#xf6;" k="41" />
<hkern u1="&#x156;" u2="&#xf5;" k="41" />
<hkern u1="&#x156;" u2="&#xf4;" k="41" />
<hkern u1="&#x156;" u2="&#xf3;" k="41" />
<hkern u1="&#x156;" u2="&#xf2;" k="41" />
<hkern u1="&#x156;" u2="&#xeb;" k="41" />
<hkern u1="&#x156;" u2="&#xea;" k="41" />
<hkern u1="&#x156;" u2="&#xe9;" k="41" />
<hkern u1="&#x156;" u2="&#xe8;" k="41" />
<hkern u1="&#x156;" u2="&#xe6;" k="31" />
<hkern u1="&#x156;" u2="&#xe5;" k="31" />
<hkern u1="&#x156;" u2="&#xe4;" k="31" />
<hkern u1="&#x156;" u2="&#xe3;" k="31" />
<hkern u1="&#x156;" u2="&#xe2;" k="31" />
<hkern u1="&#x156;" u2="&#xe1;" k="31" />
<hkern u1="&#x156;" u2="&#xe0;" k="31" />
<hkern u1="&#x156;" u2="&#xd8;" k="41" />
<hkern u1="&#x156;" u2="&#xd6;" k="41" />
<hkern u1="&#x156;" u2="&#xd5;" k="41" />
<hkern u1="&#x156;" u2="&#xd4;" k="41" />
<hkern u1="&#x156;" u2="&#xd3;" k="41" />
<hkern u1="&#x156;" u2="&#xd2;" k="41" />
<hkern u1="&#x156;" u2="&#xc7;" k="41" />
<hkern u1="&#x156;" u2="&#xa9;" k="41" />
<hkern u1="&#x156;" u2="y" k="31" />
<hkern u1="&#x156;" u2="o" k="61" />
<hkern u1="&#x156;" u2="e" k="61" />
<hkern u1="&#x156;" u2="Y" k="143" />
<hkern u1="&#x156;" u2="V" k="102" />
<hkern u1="&#x156;" u2="Q" k="41" />
<hkern u1="&#x156;" u2="O" k="41" />
<hkern u1="&#x156;" u2="G" k="41" />
<hkern u1="&#x157;" g2="ellipsis" k="82" />
<hkern u1="&#x157;" u2="&#xf6;" k="20" />
<hkern u1="&#x157;" u2="&#xf5;" k="20" />
<hkern u1="&#x157;" u2="&#xf4;" k="20" />
<hkern u1="&#x157;" u2="&#xf3;" k="20" />
<hkern u1="&#x157;" u2="&#xf2;" k="20" />
<hkern u1="&#x157;" u2="&#xeb;" k="20" />
<hkern u1="&#x157;" u2="&#xea;" k="20" />
<hkern u1="&#x157;" u2="&#xe9;" k="20" />
<hkern u1="&#x157;" u2="&#xe8;" k="20" />
<hkern u1="&#x157;" u2="&#xe6;" k="31" />
<hkern u1="&#x157;" u2="&#xe5;" k="31" />
<hkern u1="&#x157;" u2="&#xe4;" k="31" />
<hkern u1="&#x157;" u2="&#xe3;" k="31" />
<hkern u1="&#x157;" u2="&#xe2;" k="31" />
<hkern u1="&#x157;" u2="&#xe1;" k="31" />
<hkern u1="&#x157;" u2="&#xe0;" k="31" />
<hkern u1="&#x157;" u2="&#xae;" k="-143" />
<hkern u1="&#x157;" u2="z" k="-20" />
<hkern u1="&#x157;" u2="v" k="-10" />
<hkern u1="&#x157;" u2="o" k="20" />
<hkern u1="&#x157;" u2="e" k="20" />
<hkern u1="&#x157;" u2="c" k="20" />
<hkern u1="&#x157;" u2="&#x2e;" k="82" />
<hkern u1="&#x158;" u2="&#x152;" k="41" />
<hkern u1="&#x158;" u2="&#xf6;" k="41" />
<hkern u1="&#x158;" u2="&#xf5;" k="41" />
<hkern u1="&#x158;" u2="&#xf4;" k="41" />
<hkern u1="&#x158;" u2="&#xf3;" k="41" />
<hkern u1="&#x158;" u2="&#xf2;" k="41" />
<hkern u1="&#x158;" u2="&#xeb;" k="41" />
<hkern u1="&#x158;" u2="&#xea;" k="41" />
<hkern u1="&#x158;" u2="&#xe9;" k="41" />
<hkern u1="&#x158;" u2="&#xe8;" k="41" />
<hkern u1="&#x158;" u2="&#xe6;" k="31" />
<hkern u1="&#x158;" u2="&#xe5;" k="31" />
<hkern u1="&#x158;" u2="&#xe4;" k="31" />
<hkern u1="&#x158;" u2="&#xe3;" k="31" />
<hkern u1="&#x158;" u2="&#xe2;" k="31" />
<hkern u1="&#x158;" u2="&#xe1;" k="31" />
<hkern u1="&#x158;" u2="&#xe0;" k="31" />
<hkern u1="&#x158;" u2="&#xd8;" k="41" />
<hkern u1="&#x158;" u2="&#xd6;" k="41" />
<hkern u1="&#x158;" u2="&#xd5;" k="41" />
<hkern u1="&#x158;" u2="&#xd4;" k="41" />
<hkern u1="&#x158;" u2="&#xd3;" k="41" />
<hkern u1="&#x158;" u2="&#xd2;" k="41" />
<hkern u1="&#x158;" u2="&#xc7;" k="41" />
<hkern u1="&#x158;" u2="&#xa9;" k="41" />
<hkern u1="&#x158;" u2="y" k="31" />
<hkern u1="&#x158;" u2="o" k="61" />
<hkern u1="&#x158;" u2="e" k="61" />
<hkern u1="&#x158;" u2="Y" k="143" />
<hkern u1="&#x158;" u2="V" k="102" />
<hkern u1="&#x158;" u2="Q" k="41" />
<hkern u1="&#x158;" u2="O" k="41" />
<hkern u1="&#x158;" u2="G" k="41" />
<hkern u1="&#x159;" g2="ellipsis" k="82" />
<hkern u1="&#x159;" u2="&#xf6;" k="20" />
<hkern u1="&#x159;" u2="&#xf5;" k="20" />
<hkern u1="&#x159;" u2="&#xf4;" k="20" />
<hkern u1="&#x159;" u2="&#xf3;" k="20" />
<hkern u1="&#x159;" u2="&#xf2;" k="20" />
<hkern u1="&#x159;" u2="&#xeb;" k="20" />
<hkern u1="&#x159;" u2="&#xea;" k="20" />
<hkern u1="&#x159;" u2="&#xe9;" k="20" />
<hkern u1="&#x159;" u2="&#xe8;" k="20" />
<hkern u1="&#x159;" u2="&#xe6;" k="31" />
<hkern u1="&#x159;" u2="&#xe5;" k="31" />
<hkern u1="&#x159;" u2="&#xe4;" k="31" />
<hkern u1="&#x159;" u2="&#xe3;" k="31" />
<hkern u1="&#x159;" u2="&#xe2;" k="31" />
<hkern u1="&#x159;" u2="&#xe1;" k="31" />
<hkern u1="&#x159;" u2="&#xe0;" k="31" />
<hkern u1="&#x159;" u2="&#xae;" k="-143" />
<hkern u1="&#x159;" u2="z" k="-20" />
<hkern u1="&#x159;" u2="v" k="-10" />
<hkern u1="&#x159;" u2="o" k="20" />
<hkern u1="&#x159;" u2="e" k="20" />
<hkern u1="&#x159;" u2="c" k="20" />
<hkern u1="&#x159;" u2="&#x2e;" k="82" />
<hkern u1="&#x162;" u2="&#x2122;" k="-61" />
<hkern u1="&#x162;" g2="ellipsis" k="123" />
<hkern u1="&#x162;" u2="&#x2014;" k="82" />
<hkern u1="&#x162;" u2="&#x2013;" k="82" />
<hkern u1="&#x162;" u2="&#xf6;" k="102" />
<hkern u1="&#x162;" u2="&#xf5;" k="102" />
<hkern u1="&#x162;" u2="&#xf4;" k="102" />
<hkern u1="&#x162;" u2="&#xf3;" k="102" />
<hkern u1="&#x162;" u2="&#xf2;" k="102" />
<hkern u1="&#x162;" u2="&#xeb;" k="102" />
<hkern u1="&#x162;" u2="&#xea;" k="102" />
<hkern u1="&#x162;" u2="&#xe9;" k="102" />
<hkern u1="&#x162;" u2="&#xe8;" k="102" />
<hkern u1="&#x162;" u2="&#xc5;" k="143" />
<hkern u1="&#x162;" u2="&#xc4;" k="143" />
<hkern u1="&#x162;" u2="&#xc3;" k="143" />
<hkern u1="&#x162;" u2="&#xc2;" k="143" />
<hkern u1="&#x162;" u2="&#xc1;" k="143" />
<hkern u1="&#x162;" u2="&#xc0;" k="143" />
<hkern u1="&#x162;" u2="x" k="61" />
<hkern u1="&#x162;" u2="s" k="82" />
<hkern u1="&#x162;" u2="o" k="102" />
<hkern u1="&#x162;" u2="e" k="102" />
<hkern u1="&#x162;" u2="d" k="92" />
<hkern u1="&#x162;" u2="Y" k="-31" />
<hkern u1="&#x162;" u2="V" k="-10" />
<hkern u1="&#x162;" u2="Q" k="20" />
<hkern u1="&#x162;" u2="O" k="20" />
<hkern u1="&#x162;" u2="&#x2e;" k="102" />
<hkern u1="&#x163;" u2="&#x2014;" k="61" />
<hkern u1="&#x163;" u2="&#x2013;" k="61" />
<hkern u1="&#x163;" u2="&#xae;" k="-82" />
<hkern u1="&#x163;" u2="z" k="-10" />
<hkern u1="&#x163;" u2="y" k="-10" />
<hkern u1="&#x164;" u2="&#x2122;" k="-61" />
<hkern u1="&#x164;" g2="ellipsis" k="123" />
<hkern u1="&#x164;" u2="&#x2014;" k="82" />
<hkern u1="&#x164;" u2="&#x2013;" k="82" />
<hkern u1="&#x164;" u2="&#xf6;" k="102" />
<hkern u1="&#x164;" u2="&#xf5;" k="102" />
<hkern u1="&#x164;" u2="&#xf4;" k="102" />
<hkern u1="&#x164;" u2="&#xf3;" k="102" />
<hkern u1="&#x164;" u2="&#xf2;" k="102" />
<hkern u1="&#x164;" u2="&#xeb;" k="102" />
<hkern u1="&#x164;" u2="&#xea;" k="102" />
<hkern u1="&#x164;" u2="&#xe9;" k="102" />
<hkern u1="&#x164;" u2="&#xe8;" k="102" />
<hkern u1="&#x164;" u2="&#xc5;" k="143" />
<hkern u1="&#x164;" u2="&#xc4;" k="143" />
<hkern u1="&#x164;" u2="&#xc3;" k="143" />
<hkern u1="&#x164;" u2="&#xc2;" k="143" />
<hkern u1="&#x164;" u2="&#xc1;" k="143" />
<hkern u1="&#x164;" u2="&#xc0;" k="143" />
<hkern u1="&#x164;" u2="x" k="61" />
<hkern u1="&#x164;" u2="s" k="82" />
<hkern u1="&#x164;" u2="o" k="102" />
<hkern u1="&#x164;" u2="e" k="102" />
<hkern u1="&#x164;" u2="d" k="92" />
<hkern u1="&#x164;" u2="Y" k="-31" />
<hkern u1="&#x164;" u2="V" k="-10" />
<hkern u1="&#x164;" u2="Q" k="20" />
<hkern u1="&#x164;" u2="O" k="20" />
<hkern u1="&#x164;" u2="&#x2e;" k="102" />
<hkern u1="&#x165;" u2="&#x2014;" k="61" />
<hkern u1="&#x165;" u2="&#x2013;" k="61" />
<hkern u1="&#x165;" u2="&#xae;" k="-82" />
<hkern u1="&#x165;" u2="z" k="-10" />
<hkern u1="&#x165;" u2="y" k="-10" />
<hkern u1="&#x166;" u2="&#x2122;" k="-61" />
<hkern u1="&#x166;" g2="ellipsis" k="123" />
<hkern u1="&#x166;" u2="&#x2014;" k="82" />
<hkern u1="&#x166;" u2="&#x2013;" k="82" />
<hkern u1="&#x166;" u2="&#xf6;" k="102" />
<hkern u1="&#x166;" u2="&#xf5;" k="102" />
<hkern u1="&#x166;" u2="&#xf4;" k="102" />
<hkern u1="&#x166;" u2="&#xf3;" k="102" />
<hkern u1="&#x166;" u2="&#xf2;" k="102" />
<hkern u1="&#x166;" u2="&#xeb;" k="102" />
<hkern u1="&#x166;" u2="&#xea;" k="102" />
<hkern u1="&#x166;" u2="&#xe9;" k="102" />
<hkern u1="&#x166;" u2="&#xe8;" k="102" />
<hkern u1="&#x166;" u2="&#xc5;" k="143" />
<hkern u1="&#x166;" u2="&#xc4;" k="143" />
<hkern u1="&#x166;" u2="&#xc3;" k="143" />
<hkern u1="&#x166;" u2="&#xc2;" k="143" />
<hkern u1="&#x166;" u2="&#xc1;" k="143" />
<hkern u1="&#x166;" u2="&#xc0;" k="143" />
<hkern u1="&#x166;" u2="x" k="61" />
<hkern u1="&#x166;" u2="s" k="82" />
<hkern u1="&#x166;" u2="o" k="102" />
<hkern u1="&#x166;" u2="e" k="102" />
<hkern u1="&#x166;" u2="d" k="92" />
<hkern u1="&#x166;" u2="Y" k="-31" />
<hkern u1="&#x166;" u2="V" k="-10" />
<hkern u1="&#x166;" u2="Q" k="20" />
<hkern u1="&#x166;" u2="O" k="20" />
<hkern u1="&#x166;" u2="&#x2e;" k="102" />
<hkern u1="&#x167;" u2="&#x2014;" k="61" />
<hkern u1="&#x167;" u2="&#x2013;" k="61" />
<hkern u1="&#x167;" u2="&#xae;" k="-82" />
<hkern u1="&#x167;" u2="z" k="-10" />
<hkern u1="&#x167;" u2="y" k="-10" />
<hkern u1="&#x168;" u2="&#xc5;" k="102" />
<hkern u1="&#x168;" u2="&#xc4;" k="102" />
<hkern u1="&#x168;" u2="&#xc3;" k="102" />
<hkern u1="&#x168;" u2="&#xc2;" k="102" />
<hkern u1="&#x168;" u2="&#xc1;" k="102" />
<hkern u1="&#x168;" u2="&#xc0;" k="102" />
<hkern u1="&#x168;" u2="&#x2e;" k="41" />
<hkern u1="&#x16a;" u2="&#xc5;" k="102" />
<hkern u1="&#x16a;" u2="&#xc4;" k="102" />
<hkern u1="&#x16a;" u2="&#xc3;" k="102" />
<hkern u1="&#x16a;" u2="&#xc2;" k="102" />
<hkern u1="&#x16a;" u2="&#xc1;" k="102" />
<hkern u1="&#x16a;" u2="&#xc0;" k="102" />
<hkern u1="&#x16a;" u2="&#x2e;" k="41" />
<hkern u1="&#x16c;" u2="&#xc5;" k="102" />
<hkern u1="&#x16c;" u2="&#xc4;" k="102" />
<hkern u1="&#x16c;" u2="&#xc3;" k="102" />
<hkern u1="&#x16c;" u2="&#xc2;" k="102" />
<hkern u1="&#x16c;" u2="&#xc1;" k="102" />
<hkern u1="&#x16c;" u2="&#xc0;" k="102" />
<hkern u1="&#x16c;" u2="&#x2e;" k="41" />
<hkern u1="&#x16e;" u2="&#xc5;" k="102" />
<hkern u1="&#x16e;" u2="&#xc4;" k="102" />
<hkern u1="&#x16e;" u2="&#xc3;" k="102" />
<hkern u1="&#x16e;" u2="&#xc2;" k="102" />
<hkern u1="&#x16e;" u2="&#xc1;" k="102" />
<hkern u1="&#x16e;" u2="&#xc0;" k="102" />
<hkern u1="&#x16e;" u2="&#x2e;" k="41" />
<hkern u1="&#x170;" u2="&#xc5;" k="102" />
<hkern u1="&#x170;" u2="&#xc4;" k="102" />
<hkern u1="&#x170;" u2="&#xc3;" k="102" />
<hkern u1="&#x170;" u2="&#xc2;" k="102" />
<hkern u1="&#x170;" u2="&#xc1;" k="102" />
<hkern u1="&#x170;" u2="&#xc0;" k="102" />
<hkern u1="&#x170;" u2="&#x2e;" k="41" />
<hkern u1="&#x172;" u2="&#xc5;" k="102" />
<hkern u1="&#x172;" u2="&#xc4;" k="102" />
<hkern u1="&#x172;" u2="&#xc3;" k="102" />
<hkern u1="&#x172;" u2="&#xc2;" k="102" />
<hkern u1="&#x172;" u2="&#xc1;" k="102" />
<hkern u1="&#x172;" u2="&#xc0;" k="102" />
<hkern u1="&#x172;" u2="&#x2e;" k="41" />
<hkern u1="&#x174;" u2="&#x2122;" k="-61" />
<hkern u1="&#x174;" u2="&#x2014;" k="82" />
<hkern u1="&#x174;" u2="&#x2013;" k="82" />
<hkern u1="&#x174;" u2="&#xf6;" k="143" />
<hkern u1="&#x174;" u2="&#xf5;" k="143" />
<hkern u1="&#x174;" u2="&#xf4;" k="143" />
<hkern u1="&#x174;" u2="&#xf3;" k="143" />
<hkern u1="&#x174;" u2="&#xf2;" k="143" />
<hkern u1="&#x174;" u2="&#xeb;" k="143" />
<hkern u1="&#x174;" u2="&#xea;" k="143" />
<hkern u1="&#x174;" u2="&#xe9;" k="143" />
<hkern u1="&#x174;" u2="&#xe8;" k="143" />
<hkern u1="&#x174;" u2="&#xe6;" k="102" />
<hkern u1="&#x174;" u2="&#xe5;" k="102" />
<hkern u1="&#x174;" u2="&#xe4;" k="102" />
<hkern u1="&#x174;" u2="&#xe3;" k="102" />
<hkern u1="&#x174;" u2="&#xe2;" k="102" />
<hkern u1="&#x174;" u2="&#xe1;" k="102" />
<hkern u1="&#x174;" u2="&#xe0;" k="102" />
<hkern u1="&#x174;" u2="&#xc5;" k="287" />
<hkern u1="&#x174;" u2="&#xc4;" k="287" />
<hkern u1="&#x174;" u2="&#xc3;" k="287" />
<hkern u1="&#x174;" u2="&#xc2;" k="287" />
<hkern u1="&#x174;" u2="&#xc1;" k="287" />
<hkern u1="&#x174;" u2="&#xc0;" k="287" />
<hkern u1="&#x174;" u2="&#xba;" k="-61" />
<hkern u1="&#x174;" u2="&#xae;" k="-41" />
<hkern u1="&#x174;" u2="y" k="61" />
<hkern u1="&#x174;" u2="o" k="143" />
<hkern u1="&#x174;" u2="e" k="143" />
<hkern u1="&#x174;" u2="S" k="61" />
<hkern u1="&#x174;" u2="O" k="82" />
<hkern u1="&#x174;" u2="G" k="82" />
<hkern u1="&#x174;" u2="&#x2e;" k="123" />
<hkern u1="&#x175;" g2="ellipsis" k="102" />
<hkern u1="&#x175;" u2="&#xf6;" k="61" />
<hkern u1="&#x175;" u2="&#xf5;" k="61" />
<hkern u1="&#x175;" u2="&#xf4;" k="61" />
<hkern u1="&#x175;" u2="&#xf3;" k="61" />
<hkern u1="&#x175;" u2="&#xf2;" k="61" />
<hkern u1="&#x175;" u2="&#xeb;" k="61" />
<hkern u1="&#x175;" u2="&#xea;" k="61" />
<hkern u1="&#x175;" u2="&#xe9;" k="61" />
<hkern u1="&#x175;" u2="&#xe8;" k="61" />
<hkern u1="&#x175;" u2="&#xe6;" k="31" />
<hkern u1="&#x175;" u2="&#xe5;" k="31" />
<hkern u1="&#x175;" u2="&#xe4;" k="31" />
<hkern u1="&#x175;" u2="&#xe3;" k="31" />
<hkern u1="&#x175;" u2="&#xe2;" k="31" />
<hkern u1="&#x175;" u2="&#xe1;" k="31" />
<hkern u1="&#x175;" u2="&#xe0;" k="31" />
<hkern u1="&#x175;" u2="&#xae;" k="-123" />
<hkern u1="&#x175;" u2="y" k="-10" />
<hkern u1="&#x175;" u2="s" k="61" />
<hkern u1="&#x175;" u2="q" k="82" />
<hkern u1="&#x175;" u2="o" k="61" />
<hkern u1="&#x175;" u2="g" k="61" />
<hkern u1="&#x175;" u2="e" k="61" />
<hkern u1="&#x175;" u2="d" k="41" />
<hkern u1="&#x175;" u2="c" k="61" />
<hkern u1="&#x175;" u2="&#x2e;" k="102" />
<hkern u1="&#x2013;" u2="&#x174;" k="82" />
<hkern u1="&#x2013;" u2="&#x166;" k="102" />
<hkern u1="&#x2013;" u2="&#x164;" k="102" />
<hkern u1="&#x2013;" u2="&#x162;" k="102" />
<hkern u1="&#x2013;" u2="&#x134;" k="61" />
<hkern u1="&#x2013;" u2="&#x104;" k="102" />
<hkern u1="&#x2013;" u2="&#x102;" k="102" />
<hkern u1="&#x2013;" u2="&#x100;" k="102" />
<hkern u1="&#x2013;" u2="&#xc5;" k="102" />
<hkern u1="&#x2013;" u2="&#xc4;" k="102" />
<hkern u1="&#x2013;" u2="&#xc3;" k="102" />
<hkern u1="&#x2013;" u2="&#xc2;" k="102" />
<hkern u1="&#x2013;" u2="&#xc1;" k="102" />
<hkern u1="&#x2013;" u2="&#xc0;" k="102" />
<hkern u1="&#x2013;" u2="x" k="41" />
<hkern u1="&#x2013;" u2="Z" k="61" />
<hkern u1="&#x2013;" u2="Y" k="164" />
<hkern u1="&#x2013;" u2="X" k="143" />
<hkern u1="&#x2013;" u2="W" k="82" />
<hkern u1="&#x2013;" u2="V" k="143" />
<hkern u1="&#x2013;" u2="T" k="102" />
<hkern u1="&#x2013;" u2="J" k="61" />
<hkern u1="&#x2013;" u2="A" k="102" />
<hkern u1="&#x2013;" u2="&#x37;" k="82" />
<hkern u1="&#x2013;" u2="&#x31;" k="102" />
<hkern u1="&#x2014;" u2="&#x174;" k="82" />
<hkern u1="&#x2014;" u2="&#x166;" k="102" />
<hkern u1="&#x2014;" u2="&#x164;" k="102" />
<hkern u1="&#x2014;" u2="&#x162;" k="102" />
<hkern u1="&#x2014;" u2="&#x134;" k="61" />
<hkern u1="&#x2014;" u2="&#x104;" k="102" />
<hkern u1="&#x2014;" u2="&#x102;" k="102" />
<hkern u1="&#x2014;" u2="&#x100;" k="102" />
<hkern u1="&#x2014;" u2="&#xc5;" k="102" />
<hkern u1="&#x2014;" u2="&#xc4;" k="102" />
<hkern u1="&#x2014;" u2="&#xc3;" k="102" />
<hkern u1="&#x2014;" u2="&#xc2;" k="102" />
<hkern u1="&#x2014;" u2="&#xc1;" k="102" />
<hkern u1="&#x2014;" u2="&#xc0;" k="102" />
<hkern u1="&#x2014;" u2="x" k="41" />
<hkern u1="&#x2014;" u2="Z" k="61" />
<hkern u1="&#x2014;" u2="Y" k="164" />
<hkern u1="&#x2014;" u2="X" k="143" />
<hkern u1="&#x2014;" u2="W" k="82" />
<hkern u1="&#x2014;" u2="V" k="143" />
<hkern u1="&#x2014;" u2="T" k="102" />
<hkern u1="&#x2014;" u2="J" k="61" />
<hkern u1="&#x2014;" u2="A" k="102" />
<hkern u1="&#x2014;" u2="&#x37;" k="82" />
<hkern u1="&#x2014;" u2="&#x31;" k="102" />
<hkern u1="&#x2018;" u2="&#x104;" k="225" />
<hkern u1="&#x2018;" u2="&#x102;" k="225" />
<hkern u1="&#x2018;" u2="&#x100;" k="225" />
<hkern u1="&#x2018;" u2="&#xf6;" k="41" />
<hkern u1="&#x2018;" u2="&#xf5;" k="41" />
<hkern u1="&#x2018;" u2="&#xf4;" k="41" />
<hkern u1="&#x2018;" u2="&#xf3;" k="41" />
<hkern u1="&#x2018;" u2="&#xf2;" k="41" />
<hkern u1="&#x2018;" u2="&#xc5;" k="225" />
<hkern u1="&#x2018;" u2="&#xc4;" k="225" />
<hkern u1="&#x2018;" u2="&#xc3;" k="225" />
<hkern u1="&#x2018;" u2="&#xc2;" k="225" />
<hkern u1="&#x2018;" u2="&#xc1;" k="225" />
<hkern u1="&#x2018;" u2="&#xc0;" k="225" />
<hkern u1="&#x2018;" u2="o" k="41" />
<hkern u1="&#x2018;" u2="c" k="41" />
<hkern u1="&#x2018;" u2="A" k="225" />
<hkern u1="&#x2019;" u2="s" k="102" />
<hkern u1="&#x2019;" u2="d" k="164" />
<hkern u1="&#x2019;" u2="&#x2e;" k="266" />
<hkern u1="&#x201c;" u2="&#xf6;" k="41" />
<hkern u1="&#x201c;" u2="&#xf5;" k="41" />
<hkern u1="&#x201c;" u2="&#xf4;" k="41" />
<hkern u1="&#x201c;" u2="&#xf3;" k="41" />
<hkern u1="&#x201c;" u2="&#xf2;" k="41" />
<hkern u1="&#x201c;" u2="&#xc5;" k="225" />
<hkern u1="&#x201c;" u2="&#xc4;" k="225" />
<hkern u1="&#x201c;" u2="&#xc3;" k="225" />
<hkern u1="&#x201c;" u2="&#xc2;" k="225" />
<hkern u1="&#x201c;" u2="&#xc1;" k="225" />
<hkern u1="&#x201c;" u2="&#xc0;" k="225" />
<hkern u1="&#x201c;" u2="o" k="41" />
<hkern u1="&#x201c;" u2="c" k="41" />
<hkern u1="&#x201d;" u2="&#x2e;" k="266" />
<hkern u1="&#x2044;" u2="&#x37;" k="-174" />
<hkern u1="&#x2044;" u2="&#x34;" k="154" />
<hkern u1="&#x2044;" u2="&#x33;" k="-102" />
<hkern u1="&#x2044;" u2="&#x31;" k="-102" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE" 	k="82" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="w,wcircumflex" 	k="61" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="t,tcommaaccent,tcaron,tbar" 	k="41" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="T,Tcommaaccent,Tcaron,Tbar" 	k="61" />
<hkern g1="K,Kcommaaccent" 	g2="hyphen,endash,emdash" 	k="164" />
<hkern g1="K,Kcommaaccent" 	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE" 	k="164" />
<hkern g1="K,Kcommaaccent" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek" 	k="82" />
<hkern g1="K,Kcommaaccent" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="72" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE" 	k="41" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="T,Tcommaaccent,Tcaron,Tbar" 	k="41" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="W,Wcircumflex" 	k="82" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek" 	k="31" />
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" 	g2="T,Tcommaaccent,Tcaron,Tbar" 	k="-41" />
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="143" />
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" 	g2="AE" 	k="184" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="102" />
<hkern g1="W,Wcircumflex" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="W,Wcircumflex" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek" 	k="102" />
<hkern g1="W,Wcircumflex" 	g2="t,tcommaaccent,tcaron,tbar" 	k="61" />
<hkern g1="W,Wcircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="287" />
<hkern g1="W,Wcircumflex" 	g2="AE" 	k="266" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng" 	g2="w,wcircumflex" 	k="51" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe" 	g2="w,wcircumflex" 	k="61" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="-20" />
<hkern g1="f" 	g2="f" 	k="-10" />
<hkern g1="f" 	g2="t,tcommaaccent,tcaron,tbar" 	k="-10" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="61" />
<hkern g1="hyphen,endash,emdash" 	g2="T,Tcommaaccent,Tcaron,Tbar" 	k="102" />
<hkern g1="hyphen,endash,emdash" 	g2="W,Wcircumflex" 	k="82" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="102" />
<hkern g1="hyphen,endash,emdash" 	g2="J,Jcircumflex" 	k="61" />
<hkern g1="j,ij,jcircumflex" 	g2="j,jcircumflex" 	k="-102" />
<hkern g1="k,kcommaaccent" 	g2="hyphen,endash,emdash" 	k="123" />
<hkern g1="k,kcommaaccent" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek" 	k="51" />
<hkern g1="k,kcommaaccent" 	g2="t,tcommaaccent,tcaron,tbar" 	k="41" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="j,jcircumflex" 	k="-102" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="225" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek" 	k="31" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="f" 	k="-10" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="w,wcircumflex" 	k="-10" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="comma,period,ellipsis" 	k="82" />
<hkern g1="slash" 	g2="zero,six" 	k="61" />
<hkern g1="t,tcommaaccent,tcaron,tbar" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="w,wcircumflex" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek" 	k="31" />
<hkern g1="w,wcircumflex" 	g2="t,tcommaaccent,tcaron,tbar" 	k="-10" />
<hkern g1="w,wcircumflex" 	g2="comma,period,ellipsis" 	k="102" />
<hkern g1="zero,nine" 	g2="slash" 	k="61" />
</font>
</defs></svg> 
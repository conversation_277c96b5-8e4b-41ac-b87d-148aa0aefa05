{"type": "continuousPage", "content": "{\"type\":\"doc\",\"content\":[{\"type\":\"title\",\"attrs\":{\"uid\":\"0244d35a-910b-4d85-89dd-1e70f290c835\",\"hookId\":\"hook-0addaca8--blockHook\",\"hookType\":\"blockHook\",\"name\":\"title\",\"class\":\"title\"},\"content\":[{\"type\":\"text\",\"text\":\"Principles of Napkin Visual AI\"}]},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"6c9546e8-a9d9-44e2-b4c9-95e28ebfb71f\",\"hookId\":\"hook-01276a93--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"<PERSON><PERSON><PERSON> team strongly believes that AI will help creating a successful environment for users. In order to create this successful environment, AI and User Experience must be tightly designed together. The principles listed below are take-aways from experiments run between mid 2021 and beginning of 2022 that will help drive future AI features.\"}]},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"908a8a08-8b2e-4d1b-97d2-4944bd3f5d66\",\"hookId\":\"hook-7cdb00a0--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"2bbb05ac-c390-4b92-bc19-80c6f4c647b7\",\"hookId\":\"hook-7f8a51bb--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"In the following the term AI refers to an algorithm that is executed automatically and that presents a result to the user (can be an auto-suggestion, make some motion sticky...).\"}]},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"d35a6efa-337e-4df6-8f93-7d9c9fe0c1b5\",\"hookId\":\"hook-141be037--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"ab0fb7c7-e260-4418-8c3c-cb65c8646b80\",\"hookId\":\"hook-14432579--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"0cc09172-4594-4704-b10d-7e00066b27e1\",\"hookId\":\"hook-c12665f9--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"ba41e5b7-c037-4465-b4e4-7f9dcf99c018\",\"hookId\":\"hook-0ce1fd5c--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"0f3dc517-6370-4271-90c2-28deb8dcafe2\",\"hookId\":\"hook-f91dc7c6--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"924abdae-3aca-471c-8645-73315b5e64f8\",\"hookId\":\"hook-c963152e--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"cbb1c3f7-c857-499b-8211-d58e3dfa6486\",\"hookId\":\"hook-1c748e31--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"17da9b8c-0731-42ab-9bec-3011260498a7\",\"hookId\":\"hook-a4fbfcac--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"f114deb7-6a22-4ea0-846f-6fc8e18b7a8b\",\"hookId\":\"hook-2d163959--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"cca433b7-87da-4d72-af43-8443ca531262\",\"hookId\":\"hook-086129b7--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"add619ec-fbf0-47d2-9c5a-674ab7e3cb95\",\"hookId\":\"hook-9822f03f--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"e01500d4-d318-4951-bbcb-158dfd424f99\",\"hookId\":\"hook-81011122--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"4aa7b226-9cce-4a57-8172-543f17093301\",\"hookId\":\"hook-13fde2a9--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"a36233ec-8884-4b35-937f-f7fbc703e4d9\",\"hookId\":\"hook-488db930--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"0d93cd81-d440-4b74-a8ee-652563c280d4\",\"hookId\":\"hook-1e685168--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"6723838d-2d44-4be1-bd94-d506ea24876b\",\"hookId\":\"hook-a274b64e--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"b7052da8-abb2-40dd-b32f-c8f01602b92b\",\"hookId\":\"hook-c08c4871--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"d5f4aa9f-424e-4d21-848b-b351d9870e9d\",\"hookId\":\"hook-c077af29--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"bulletList\",\"attrs\":{\"uid\":\"796a576b-a8a0-452b-b589-cde4955b29c1\",\"hookId\":\"hook-fa2cd233--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"listItem\",\"attrs\":{\"uid\":\"0c857b0e-f605-4456-bbdf-a4985469ac88\",\"hookId\":\"hook-d601457c--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"b68626d9-f2f3-4c97-996f-65a2f42091f4\",\"hookId\":\"hook-fa550391--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"User must be in control\"}]},{\"type\":\"bulletList\",\"attrs\":{\"uid\":\"d384b7a2-1fcc-4207-a590-68877f7d552b\",\"hookId\":\"hook-e0c31248--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"listItem\",\"attrs\":{\"uid\":\"9de757c9-9715-4012-a48c-f96868ad0c43\",\"hookId\":\"hook-0e98a2d7--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"e11c34b0-dc59-46b6-a434-21611cfed605\",\"hookId\":\"hook-9eaa163b--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"AI is run after a user action that \"},{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"changed\"},{\"type\":\"text\",\"text\":\" the document (e.g moving an item, sketching..., not selecting an item), or after a \"},{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"clear user intent\"},{\"type\":\"text\",\"text\":\" (e.g select spark).\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"184ddf1c-e57e-4ee8-992e-7135d1b64317\",\"hookId\":\"hook-e3e947ad--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"d254427b-fd7b-4408-bd11-bbb2a47626d0\",\"hookId\":\"hook-0edd0627--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"The goal is to reduce frustration\"}]}]}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"5b397815-8c1c-44ca-bd8d-80caffe84607\",\"hookId\":\"hook-97eeeced--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"bdc929af-19ac-4347-8e69-2116601c98df\",\"hookId\":\"hook-de046b8f--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Not annoying\"}]},{\"type\":\"bulletList\",\"attrs\":{\"uid\":\"157df869-01ef-4eff-861f-d798ec138aa5\",\"hookId\":\"hook-eed0463f--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"listItem\",\"attrs\":{\"uid\":\"f44dbded-bd83-4d8d-8ad5-be0a70a37d4a\",\"hookId\":\"hook-0d5141c3--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"e87a865c-d72c-4945-ad85-bcfe33d31dfb\",\"hookId\":\"hook-6c148a05--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"AI result must stick to the user \"},{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"context\"},{\"type\":\"text\",\"text\":\" (e.g auto-suggest a new item after while sketching, applying placement constraints while moving an object..., not suggesting an item when moving cursor to section title).\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"d38973c0-b31b-425f-899a-522af9230fe4\",\"hookId\":\"hook-e3555877--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"d0c3dc3f-58e3-4fd3-a94e-57e785d775f8\",\"hookId\":\"hook-9b1f479c--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"AI result must be presented only when \"},{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"confidence is high\"},{\"type\":\"text\",\"text\":\".\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"b2a275f8-7571-4f22-9f2b-a48874e55966\",\"hookId\":\"hook-81827e97--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"57e6ffba-e01e-41d6-901c-c971460841d6\",\"hookId\":\"hook-9ec5ead4--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Competing\"},{\"type\":\"text\",\"text\":\" AIs must not cancel each other (e.g moving an item is difficult because it sticks to many location).\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"3da5a981-c092-4ae1-ad11-4980ce053c70\",\"hookId\":\"hook-f306fc67--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"44c6dc46-16f7-46d4-b539-b6c19b8e69ad\",\"hookId\":\"hook-7d989280--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"The goal is to prevent user disruption, and maintain user trust by hiding potentially bad results.\"}]}]}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"12c71e34-b577-460e-a2bc-f2407f298f1f\",\"hookId\":\"hook-7fdfa682--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"267d2fb5-13f3-4355-8f1a-858e2a080e78\",\"hookId\":\"hook-bef6e32b--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Previewable\"}]},{\"type\":\"bulletList\",\"attrs\":{\"uid\":\"766d2732-01c4-450a-9678-ecc2ee5b0448\",\"hookId\":\"hook-3343d620--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"listItem\",\"attrs\":{\"uid\":\"9751b323-e25d-4968-b63b-928ab7660b3e\",\"hookId\":\"hook-8750cccb--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"d3da3a13-bf60-4d56-ab02-2ed6e1cea95f\",\"hookId\":\"hook-21e1de39--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"User must be able to \"},{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"understand\"},{\"type\":\"text\",\"text\":\" what is happening (e.g Spark preview, item alignments)\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"7bff74a2-cbf3-4477-b4a9-19d78face66e\",\"hookId\":\"hook-4dfb18ed--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"fe879265-6a73-47f6-9cfc-2d1c13854a85\",\"hookId\":\"hook-eb68932a--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"The goal is to help user build a mental model of working with AI\"}]}]}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"3ed898c2-71c3-49cc-8e57-0f1d352cad38\",\"hookId\":\"hook-d2e3cf21--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"45c8b83f-7c71-41d4-aadb-add4ba17c022\",\"hookId\":\"hook-ef6cd0af--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Editable\"}]},{\"type\":\"bulletList\",\"attrs\":{\"uid\":\"fab5317c-cb9d-4178-af3b-627d25a5b37c\",\"hookId\":\"hook-9e54e415--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"listItem\",\"attrs\":{\"uid\":\"d41eab42-4393-434e-bbb3-944040b25962\",\"hookId\":\"hook-f7e143db--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"20fee64e-0c8f-41fd-8b62-d0115544e90d\",\"hookId\":\"hook-95195dc2--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"AI results must return items that can be \"},{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"edited\"},{\"type\":\"text\",\"text\":\" (e.g connectors contact points and midpoint), or very cheap to re-create.\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"70d0d0e1-8064-48f8-b562-0ce75664afaf\",\"hookId\":\"hook-b191cc18--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"dfdcc226-9d3c-4322-bcfc-f158e53fc06a\",\"hookId\":\"hook-f091b72e--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"The goal is that even if the AI result is not 100% what the user wanted; user can edit to reach 100%, and it is still a win for AI.\"}]}]}]}]}]},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"3e5f353e-20f7-4d72-9824-e647cc9ebc73\",\"hookId\":\"hook-3dba1339--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"heading\",\"attrs\":{\"uid\":\"683ecf8d-409e-4c7a-ae9c-045bfb428e9b\",\"hookId\":\"hook-130005c7--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true,\"level\":1},\"content\":[{\"type\":\"text\",\"text\":\"Experiment bank\"}]},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"3db3a941-4cbe-4088-8acf-d9751cc87bda\",\"hookId\":\"hook-b63abb47--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"In this sections, we're listing experiments run between mid-2021 and beginning of 2022. \"}]},{\"type\":\"heading\",\"attrs\":{\"uid\":\"0233110e-fbdf-414d-9989-031f58dcaaa7\",\"hookId\":\"hook-56ff899b--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true,\"level\":2},\"content\":[{\"type\":\"text\",\"text\":\"Successful experiments\"}]},{\"type\":\"bulletList\",\"attrs\":{\"uid\":\"b6118ac1-451d-4292-89fb-ec330d4011e5\",\"hookId\":\"hook-4538e7b7--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"listItem\",\"attrs\":{\"uid\":\"591a3489-2bef-4597-b9e4-153368e831c0\",\"hookId\":\"hook-3f5c6a62--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"0770347f-e1fb-42f4-aa39-7326fbf1db1e\",\"hookId\":\"hook-37023bd0--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Connector edition with midpoint\"},{\"type\":\"text\",\"text\":\": connector shape can be updated by moving the midpoint. When the midpoint is close to the line segments between the 2 extremities, the connector becomes a line segment, otherwise it is a curve.\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"67ca5fde-cbd1-492a-905e-33cea2bb40a4\",\"hookId\":\"hook-96a83eff--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"f4b1a089-9f92-4b6d-8d7a-f016eb529eec\",\"hookId\":\"hook-c6442775--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Label decorators\"},{\"type\":\"text\",\"text\":\": using spark, we can create text with decorators (by selecting or adding prefix and/or suffix to the text itself).\"}]}]}]},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"24fa7fd4-1715-4b06-b193-b8b00b1f456d\",\"hookId\":\"hook-7bcd2c3d--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"heading\",\"attrs\":{\"uid\":\"5439297f-40a8-46cd-80d1-730a5968b744\",\"hookId\":\"hook-eb86acfa--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":\"true\",\"level\":2},\"content\":[{\"type\":\"text\",\"text\":\"Good concept (may be better UX to be successful)\"}]},{\"type\":\"bulletList\",\"attrs\":{\"uid\":\"aca8d832-1400-44cb-8b51-9235da0eb7cc\",\"hookId\":\"hook-815fb57b--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"listItem\",\"attrs\":{\"uid\":\"222f0ef0-b830-4a03-9ee1-eb802bd4445b\",\"hookId\":\"hook-2edbb8a3--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"4ec1a80d-9879-459d-8506-7d2a40ab065d\",\"hookId\":\"hook-4b0fda6c--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Section title icons\"},{\"type\":\"text\",\"text\":\": when the text cursor is located on a section title (Title / H1 / h2), an icon related to the title is auto-suggested.\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"2c8c95da-40d0-41c3-a678-d91fac44c00a\",\"hookId\":\"hook-c63337b2--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"175785bb-7502-40a3-83c2-90992f72f1ab\",\"hookId\":\"hook-f39d8115--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Label icons\"},{\"type\":\"text\",\"text\":\": when a label is created or edited, an icon related to the text is auto-suggested.\"}]}]}]},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"478b3e86-fbee-4458-9e99-2460b630300d\",\"hookId\":\"hook-2ebebfca--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"heading\",\"attrs\":{\"uid\":\"2777a39d-3e06-4e33-b89d-b7af2ca6f03b\",\"hookId\":\"hook-ab060041--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true,\"level\":2},\"content\":[{\"type\":\"text\",\"text\":\"Need more time to validate\"}]},{\"type\":\"bulletList\",\"attrs\":{\"uid\":\"3551f571-31cb-4832-8ace-999b8d3a7aa8\",\"hookId\":\"hook-2cd859fc--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"listItem\",\"attrs\":{\"uid\":\"1d40cace-ae04-4919-a2e3-37d165998bef\",\"hookId\":\"hook-781c9e65--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"1a753be5-e1a8-44b6-ae95-c0f22458c226\",\"hookId\":\"hook-7c378f17--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Sketch shape auto-suggest\"},{\"type\":\"text\",\"text\":\": circle, rectangle are recognize while sketching and auto-suggested to the user.\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"9c379c2e-d073-4164-85e8-b090eb3d99a2\",\"hookId\":\"hook-7571c0c3--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"d55d67ac-d0e6-4482-9d96-3235f78469c3\",\"hookId\":\"hook-9b06eca6--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Sketch connector auto-suggest\"},{\"type\":\"text\",\"text\":\": when sketching from an item in the direction of another item, the connector between the two items is auto-suggested.\"}]}]}]},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"0dec801f-bcac-43bd-a38e-faf7b41aab2f\",\"hookId\":\"hook-5f7646f1--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"heading\",\"attrs\":{\"uid\":\"5439297f-40a8-46cd-80d1-730a5968b744\",\"hookId\":\"hook-589989ed--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":\"true\",\"level\":2},\"content\":[{\"type\":\"text\",\"text\":\"Unsuccessful experiments\"}]},{\"type\":\"bulletList\",\"attrs\":{\"uid\":\"cdd9e523-10d5-4bb1-ac37-7b984205b892\",\"hookId\":\"hook-84fdf640--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":\"true\"},\"content\":[{\"type\":\"listItem\",\"attrs\":{\"uid\":\"f4e5b2df-6471-4dad-b2dc-18231253b209\",\"hookId\":\"hook-12f92f37--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"1dcab3be-7bed-46d6-8ada-148a3f73ca7f\",\"hookId\":\"hook-ffe01a5d--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":\"true\"},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Sketch auto-correct\"},{\"type\":\"text\",\"text\":\": the user-drawn shape is automatically beautified after the user releases the mouse button.\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"2f4fa1b5-7fdc-479a-b68e-48ad518f6edc\",\"hookId\":\"hook-a8a88817--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"aa2cdc6c-1f98-4b81-bfa9-5ae31e555800\",\"hookId\":\"hook-4406a708--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":\"true\"},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Auto-connectors\"},{\"type\":\"text\",\"text\":\": potential connectors are auto-suggested when adding a new item, or selecting an item, or selecting an item and moving the cursor over another item.\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"1d5296c6-d378-4fa9-ae3a-a5e75b0cf8cd\",\"hookId\":\"hook-71334275--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"455e25e0-2e86-4ba5-8502-915d60658eb6\",\"hookId\":\"hook-2239a1e5--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":\"true\"},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Smart selection\"},{\"type\":\"text\",\"text\":\": when clicking several times on the same item, the first time it is selected, then the selection increases with surrounding objects.\"}]}]},{\"type\":\"listItem\",\"attrs\":{\"uid\":\"4ea24206-f714-4e80-a73d-2a12b7c6b22c\",\"hookId\":\"hook-49aa7e87--blockHook\",\"hookType\":\"blockHook\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"7bd0e032-37f5-4dea-bdb7-66535fd1ad7c\",\"hookId\":\"hook-283402a1--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Graphics from bullet points\"},{\"type\":\"text\",\"text\":\": create a list with bullet points with a specific formatting, and after clocking a button, a diagram is created.\"}]}]}]},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"377352d8-bcec-4bce-b67a-fb3e081a28ea\",\"hookId\":\"hook-90e3d528--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"afbc675e-b31b-4be6-8e62-4c8de714efe2\",\"hookId\":\"hook-1c866cff--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"1b4bebf1-aab0-4293-b61f-4f2064756ae6\",\"hookId\":\"hook-391b16f2--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true},\"content\":[{\"type\":\"text\",\"text\":\"Trying to map the unsuccessful experiments and good concepts to principles they do not fulfill.\"}]},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"52f7ac4b-132e-4973-b462-4aefb211c87d\",\"hookId\":\"hook-a3d1c77a--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"7b9ee519-5be1-456e-a954-39b45c3bb6b3\",\"hookId\":\"hook-f4ee1e49--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"d128886f-db6e-4976-9d05-39ffba7295b0\",\"hookId\":\"hook-7fa06137--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"caffe217-f997-475e-a7de-c2f7c67fdb8c\",\"hookId\":\"hook-34af55d0--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"ae773f04-e75d-43bd-a3e3-f0c12065d8fa\",\"hookId\":\"hook-6cee3a4a--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"6149f7eb-fe4c-4d63-bd5f-b08f17ff6bdd\",\"hookId\":\"hook-f0721801--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"03116ce5-e4e6-40e7-9f5f-0d7345efb1ee\",\"hookId\":\"hook-70531bd2--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"fa91882f-2220-4da8-8f34-fc124b8398ba\",\"hookId\":\"hook-72ceafe5--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"6eb06f98-96cc-40dd-a9c3-f41926e561fe\",\"hookId\":\"hook-4ad442ef--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"6ef2f267-10a5-4610-ac9f-31c06e5354cf\",\"hookId\":\"hook-c8b43ca3--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"e5306413-a341-455b-88dc-9da228289490\",\"hookId\":\"hook-7529f809--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"6ac39036-1932-4344-93c4-4ac7bb2346f8\",\"hookId\":\"hook-238f2fc4--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"04cb7803-c0ad-4d86-bcb4-2bb1669c1182\",\"hookId\":\"hook-44239708--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"7d4cb761-89d1-47da-ab82-a192f324bc1b\",\"hookId\":\"hook-aa8f6341--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"fcf3ebf4-dab6-4f00-a380-d43e9d73d912\",\"hookId\":\"hook-6f5ca081--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"942536be-e5ff-402c-b9a2-2723d6315c7c\",\"hookId\":\"hook-c31a29d3--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"92afa916-c217-4551-a397-cb94a57cb8fc\",\"hookId\":\"hook-208baf91--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"6d532a24-c7de-4f10-a8cf-cead24e981ff\",\"hookId\":\"hook-7227cb6a--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"486d1cd1-c917-4987-8bec-6e055d7f75c2\",\"hookId\":\"hook-20253dd7--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"aa217d68-d6da-4cf2-a3c5-5dc2e324424c\",\"hookId\":\"hook-26c26d84--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"01b56ba2-a643-4fd4-8f52-41ba058071cf\",\"hookId\":\"hook-e5731a77--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"ca3fa278-8de6-42dd-9cef-5fff6e39b1f3\",\"hookId\":\"hook-62b76678--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"c4e138ff-efed-4b55-af6d-9f103377527a\",\"hookId\":\"hook-9056d4d7--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"80fa91cc-600e-4068-8e47-213dbba97e6d\",\"hookId\":\"hook-88beb365--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"06bc1565-c46e-41dc-a1ab-ac09009b5bac\",\"hookId\":\"hook-21604f43--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"bb8f6ba2-a153-4da2-80c3-2f30893d46d0\",\"hookId\":\"hook-cc2ce23c--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"56c9284d-1316-4069-9bc7-d698020440a6\",\"hookId\":\"hook-7a818655--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"8467ab17-b219-4acd-abd2-abc2da9aa1a0\",\"hookId\":\"hook-b0d6bf6a--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"f28836fc-3bc5-48f1-b291-ee103076058c\",\"hookId\":\"hook-d434b35b--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"06a48908-632a-4536-9a24-61ce11e7d56d\",\"hookId\":\"hook-8c233f7c--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"6cfd14a6-3ce5-4416-9aa3-c58fffabe18b\",\"hookId\":\"hook-1dd271ef--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"b03bf7a3-3cae-419e-95fa-097ed7de9f5a\",\"hookId\":\"hook-d5a1d451--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"043eac2e-4de0-4b41-8354-3fac912e1dc4\",\"hookId\":\"hook-74aaa4a2--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"29a37e7b-df48-46ad-a26b-d3d84fad4d4a\",\"hookId\":\"hook-0b13e77f--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"a9cad108-d5ab-49ef-ad21-0992bd1c7221\",\"hookId\":\"hook-ed020bc2--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"ac170da5-1959-486b-a126-285cf87f1dcd\",\"hookId\":\"hook-f12353ac--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"a7ca1608-72a2-4449-b956-b666d41e99e2\",\"hookId\":\"hook-4429c813--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"fda71886-b918-43ae-8f7f-552364d8d45c\",\"hookId\":\"hook-9772144b--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"12c43f0a-9ab9-4da7-8062-468407d77392\",\"hookId\":\"hook-9286e66e--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"327fef33-21a7-41ca-89c2-a745646b81ca\",\"hookId\":\"hook-373da0c0--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"842f1e34-5124-4ff5-9e8d-8fbfd185445d\",\"hookId\":\"hook-4e22d086--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"3ad97afb-d001-47cc-bb85-cec218d31178\",\"hookId\":\"hook-69799516--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"3623241f-2d75-4950-a508-89e96cb709db\",\"hookId\":\"hook-8803eb22--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"0ff1ef48-601f-4ac8-9f9f-97c7d1b55c20\",\"hookId\":\"hook-179f7a26--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"bf65b875-**************-aa8927cbebef\",\"hookId\":\"hook-ac7ae4e3--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"6626ff00-8a07-448c-bab6-6bf108826c11\",\"hookId\":\"hook-299ae668--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"154f1b17-63f7-46f1-bd8a-4bb19df27c49\",\"hookId\":\"hook-a7561a04--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"5bd9920a-6e11-4c3c-9899-ef478a9e153e\",\"hookId\":\"hook-00b1c864--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"641325c1-c957-4f04-b30c-dbba0b58c387\",\"hookId\":\"hook-61e5abda--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"77bfe2c7-deb9-4630-892f-9e9311a64880\",\"hookId\":\"hook-ce569187--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"ad805b6e-f800-4b19-990e-05ac3b1a3fbc\",\"hookId\":\"hook-45ebd543--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"a7ad9bbd-9a86-4cdd-bb50-fba479d4e48b\",\"hookId\":\"hook-15515065--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"5e28ce74-f7e6-4672-af8c-f2069e279a6c\",\"hookId\":\"hook-a90d4eec--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"0643135e-a364-46e0-abf4-dd1838f3890d\",\"hookId\":\"hook-19546248--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"59a7db67-c5f2-48dd-b21e-3f50e5f2c90b\",\"hookId\":\"hook-5f90dcd3--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}},{\"type\":\"paragraph\",\"attrs\":{\"uid\":\"93646a32-350a-4c52-b5f5-0a105c14b1c3\",\"hookId\":\"hook-9b1b3b9f--blockHook\",\"hookType\":\"blockHook\",\"bubbleMenu\":true}}]}", "items": [{"v": 4, "sessionId": "text", "id": "mffk7fq1nhmu", "type": "ItemDrawingText", "clusterId": "mffk7fq1nhmu", "positioner": {"StaticPosition": [456.8399658203125, 395], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [234.20001220703125, 389], "hookId": "hook-c12665f9--block<PERSON>ook", "deltaHook": [0.200042724609375, 16.999969482421875]}, "clusterToTopLeft": [222.63995361328125, 6], "position": [456.8399658203125, 395]}, "aiData": {"decorators": [], "connectedBy": ["1lwqjuzqlcuf6", "dffeobqliiv4", "zodtqjmfiz9c"]}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[3.507164478302002, -0.21547508239746094, 0, 3.127589225769043, 4.618740081787109, 0, 2.6566152572631836, 15.01864242553711, 0, 13.25148868560791, 15.491643905639648, 0, 13.196901321411133, 4.880828857421875, 0, 13.559812545776367, -0.07395553588867188, 0], [23.129627227783203, 5.235477447509766, 0, 20.486955642700195, 3.997457504272461, 0, 16.352928161621094, 3.895939826965332, 0, 17.06449317932129, 7.700038909912109, 0, 22.280628204345703, 11.344919204711914, 0, 22.54715919494629, 14.222315788269043, 0, 17.511293411254883, 15.308965682983398, 0, 16.729148864746094, 13.1541748046875, 0], [26.677379608154297, 10.022153854370117, 0, 37.36164093017578, 9.900123596191406, 0, 36.447322845458984, 5.999407768249512, 0, 29.643665313720703, 3.6151466369628906, 0, 26.295286178588867, 11.188714981079102, 0, 29.277713775634766, 16.19681739807129, 0, 34.39868927001953, 15.756192207336426, 0, 36.706787109375, 15.013432502746582, 0], [41.265830993652344, 4.8239288330078125, 0, 40.704833984375, 15.476699829101562, 0], [41.572044372558594, 8.746959686279297, 0, 41.79998779296875, 4.546602249145508, 0, 45.165435791015625, 4.331535339355469, 0], [57.64501190185547, 4.371809959411621, 0, 57.4566535949707, 16.190671920776367, 0], [56.80183410644531, 12.156803131103516, 0, 57.36249923706055, 4.961278915405273, 0, 64.32669067382812, 4.501666069030762, 0, 65.2649154663086, 12.024436950683594, 0, 64.39603424072266, 15.864777565002441, 0], [64.58209228515625, 12.681392669677734, 0, 65.8531723022461, 5.180764198303223, 0, 71.37245178222656, 4.145241737365723, 0, 72.52452850341797, 12.104726791381836, 0, 72.14240264892578, 15.05411148071289, 0], [77.66388702392578, 4.84794807434082, 0, 77.34443664550781, 15.937677383422852, 0, 85.33160400390625, 15.610923767089844, 0, 84.62541961669922, 7.672778129577637, 0], [84.98682403564453, 3.8156232833862305, 0, 85.2962875366211, 15.209609985351562, 0], [96.1356201171875, 6.4102373123168945, 0, 94.47310638427734, 3.5354461669921875, 0, 90.9727554321289, 4.870826721191406, 0, 90.68755340576172, 8.325483322143555, 0, 96.85023498535156, 11.834489822387695, 0, 96.35962677001953, 14.623818397521973, 0, 91.76896667480469, 16.407325744628906, 0, 89.91817474365234, 13.791715621948242, 0], [101.09042358398438, -0.21405792236328125, 0, 100.9456787109375, 8.953815460205078, 0, 100.4485855102539, 15.902527809143066, 0, 103.63383483886719, 16.276418685913086, 0], [99.10313415527344, 5.004056930541992, 0, 103.60224151611328, 4.322731971740723, 0], [116.33184814453125, 0.4881439208984375, 0, 116.1127700805664, 16.40019416809082, 0], [116.33269500732422, 12.650747299194336, 0, 117.31861877441406, 4.791375160217285, 0, 126.30024719238281, 5.427898406982422, 0, 125.56487274169922, 16.48496437072754, 0, 116.94454193115234, 16.764787673950195, 0, 116.531982421875, 8.59694766998291, 0], [130.41709899902344, 11.127676010131836, 0, 139.3517608642578, 10.352117538452148, 0, 138.67344665527344, 5.381430625915527, 0, 132.38845825195312, 4.9358673095703125, 0, 129.2923583984375, 11.208709716796875, 0, 131.74057006835938, 16.63062858581543, 0, 137.41661071777344, 16.043811798095703, 0, 139.72410583496094, 15.716278076171875, 0], [152.8291778564453, 4.2863311767578125, 0, 152.03781127929688, 16.61178970336914, 0], [151.9397735595703, 0.898101806640625, 0, 152.52401733398438, 1.6639957427978516, 0], [157.66014099121094, 3.774972915649414, 0, 157.56129455566406, 16.080530166625977, 0], [157.08773803710938, 12.606583595275879, 0, 157.78269958496094, 4.710494041442871, 0, 164.93569946289062, 3.462779998779297, 0, 165.20693969726562, 12.639229774475098, 0, 165.64151000976562, 15.86217212677002, 0], [187.69476318359375, 6.4566650390625, 0, 183.56167602539062, 4.390918731689453, 0, 178.685546875, 8.191427230834961, 0, 179.238037109375, 13.829092979431152, 0, 184.18507385253906, 17.322050094604492, 0, 187.54103088378906, 14.974786758422852, 0], [196.5879364013672, 4.758659362792969, 0, 191.52809143066406, 4.8360395431518555, 0, 191.89910888671875, 16.710609436035156, 0, 201.89686584472656, 17.31264305114746, 0, 201.4790802001953, 5.488983154296875, 0, 196.58155822753906, 4.836722373962402, 0], [207.5059356689453, 4.659916877746582, 0, 207.43856811523438, 15.166023254394531, 0], [206.85545349121094, 12.400501251220703, 0, 208.23069763183594, 3.954037666320801, 0, 213.619384765625, 4.155022621154785, 0, 214.92300415039062, 12.142915725708008, 0, 214.58551025390625, 15.655733108520508, 0], [220.84530639648438, 0.7089853286743164, 0, 220.37046813964844, 9.065780639648438, 0, 220.57073974609375, 16.565095901489258, 0, 223.73236083984375, 16.543869018554688, 0], [218.71681213378906, 5.417702674865723, 0, 223.2890167236328, 5.776721000671387, 0], [226.50169372558594, 4.6602325439453125, 0, 226.85748291015625, 15.912884712219238, 0], [226.45130920410156, 9.666049003601074, 0, 228.0154266357422, 5.714389801025391, 0, 230.2973175048828, 5.2591753005981445, 0], [239.44541931152344, 5.7003984451293945, 0, 234.5589141845703, 4.607723236083984, 0, 234.10301208496094, 17.092670440673828, 0, 244.08363342285156, 16.157522201538086, 0, 245.00018310546875, 5.108315467834473, 0, 239.3946075439453, 5.503994941711426, 0], [248.9011688232422, -0.23396682739257812, 0, 249.40480041503906, 15.673992156982422, 0]], "curvesBox": [2.6566152572631836, -0.23396682739257812, 249.40480041503906, 17.322050094604492]}, "frame": {}, "text": {"text": "User must be in control", "lineHeight": 22, "boardSize": [264, 28], "textBox": [0, 0, 252, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 85, "char": "U", "curves": 1, "bounds": [1.850000023841858, 2, 600], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 83, "char": "s", "curves": 1, "bounds": [1.100000023841858, 2, 700], "offset": [15.800000190734863, 16, 400], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [25.600000381469727, 16, 850], "scale": [8, 8, 0.5]}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [39.400001525878906, 16, 1250], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [47.60000228881836, 16, 1550], "scale": [8, 8, 0.5]}, {"key": 77, "char": "m", "curves": 3, "bounds": [2.4000000953674316, 2, 800], "offset": [55.60000228881836, 16, 1700], "scale": [8, 8, 0.5]}, {"key": 85, "char": "u", "curves": 2, "bounds": [1.5, 2, 500], "offset": [75.80000305175781, 16, 2200], "scale": [8, 8, 0.5]}, {"key": 83, "char": "s", "curves": 1, "bounds": [1.100000023841858, 2, 700], "offset": [88.80000305175781, 16, 2550], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [98.60000610351562, 16, 3000], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [106.00000762939453, 16, 3350], "scale": [8, 8, 0.5]}, {"key": 66, "char": "b", "curves": 2, "bounds": [1.7000000476837158, 2, 700], "offset": [114.00000762939453, 16, 3500], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [128.60000610351562, 16, 3950], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [142.40000915527344, 16, 4350], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [150.40000915527344, 16, 4500], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [155.8000030517578, 16, 4800], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [168.8000030517578, 16, 5150], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [176.8000030517578, 16, 5300], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [189.8000030517578, 16, 5700], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [204.40000915527344, 16, 6100], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [217.40000915527344, 16, 6450], "scale": [8, 8, 0.5]}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [224.8000030517578, 16, 6800], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [233, 16, 7100], "scale": [8, 8, 0.5]}, {"key": 76, "char": "l", "curves": 1, "bounds": [0.550000011920929, 2, 300], "offset": [247.60000610351562, 16, 7500], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "text", "id": "dcabyjqb92q5", "type": "ItemDrawingText", "clusterId": "mffk7fq1nhmu", "positioner": {"StaticPosition": [240.20001220703125, 520.5], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [234.20001220703125, 389], "hookId": "hook-c12665f9--block<PERSON>ook", "deltaHook": [0.200042724609375, 16.999969482421875]}, "clusterToTopLeft": [6, 131.5], "position": [240.20001220703125, 520.5]}, "aiData": {"decorators": [], "connectedBy": ["1lwqjuzqlcuf6", "1qbbmrvqle9za", "1d1pbhnonfpc4", "4ij3rfmfiyv4"]}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[3.2752037048339844, 1.1307373046875, 0, 2.765873908996582, 16.59659767150879, 0], [3.3547115325927734, 1.2622175216674805, 0, 13.325342178344727, 17.069015502929688, 0], [13.291125297546387, 15.82577896118164, 0, 13.178457260131836, 1.3521709442138672, 0], [23.018585205078125, 3.8740129470825195, 0, 17.264408111572266, 4.242597579956055, 0, 17.368701934814453, 15.891098976135254, 0, 27.80086898803711, 15.307394027709961, 0, 28.51641845703125, 4.481717109680176, 0, 22.58233642578125, 5.205224990844727, 0], [33.621437072753906, -0.2645416259765625, 0, 33.546756744384766, 8.798540115356445, 0, 33.65138626098633, 16.56269073486328, 0, 36.90656661987305, 15.832151412963867, 0], [31.083288192749023, 4.836208343505859, 0, 36.78145217895508, 5.157886505126953, 0], [56.76377487182617, 16.05050277709961, 0, 55.93812942504883, 9.640772819519043, 0, 48.03530502319336, 9.57974910736084, 0, 47.73872756958008, 16.11250877380371, 0, 55.54684829711914, 16.388826370239258, 0, 56.895565032958984, 9.870964050292969, 0, 56.68977355957031, 6.235791206359863, 0, 52.98469543457031, 4.330691337585449, 0, 48.25637435913086, 6.099176406860352, 0], [61.178070068359375, 4.576663017272949, 0, 61.25457763671875, 16.010499954223633, 0], [60.639312744140625, 12.276708602905273, 0, 62.608272552490234, 4.643853187561035, 0, 68.22218322753906, 3.9252681732177734, 0, 68.94676208496094, 13.242199897766113, 0, 69.58887481689453, 16.395246505737305, 0], [73.62165832519531, 4.741277694702148, 0, 74.1612777709961, 15.847028732299805, 0], [73.94691467285156, 12.890178680419922, 0, 74.90812683105469, 5.865069389343262, 0, 80.24707794189453, 5.582645416259766, 0, 81.88101959228516, 12.549779891967773, 0, 81.34304809570312, 16.702341079711914, 0], [91.28762817382812, 6.069912910461426, 0, 86.25408935546875, 5.126824378967285, 0, 86.86474609375, 16.590957641601562, 0, 96.73515319824219, 16.659582138061523, 0, 96.66707611083984, 5.323207855224609, 0, 91.1116943359375, 5.748471260070801, 0], [101.00942993164062, 4.9154558181762695, 0, 104.91165924072266, 15.937909126281738, 0], [110.07415771484375, 4.1329450607299805, 0, 104.1707534790039, 20.311786651611328, 0], [114.77558898925781, 4.955748558044434, 0, 114.26134490966797, 16.41500473022461, 0], [114.0771255493164, 1.341867446899414, 0, 114.00770568847656, 2.1028194427490234, 0], [119.23674774169922, 5.597079277038574, 0, 120.2094497680664, 16.033329010009766, 0], [119.83148956298828, 12.533855438232422, 0, 119.98565673828125, 5.537158012390137, 0, 126.9168930053711, 5.0657548904418945, 0, 128.14305114746094, 12.721558570861816, 0, 127.75690460205078, 15.841485023498535, 0], [141.03990173339844, 12.298233985900879, 0, 140.7754669189453, 4.258222579956055, 0, 130.80166625976562, 4.348654747009277, 0, 130.72442626953125, 14.816722869873047, 0, 140.12013244628906, 14.782675743103027, 0, 141.70306396484375, 8.004884719848633, 0], [141.35235595703125, 3.610158920288086, 0, 140.6230926513672, 12.440896987915039, 0, 140.91941833496094, 16.767047882080078, 0, 137.47293090820312, 21.158184051513672, 0, 132.05291748046875, 18.425434112548828, 0]], "curvesBox": [2.765873908996582, -0.2645416259765625, 141.70306396484375, 21.158184051513672]}, "frame": {}, "text": {"text": "Not annoying", "lineHeight": 22, "boardSize": [155.60000610351562, 28], "textBox": [0, 0, 143.60000610351562, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 78, "char": "N", "curves": 3, "bounds": [1.9500000476837158, 2, 500], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [16.600000381469727, 16, 350], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [31.200000762939453, 16, 750], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [38.60000228881836, 16, 1100], "scale": [8, 8, 0.5]}, {"key": 65, "char": "a", "curves": 1, "bounds": [1.399999976158142, 2, 900], "offset": [46.60000228881836, 16, 1250], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [58.80000305175781, 16, 1800], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [71.80000305175781, 16, 2150], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [84.80000305175781, 16, 2500], "scale": [8, 8, 0.5]}, {"key": 89, "char": "y", "curves": 2, "bounds": [1.399999976158142, 2, 400], "offset": [99.4000015258789, 16, 2900], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [111.5999984741211, 16, 3200], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [117, 16, 3500], "scale": [8, 8, 0.5]}, {"key": 71, "char": "g", "curves": 2, "bounds": [1.7000000476837158, 2, 1000], "offset": [130, 16, 3850], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "zn4fmzqbn3sx", "id": "zn4fmzqbn3sy", "type": "ItemDrawingText", "clusterId": "mffk7fq1nhmu", "positioner": {"StaticPosition": [510, 520.5], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [234.20001220703125, 389], "hookId": "hook-c12665f9--block<PERSON>ook", "deltaHook": [0.200042724609375, 17]}, "clusterToTopLeft": [275.79998779296875, 131.5], "position": [510, 520.5]}, "aiData": {"decorators": [], "connectedBy": ["1undbrfmfls02", "4ij3rfmfiyv4"]}, "schedule": {"start": 0, "end": 4.37327099609375}, "behavior": {"type": "label"}, "curves": {"curves": [[0.9400746822357178, 15.965170860290527, 0, 7.391744613647461, 0.9947395324707031, 46.78839111328125], [7.775881767272949, 1.46575927734375, 56.778076171875, 13.408617973327637, 15.726948738098145, 93.935302734375], [4.187674045562744, 10.025847434997559, 140.1265869140625, 10.543554306030273, 10.238421440124512, 200.49127197265625], [16.302885055541992, 0.5548515319824219, 294.9593505859375, 17.297542572021484, 14.588233947753906, 368.5467529296875], [31.13918685913086, 0.9986982345581055, 662.101318359375, 30.37824058532715, 16.18033218383789, 704.184326171875], [30.82166290283203, 1.1777591705322266, 760.3568115234375, 38.52306365966797, 1.374211311340332, 816.303955078125, 38.40021514892578, 8.524032592773438, 865.532958984375, 31.208730697631836, 9.310319900512695, 896.9736328125], [43.395870208740234, 0.1414051055908203, 1028.5748291015625, 42.998992919921875, 15.937463760375977, 1068.8763427734375], [43.50942611694336, 0.10499858856201172, 1125.3946533203125, 50.84608840942383, 0.2579307556152344, 1179.1954345703125, 49.92882537841797, 8.186132431030273, 1212.5235595703125, 43.671974182128906, 8.123981475830078, 1284.349609375], [46.88418960571289, 8.693458557128906, 1323.8125, 51.510894775390625, 14.999191284179688, 1377.27001953125], [55.86387252807617, 1.1794605255126953, 1529.219482421875, 56.27581787109375, 15.249119758605957, 1581.72119140625], [61.906410217285156, -0.4237785339355469, 1772.236083984375, 62.66499710083008, 15.514826774597168, 1806.59765625], [62.68960952758789, 0.5961761474609375, 1850.5166015625, 72.93785095214844, 14.754472732543945, 1908.1923828125], [72.8158187866211, 14.862892150878906, 1900.265380859375, 73.07124328613281, -0.409088134765625, 1949.305419921875], [88.95994567871094, 3.1897335052490234, 2098.94921875, 86.08368682861328, -0.6107387542724609, 2150.334716796875, 77.4681396484375, 3.6521549224853516, 2197.5673828125, 78.05487060546875, 13.137425422668457, 2244.730224609375, 86.2169189453125, 17.071779251098633, 2304.033935546875, 89.69483947753906, 14.553112983703613, 2333.01220703125], [95.27486419677734, -0.01224517822265625, 2671.75927734375, 95.61924743652344, 15.96622371673584, 2721.1318359375], [99.92053985595703, 0.6199121475219727, 2861.65869140625, 100.82660675048828, 14.885577201843262, 2901.865478515625], [100.44458770751953, -0.15430831909179688, 2960.885986328125, 107.29742431640625, 0.5108976364135742, 2991.241455078125, 107.07460021972656, 8.323528289794922, 3046.49267578125, 99.91761016845703, 7.624293327331543, 3109.385986328125], [112.88337707519531, 1.4948854446411133, 3251.037353515625, 113.39134216308594, 16.388290405273438, 3289.001953125], [113.5806655883789, 16.05228614807129, 3281.139404296875, 120.41448974609375, 15.299732208251953, 3385.03369140625], [125.22657775878906, 0.4080171585083008, 3539.28369140625, 124.8057632446289, 16.319440841674805, 3591.345703125], [125.678466796875, 0.6551628112792969, 3634.57470703125, 131.98709106445312, 0.4728965759277344, 3698.90380859375], [125.24267578125, 7.80061149597168, 3746.90771484375, 130.82994079589844, 7.8262434005737305, 3785.63427734375], [125.79473876953125, 15.607601165771484, 3854.1171875, 132.49090576171875, 16.348896026611328, 3890.00830078125], [143.26571655273438, 3.017022132873535, 4012.31787109375, 140.4185333251953, 0.42426490783691406, 4063.9345703125, 136.2494354248047, 2.2527942657470703, 4099.783203125, 136.15048217773438, 7.514304161071777, 4154.1533203125, 143.15487670898438, 10.091965675354004, 4222.49267578125, 143.50384521484375, 16.350296020507812, 4255.5458984375, 137.26475524902344, 16.610639572143555, 4307.3525390625, 134.88233947753906, 13.86043930053711, 4373.27099609375]], "curvesBox": [0.9400746822357178, -0.6107387542724609, 143.50384521484375, 17.071779251098633]}, "frame": {}, "text": {"text": "AI PRINCIPLES", "lineHeight": 22, "boardSize": [157.67999267578125, 28], "textBox": [0, 0, 145.67999267578125, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 65, "char": "A", "curves": 3, "bounds": [1.75, 2, 500], "offset": [0, 16, 657], "scale": [8, 8, 0.5]}, {"key": 73, "char": "I", "curves": 1, "bounds": [0.6200000047683716, 2, 200], "offset": [15, 16, 1007], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [20.959999084472656, 16, 1207], "scale": [8, 8, 0.5]}, {"key": 80, "char": "P", "curves": 2, "bounds": [1.350000023841858, 2, 600], "offset": [28.959999084472656, 16, 1357], "scale": [8, 8, 0.5]}, {"key": 82, "char": "R", "curves": 3, "bounds": [1.475000023841858, 2, 800], "offset": [40.7599983215332, 16, 1757], "scale": [8, 8, 0.5]}, {"key": 73, "char": "I", "curves": 1, "bounds": [0.6200000047683716, 2, 200], "offset": [53.55999755859375, 16, 2257], "scale": [8, 8, 0.5]}, {"key": 78, "char": "N", "curves": 3, "bounds": [1.9500000476837158, 2, 500], "offset": [59.519996643066406, 16, 2457], "scale": [8, 8, 0.5]}, {"key": 67, "char": "C", "curves": 1, "bounds": [1.899999976158142, 2, 900], "offset": [76.1199951171875, 16, 2807], "scale": [8, 8, 0.5]}, {"key": 73, "char": "I", "curves": 1, "bounds": [0.6200000047683716, 2, 200], "offset": [92.31999206542969, 16, 3357], "scale": [8, 8, 0.5]}, {"key": 80, "char": "P", "curves": 2, "bounds": [1.350000023841858, 2, 600], "offset": [98.27999114990234, 16, 3557], "scale": [8, 8, 0.5]}, {"key": 76, "char": "L", "curves": 2, "bounds": [1.5, 2, 400], "offset": [110.07999420166016, 16, 3957], "scale": [8, 8, 0.5]}, {"key": 69, "char": "E", "curves": 4, "bounds": [1.2999999523162842, 2, 800], "offset": [123.07999420166016, 16, 4257], "scale": [8, 8, 0.5]}, {"key": 83, "char": "S", "curves": 1, "bounds": [1.399999976158142, 2, 800], "offset": [134.47999572753906, 16, 4757], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "text", "id": "1lz86rvph37yd", "type": "ItemDrawingText", "clusterId": "mffk7fq1nhmu", "positioner": {"StaticPosition": [540.93994140625, 652], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [234.20001220703125, 389], "hookId": "hook-c12665f9--block<PERSON>ook", "deltaHook": [0.200042724609375, 16.999969482421875]}, "clusterToTopLeft": [306.73992919921875, 263], "position": [540.93994140625, 652]}, "aiData": {"decorators": [], "connectedBy": ["1qbbmrvqle9za", "dffeobqliiv4", "14636x7mfg64q"]}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[3.2213022708892822, 1.0537757873535156, 0, 3.208733081817627, 15.435881614685059, 0], [3.382305860519409, 1.0728521347045898, 0, 9.887218475341797, 0.5961236953735352, 0], [3.431914806365967, 7.863761901855469, 0, 9.2111234664917, 7.728323936462402, 0], [3.060603380203247, 15.7625150680542, 0, 9.03842544555664, 15.36884880065918, 0], [22.034015655517578, 13.83081340789795, 0, 20.759902954101562, 5.344816207885742, 0, 12.624135971069336, 4.701543807983398, 0, 12.991218566894531, 16.075759887695312, 0, 21.848482131958008, 17.05162239074707, 0, 21.595172882080078, 7.99477481842041, 0], [22.577571868896484, -0.2487945556640625, 0, 22.27054214477539, 16.663732528686523, 0], [28.07548713684082, 4.643071174621582, 0, 27.18373680114746, 16.589393615722656, 0], [26.83837127685547, 1.5220756530761719, 0, 28.45624542236328, 0.6404762268066406, 0], [33.62112045288086, -0.5762977600097656, 0, 33.92037582397461, 9.049932479858398, 0, 33.41806411743164, 16.069387435913086, 0, 36.58811569213867, 16.21194839477539, 0], [31.652212142944336, 4.688077926635742, 0, 36.116146087646484, 5.15349006652832, 0], [48.67759704589844, 16.269384384155273, 0, 47.523521423339844, 9.701129913330078, 0, 40.4200325012207, 9.501714706420898, 0, 41.09787368774414, 15.421806335449219, 0, 47.4632568359375, 16.481443405151367, 0, 48.91599655151367, 9.596237182617188, 0, 49.253501892089844, 6.049684524536133, 0, 45.59946060180664, 3.8063831329345703, 0, 40.59382629394531, 5.642825126647949, 0], [52.774749755859375, 0.017823219299316406, 0, 53.562530517578125, 15.610404014587402, 0], [53.49674987792969, 13.038368225097656, 0, 53.4206428527832, 4.9529571533203125, 0, 62.12953186035156, 4.514616012573242, 0, 62.133174896240234, 15.434417724609375, 0, 53.9970588684082, 15.742834091186523, 0, 52.88249206542969, 7.6260833740234375, 0], [67.6500473022461, 0.010891914367675781, 0, 68.19441986083984, 15.692268371582031, 0], [72.36236572265625, 9.396692276000977, 0, 82.65049743652344, 9.494842529296875, 0, 82.01732635498047, 4.495772361755371, 0, 75.35753631591797, 3.352537155151367, 0, 72.30181121826172, 9.871705055236816, 0, 73.99129486083984, 15.376113891601562, 0, 80.4741439819336, 15.693599700927734, 0, 81.78800201416016, 13.544695854187012, 0]], "curvesBox": [3.060603380203247, -0.5762977600097656, 82.65049743652344, 17.05162239074707]}, "frame": {}, "text": {"text": "Editable", "lineHeight": 22, "boardSize": [95.80000305175781, 28], "textBox": [0, 0, 83.80000305175781, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 69, "char": "E", "curves": 4, "bounds": [1.2999999523162842, 2, 800], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 68, "char": "d", "curves": 2, "bounds": [1.7000000476837158, 2, 800], "offset": [11.399999618530273, 16, 500], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [26, 16, 1000], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [31.399999618530273, 16, 1300], "scale": [8, 8, 0.5]}, {"key": 65, "char": "a", "curves": 1, "bounds": [1.399999976158142, 2, 900], "offset": [38.79999923706055, 16, 1650], "scale": [8, 8, 0.5]}, {"key": 66, "char": "b", "curves": 2, "bounds": [1.7000000476837158, 2, 700], "offset": [51, 16, 2200], "scale": [8, 8, 0.5]}, {"key": 76, "char": "l", "curves": 1, "bounds": [0.550000011920929, 2, 300], "offset": [65.5999984741211, 16, 2650], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [71, 16, 2900], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "text", "id": "zs540rnjirk1", "type": "ItemDrawingText", "clusterId": "mffk7fq1nhmu", "positioner": {"StaticPosition": [790.8400268554688, 520.5], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [234.20001220703125, 389], "hookId": "hook-c12665f9--block<PERSON>ook", "deltaHook": [0.200042724609375, 16.999969482421875]}, "clusterToTopLeft": [556.6400146484375, 131.5], "position": [790.8400268554688, 520.5]}, "aiData": {"decorators": [], "connectedBy": ["1undbrfmfls02"], "patch_type": "text", "hasVirtualLineBreak": false}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[2.3464930057525635, 0.22325515747070312, 28.593751907348633, 2.5255420207977295, 15.356306076049805, 28.593751907348633], [1.871698021888733, 0.913081169128418, 28.593751907348633, 9.203554153442383, 0.2930889129638672, 28.593751907348633, 8.81857967376709, 7.934514045715332, 28.593751907348633, 2.4905807971954346, 8.442842483520508, 28.593751907348633], [14.580859184265137, 5.534482002258301, 228.75001525878906, 14.70002269744873, 16.490020751953125, 228.75001525878906], [13.80373477935791, 10.287820816040039, 228.75001525878906, 15.662885665893555, 5.968869209289551, 228.75001525878906, 18.383827209472656, 4.966444969177246, 228.75001525878906], [20.97770881652832, 10.619207382202148, 371.7187805175781, 30.813501358032227, 11.479904174804688, 371.7187805175781, 29.306983947753906, 6.347743988037109, 371.7187805175781, 23.265426635742188, 4.250110626220703, 371.7187805175781, 20.266498565673828, 10.672260284423828, 371.7187805175781, 22.208023071289062, 16.82535171508789, 371.7187805175781, 28.203237533569336, 16.488109588623047, 371.7187805175781, 30.518199920654297, 14.839889526367188, 371.7187805175781], [35.230712890625, 4.3255767822265625, 571.8750610351562, 39.1512451171875, 15.501102447509766, 571.8750610351562], [39.39961242675781, 14.940178871154785, 571.8750610351562, 43.488441467285156, 4.5094099044799805, 571.8750610351562], [47.06161117553711, 3.9255075454711914, 686.2500610351562, 47.013065338134766, 14.776718139648438, 686.2500610351562], [47.61552047729492, 0.02728271484375, 686.2500610351562, 48.03699493408203, -0.5031337738037109, 686.2500610351562], [52.33457946777344, 10.209877014160156, 829.2188110351562, 62.353614807128906, 10.855382919311523, 829.2188110351562, 61.040470123291016, 5.926878929138184, 829.2188110351562, 54.88764572143555, 4.82716178894043, 829.2188110351562, 51.756996154785156, 10.957569122314453, 829.2188110351562, 54.43128967285156, 16.022871017456055, 829.2188110351562, 59.47929382324219, 15.600700378417969, 829.2188110351562, 61.17041778564453, 15.591705322265625, 829.2188110351562], [65.92102813720703, 5.426513671875, 1029.3751220703125, 70.01508331298828, 16.29528045654297, 1029.3751220703125], [70.29012298583984, 16.359676361083984, 1029.3751220703125, 73.90193939208984, 4.762752532958984, 1029.3751220703125], [74.03241729736328, 5.505641937255859, 1029.3751220703125, 77.9616470336914, 16.97296142578125, 1029.3751220703125], [77.76457214355469, 17.1132755279541, 1029.3751220703125, 82.72016906738281, 5.438226699829102, 1029.3751220703125], [93.51692199707031, 15.532451629638672, 1200.9376220703125, 92.98001861572266, 9.316814422607422, 1200.9376220703125, 85.01417541503906, 9.398075103759766, 1200.9376220703125, 85.13977813720703, 16.278921127319336, 1200.9376220703125, 92.77391815185547, 15.563568115234375, 1200.9376220703125, 93.03096771240234, 9.18178939819336, 1200.9376220703125, 92.69577026367188, 4.902154922485352, 1200.9376220703125, 90.1539535522461, 3.6756248474121094, 1200.9376220703125, 85.85309600830078, 4.7830705642700195, 1200.9376220703125], [99.10711669921875, 0.6075906753540039, 1486.8751220703125, 98.61489868164062, 15.21969223022461, 1486.8751220703125], [98.48664855957031, 12.650136947631836, 1486.8751220703125, 99.79977416992188, 4.223116874694824, 1486.8751220703125, 109.09837341308594, 5.052194595336914, 1486.8751220703125, 108.6627197265625, 15.257577896118164, 1486.8751220703125, 99.94269561767578, 16.277549743652344, 1486.8751220703125, 98.74298858642578, 7.276721000671387, 1486.8751220703125], [112.73148345947266, -0.36093902587890625, 1715.6251220703125, 113.5895004272461, 15.857930183410645, 1715.6251220703125], [117.46672821044922, 10.469551086425781, 1830.0001220703125, 126.6060562133789, 10.151005744934082, 1830.0001220703125, 126.24947357177734, 6.501497268676758, 1830.0001220703125, 119.5928955078125, 4.698034286499023, 1830.0001220703125, 116.1819839477539, 10.626565933227539, 1830.0001220703125, 119.17646789550781, 15.796652793884277, 1830.0001220703125, 123.83345031738281, 16.532054901123047, 1830.0001220703125, 125.79936218261719, 15.332743644714355, 1830.0001220703125]], "curvesBox": [1.871698021888733, -0.5031337738037109, 126.6060562133789, 17.1132755279541]}, "frame": {}, "text": {"text": "Previewable", "lineHeight": 22, "boardSize": [141.8000030517578, 28], "textBox": [0, 0, 129.8000030517578, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 80, "char": "P", "curves": 2, "bounds": [1.350000023841858, 2, 600], "offset": [0, 16, 0], "scale": [8, 8, 0.28593751788139343], "dataStoredLocally": false}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [11.800000190734863, 16, 200.15626525878906], "scale": [8, 8, 0.28593751788139343], "dataStoredLocally": false}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [20, 16, 343.1250305175781], "scale": [8, 8, 0.28593751788139343], "dataStoredLocally": false}, {"key": 86, "char": "v", "curves": 2, "bounds": [1.350000023841858, 2, 300], "offset": [33.79999923706055, 16, 543.2813110351562], "scale": [8, 8, 0.28593751788139343], "dataStoredLocally": false}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [45.599998474121094, 16, 657.6563110351562], "scale": [8, 8, 0.28593751788139343], "dataStoredLocally": false}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [51, 16, 800.6250610351562], "scale": [8, 8, 0.28593751788139343], "dataStoredLocally": false}, {"key": 87, "char": "w", "curves": 4, "bounds": [2.25, 2, 500], "offset": [64.80000305175781, 16, 1000.7813110351562], "scale": [8, 8, 0.28593751788139343], "dataStoredLocally": false}, {"key": 65, "char": "a", "curves": 1, "bounds": [1.399999976158142, 2, 900], "offset": [83.80000305175781, 16, 1172.3438720703125], "scale": [8, 8, 0.28593751788139343], "dataStoredLocally": false}, {"key": 66, "char": "b", "curves": 2, "bounds": [1.7000000476837158, 2, 700], "offset": [96, 16, 1458.2813720703125], "scale": [8, 8, 0.28593751788139343], "dataStoredLocally": false}, {"key": 76, "char": "l", "curves": 1, "bounds": [0.550000011920929, 2, 300], "offset": [110.5999984741211, 16, 1687.0313720703125], "scale": [8, 8, 0.28593751788139343], "dataStoredLocally": false}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [116, 16, 1801.4063720703125], "scale": [8, 8, 0.28593751788139343], "dataStoredLocally": false}]}}, {"v": 4, "sessionId": "swot", "id": "dhwfjfo39hcb", "type": "ItemDrawingCurve", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [181.49996948242188, 2531.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [7, 278], "position": [181.49996948242188, 2531.000244140625]}, "aiData": {"CurveType": "unknown", "connections": [{"id": "dhwfjfo39hcc", "toNodeId": "", "end": false, "contact": [0, 0], "dir": [1, 0], "probability": 1, "shaftIndices": [0]}, {"id": "dhwfjfo39hcd", "toNodeId": "", "end": true, "contact": [799, 0], "dir": [-1, -0.0], "probability": 1, "shaftIndices": [0]}], "followers": [], "decorators": []}, "schedule": {"start": 0, "end": 0}, "curves": {"curves": [[0, 0, 0, 199.75, 0, 0, 399.5, 0, 0, 599.25, 0, 0, 799, 0, 0]], "curvesBox": [0, 0, 799, 0], "sketchControls": [[0, 2, 4]]}, "frame": {}, "text": {"boardSize": [12, 12]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}}, {"v": 4, "sessionId": "swot", "id": "dhwfjfo39hce", "type": "ItemDrawingCurve", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [580.5, 2281.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [406.0000305175781, 28], "position": [580.5, 2281.000244140625]}, "aiData": {"CurveType": "unknown", "connections": [{"id": "dhwfjfo39hcf", "toNodeId": "", "end": false, "contact": [0, 0], "dir": [0, 1], "probability": 1, "shaftIndices": [0]}, {"id": "hxpxnvo39hc0", "toNodeId": "", "end": true, "contact": [0, 500], "dir": [-0.0, -1], "probability": 1, "shaftIndices": [0]}], "followers": [], "decorators": []}, "schedule": {"start": 0, "end": 0}, "curves": {"curves": [[0, 0, 0, 0, 125, 0, 0, 250, 0, 0, 375, 0, 0, 500, 0]], "curvesBox": [0, 0, 0, 500], "sketchControls": [[0, 2, 4]]}, "frame": {}, "text": {"boardSize": [12, 12]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}}, {"v": 4, "sessionId": "text", "id": "hxpxnvo39hc1", "type": "ItemDrawingText", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [180.49996948242188, 2265.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [6, 12], "position": [180.49996948242188, 2265.000244140625]}, "aiData": {"decorators": []}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[2.1027824878692627, 0.967681884765625, 0, 2.0060224533081055, 5.453769683837891, 0, 2.8608298301696777, 16.541460037231445, 0, 12.253341674804688, 15.800923347473145, 0, 12.31946086883545, 5.718027114868164, 0, 12.818395614624023, 1.4493465423583984, 0], [22.472881317138672, 6.133495330810547, 400, 21.54442596435547, 4.466183662414551, 400, 16.744356155395508, 3.901102066040039, 400, 16.781728744506836, 7.871061325073242, 400, 22.50201416015625, 10.341700553894043, 400, 23.238189697265625, 15.258293151855469, 400, 18.48058319091797, 15.208961486816406, 400, 16.837568283081055, 12.633009910583496, 400], [26.351531982421875, 10.402047157287598, 850, 35.730133056640625, 10.325338363647461, 850, 35.27568817138672, 6.671015739440918, 850, 29.27397346496582, 4.490629196166992, 850, 25.292932510375977, 11.091032028198242, 850, 28.532934188842773, 15.941699028015137, 850, 33.43760681152344, 16.438980102539062, 850, 35.69464874267578, 15.580767631530762, 850], [41.93208312988281, 5.754952430725098, 1250, 42.127655029296875, 16.614864349365234, 1250], [42.28235626220703, 10.042698860168457, 1250, 42.647003173828125, 5.567775726318359, 1250, 45.91255187988281, 5.559167861938477, 1250], [57.76945114135742, 3.7862091064453125, 1700, 57.14899826049805, 14.756470680236816, 1700], [57.06699752807617, -0.2268505096435547, 1700, 57.79301071166992, 0.14113998413085938, 1700], [62.78245162963867, 3.7081985473632812, 2000, 63.19807815551758, 15.560685157775879, 2000], [63.45896530151367, 13.031428337097168, 2000, 63.2974967956543, 4.220185279846191, 2000, 69.67131042480469, 5.073284149169922, 2000, 70.75904846191406, 11.92497730255127, 2000, 70.55557250976562, 15.613961219787598, 2000], [93.14815521240234, 6.523191452026367, 2500, 88.6280288696289, 4.497886657714844, 2500, 84.45049285888672, 7.050275802612305, 2500, 83.29853057861328, 14.575372695922852, 2500, 89.29012298583984, 16.08185386657715, 2500, 93.18534088134766, 13.856245994567871, 2500], [102.74250793457031, 3.6992368698120117, 2900, 97.65046691894531, 4.557848930358887, 2900, 97.10263061523438, 15.953712463378906, 2900, 108.07264709472656, 14.876970291137695, 2900, 107.8790283203125, 3.457611083984375, 2900, 102.08294677734375, 4.6048994064331055, 2900], [111.70854949951172, 4.837306976318359, 3300, 111.77088165283203, 16.351171493530273, 3300], [111.24254608154297, 12.978508949279785, 3300, 111.74311065673828, 4.722237586975098, 3300, 119.11864471435547, 4.7218122482299805, 3300, 119.20482635498047, 13.341035842895508, 3300, 119.92574310302734, 15.887103080749512, 3300], [125.23690032958984, -0.5475234985351562, 3650, 125.8769760131836, 9.067336082458496, 3650, 125.79894256591797, 16.464773178100586, 3650, 128.4117431640625, 15.042322158813477, 3650], [123.77092742919922, 3.8047542572021484, 3650, 128.61041259765625, 4.0875396728515625, 3650], [132.24171447753906, 4.029516220092773, 4000, 131.53762817382812, 15.820566177368164, 4000], [131.73361206054688, 9.023590087890625, 4000, 133.35107421875, 5.511635780334473, 4000, 136.24862670898438, 3.82454776763916, 4000], [145.08035278320312, 5.719339370727539, 4300, 139.80384826660156, 4.641196250915527, 4300, 139.8440704345703, 16.11656379699707, 4300, 150.19297790527344, 15.874917030334473, 4300, 150.67723083496094, 4.803681373596191, 4300, 144.99266052246094, 5.059961318969727, 4300], [155.580322265625, -0.042057037353515625, 4700, 154.84568786621094, 15.071920394897461, 4700]], "curvesBox": [2.0060224533081055, -0.5475234985351562, 155.580322265625, 16.614864349365234]}, "frame": {}, "text": {"text": "User in control", "lineHeight": 22, "boardSize": [169.1999969482422, 28], "textBox": [0, 0, 157.1999969482422, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 85, "char": "U", "curves": 1, "bounds": [1.850000023841858, 2, 600], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 83, "char": "s", "curves": 1, "bounds": [1.100000023841858, 2, 700], "offset": [15.800000190734863, 16, 400], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [25.600000381469727, 16, 850], "scale": [8, 8, 0.5]}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [39.400001525878906, 16, 1250], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [47.60000228881836, 16, 1550], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [55.60000228881836, 16, 1700], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [61.000003814697266, 16, 2000], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [74, 16, 2350], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [82, 16, 2500], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [95, 16, 2900], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [109.5999984741211, 16, 3300], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [122.5999984741211, 16, 3650], "scale": [8, 8, 0.5]}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [130, 16, 4000], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [138.1999969482422, 16, 4300], "scale": [8, 8, 0.5]}, {"key": 76, "char": "l", "curves": 1, "bounds": [0.550000011920929, 2, 300], "offset": [152.8000030517578, 16, 4700], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "text", "id": "922xezo39i4q", "type": "ItemDrawingText", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [836.9000244140625, 2259.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [662.4000244140625, 6], "position": [836.9000244140625, 2259.000244140625]}, "aiData": {"decorators": []}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[2.5572848320007324, 0.8170919418334961, 0, 2.9721665382385254, 16.20979118347168, 45.751312255859375], [2.783691883087158, 1.1024513244628906, 86.75775146484375, 12.804142951965332, 16.01993751525879, 141.74148559570312], [12.873257637023926, 16.436752319335938, 142.54608154296875, 13.223677635192871, 0.7964363098144531, 194.93643188476562], [23.341474533081055, 3.399672508239746, 350, 17.896230697631836, 4.316905975341797, 401.6073913574219, 18.22211456298828, 15.833001136779785, 472.1582946777344, 29.236431121826172, 15.235933303833008, 511.1819763183594, 28.850692749023438, 3.938680648803711, 551.95166015625, 24.02915382385254, 3.789055824279785, 621.5870361328125], [34.08563232421875, 0.1669292449951172, 750, 33.360530853271484, 8.410198211669922, 757.9570922851562, 34.30491638183594, 15.962523460388184, 777.7149658203125, 36.714778900146484, 15.139440536499023, 829.1594848632812], [31.84477424621582, 3.907486915588379, 884.1747436523438, 37.257652282714844, 4.016956329345703, 937.0509033203125], [55.3990478515625, 16.158445358276367, 1250, 55.17837905883789, 9.735936164855957, 1297.1175537109375, 47.40140914916992, 9.361884117126465, 1339.6026611328125, 47.96410369873047, 16.09526252746582, 1383.321533203125, 54.99018478393555, 16.70317268371582, 1449.35595703125, 56.20146179199219, 10.133893966674805, 1496.53076171875, 55.498985290527344, 6.7428083419799805, 1542.216064453125, 52.63420104980469, 3.9550552368164062, 1582.631591796875, 47.77119064331055, 6.016400337219238, 1640.7647705078125], [60.68886184692383, 4.433993339538574, 1800, 61.097145080566406, 16.739389419555664, 1800], [61.10728073120117, 12.911360740661621, 1800, 61.1359977722168, 4.846216201782227, 1800, 67.26849365234375, 5.045299530029297, 1800, 68.79107666015625, 12.932332992553711, 1800, 68.7892074584961, 15.920562744140625, 1800], [73.25528717041016, 5.119372367858887, 2150, 73.65744018554688, 16.875898361206055, 2150], [73.77379608154297, 13.141138076782227, 2150, 74.32469177246094, 5.223519325256348, 2150, 80.58403778076172, 5.019652366638184, 2150, 82.00366973876953, 13.423993110656738, 2150, 81.51367950439453, 16.880184173583984, 2150], [91.08611297607422, 4.883045196533203, 2500, 86.76280212402344, 4.495509147644043, 2500, 85.80994415283203, 16.08677864074707, 2500, 96.99126434326172, 16.215246200561523, 2500, 96.48896789550781, 4.3555402755737305, 2500, 90.87063598632812, 5.4604034423828125, 2500], [99.54110717773438, 3.8539552688598633, 2900, 103.92919158935547, 16.035022735595703, 2900], [108.63652801513672, 3.8553009033203125, 2900, 102.88294982910156, 19.845687866210938, 2900], [113.64209747314453, 5.636407852172852, 3200, 114.22653198242188, 16.896249771118164, 3200], [113.8530502319336, 2.0281715393066406, 3200, 114.47408294677734, 1.339447021484375, 3200], [119.26768493652344, 5.667050361633301, 3500, 118.94157409667969, 16.74940299987793, 3500], [119.27381896972656, 12.970928192138672, 3500, 120.01643371582031, 5.920707702636719, 3500, 125.68024444580078, 5.044401168823242, 3500, 127.28974151611328, 13.159261703491211, 3500, 126.55723571777344, 15.853904724121094, 3500], [140.58204650878906, 12.955262184143066, 3850, 140.1136474609375, 5.950440406799316, 3850, 130.66148376464844, 4.856083869934082, 3850, 131.08966064453125, 17.060943603515625, 3850, 140.65371704101562, 16.963523864746094, 3850, 140.93701171875, 7.894386291503906, 3850], [140.7360382080078, 5.542492866516113, 3850, 140.733642578125, 14.582630157470703, 3850, 141.38778686523438, 18.29279136657715, 3850, 137.14410400390625, 21.13882827758789, 3850, 131.73106384277344, 20.0521183013916, 3850]], "curvesBox": [2.5572848320007324, 0.1669292449951172, 141.38778686523438, 21.13882827758789]}, "frame": {}, "text": {"text": "Not annoying", "lineHeight": 22, "boardSize": [155.60000610351562, 28], "textBox": [0, 0, 143.60000610351562, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 78, "char": "N", "curves": 3, "bounds": [1.9500000476837158, 2, 500], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [16.600000381469727, 16, 350], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [31.200000762939453, 16, 750], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [38.60000228881836, 16, 1100], "scale": [8, 8, 0.5]}, {"key": 65, "char": "a", "curves": 1, "bounds": [1.399999976158142, 2, 900], "offset": [46.60000228881836, 16, 1250], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [58.80000305175781, 16, 1800], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [71.80000305175781, 16, 2150], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [84.80000305175781, 16, 2500], "scale": [8, 8, 0.5]}, {"key": 89, "char": "y", "curves": 2, "bounds": [1.399999976158142, 2, 400], "offset": [99.4000015258789, 16, 2900], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [111.5999984741211, 16, 3200], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [117, 16, 3500], "scale": [8, 8, 0.5]}, {"key": 71, "char": "g", "curves": 2, "bounds": [1.7000000476837158, 2, 1000], "offset": [130, 16, 3850], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "text", "id": "922xezo39i4r", "type": "ItemDrawingText", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [180.49996948242188, 2765.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [6, 512], "position": [180.49996948242188, 2765.000244140625]}, "aiData": {"decorators": []}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[1.8473546504974365, 0.29695987701416016, 83.42637634277344, 1.9297987222671509, 14.963176727294922, 117.36299133300781], [2.049652338027954, -0.24053955078125, 162.89163208007812, 9.210441589355469, 0.24285316467285156, 230.021728515625, 9.84584903717041, 8.372632026672363, 269.6588439941406, 1.9546746015548706, 8.68062973022461, 327.039794921875], [14.89116382598877, 4.845410346984863, 400, 14.495549201965332, 15.742459297180176, 400], [13.775887489318848, 8.387689590454102, 400, 15.138745307922363, 4.480429649353027, 400, 18.118350982666016, 3.5403709411621094, 400], [20.62299346923828, 11.233798027038574, 700, 31.161354064941406, 11.35311508178711, 700, 30.07616424560547, 5.577315330505371, 700, 23.006942749023438, 5.100161552429199, 700, 20.404380798339844, 11.373151779174805, 700, 22.891372680664062, 16.879520416259766, 700, 27.68000030517578, 16.103553771972656, 700, 30.489715576171875, 14.992757797241211, 700], [35.13539505004883, 5.939248085021973, 1100, 39.026126861572266, 16.09361457824707, 1100], [39.57289505004883, 16.342866897583008, 1100, 44.26152420043945, 5.835687637329102, 1100], [46.90117645263672, 4.700737953186035, 1350, 47.732269287109375, 16.065048217773438, 1350], [46.94718933105469, 1.0027523040771484, 1350, 47.14933776855469, 0.8222131729125977, 1350], [53.39854431152344, 9.747714042663574, 1650, 63.1142578125, 10.323001861572266, 1650, 62.41459655761719, 4.73127555847168, 1650, 55.59892272949219, 2.85762882232666, 1650, 52.49345397949219, 9.468915939331055, 1650, 54.394744873046875, 15.722750663757324, 1650, 60.680816650390625, 15.066825866699219, 1650, 62.66331481933594, 13.88945484161377, 1650], [65.30824279785156, 4.706728935241699, 2050, 69.48829650878906, 16.911029815673828, 2050], [68.98423767089844, 16.21123695373535, 2050, 73.81859588623047, 6.01439094543457, 2050], [72.80589294433594, 5.403779983520508, 2050, 77.40924835205078, 16.65974998474121, 2050], [77.60101318359375, 17.078876495361328, 2050, 80.79624938964844, 5.418160438537598, 2050], [93.47123718261719, 16.708398818969727, 2400, 92.54670715332031, 9.993715286254883, 2400, 85.49021911621094, 10.382485389709473, 2400, 86.36347961425781, 16.266944885253906, 2400, 92.71116638183594, 16.93146324157715, 2400, 94.06732177734375, 9.52410888671875, 2400, 93.72465515136719, 7.017952919006348, 2400, 90.38459777832031, 4.658342361450195, 2400, 85.85685729980469, 5.666586875915527, 2400], [97.69972229003906, -1.0416908264160156, 2950, 98.30094909667969, 15.203779220581055, 2950], [97.52964782714844, 12.13676643371582, 2950, 98.60658264160156, 4.615198135375977, 2950, 107.47828674316406, 4.317647933959961, 2950, 107.48103332519531, 15.166994094848633, 2950, 98.80754089355469, 15.396563529968262, 2950, 97.31103515625, 7.51955509185791, 2950], [112.88483428955078, 0.4144010543823242, 3400, 112.0181655883789, 16.12551498413086, 3400], [117.16743469238281, 9.821100234985352, 3650, 126.67604064941406, 9.592463493347168, 3650, 125.46505737304688, 4.876830101013184, 3650, 119.71351623535156, 4.288331031799316, 3650, 116.52381896972656, 10.317373275756836, 3650, 118.00942993164062, 15.895774841308594, 3650, 123.66624450683594, 15.617867469787598, 3650, 126.75660705566406, 14.61185359954834, 3650]], "curvesBox": [1.8473546504974365, -1.0416908264160156, 126.75660705566406, 17.078876495361328]}, "frame": {}, "text": {"text": "Previewable", "lineHeight": 22, "boardSize": [140.8000030517578, 28], "textBox": [0, 0, 128.8000030517578, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 80, "char": "P", "curves": 2, "bounds": [1.350000023841858, 2, 600], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [11.800000190734863, 16, 400], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [20, 16, 700], "scale": [8, 8, 0.5]}, {"key": 86, "char": "v", "curves": 2, "bounds": [1.350000023841858, 2, 300], "offset": [33.79999923706055, 16, 1100], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [45.599998474121094, 16, 1350], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [51, 16, 1650], "scale": [8, 8, 0.5]}, {"key": 87, "char": "w", "curves": 4, "bounds": [2.25, 2, 500], "offset": [64.80000305175781, 16, 2050], "scale": [8, 8, 0.5]}, {"key": 65, "char": "a", "curves": 1, "bounds": [1.399999976158142, 2, 900], "offset": [83.80000305175781, 16, 2400], "scale": [8, 8, 0.5]}, {"key": 66, "char": "b", "curves": 2, "bounds": [1.7000000476837158, 2, 700], "offset": [96, 16, 2950], "scale": [8, 8, 0.5]}, {"key": 76, "char": "l", "curves": 1, "bounds": [0.550000011920929, 2, 300], "offset": [110.5999984741211, 16, 3400], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [116, 16, 3650], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "text", "id": "922xezo39i4s", "type": "ItemDrawingText", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [899.5, 2764.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [725, 511], "position": [899.5, 2764.000244140625]}, "aiData": {"decorators": []}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[1.976001262664795, 0.6036319732666016, 39.76810836791992, 2.1783196926116943, 15.8482027053833, 88.6243896484375], [2.36291766166687, -0.034427642822265625, 147.89280700683594, 8.743789672851562, 1.0279836654663086, 211.42160034179688], [1.662346601486206, 6.966448783874512, 261.8784484863281, 7.548876762390137, 8.091976165771484, 294.2984924316406], [2.6042957305908203, 15.272340774536133, 340.99981689453125, 8.973804473876953, 15.445612907409668, 388.6573181152344], [22.57294273376465, 11.621576309204102, 500, 22.189496994018555, 4.863675117492676, 500, 13.402700424194336, 4.452396392822266, 500, 13.420019149780273, 15.68388843536377, 500, 21.635793685913086, 15.101259231567383, 500, 23.256200790405273, 7.6332807540893555, 500], [22.915555953979492, -0.7395133972167969, 500, 22.55195426940918, 15.655701637268066, 500], [28.93914794921875, 4.111620903015137, 1000, 28.92999267578125, 15.866792678833008, 1000], [28.420516967773438, -0.2780265808105469, 1000, 28.995162963867188, 0.9943485260009766, 1000], [33.804954528808594, -0.701934814453125, 1300, 34.03337860107422, 9.07990550994873, 1300, 34.619651794433594, 16.080123901367188, 1300, 37.868072509765625, 16.04218292236328, 1300], [32.26448059082031, 5.008808135986328, 1300, 37.6583251953125, 4.200617790222168, 1300], [48.473934173583984, 16.61786460876465, 1650, 47.05451583862305, 9.272789001464844, 1650, 40.46965408325195, 9.411749839782715, 1650, 40.16364669799805, 16.99323272705078, 1650, 47.91598129272461, 15.749324798583984, 1650, 48.79288864135742, 10.153177261352539, 1650, 48.60544967651367, 5.839224815368652, 1650, 44.93124008178711, 4.9518280029296875, 1650, 40.78561019897461, 6.545625686645508, 1650], [52.99676513671875, 0.010772705078125, 2200, 53.57696533203125, 15.583691596984863, 2200], [53.65104675292969, 12.43757438659668, 2200, 53.69844055175781, 4.416288375854492, 2200, 63.3212890625, 4.45361328125, 2200, 63.18031311035156, 15.606478691101074, 2200, 53.99915313720703, 16.471593856811523, 2200, 52.93067169189453, 7.732878684997559, 2200], [67.72244262695312, -0.6250572204589844, 2650, 68.09879302978516, 15.138468742370605, 2650], [72.10330963134766, 10.358560562133789, 2900, 82.48692321777344, 10.568086624145508, 2900, 80.90673828125, 6.040655136108398, 2900, 74.66329956054688, 3.5770606994628906, 2900, 71.5135498046875, 11.108604431152344, 2900, 73.96996307373047, 15.576269149780273, 2900, 80.27244567871094, 16.931522369384766, 2900, 82.33946228027344, 14.730640411376953, 2900]], "curvesBox": [1.662346601486206, -0.7395133972167969, 82.48692321777344, 16.99323272705078]}, "frame": {}, "text": {"text": "Editable", "lineHeight": 22, "boardSize": [95.80000305175781, 28], "textBox": [0, 0, 83.80000305175781, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 69, "char": "E", "curves": 4, "bounds": [1.2999999523162842, 2, 800], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 68, "char": "d", "curves": 2, "bounds": [1.7000000476837158, 2, 800], "offset": [11.399999618530273, 16, 500], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [26, 16, 1000], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [31.399999618530273, 16, 1300], "scale": [8, 8, 0.5]}, {"key": 65, "char": "a", "curves": 1, "bounds": [1.399999976158142, 2, 900], "offset": [38.79999923706055, 16, 1650], "scale": [8, 8, 0.5]}, {"key": 66, "char": "b", "curves": 2, "bounds": [1.7000000476837158, 2, 700], "offset": [51, 16, 2200], "scale": [8, 8, 0.5]}, {"key": 76, "char": "l", "curves": 1, "bounds": [0.550000011920929, 2, 300], "offset": [65.5999984741211, 16, 2650], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [71, 16, 2900], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "text", "id": "4jrvl7oxgqof", "type": "ItemDrawingText", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [251.49996948242188, 2593.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [77, 340], "position": [251.49996948242188, 2593.000244140625]}, "aiData": {"decorators": []}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[13.504121780395508, 1.9530630111694336, 0, 9.826227188110352, -0.6226902008056641, 0, 1.6301233768463135, 2.773228645324707, 0, 1.9593276977539062, 13.01990032196045, 0, 9.808839797973633, 16.087947845458984, 0, 13.953948020935059, 13.944231986999512, 0], [14.772127151489258, 13.72182559967041, 0, 14.358181953430176, 8.756442070007324, 0], [14.70743179321289, 9.746662139892578, 0, 9.723462104797363, 9.477449417114258, 0], [19.33156967163086, 4.69791316986084, 0, 20.014556884765625, 15.447425842285156, 0], [19.457571029663086, 8.838506698608398, 0, 20.564167022705078, 5.092502593994141, 0, 24.043880462646484, 4.239109992980957, 0], [34.86113739013672, 16.129976272583008, 0, 34.33979034423828, 8.626667022705078, 0, 26.946182250976562, 8.357576370239258, 0, 27.26007652282715, 16.233909606933594, 0, 34.514503479003906, 16.062734603881836, 0, 34.79609680175781, 8.557914733886719, 0, 35.11998748779297, 5.254360198974609, 0, 31.206462860107422, 3.476241111755371, 0, 26.864450454711914, 5.024664878845215, 0], [39.31013870239258, 4.612399101257324, 0, 39.17340087890625, 20.294940948486328, 0], [39.87675857543945, 12.40932846069336, 0, 41.05794143676758, 4.294732093811035, 0, 49.78260803222656, 4.613950729370117, 0, 49.31427764892578, 15.612533569335938, 0, 40.999412536621094, 16.3508243560791, 0, 39.29713821411133, 7.277237892150879, 0], [54.788902282714844, 0.07158565521240234, 0, 54.508968353271484, 16.30023765563965, 0], [54.467681884765625, 12.399374008178711, 0, 55.14984130859375, 4.508801460266113, 0, 60.86256408691406, 5.132449150085449, 0, 61.625850677490234, 12.987710952758789, 0, 62.14191818237305, 15.948124885559082, 0], [67.8027572631836, 4.795238494873047, 0, 68.3427734375, 15.279385566711426, 0], [68.17452239990234, 0.8397665023803711, 0, 67.95116424560547, -0.009943008422851562, 0], [80.54179382324219, 7.184360504150391, 0, 77.0169906616211, 5.445134162902832, 0, 71.86558532714844, 8.009666442871094, 0, 72.48433685302734, 13.779973030090332, 0, 76.67724609375, 17.539134979248047, 0, 80.96379852294922, 14.768318176269531, 0], [91.03483581542969, 6.029933929443359, 0, 88.39302825927734, 4.011475563049316, 0, 84.30870056152344, 4.107076644897461, 0, 85.33275604248047, 7.291269302368164, 0, 91.44415283203125, 10.950003623962402, 0, 90.92420196533203, 15.67063045501709, 0, 86.3226318359375, 16.338008880615234, 0, 84.069091796875, 12.36004638671875, 0], [107.68710327148438, 0.43444156646728516, 0, 106.71158599853516, 0.1666421890258789, 0, 104.81979370117188, 2.1557111740112305, 0, 103.7129898071289, 6.228511810302734, 0, 103.72227478027344, 16.04733657836914, 0], [101.77904510498047, 5.092348098754883, 0, 108.08493041992188, 5.054954528808594, 0], [110.16476440429688, 6.273139953613281, 0, 111.05492401123047, 16.633285522460938, 0], [109.95529174804688, 9.724441528320312, 0, 110.72423553466797, 5.707209587097168, 0, 114.97721862792969, 6.244729995727539, 0], [123.93494415283203, 4.243659019470215, 0, 118.50879669189453, 4.569882392883301, 0, 118.98451232910156, 15.768750190734863, 0, 129.6143341064453, 16.379976272583008, 0, 129.38531494140625, 4.275193214416504, 0, 124.06278991699219, 4.818082809448242, 0], [132.90003967285156, 4.539030075073242, 0, 132.72634887695312, 15.970966339111328, 0], [133.19216918945312, 12.649202346801758, 0, 133.43621826171875, 4.634563446044922, 0, 139.5372314453125, 4.6770219802856445, 0, 141.0586395263672, 11.440162658691406, 0, 140.51881408691406, 15.863580703735352, 0], [141.24537658691406, 11.741598129272461, 0, 141.19285583496094, 4.262128829956055, 0, 147.79232788085938, 4.676207542419434, 0, 148.7759246826172, 12.724918365478516, 0, 148.43211364746094, 14.83035659790039, 0], [162.8221435546875, 0.22980976104736328, 0, 162.4188690185547, 16.575876235961914, 0], [162.8636474609375, 13.622953414916992, 0, 163.3711395263672, 4.4530029296875, 0, 171.64869689941406, 5.086091995239258, 0, 172.09959411621094, 16.6240291595459, 0, 162.62973022460938, 16.899436950683594, 0, 162.96237182617188, 7.834953308105469, 0], [175.63076782226562, 5.365932464599609, 0, 175.98611450195312, 16.618837356567383, 0, 184.12545776367188, 16.316190719604492, 0, 184.10546875, 8.736103057861328, 0], [184.38870239257812, 6.036649703979492, 0, 184.1074981689453, 17.178640365600586, 0], [189.5964813232422, -0.6290016174316406, 0, 189.14797973632812, 15.766695976257324, 0], [195.1084747314453, 0.17654895782470703, 0, 194.83834838867188, 15.914802551269531, 0], [199.84164428710938, 10.355952262878418, 0, 209.24143981933594, 9.974783897399902, 0, 208.67892456054688, 5.358340263366699, 0, 202.33670043945312, 4.786036491394043, 0, 199.18006896972656, 10.709260940551758, 0, 201.60360717773438, 15.832758903503418, 0, 206.86473083496094, 15.634556770324707, 0, 209.1804656982422, 15.549612998962402, 0], [213.48695373535156, -0.1582508087158203, 0, 214.63668823242188, 9.15273666381836, 0, 214.4271697998047, 17.38603401184082, 0, 216.748046875, 16.545066833496094, 0], [212.61073303222656, 5.553226470947266, 0, 216.9039764404297, 5.405832290649414, 0], [229.6851806640625, 5.491477012634277, 0, 230.06393432617188, 20.797719955444336, 0], [230.41329956054688, 13.65079116821289, 0, 230.41163635253906, 4.865684509277344, 0, 239.70594787597656, 4.46504020690918, 0, 239.57955932617188, 16.758819580078125, 0, 231.1631317138672, 15.765413284301758, 0, 229.85414123535156, 8.681558609008789, 0], [248.48040771484375, 5.379575729370117, 0, 242.72372436523438, 4.954936981201172, 0, 242.92282104492188, 16.07356071472168, 0, 253.51698303222656, 15.914630889892578, 0, 253.11415100097656, 5.828640937805176, 0, 248.19473266601562, 5.091238021850586, 0], [257.94671630859375, 4.327869415283203, 0, 258.843505859375, 16.20676612854004, 0], [258.46612548828125, 0.8444099426269531, 0, 258.4139099121094, -0.15053939819335938, 0], [263.34326171875, 4.939492225646973, 0, 263.9881591796875, 14.968823432922363, 0], [264.0506286621094, 12.270868301391602, 0, 265.0501708984375, 4.628068923950195, 0, 270.9969482421875, 4.441391944885254, 0, 271.2012634277344, 12.192655563354492, 0, 272.3709716796875, 15.60621452331543, 0], [277.0152587890625, -1.3349666595458984, 0, 276.97442626953125, 8.571208953857422, 0, 276.873779296875, 15.593113899230957, 0, 280.2709655761719, 15.394255638122559, 0], [275.0512390136719, 4.792760848999023, 0, 280.51031494140625, 3.486661911010742, 0], [290.04681396484375, 6.06014347076416, 0, 287.6970520019531, 5.059356689453125, 0, 284.45355224609375, 4.882723808288574, 0, 283.81427001953125, 9.164170265197754, 0, 290.4963073730469, 11.492388725280762, 0, 289.6688537597656, 15.220905303955078, 0, 285.2225646972656, 16.54567527770996, 0, 283.271240234375, 14.177362442016602, 0]], "curvesBox": [1.6301233768463135, -1.3349666595458984, 290.4963073730469, 20.797719955444336]}, "frame": {}, "text": {"text": "Graphics from bullet points", "lineHeight": 22, "boardSize": [302.9999694824219, 28], "textBox": [0, 0, 290.9999694824219, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 71, "char": "G", "curves": 3, "bounds": [2.049999952316284, 2, 800], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [17.399999618530273, 16, 500], "scale": [8, 8, 0.5]}, {"key": 65, "char": "a", "curves": 1, "bounds": [1.399999976158142, 2, 900], "offset": [25.599998474121094, 16, 800], "scale": [8, 8, 0.5]}, {"key": 80, "char": "p", "curves": 2, "bounds": [1.7000000476837158, 2, 700], "offset": [37.79999923706055, 16, 1350], "scale": [8, 8, 0.5]}, {"key": 72, "char": "h", "curves": 2, "bounds": [1.5, 2, 600], "offset": [52.400001525878906, 16, 1800], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [65.4000015258789, 16, 2200], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [70.80000305175781, 16, 2500], "scale": [8, 8, 0.5]}, {"key": 83, "char": "s", "curves": 1, "bounds": [1.100000023841858, 2, 700], "offset": [83.80000305175781, 16, 2900], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [93.60000610351562, 16, 3350], "scale": [8, 8, 0.5]}, {"key": 70, "char": "f", "curves": 2, "bounds": [0.75, 2, 500], "offset": [101.60000610351562, 16, 3500], "scale": [8, 8, 0.5]}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [108.60000610351562, 16, 3850], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [116.80000305175781, 16, 4150], "scale": [8, 8, 0.5]}, {"key": 77, "char": "m", "curves": 3, "bounds": [2.4000000953674316, 2, 800], "offset": [131.40000915527344, 16, 4550], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [151.60000610351562, 16, 5050], "scale": [8, 8, 0.5]}, {"key": 66, "char": "b", "curves": 2, "bounds": [1.7000000476837158, 2, 700], "offset": [159.60000610351562, 16, 5200], "scale": [8, 8, 0.5]}, {"key": 85, "char": "u", "curves": 2, "bounds": [1.5, 2, 500], "offset": [174.20001220703125, 16, 5650], "scale": [8, 8, 0.5]}, {"key": 76, "char": "l", "curves": 1, "bounds": [0.550000011920929, 2, 300], "offset": [187.20001220703125, 16, 6000], "scale": [8, 8, 0.5]}, {"key": 76, "char": "l", "curves": 1, "bounds": [0.550000011920929, 2, 300], "offset": [192.60000610351562, 16, 6250], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [198, 16, 6500], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [211.8000030517578, 16, 6900], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [219.1999969482422, 16, 7250], "scale": [8, 8, 0.5]}, {"key": 80, "char": "p", "curves": 2, "bounds": [1.7000000476837158, 2, 700], "offset": [227.1999969482422, 16, 7400], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [241.8000030517578, 16, 7850], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [256.3999938964844, 16, 8250], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [261.79998779296875, 16, 8550], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [274.79998779296875, 16, 8900], "scale": [8, 8, 0.5]}, {"key": 83, "char": "s", "curves": 1, "bounds": [1.100000023841858, 2, 700], "offset": [282.1999816894531, 16, 9250], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "text", "id": "vb2x7vp7ga2p", "type": "ItemDrawingText", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [315.2999572753906, 2402.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [140.79998779296875, 149], "position": [315.2999572753906, 2402.000244140625]}, "aiData": {"decorators": []}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[9.857131004333496, 3.622943878173828, 0, 8.080415725708008, 0.41968631744384766, 0, 2.7924962043762207, 2.1868419647216797, 0, 3.1077702045440674, 6.17166805267334, 0, 10.081212043762207, 9.306852340698242, 0, 9.413564682006836, 15.470626831054688, 0, 3.1734466552734375, 16.90118408203125, 0, 1.4309104681015015, 13.126626014709473, 0], [13.172348022460938, 6.055676460266113, 0, 13.355071067810059, 17.162492752075195, 0], [13.495258331298828, 12.958721160888672, 0, 14.556848526000977, 5.691867828369141, 0, 20.086240768432617, 6.004072189331055, 0, 21.65734100341797, 13.339668273925781, 0, 21.575580596923828, 16.22285270690918, 0], [21.60468292236328, 13.943443298339844, 0, 21.857667922973633, 5.802895545959473, 0, 27.589733123779297, 4.651706695556641, 0, 29.026935577392578, 13.592866897583008, 0, 28.422935485839844, 16.193981170654297, 0], [41.557456970214844, 14.783712387084961, 0, 40.42231750488281, 8.373244285583496, 0, 33.63087463378906, 8.350746154785156, 0, 33.56679916381836, 15.840337753295898, 0, 40.963802337646484, 15.282066345214844, 0, 41.89004898071289, 8.105091094970703, 0, 41.814598083496094, 4.153792381286621, 0, 38.63816833496094, 2.6318540573120117, 0, 34.19425582885742, 5.462315559387207, 0], [46.690513610839844, 4.096468925476074, 0, 47.13258361816406, 15.751763343811035, 0], [47.08871078491211, 9.501056671142578, 0, 47.74582290649414, 5.07088565826416, 0, 50.600242614746094, 3.945209503173828, 0], [55.983402252197266, -0.3524017333984375, 0, 55.76313781738281, 9.183188438415527, 0, 55.9837646484375, 15.640864372253418, 0, 59.07912826538086, 15.80893325805664, 0], [54.02925109863281, 5.411032676696777, 0, 59.385215759277344, 5.369527816772461, 0], [75.10395050048828, 6.327627182006836, 0, 74.00054931640625, 4.977653503417969, 0, 69.83927917480469, 5.389804840087891, 0, 69.27507019042969, 9.07695198059082, 0, 76.02896118164062, 11.106054306030273, 0, 75.79977416992188, 15.770264625549316, 0, 70.49266815185547, 17.248062133789062, 0, 69.4801254272461, 13.361407279968262, 0], [80.30366516113281, 10.410152435302734, 0, 89.57383728027344, 10.651945114135742, 0, 88.58671569824219, 6.295932769775391, 0, 82.02433013916016, 5.061810493469238, 0, 79.32410430908203, 10.210514068603516, 0, 80.86167907714844, 16.161588668823242, 0, 86.59453582763672, 16.810100555419922, 0, 89.03606414794922, 15.323857307434082, 0], [94.10943603515625, 0.4250526428222656, 0, 93.70968627929688, 16.196155548095703, 0], [98.23812103271484, 10.523712158203125, 0, 108.18894958496094, 10.209878921508789, 0, 107.12095642089844, 5.848719596862793, 0, 101.03800201416016, 3.262500762939453, 0, 97.52893829345703, 9.306888580322266, 0, 99.57684326171875, 15.938218116760254, 0, 105.96611785888672, 15.24864387512207, 0, 107.81224822998047, 14.30146312713623, 0], [121.1895751953125, 5.3247222900390625, 0, 117.34403991699219, 5.081724166870117, 0, 112.32693481445312, 7.206260681152344, 0, 111.84461975097656, 13.960856437683105, 0, 116.44048309326172, 15.92778491973877, 0, 121.0745620727539, 14.252876281738281, 0], [126.24862670898438, 0.6710433959960938, 0, 126.76497650146484, 9.892433166503906, 0, 126.04816436767578, 16.323747634887695, 0, 130.06629943847656, 16.10220718383789, 0], [124.8438720703125, 4.9064226150512695, 0, 129.3441925048828, 4.853278160095215, 0], [134.42552185058594, 4.757753372192383, 0, 133.8142852783203, 15.92235279083252, 0], [134.18003845214844, 1.4717731475830078, 0, 134.02413940429688, 0.7853231430053711, 0], [143.35784912109375, 4.477529525756836, 0, 138.1981201171875, 4.514547348022461, 0, 138.14291381835938, 16.749832153320312, 0, 149.7238006591797, 16.111474990844727, 0, 148.96165466308594, 4.967268943786621, 0, 143.92701721191406, 5.515093803405762, 0], [153.79310607910156, 4.241538047790527, 0, 154.0033721923828, 15.986278533935547, 0], [153.956787109375, 12.633397102355957, 0, 154.64292907714844, 4.428752899169922, 0, 160.47232055664062, 4.778838157653809, 0, 161.97164916992188, 11.94880485534668, 0, 162.17742919921875, 15.346535682678223, 0]], "curvesBox": [1.4309104681015015, -0.3524017333984375, 162.17742919921875, 17.248062133789062]}, "frame": {}, "text": {"text": "Smart selection", "lineHeight": 22, "boardSize": [175.40000915527344, 28], "textBox": [0, 0, 163.40000915527344, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 83, "char": "S", "curves": 1, "bounds": [1.399999976158142, 2, 800], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 77, "char": "m", "curves": 3, "bounds": [2.4000000953674316, 2, 800], "offset": [12.199999809265137, 16, 500], "scale": [8, 8, 0.5]}, {"key": 65, "char": "a", "curves": 1, "bounds": [1.399999976158142, 2, 900], "offset": [32.400001525878906, 16, 1000], "scale": [8, 8, 0.5]}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [44.60000228881836, 16, 1550], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [52.80000305175781, 16, 1850], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [60.20000457763672, 16, 2200], "scale": [8, 8, 0.5]}, {"key": 83, "char": "s", "curves": 1, "bounds": [1.100000023841858, 2, 700], "offset": [68.20000457763672, 16, 2350], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [78.00000762939453, 16, 2800], "scale": [8, 8, 0.5]}, {"key": 76, "char": "l", "curves": 1, "bounds": [0.550000011920929, 2, 300], "offset": [91.80001068115234, 16, 3200], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [97.20001220703125, 16, 3450], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [111.00001525878906, 16, 3850], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [124.00001525878906, 16, 4250], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [131.40000915527344, 16, 4600], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [136.8000030517578, 16, 4900], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [151.40000915527344, 16, 5300], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "text", "id": "145g6ujphaa6i", "type": "ItemDrawingText", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [711.4998779296875, 2341.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [536.9998779296875, 88], "position": [711.4998779296875, 2341.000244140625]}, "aiData": {"decorators": []}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[1.7999868392944336, 15.504899978637695, 0, 7.144136428833008, -0.6077136993408203, 0], [7.100146293640137, 0.4895181655883789, 0, 12.973445892333984, 14.592001914978027, 0], [3.7639009952545166, 9.316559791564941, 0, 10.722635269165039, 8.34445858001709, 0], [17.475879669189453, 4.279386520385742, 0, 17.97255516052246, 15.347832679748535, 0, 24.640060424804688, 16.512454986572266, 0, 24.808589935302734, 7.379763603210449, 0], [25.372371673583984, 4.996417045593262, 0, 24.446168899536133, 15.505428314208984, 0], [29.526473999023438, -0.5354652404785156, 0, 30.110210418701172, 8.856953620910645, 0, 29.185089111328125, 16.085512161254883, 0, 33.16883850097656, 15.688722610473633, 0], [27.192466735839844, 5.009564399719238, 0, 33.516910552978516, 4.000162124633789, 0], [42.37114334106445, 4.7214508056640625, 0, 36.25799560546875, 4.962582588195801, 0, 37.36359786987305, 16.13041877746582, 0, 47.62727737426758, 15.850746154785156, 0, 47.749698638916016, 5.329967498779297, 0, 41.54717254638672, 4.803369522094727, 0], [50.85873794555664, 9.981887817382812, 0, 55.77685546875, 9.596179008483887, 0], [68.60511779785156, 5.616127967834473, 0, 63.86902618408203, 4.581411361694336, 0, 58.8592643737793, 7.033062934875488, 0, 59.1278076171875, 12.648200988769531, 0, 63.88759994506836, 16.542089462280273, 0, 67.59290313720703, 13.783571243286133, 0], [77.2984619140625, 4.824888229370117, 0, 72.1197509765625, 3.994220733642578, 0, 72.74767303466797, 15.405248641967773, 0, 82.87431335449219, 14.966394424438477, 0, 82.32432556152344, 4.6743011474609375, 0, 77.99400329589844, 4.404382705688477, 0], [87.15121459960938, 5.2746782302856445, 0, 87.1554946899414, 17.23164939880371, 0], [87.01688385009766, 13.88549518585205, 0, 88.046875, 5.15067195892334, 0, 95.05891418457031, 4.820662498474121, 0, 94.94905090332031, 13.742587089538574, 0, 95.78636932373047, 17.2530517578125, 0], [100.28888702392578, 5.265296936035156, 0, 100.89208221435547, 16.78900146484375, 0], [100.58251190185547, 13.979996681213379, 0, 100.8304214477539, 5.004289627075195, 0, 107.81005096435547, 6.203278541564941, 0, 108.29374694824219, 13.255767822265625, 0, 108.59558868408203, 16.8775691986084, 0], [112.71866607666016, 9.339399337768555, 0, 122.22529602050781, 9.844331741333008, 0, 121.61642456054688, 5.441213607788086, 0, 115.19314575195312, 3.0326156616210938, 0, 111.7823486328125, 10.156118392944336, 0, 114.53553771972656, 16.05186653137207, 0, 120.44091033935547, 14.810588836669922, 0, 121.74047088623047, 14.502042770385742, 0], [136.14117431640625, 6.132287979125977, 0, 131.768310546875, 4.713096618652344, 0, 127.1935043334961, 7.809629440307617, 0, 126.86578369140625, 15.017687797546387, 0, 131.96986389160156, 17.035293579101562, 0, 135.16961669921875, 15.480951309204102, 0], [141.06924438476562, -0.42580604553222656, 0, 141.15806579589844, 9.01315689086914, 0, 141.02349853515625, 15.942779541015625, 0, 144.10586547851562, 16.581512451171875, 0], [138.6463165283203, 4.515403747558594, 0, 144.18801879882812, 5.128634452819824, 0], [152.3751220703125, 4.858579635620117, 0, 146.9415740966797, 3.894773483276367, 0, 146.67848205566406, 14.783700942993164, 0, 157.85435485839844, 15.519957542419434, 0, 157.16909790039062, 4.588711738586426, 0, 151.8485870361328, 4.801843643188477, 0], [163.0326690673828, 5.13278865814209, 0, 162.55902099609375, 15.11068344116211, 0], [162.51889038085938, 9.913464546203613, 0, 163.35328674316406, 5.627782821655273, 0, 165.96218872070312, 5.176994323730469, 0], [176.50576782226562, 6.681243896484375, 0, 174.5966796875, 4.393002510070801, 0, 170.83465576171875, 5.296526908874512, 0, 171.2984161376953, 9.273578643798828, 0, 177.251953125, 12.78972339630127, 0, 177.05616760253906, 15.934538841247559, 0, 171.9617156982422, 17.21417999267578, 0, 170.6193084716797, 14.285594940185547, 0]], "curvesBox": [1.7999868392944336, -0.6077136993408203, 177.251953125, 17.2530517578125]}, "frame": {}, "text": {"text": "Auto-connectors", "lineHeight": 22, "boardSize": [189.60000610351562, 28], "textBox": [0, 0, 177.60000610351562, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 65, "char": "A", "curves": 3, "bounds": [1.75, 2, 500], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 85, "char": "u", "curves": 2, "bounds": [1.5, 2, 500], "offset": [15, 16, 350], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [28, 16, 700], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [35.400001525878906, 16, 1050], "scale": [8, 8, 0.5]}, {"key": 45, "char": "-", "curves": 1, "bounds": [0.8999999761581421, 2, 200], "offset": [50, 16, 1450], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [58.20000076293945, 16, 1650], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [71.19999694824219, 16, 2050], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [85.79999542236328, 16, 2450], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [98.79999542236328, 16, 2800], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [111.79999542236328, 16, 3150], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [125.5999984741211, 16, 3550], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [138.60000610351562, 16, 3950], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [146, 16, 4300], "scale": [8, 8, 0.5]}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [160.60000610351562, 16, 4700], "scale": [8, 8, 0.5]}, {"key": 83, "char": "s", "curves": 1, "bounds": [1.100000023841858, 2, 700], "offset": [168.8000030517578, 16, 5000], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "text", "id": "1urriy3qbg2yq", "type": "ItemDrawingText", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [291.4999694824219, 2341.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [117, 88], "position": [291.4999694824219, 2341.000244140625]}, "aiData": {"decorators": []}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[8.705953598022461, 2.7924489974975586, 0, 6.873417377471924, -0.8343677520751953, 0, 1.2342008352279663, 1.8345108032226562, 0, 2.3260507583618164, 6.598335266113281, 0, 9.666077613830566, 9.520031929016113, 0, 9.398420333862305, 14.907320976257324, 0, 2.880316972732544, 15.151156425476074, 0, 1.64447820186615, 12.090316772460938, 0], [14.116312980651855, -0.117156982421875, 0, 14.484674453735352, 15.550629615783691, 0], [15.015493392944336, 8.60103988647461, 0, 20.87105941772461, 4.969377517700195, 0], [14.568948745727539, 9.0399808883667, 0, 21.482585906982422, 16.17382049560547, 0], [24.783708572387695, 11.513864517211914, 0, 34.69710159301758, 11.006107330322266, 0, 32.860069274902344, 6.599103927612305, 0, 26.889623641967773, 3.910259246826172, 0, 23.829072952270508, 11.463122367858887, 0, 25.858911514282227, 16.38932228088379, 0, 31.456554412841797, 15.990785598754883, 0, 33.21352767944336, 15.250358581542969, 0], [39.172019958496094, -0.9663639068603516, 0, 39.74097442626953, 8.126005172729492, 0, 39.17203903198242, 15.466740608215332, 0, 42.806976318359375, 15.720113754272461, 0], [37.41673278808594, 3.8001699447631836, 0, 43.26556396484375, 3.8644752502441406, 0], [54.29924011230469, 6.16737174987793, 0, 50.1572265625, 4.378857612609863, 0, 44.996463775634766, 6.658193588256836, 0, 45.69308090209961, 13.716113090515137, 0, 50.38466262817383, 15.901496887207031, 0, 54.45446014404297, 13.86292839050293, 0], [60.46284484863281, -1.419198989868164, 0, 60.46759033203125, 15.383312225341797, 0], [60.72041320800781, 11.680436134338379, 0, 61.33056640625, 4.7118730545043945, 0, 66.67121887207031, 4.378676414489746, 0, 68.08419799804688, 12.444160461425781, 0, 67.77511596679688, 15.25439739227295, 0], [87.93907928466797, 16.116769790649414, 0, 86.98858642578125, 8.832117080688477, 0, 80.12547302246094, 9.166583061218262, 0, 79.4800796508789, 16.3382568359375, 0, 86.45732116699219, 15.557083129882812, 0, 87.20600128173828, 9.773032188415527, 0, 87.81832885742188, 5.326342582702637, 0, 83.48883819580078, 3.65897274017334, 0, 79.79512786865234, 5.849795341491699, 0], [93.1863021850586, 5.055882453918457, 0, 92.90695190429688, 16.225173950195312, 0, 100.36351013183594, 16.589542388916016, 0, 100.89340209960938, 8.87432861328125, 0], [100.15269470214844, 4.891351699829102, 0, 100.78416442871094, 15.929101943969727, 0], [105.49530029296875, -0.035602569580078125, 0, 105.29903411865234, 10.107856750488281, 0, 106.09169006347656, 16.941072463989258, 0, 109.36323547363281, 16.68248748779297, 0], [103.2168960571289, 5.779013633728027, 0, 109.44940185546875, 4.94081974029541, 0], [117.77458190917969, 4.520083427429199, 0, 112.87870788574219, 4.997907638549805, 0, 112.17015838623047, 16.810243606567383, 0, 123.32406616210938, 15.630033493041992, 0, 122.53582000732422, 5.1172285079956055, 0, 118.39146423339844, 4.83670711517334, 0], [127.24565887451172, 9.993461608886719, 0, 131.9038543701172, 9.647500991821289, 0], [143.34652709960938, 5.248552322387695, 0, 139.53358459472656, 3.487438201904297, 0, 134.60728454589844, 7.023763656616211, 0, 134.55667114257812, 13.483132362365723, 0, 139.7818603515625, 16.463598251342773, 0, 143.46099853515625, 13.505159378051758, 0], [152.70635986328125, 5.232334136962891, 0, 147.61256408691406, 5.013678550720215, 0, 147.9440460205078, 16.389328002929688, 0, 157.9235382080078, 17.041654586791992, 0, 158.5376739501953, 5.795525550842285, 0, 153.4223175048828, 5.882781982421875, 0], [163.19271850585938, 5.017756462097168, 0, 162.806396484375, 16.69354820251465, 0], [163.5018768310547, 10.502323150634766, 0, 163.9210662841797, 6.246407508850098, 0, 166.9755096435547, 4.574802398681641, 0], [172.5989990234375, 4.621143341064453, 0, 172.06869506835938, 16.86239242553711, 0], [172.73570251464844, 9.882083892822266, 0, 173.53860473632812, 5.392819404602051, 0, 176.49143981933594, 4.929887771606445, 0], [178.9823760986328, 10.084083557128906, 0, 188.3873291015625, 10.673236846923828, 0, 188.11204528808594, 5.466302871704102, 0, 181.8422393798828, 3.394118309020996, 0, 178.6742401123047, 9.845743179321289, 0, 180.8013153076172, 16.1739559173584, 0, 186.7726287841797, 15.921734809875488, 0, 188.2097930908203, 14.367844581604004, 0], [202.30874633789062, 5.564846038818359, 0, 197.5806121826172, 4.385401725769043, 0, 192.3621063232422, 6.3607072830200195, 0, 193.50576782226562, 12.703845977783203, 0, 197.3705291748047, 16.673667907714844, 0, 202.2612762451172, 14.21512508392334, 0], [206.59173583984375, -0.15854454040527344, 0, 206.42567443847656, 7.644057273864746, 0, 206.34083557128906, 15.808326721191406, 0, 209.25267028808594, 15.23478889465332, 0], [204.28842163085938, 3.6543264389038086, 0, 209.5500946044922, 4.3226213455200195, 0]], "curvesBox": [1.2342008352279663, -1.419198989868164, 209.5500946044922, 17.041654586791992]}, "frame": {}, "text": {"text": "Sketch auto-correct", "lineHeight": 22, "boardSize": [223, 28], "textBox": [0, 0, 211, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 83, "char": "S", "curves": 1, "bounds": [1.399999976158142, 2, 800], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 75, "char": "k", "curves": 3, "bounds": [1.25, 2, 600], "offset": [12.199999809265137, 16, 500], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [23.200000762939453, 16, 900], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [37, 16, 1300], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [44.400001525878906, 16, 1650], "scale": [8, 8, 0.5]}, {"key": 72, "char": "h", "curves": 2, "bounds": [1.5, 2, 600], "offset": [57.400001525878906, 16, 2050], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [70.4000015258789, 16, 2450], "scale": [8, 8, 0.5]}, {"key": 65, "char": "a", "curves": 1, "bounds": [1.399999976158142, 2, 900], "offset": [78.4000015258789, 16, 2600], "scale": [8, 8, 0.5]}, {"key": 85, "char": "u", "curves": 2, "bounds": [1.5, 2, 500], "offset": [90.5999984741211, 16, 3150], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [103.5999984741211, 16, 3500], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [111, 16, 3850], "scale": [8, 8, 0.5]}, {"key": 45, "char": "-", "curves": 1, "bounds": [0.8999999761581421, 2, 200], "offset": [125.5999984741211, 16, 4250], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [133.8000030517578, 16, 4450], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [146.8000030517578, 16, 4850], "scale": [8, 8, 0.5]}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [161.40000915527344, 16, 5250], "scale": [8, 8, 0.5]}, {"key": 82, "char": "r", "curves": 2, "bounds": [0.8999999761581421, 2, 400], "offset": [169.60000610351562, 16, 5550], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [177.8000030517578, 16, 5850], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [191.60000610351562, 16, 6250], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [204.60000610351562, 16, 6650], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "4lne0rn9rksq", "id": "4lne0rn9rksr", "type": "ItemDrawingText", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [705.4998779296875, 2403.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [530.9998779296875, 150], "position": [705.4998779296875, 2403.000244140625]}, "aiData": {"decorators": []}, "schedule": {"start": 0, "end": 6.37515771484375}, "behavior": {"type": "label"}, "curves": {"curves": [[8.741683006286621, 2.889481544494629, 0, 6.617424964904785, 0.10989856719970703, 37.31646728515625, 2.1674954891204834, 0.761688232421875, 89.25, 2.046548843383789, 6.606593132019043, 146.82354736328125, 8.672659873962402, 9.690361022949219, 204.8885498046875, 9.117937088012695, 14.565710067749023, 242.45562744140625, 2.633484363555908, 15.408506393432617, 290.562255859375, 0.4306793808937073, 12.99713134765625, 347.62506103515625], [14.537313461303711, 10.388421058654785, 499.182373046875, 23.555116653442383, 11.617196083068848, 540.4984130859375, 23.447059631347656, 6.906435012817383, 597.3490600585938, 16.446481704711914, 4.028695106506348, 650.1714477539062, 13.889627456665039, 11.754029273986816, 695.6719360351562, 15.568727493286133, 16.294885635375977, 752.1198120117188, 21.481975555419922, 16.108016967773438, 804.8926391601562, 23.991992950439453, 16.039152145385742, 861.4287719726562], [36.12018966674805, 6.394003868103027, 850.4544067382812, 32.37421798706055, 5.522496223449707, 892.8743286132812, 27.803850173950195, 7.6670637130737305, 934.3961791992188, 26.71605110168457, 14.088228225708008, 990.3621215820312, 32.39026641845703, 16.791412353515625, 1039.974609375, 36.068450927734375, 14.986201286315918, 1106.733154296875], [41.5301513671875, -0.20560646057128906, 1326.308837890625, 41.16036605834961, 8.930068016052246, 1345.47314453125, 40.77813720703125, 15.388526916503906, 1356.292724609375, 44.420955657958984, 15.336562156677246, 1414.263916015625], [39.39714431762695, 4.326714515686035, 1471.605224609375, 44.98728561401367, 4.913422584533691, 1509.949951171875], [48.79758071899414, 5.108415603637695, 1624.225830078125, 49.469970703125, 16.07166290283203, 1680.153076171875], [48.44282913208008, 1.6799650192260742, 1734.446533203125, 49.52385330200195, 0.8943147659301758, 1772.271728515625], [58.29759216308594, 4.7801055908203125, 1875.939697265625, 53.35254669189453, 3.7144346237182617, 1930.0302734375, 53.57062911987305, 16.01946449279785, 1969.37060546875, 63.97043991088867, 15.377132415771484, 2039.265380859375, 63.54724884033203, 4.201350212097168, 2073.500244140625, 58.93216323852539, 3.6237735748291016, 2139.731689453125], [68.72330474853516, 5.366084098815918, 2306.6796875, 68.1805191040039, 16.411720275878906, 2345.861083984375], [68.36689758300781, 13.464895248413086, 2348.8466796875, 69.10440826416016, 6.164649963378906, 2413.12060546875, 76.28221893310547, 6.010846138000488, 2446.571044921875, 76.10176849365234, 13.21422004699707, 2486.54052734375, 76.66805267333984, 17.328432083129883, 2500.625], [88.73794555664062, 0.8324928283691406, 2843.05859375, 89.64469909667969, 9.687114715576172, 2876.09619140625, 88.77494812011719, 16.251310348510742, 2904.76025390625, 91.91852569580078, 16.685874938964844, 2944.828369140625], [87.82556915283203, 5.690164566040039, 2991.108154296875, 92.60672760009766, 4.910343170166016, 3049.353759765625], [96.27252197265625, 4.738629341125488, 3153.122314453125, 97.27426147460938, 15.724897384643555, 3215.44140625], [96.27748107910156, 0.9861860275268555, 3249.99755859375, 97.5602798461914, 0.9615583419799805, 3314.841796875], [102.59976959228516, -0.16189002990722656, 3445.81396484375, 103.1089859008789, 9.077468872070312, 3451.8134765625, 102.189453125, 17.009504318237305, 3476.37548828125, 106.45426940917969, 16.705018997192383, 3536.1337890625], [100.17436218261719, 5.409110069274902, 3585.40576171875, 106.33596801757812, 5.580358505249023, 3626.528564453125], [110.224853515625, -1.0828361511230469, 3852.473876953125, 110.13802337646484, 16.055931091308594, 3967.805419921875], [114.4361343383789, 10.996803283691406, 4058.750244140625, 125.06529235839844, 10.458271026611328, 4098.8310546875, 123.26821899414062, 6.657981872558594, 4149.98291015625, 116.97403717041016, 4.910844802856445, 4206.07177734375, 114.71472930908203, 10.173267364501953, 4261.77197265625, 117.1690444946289, 16.33112335205078, 4301.33642578125, 122.13103485107422, 16.6530704498291, 4354.84912109375, 123.77508544921875, 14.820850372314453, 4401.84130859375], [137.1541748046875, 4.4955291748046875, 4608.072265625, 136.015625, 16.08824348449707, 4641.1416015625], [136.25015258789062, 0.056041717529296875, 4696.48388671875, 137.34425354003906, 1.185647964477539, 4760.9873046875], [151.298583984375, 6.335328102111816, 4960.53515625, 147.26641845703125, 4.482989311218262, 5029.10986328125, 141.42393493652344, 8.19013786315918, 5060.80029296875, 142.32579040527344, 13.837335586547852, 5126.47119140625, 146.2856903076172, 16.749860763549805, 5172.25, 150.86753845214844, 14.756651878356934, 5219.1259765625], [160.6684112548828, 4.446861267089844, 5284.2958984375, 154.68423461914062, 5.225780487060547, 5337.8125, 154.9260711669922, 16.929964065551758, 5370.7646484375, 165.84222412109375, 15.715320587158203, 5426.61181640625, 165.02906799316406, 4.797791481018066, 5480.64404296875, 160.353759765625, 5.15347957611084, 5530.1083984375], [169.1691436767578, 4.208044052124023, 5746.13916015625, 169.61306762695312, 15.677546501159668, 5782.04248046875], [169.26174926757812, 11.570201873779297, 5775.19189453125, 169.9021759033203, 3.68936824798584, 5831.92578125, 176.616455078125, 3.8516016006469727, 5876.08154296875, 177.30709838867188, 11.5750093460083, 5902.75537109375, 177.22666931152344, 14.92711067199707, 5937.12646484375], [187.87277221679688, 6.024114608764648, 6038.197265625, 186.15402221679688, 4.323566436767578, 6084.28955078125, 182.232666015625, 4.905976295471191, 6142.12060546875, 181.9892120361328, 7.659119606018066, 6183.15380859375, 188.7646026611328, 11.22359848022461, 6244.3232421875, 187.94866943359375, 14.448022842407227, 6282.25244140625, 183.7078857421875, 16.3085880279541, 6341.89111328125, 182.39266967773438, 12.834153175354004, 6375.15771484375]], "curvesBox": [0.4306793808937073, -1.0828361511230469, 188.7646026611328, 17.328432083129883]}, "frame": {}, "text": {"text": "Section title icons", "lineHeight": 22, "boardSize": [201.6000213623047, 28], "textBox": [0, 0, 189.6000213623047, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 83, "char": "S", "curves": 1, "bounds": [1.399999976158142, 2, 800], "offset": [0, 16, 309], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [12.199999809265137, 16, 809], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [26, 16, 1209], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [39, 16, 1609], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [46.400001525878906, 16, 1959], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [51.80000305175781, 16, 2259], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [66.4000015258789, 16, 2659], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [79.4000015258789, 16, 3009], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [87.4000015258789, 16, 3159], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [94.80000305175781, 16, 3509], "scale": [8, 8, 0.5]}, {"key": 84, "char": "t", "curves": 2, "bounds": [0.800000011920929, 2, 500], "offset": [100.20000457763672, 16, 3809], "scale": [8, 8, 0.5]}, {"key": 76, "char": "l", "curves": 1, "bounds": [0.550000011920929, 2, 300], "offset": [107.60000610351562, 16, 4159], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [113.00000762939453, 16, 4409], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [126.80001068115234, 16, 4809], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [134.80001831054688, 16, 4959], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [140.20001220703125, 16, 5259], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [153.20001220703125, 16, 5659], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [167.80001831054688, 16, 6059], "scale": [8, 8, 0.5]}, {"key": 83, "char": "s", "curves": 1, "bounds": [1.100000023841858, 2, 700], "offset": [180.80001831054688, 16, 6409], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "text", "id": "1uomw1nnjmzgo", "type": "ItemDrawingText", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [739.39990234375, 2593.000244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [564.89990234375, 340], "position": [739.39990234375, 2593.000244140625]}, "aiData": {"decorators": []}, "schedule": {"start": 0, "end": 0}, "behavior": {"type": "label"}, "curves": {"curves": [[2.237870216369629, 1.1468706130981445, 0, 2.5353586673736572, 15.859051704406738, 0], [2.7970032691955566, 16.28838539123535, 0, 8.936999320983887, 15.366372108459473, 0], [22.843017578125, 15.116047859191895, 0, 21.943897247314453, 8.116484642028809, 0, 14.929983139038086, 8.688629150390625, 0, 14.849967956542969, 15.852314949035645, 0, 21.48052215576172, 15.914636611938477, 0, 21.807538986206055, 8.766054153442383, 0, 22.892440795898438, 5.37852668762207, 0, 18.644508361816406, 3.3962974548339844, 0, 14.786145210266113, 4.742862701416016, 0], [28.073017120361328, 0.04679298400878906, 0, 27.82098960876465, 16.027053833007812, 0], [27.50853729248047, 13.60901165008545, 0, 28.762115478515625, 5.431807518005371, 0, 37.386661529541016, 5.0825653076171875, 0, 38.075523376464844, 16.005464553833008, 0, 28.283355712890625, 16.834688186645508, 0, 28.641813278198242, 8.837449073791504, 0], [40.80448532104492, 10.091814994812012, 0, 50.999019622802734, 10.494283676147461, 0, 49.35157775878906, 6.215932846069336, 0, 43.53809356689453, 4.317570686340332, 0, 39.97584533691406, 10.726101875305176, 0, 42.33393096923828, 16.637760162353516, 0, 48.100738525390625, 15.689699172973633, 0, 50.414710998535156, 15.545511245727539, 0], [55.32767105102539, 1.0171136856079102, 0, 55.517333984375, 17.066749572753906, 0], [68.52587127685547, 5.130143165588379, 0, 69.5785140991211, 16.650047302246094, 0], [68.99584197998047, 2.206477165222168, 0, 68.9260482788086, 2.073415756225586, 0], [83.73839569091797, 4.83778190612793, 0, 79.91978454589844, 3.117192268371582, 0, 74.4281234741211, 7.0331010818481445, 0, 74.21589660644531, 13.048423767089844, 0, 79.82325744628906, 16.43044090270996, 0, 83.06614685058594, 13.782477378845215, 0], [91.14973449707031, 5.202592849731445, 0, 86.12630462646484, 5.404863357543945, 0, 86.14997863769531, 16.495431900024414, 0, 96.74274444580078, 16.422544479370117, 0, 96.36341857910156, 4.40802001953125, 0, 91.30963897705078, 4.295039176940918, 0], [102.54679870605469, 4.627960205078125, 0, 101.55902099609375, 16.079654693603516, 0], [101.4748764038086, 12.953753471374512, 0, 103.28290557861328, 5.481020927429199, 0, 108.69962310791016, 5.255578994750977, 0, 110.3830795288086, 13.6234130859375, 0, 110.119384765625, 16.809837341308594, 0], [120.31079864501953, 7.331976890563965, 0, 118.32643127441406, 5.557470321655273, 0, 114.43608856201172, 5.979671478271484, 0, 114.19854736328125, 8.24732780456543, 0, 120.47372436523438, 12.530985832214355, 0, 120.4103012084961, 16.745065689086914, 0, 115.1607437133789, 17.579172134399414, 0, 114.67608642578125, 13.923876762390137, 0]], "curvesBox": [2.237870216369629, 0.04679298400878906, 120.47372436523438, 17.579172134399414]}, "frame": {}, "text": {"text": "Label icons", "lineHeight": 22, "boardSize": [133.8000030517578, 28], "textBox": [0, 0, 121.80000305175781, 16]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}, "textItem": {"inputs": [{"key": 76, "char": "L", "curves": 2, "bounds": [1.5, 2, 400], "offset": [0, 16, 0], "scale": [8, 8, 0.5]}, {"key": 65, "char": "a", "curves": 1, "bounds": [1.399999976158142, 2, 900], "offset": [13, 16, 300], "scale": [8, 8, 0.5]}, {"key": 66, "char": "b", "curves": 2, "bounds": [1.7000000476837158, 2, 700], "offset": [25.200000762939453, 16, 850], "scale": [8, 8, 0.5]}, {"key": 69, "char": "e", "curves": 1, "bounds": [1.600000023841858, 2, 600], "offset": [39.80000305175781, 16, 1300], "scale": [8, 8, 0.5]}, {"key": 76, "char": "l", "curves": 1, "bounds": [0.550000011920929, 2, 300], "offset": [53.60000228881836, 16, 1700], "scale": [8, 8, 0.5]}, {"key": 32, "char": " ", "offset": [59.000003814697266, 16, 1950], "scale": [8, 8, 0.5]}, {"key": 73, "char": "i", "curves": 2, "bounds": [0.550000011920929, 2, 400], "offset": [67, 16, 2100], "scale": [8, 8, 0.5]}, {"key": 67, "char": "c", "curves": 1, "bounds": [1.5, 2, 600], "offset": [72.4000015258789, 16, 2400], "scale": [8, 8, 0.5]}, {"key": 79, "char": "o", "curves": 1, "bounds": [1.7000000476837158, 2, 600], "offset": [85.4000015258789, 16, 2800], "scale": [8, 8, 0.5]}, {"key": 78, "char": "n", "curves": 2, "bounds": [1.5, 2, 500], "offset": [100, 16, 3200], "scale": [8, 8, 0.5]}, {"key": 83, "char": "s", "curves": 1, "bounds": [1.100000023841858, 2, 700], "offset": [113, 16, 3550], "scale": [8, 8, 0.5]}]}}, {"v": 4, "sessionId": "1hd6aijp752x2", "id": "1hd6aijp752x3", "type": "ItemDrawingCurve", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [179.49996948242188, 2293.500244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [5, 40.5], "position": [179.49996948242188, 2293.500244140625]}, "aiData": {"followers": [], "decorators": [], "source": "_runAutoCompleteTransform", "CurveType": "unknown", "need_post_processing": true, "connections": [{"id": "8x3u0rp7oqva", "toNodeId": "", "end": false, "contact": [0, 0], "dir": [1, 0], "probability": 1}, {"id": "8x3u0rp7oqvb", "toNodeId": "", "end": true, "contact": [158, 0], "dir": [-1, -0.0], "probability": 1}]}, "schedule": {"start": 0, "end": 0.5798400268554688}, "curves": {"curves": [[0, 0, 0, 39.5, 0, 144.9600067138672, 79, 0, 289.9200134277344, 118.5, 0, 434.8800048828125, 158, 0, 579.8400268554688]], "curvesBox": [0, 0, 158, 0], "sketchControls": [[0, 2, 4]]}, "frame": {}, "text": {"boardSize": [12, 12]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}}, {"v": 4, "sessionId": "1hbb4qjp7j4so", "id": "1cvhmm3p7j5kx", "type": "ItemDrawingCurve", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [836.5, 2284.500244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [662, 31.5], "position": [836.5, 2284.500244140625]}, "aiData": {"followers": [], "decorators": [], "source": "_runAutoCompleteTransform", "CurveType": "unknown", "need_post_processing": true, "connections": [{"id": "1q6br23p7evpg", "toNodeId": "", "end": false, "contact": [0, 0], "dir": [1, 0], "probability": 1}, {"id": "1q6br23p7evph", "toNodeId": "", "end": true, "contact": [142, 0], "dir": [-1, -0.0], "probability": 1}]}, "schedule": {"start": 0.066, "end": 0.738}, "curves": {"curves": [[0, 0, 66, 35.5, 0, 234, 71, 0, 402, 106.5, 0, 570, 142, 0, 738]], "curvesBox": [0, 0, 142, 0], "sketchControls": [[0, 2, 4]]}, "frame": {}, "text": {"boardSize": [12, 12]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}}, {"v": 4, "sessionId": "142z9a3p7gcg6", "id": "v7yq4bp7ga33", "type": "ItemDrawingCurve", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [897.5, 2790.500244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [723, 537.5], "position": [897.5, 2790.500244140625]}, "aiData": {"followers": [], "decorators": [], "source": "_runAutoCompleteTransform", "CurveType": "unknown", "need_post_processing": true, "connections": [{"id": "1lvgwjfph629y", "toNodeId": "", "end": false, "contact": [0, 0], "dir": [1, 0], "probability": 1}, {"id": "1lvgwjfph629z", "toNodeId": "", "end": true, "contact": [87, 0], "dir": [-1, -0.0], "probability": 1}]}, "schedule": {"start": 1.149, "end": 1.6265999755859375}, "curves": {"curves": [[0, 0, 1149, 21.75, 0, 1268.4000244140625, 43.5, 0, 1387.800048828125, 65.25, 0, 1507.199951171875, 87, 0, 1626.5999755859375]], "curvesBox": [0, 0, 87, 0], "sketchControls": [[0, 2, 4]]}, "frame": {}, "text": {"boardSize": [12, 12]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}}, {"v": 4, "sessionId": "13z79obphbpqg", "id": "13z79obphbpqh", "type": "ItemDrawingCurve", "clusterId": "922xezo39i4q", "positioner": {"StaticPosition": [176.49996948242188, 2790.500244140625], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [174.49996948242188, 2253.000244140625], "hookId": "hook-f4ee1e49--blockHook", "deltaHook": [-59.5, 9]}, "clusterToTopLeft": [2, 537.5], "position": [176.49996948242188, 2790.500244140625]}, "aiData": {"followers": [], "decorators": [], "source": "_runAutoCompleteTransform", "CurveType": "unknown", "need_post_processing": true, "connections": [{"id": "zkmsvvph3aq4", "toNodeId": "", "end": false, "contact": [0, 0], "dir": [1, 0], "probability": 1}, {"id": "zkmsvvph3aq5", "toNodeId": "", "end": true, "contact": [133, 0], "dir": [-1, -0.0], "probability": 1}]}, "schedule": {"start": 1.677, "end": 2.209800048828125}, "curves": {"curves": [[0, 0, 1677, 33.25, 0, 1810.199951171875, 66.5, 0, 1943.4000244140625, 99.75, 0, 2076.60009765625, 133, 0, 2209.800048828125]], "curvesBox": [0, 0, 133, 0], "sketchControls": [[0, 2, 4]]}, "frame": {}, "text": {"boardSize": [12, 12]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}}, {"v": 4, "sessionId": "1undbrfmfls01", "id": "1undbrfmfls02", "type": "ItemDrawingCurve", "clusterId": "mffk7fq1nhmu", "positioner": {"StaticPosition": [671.679931640625, 528.5], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [234.20001220703125, 389], "hookId": "hook-c12665f9--block<PERSON>ook", "deltaHook": [0.200042724609375, 16.999969482421875]}, "clusterToTopLeft": [437.47991943359375, 139.5], "position": [671.679931640625, 528.5]}, "aiData": {"followers": [], "decorators": [], "source": "_runConnectionSimpleObject", "CurveType": "unknown", "connections": [{"id": "1urqnd7mfq0at", "toNodeId": "1q7jtmzmpqznk", "end": false, "contact": [0, 0], "dir": [1, 0], "probability": 1}, {"id": "1urqnd7mfq0au", "toNodeId": "zs540rnjirk1", "end": true, "contact": [111.1458740234375, 0], "dir": [-1, -0.0], "probability": 1}]}, "schedule": {"start": 0, "end": 0.14463999938964844}, "curves": {"curves": [[0, 0, 0, 27.786468505859375, 0, 36.15999984741211, 55.57293701171875, 0, 72.31999969482422, 83.35940551757812, 0, 108.47999572753906, 111.1458740234375, 0, 144.63999938964844]], "curvesBox": [0, 0, 111.1458740234375, 0], "sketchControls": [[0, 2, 4]]}, "frame": {}, "text": {"boardSize": [12, 12]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}}, {"v": 4, "sessionId": "v8kbm3mfiz9r", "id": "zodtqjmfiz9c", "type": "ItemDrawingCurve", "clusterId": "mffk7fq1nhmu", "positioner": {"StaticPosition": [580.8399658203125, 419], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [234.20001220703125, 389], "hookId": "hook-c12665f9--block<PERSON>ook", "deltaHook": [0.200042724609375, 16.999969482421875]}, "clusterToTopLeft": [346.63995361328125, 30], "position": [580.8399658203125, 419]}, "aiData": {"followers": [], "decorators": [], "source": "_runAutoCompleteTransform", "CurveType": "unknown", "need_post_processing": true, "connections": [{"id": "hs40jvmfokjx", "toNodeId": "1q7jtmzmpqznk", "end": false, "contact": [0, 85.5], "dir": [0, -1], "probability": 1}, {"id": "hs40jvmfokjy", "toNodeId": "mffk7fq1nhmu", "end": true, "contact": [0, 0], "dir": [-0.0, 1], "probability": 1}]}, "schedule": {"start": 0.002, "end": 0.18119999694824218}, "curves": {"curves": [[0, 85.5, 2, 0, 64.125, 46.79999923706055, 0, 42.75, 91.5999984741211, 0, 21.375, 136.39999389648438, 0, 0, 181.1999969482422]], "curvesBox": [0, 0, 0, 85.5], "sketchControls": [[0, 2, 4]]}, "frame": {}, "text": {"boardSize": [12, 12]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}}, {"v": 4, "sessionId": "2plmzmfiyvj", "id": "4ij3rfmfiyv4", "type": "ItemDrawingCurve", "clusterId": "mffk7fq1nhmu", "positioner": {"StaticPosition": [391.8000183105469, 528.5], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [234.20001220703125, 389], "hookId": "hook-c12665f9--block<PERSON>ook", "deltaHook": [0.200042724609375, 16.999969482421875]}, "clusterToTopLeft": [157.60000610351562, 139.5], "position": [391.8000183105469, 528.5]}, "aiData": {"followers": [], "decorators": [], "source": "_runConnectionSimpleObject", "CurveType": "unknown", "connections": [{"id": "desl3fmfomiv", "toNodeId": "1q7jtmzmpqznk", "end": false, "contact": [102.19998168945312, 0], "dir": [-1, 0], "probability": 1}, {"id": "desl3fmfomiw", "toNodeId": "dcabyjqb92q5", "end": true, "contact": [0, 0], "dir": [1, -0.0], "probability": 1}]}, "schedule": {"start": 0.183, "end": 0.39660000610351565}, "curves": {"curves": [[102.19998168945312, 0, 183, 76.64998626708984, 0, 236.39999389648438, 51.09999084472656, 0, 289.79998779296875, 25.54999542236328, 0, 343.20001220703125, 0, 0, 396.6000061035156]], "curvesBox": [0, 0, 102.19998168945312, 0], "sketchControls": [[0, 2, 4]]}, "frame": {}, "text": {"boardSize": [12, 12]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}}, {"v": 4, "sessionId": "14636x7mfg64p", "id": "14636x7mfg64q", "type": "ItemDrawingCurve", "clusterId": "mffk7fq1nhmu", "positioner": {"StaticPosition": [582.8399658203125, 552.5], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [234.20001220703125, 389], "hookId": "hook-c12665f9--block<PERSON>ook", "deltaHook": [0.200042724609375, 16.999969482421875]}, "clusterToTopLeft": [348.63995361328125, 163.5], "position": [582.8399658203125, 552.5]}, "aiData": {"followers": [], "decorators": [], "source": "_runConnectionSimpleObject", "CurveType": "unknown", "connections": [{"id": "v6pc5nmfol51", "toNodeId": "1q7jtmzmpqznk", "end": false, "contact": [0, 0], "dir": [0, 1], "probability": 1}, {"id": "v6pc5nmfol52", "toNodeId": "1lz86rvph37yd", "end": true, "contact": [0, 91.5], "dir": [-0.0, -1], "probability": 1}]}, "schedule": {"start": 0.35, "end": 0.6435999755859375}, "curves": {"curves": [[0, 0, 350, 0, 22.875, 423.3999938964844, 0, 45.75, 496.79998779296875, 0, 68.625, 570.2000122070312, 0, 91.5, 643.5999755859375]], "curvesBox": [0, 0, 0, 91.5], "sketchControls": [[0, 2, 4]]}, "frame": {}, "text": {"boardSize": [12, 12]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}}, {"v": 4, "sessionId": "1lrqbijmpqznz", "id": "1q7jtmzmpqznk", "type": "ItemDrawingCurve", "clusterId": "mffk7fq1nhmu", "positioner": {"StaticPosition": [494, 504.5], "type": "HookedItemPositioner", "clustered": true, "topLeft": {"inline": false, "static": [234.20001220703125, 389], "hookId": "hook-c12665f9--block<PERSON>ook", "deltaHook": [0.200042724609375, 16.999969482421875]}, "clusterToTopLeft": [259.79998779296875, 115.5], "position": [494, 504.5]}, "aiData": {"followers": [], "decorators": [], "source": "_runAutoCompleteTransform", "shape": {"probability": 0.984476625919342, "type": "rectangle", "minMax": [0, 0, 177.67999267578125, 48]}, "need_post_processing": true, "connections": [], "connectedBy": ["4ij3rfmfiyv4", "1undbrfmfls02", "zodtqjmfiz9c", "14636x7mfg64q"]}, "schedule": {"start": 0, "end": 0.839}, "curves": {"curves": [[0, 0, 0, 168.7186279296875, 0, 199.17120361328125, 177.67999267578125, 0, 209.75, 177.67999267578125, 48, 419.5, 24.53912353515625, 48, 600.28173828125, 0, 48, 629.25, 0, 0, 839]], "curvesBox": [0, 0, 177.67999267578125, 48]}, "frame": {}, "text": {"boardSize": [12, 12]}, "brush": {"type": "pen", "color": "#666666", "autoSuggestColor": "#1AC6FF"}}]}
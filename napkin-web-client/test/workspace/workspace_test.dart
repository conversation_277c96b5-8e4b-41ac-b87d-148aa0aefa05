import 'package:ai_web_lib/ai_web_lib.dart';
import 'package:analytics_web_lib/analytics_web_lib.dart';
import 'package:auth_web_lib/auth_web_lib.dart';
import 'package:test/test.dart';
import 'package:util_web_lib/browser.dart' as browser;
import 'package:util_web_lib/environment.dart';
import 'package:util_web_lib/mocks/mock_request_client.dart';

import '../../web/classic/graph/primitive/asset_data_bank.dart';
import '../../web/classic/library/library_panel.dart';
import '../../web/classic/user/user_config_workspace.dart';
import '../mocks/mock_library_host.dart';
import '../mocks/mock_resource_server.dart';
import '../utils/create_test_functions.dart';

void main() {
  group('Workspace ::', () {
    final workspaceId = 'someRandomWorkspaceId';
    late TestContext context;
    MockRequestClient? mockRequestClient;

    setUp(() {
      context = createContext();
      AuthPrivateer.clearSingleton(context.auth);
      mockRequestClient = MockRequestClient(mockLibraryResponses({}, loggedIn: true));
      mockRequestClient!.addAll(mockWorkspaceResponses(workspaceId, planClass: 'pro_v1'));
      mockRequestClient!.addAll(mockPlanResponses('pro_v1'));
      context.requestHost.client = mockRequestClient;
      DbDataBankRequesterPrivateer.makeTestable(context.requestHost, {});
      AssetDataBankRequesterPrivateer.makeTestable({}, isCli: context.isCli);
    });

    tearDown(() async {
      await context.close();
      expect(context.channelsRegistry, isEmpty);
    });

    test('workspaceId query parameter sets active workspace', () async {
      AuthPrivateer.fakeLoggedIn(context.auth, '<EMAIL>');
      FlagsPrivateer.setVariation('workspace-editor', true);

      // Set up URL with workspaceId query parameter
      Environment.location.assign('${Environment.location.origin}?joinWorkspaceId=$workspaceId');
      expect(browser.getUrlQueryParameter('joinWorkspaceId'), workspaceId);

      final libraryHost = MockLibraryHost(context);
      final libraryPanel = LibraryPanel(libraryHost, showByDefault: false);

      // Fetch workspaces to trigger the query parameter handling
      await context.workSpaceManager.fetchWorkspaces();
      await Future.delayed(const Duration(seconds: 0));

      // Verify that workspaceId was set as active workspace
      final activeWorkspaceId = await context.userConfig.getActiveWorkspaceId();
      expect(activeWorkspaceId, workspaceId);

      // Verify that the query parameter was removed from URL
      expect(browser.getUrlQueryParameter('joinWorkspaceId'), isNull);

      libraryPanel.release();
    });
  });
}

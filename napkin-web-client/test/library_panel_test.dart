import 'package:ai_web_lib/ai_web_lib.dart';
import 'package:auth_web_lib/auth_web_lib.dart';
import 'package:test/test.dart';
import 'package:util_web_lib/mocks/mock_request_client.dart';

import '../web/classic/graph/primitive/asset_data_bank.dart';
import '../web/classic/library/library_panel.dart';
import 'mocks/mock_library_host.dart';
import 'mocks/mock_resource_server.dart';
import 'utils/create_test_functions.dart';

void main() {
  group('LibraryPanel ::', () {
    late TestContext context;
    MockRequestClient? mockRequestClient;

    setUp(() {
      context = createContext();
      AuthPrivateer.clearSingleton(context.auth);
      mockRequestClient = MockRequestClient(mockLibraryResponses({}, loggedIn: true));
      context.requestHost.client = mockRequestClient;
      DbDataBankRequesterPrivateer.makeTestable(context.requestHost, {});
      AssetDataBankRequesterPrivateer.makeTestable({}, isCli: context.isCli);
    });

    tearDown(() async {
      await context.close();
      expect(context.channelsRegistry, isEmpty);
    });

    test('instantiate', () {
      final libraryHost = MockLibraryHost(context);
      final libraryPanel = LibraryPanel(libraryHost, showByDefault: true);
      expect(libraryPanel, isNotNull);
    });

    test('Reloading the library panel', () async {
      AuthPrivateer.fakeLoggedIn(context.auth, '<EMAIL>'); // required to create a pageModel

      final libraryHost = MockLibraryHost(context);
      final libraryPanel = LibraryPanel(libraryHost, showByDefault: true);
      libraryPanel.loadData(clearCache: true);
      final count = libraryPanel.pageCount;
      expect(count, 0);
    });

    test('Workspace selection triggers page loading', () async {
      AuthPrivateer.fakeLoggedIn(context.auth, '<EMAIL>');

      final libraryHost = MockLibraryHost(context);
      final libraryPanel = LibraryPanel(libraryHost, showByDefault: true);

      // Test that _onWorkspaceSelect method exists and can be called
      // This is a basic test to ensure the new functionality doesn't break the existing code
      expect(libraryPanel, isNotNull);
      expect(libraryPanel.host, equals(libraryHost));
    });
  });
}
